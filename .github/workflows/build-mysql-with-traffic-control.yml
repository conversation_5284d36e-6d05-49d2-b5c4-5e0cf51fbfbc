name: Publish mysql-with-traffic-control

on:
  push:
#    branches:
#      - "main"
#      - "*-maint"
    paths:
      - docker/mysql-with-traffic-control/**
  workflow_dispatch:

jobs:
  build-and-publish:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: ./docker/mysql-with-traffic-control
          file: ./docker/mysql-with-traffic-control/Dockerfile
          push: true
          tags: ghcr.io/${{ github.repository }}/mysql-with-traffic-control:${{ github.ref_name }}