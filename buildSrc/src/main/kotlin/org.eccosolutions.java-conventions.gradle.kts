/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
    idea
    `java-library`
    `maven-publish`
}

idea {
    module {
        isDownloadJavadoc = false
        isDownloadSources = false
    }
}

repositories {
//     mavenLocal()
    mavenCentral()

    maven {
        url = uri("https://eccosolutions.github.io/mbassador/maven")
    }

    // These are specified here to match what we have in cosmo-core because they don't get inherited
    maven {
        // id osafoundation.org
        name = "OSAF's repository"
        url = uri("https://eccosolutions.github.io/cosmo/maven")
    }

    maven {
        url = uri("https://jitpack.io")
    }

    maven {
        url = uri("https://repo.spring.io/libs-milestone-local")
    }
}

group = "org.eccosolutions"
version = "1.0.0.CI-SNAPSHOT"
java.sourceCompatibility = JavaVersion.VERSION_17


publishing {
    publications.create<MavenPublication>("maven") {
        from(components["java"])
    }
}
