Running Sheet

    GIT
        sudo apt-get install git
        git config --list --show-origin
        vi ~.gitconfig
            [core]
                repositoryformatversion = 0
                filemode = true
                bare = false
                logallrefupdates = true
                autocrlf = input
                excludesfile = /<path>/eccosolutions/.gitignore_global
                commentChar = @
            [submodule]
                active = .
            [remote "origin"]
                url = **************:eccosolutions/ecco.git
                fetch = +refs/heads/*:refs/remotes/origin/*
            [branch "main"]
                remote = origin
                merge = refs/heads/main
                rebase = true
            [alias]
                st = status
                lola = log --graph --decorate --pretty=oneline --abbrev-commit --all
                hist = log --date=local --pretty=format:\"%C(green)%h%Creset %Cred%<(15)%an%Creset %s %C(dim)(%ad)%Creset\" -n 10
                logmasterto = !bash -c 'git log --date=local --pretty=format:\"%C(green)%h%Creset %Cred%<(15)%an%Creset %s %C(dim)(%ad)%Creset\" $1 ^master --no-merges' -
            [user]
                name = <name>
                email = <email>
            [push]
                default = simple
            [merge]
                conflictstyle = diff3
                tool = meld
            [rerere]
                enabled = false
            [color]
                ui = true
            [branch]
                autosetuprebase = always
            [pull]
                rebase = true
            [log]
                decorate = auto

	CLIENT tools
        sudo apt-get install curl (because snap install fails with https://bugs.launchpad.net/snapcraft/+bug/1928185)

        install nvm - see https://github.com/nvm-sh/nvm#install--update-script
        install node / npm / yarn
            nvm install v18.20.8
            nvm alias default v18.20.8
            npm install -g npm@7.20.5
            npm install -g yarn

        as per ci.yml
        echo "//npm.pkg.github.com/:_authToken=<token here>" > ~/.npmrc

        yarn

    TOMCAT
        wget... place in /opt/tomcat/
        sudo ln -s /opt/tomcat/apache-tomcat-${VERSION} /opt/tomcat/latest
        (don't need normal tomcat security on localhost)

    IJ
	    set top level ecco project jdk - seems to then sort build errors

        shared settings? not come across?
            git config - don't bring branch rules from github
            tomcat default args to consider:
                -ea
                -Djava.net.preferIPv4Stack=true
                -Xmx400m
                -Duser.timezone=UTC
                -Dtest-category=all
                -Dtests.retries=1
                -Dtarget.host.url=http://localhost:8888/ecco-war
                -Dbrowser=CHROME
                -Denv=dev
                -Ddb.schema=ecco
                -Dcookie.insecure=true
                -Djava.locale.providers=JRE,SPI
                -Dliquibase=CREATE

    MYSQL
        sudo apt-get install mysql-server
        sudo mysql_tzinfo_to_sql /usr/share/zoneinfo/ | mysql -u root mysql -p
        NB oracle / sql server drivers gets added in parent pom install (eg install-ojdbc)

    ACCESS
        vi ~/.ssh/config
        Host            <name>
        HostName        <url>
        User            <username>
        Port            <port>
