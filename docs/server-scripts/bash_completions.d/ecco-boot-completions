#!/bin/bash

_ecco_instances()
{
    local cur prev
    COMPREPLY=()
    cur="${COMP_WORDS[COMP_CWORD]}"
    prev="${COMP_WORDS[COMP_CWORD-1]}"
    cmd="${COMP_WORDS[0]}"

    local instances=`ls /opt/ecco-spring-boot/ xargs`
    local jars=`ls *.jar | xargs`
    #echo ""
    #echo "cmd: $cmd"
    #echo "WORD1: ${COMP_WORDS[1]}"
    #echo "CWORD: $COMP_CWORD"
    case $cmd in
      release-boot)

        if [[ $COMP_CWORD -eq 2 ]] ; then
          COMPREPLY=( $(compgen -W "${jars}" -- ${cur}) )
          # This didn't work so well
          # COMPREPLY=( $(compgen -A file -o filenames -G "*.war" -- ${cur}) )
        else
          COMPREPLY=( $(compgen -W "${instances}" -- ${cur}) )
        fi
        ;;
      *)
        COMPREPLY=( $(compgen -W "${instances}" -- ${cur}) )
        ;;
    esac
    return 0
}

#complete -F _ecco_instances checklog
#complete -F _ecco_instances checkcatalinalog
complete -F _ecco_instances configure-boot-instance
#complete -F _ecco_instances configure-server-xml
#complete -F _ecco_instances configure-web-xml
complete -F _ecco_instances release-boot
complete -F _ecco_instances tailbootlog
complete -F _ecco_instances restart-boot
#complete -F _ecco_instances backup_release_and_db
#complete -F _ecco_instances heap-dump
