#!/bin/bash

_ecco_instances()
{
    local cur prev
    COMPREPLY=()
    cur="${COMP_WORDS[COMP_CWORD]}"
    prev="${COMP_WORDS[COMP_CWORD-1]}"
    cmd="${COMP_WORDS[0]}"

    local instances=`ls /opt/ | grep tomcat-ecco | sed -e 's/tomcat-ecco-//g' | xargs`
    local wars=`ls *.war | xargs`
    #echo ""
    #echo "cmd: $cmd"
    #echo "WORD1: ${COMP_WORDS[1]}"
    #echo "CWORD: $COMP_CWORD"
    case $cmd in
      release-*)

        if [[ $COMP_CWORD -eq 2 ]] ; then
          COMPREPLY=( $(compgen -W "${wars}" -- ${cur}) )
          # This didn't work so well
          # COMPREPLY=( $(compgen -A file -o filenames -G "*.war" -- ${cur}) )
        else
          COMPREPLY=( $(compgen -W "${instances}" -- ${cur}) )
        fi
        ;;
      *)
        COMPREPLY=( $(compgen -W "${instances}" -- ${cur}) )
        ;;
    esac
    return 0
}

complete -F _ecco_instances checklog
complete -F _ecco_instances checkcatalinalog
complete -F _ecco_instances configure-instance
complete -F _ecco_instances configure-server-xml
complete -F _ecco_instances configure-web-xml
complete -F _ecco_instances release-master
complete -F _ecco_instances release-release
complete -F _ecco_instances taillog
complete -F _ecco_instances restart
complete -F _ecco_instances backup_release_and_db
complete -F _ecco_instances heap-dump
