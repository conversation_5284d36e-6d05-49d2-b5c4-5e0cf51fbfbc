#!/bin/bash
if [[ "$1" == "" ]] ; then
        echo "you must pass the server instance name"
        exit
fi

inst=$1
file=$1_noBytes.sql


mysqldump -u root -p $inst --set-gtid-purged=OFF --ignore-table=$inst.uploadbytes > $file


# then when restored, do this
echo "CREATE TABLE uploadbytes (id BIGINT(20) PRIMARY KEY NOT NULL, version INT(11), bytes LONGBLOB);" >> $file
echo "insert into uploadbytes (id, version, bytes) select bytesId, 0, '' from uploadfile;" >> $file
echo "insert into uploadbytes (id, version, bytes) select bytesId, 0, '' from svcrec_attachments;" >> $file

#-- insert into uploadbytes (id, version, bytes) select bytesId, 0, '' from referralattachments;
#-- insert into uploadbytes (id, version, bytes) select bytesId, 0, '' from workerattachments;


echo To import to a test instance:
echo ""
echo mysql
echo use testXX;
echo DROP DATABASE testXX;
echo CREATE DATABSE textXX DEFAULT CHARACTER SET utf8;
echo "source $file"

