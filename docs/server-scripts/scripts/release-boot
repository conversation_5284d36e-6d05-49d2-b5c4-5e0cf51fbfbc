#!/bin/bash
# check java stopped on 808?
inst=$1
jarfile=$2
if [[ "$1" == "" ]] ; then
        echo "you must pass the server instance"
        exit
fi
if [[ "$2" == "" ]] ; then
        jarfile="boot-main.jar"
fi
if [ ! -f /root/$jarfile ]; then
    echo "File not found (missing .jar?): /root/$jarfile"
    exit
fi



# shutdown so we are forced to manually start - https://eccosolutions.atlassian.net/browse/OPS-22
service ecco-$inst stop
sleep 3

#./backup_release_and_db $1
# /root/scripts/backup_release_as_prev $1



# transfer
cp -R /root/$jarfile /opt/ecco-spring-boot/$inst/$inst.jar
#rm -R -f /opt/tomcat-ecco-$inst/work/Catalina/localhost/*
#rm -R -f /opt/tomcat-ecco-$inst/webapps/$inst

#chown -R tomcat:tomcat /opt/tomcat-ecco-$inst/webapps/$inst.war
#chown -R tomcat /opt/tomcat-ecco-$inst/logs
#chgrp -R tomcat /opt/tomcat-ecco-$inst/logs

service ecco-$inst start
tail -F /var/log/ecco-$inst.log

