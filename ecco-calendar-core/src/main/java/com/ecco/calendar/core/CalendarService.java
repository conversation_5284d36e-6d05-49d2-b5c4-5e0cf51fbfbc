package com.ecco.calendar.core;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import org.joda.time.DateTime;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * iCal-based calendar service.
 *
 * We have separate interfaces combined into one to represent what an implementation requires.
 * Implemented in CosmoCalendarService, which extends abstract CosmoCalendarBase, but
 * CosmoCalendarService brings in a CalendarRecurringService which has different implementations to investigate the
 * issue of allocating entries on a recurring basis without doing one concrete entry at a time. See DEV-1828 for the options.
 *
 * In summary, CalendarRecurringService can be:
 *   - abstract CalendarRecurringAttendeeBase simply a base class for CalendarRecurringAttendeeDecorator delegating to CosmoCalendarRecurringService.
 *   - CalendarRecurringAttendeeDecorator the new recurring service using disparate master entries (splits master entries to allocate directly to)
 *   - CosmoCalendarRecurringService the original recurring service (allocates every appointment as a concrete entry)
 *   - archive: CosmoCalendarRecurringOverrideService was a look at allocation to a master entry which could override another master entry
 */
@WriteableTransaction
public interface CalendarService extends CalendarRecurringService, CalendarNonRecurringService, CalendarServiceBase {

    static DateTime adjustStartToDayOfWeek(DateTime start, Set<Integer> calendarDays) {
        if (!calendarDays.isEmpty()) {
            int startDayOfWeekISO = start.getDayOfWeek();
            int startDayOfWeekCalendar = startDayOfWeekISO == 7 ? 1 : startDayOfWeekISO + 1;
            List<Integer> daysSorted = calendarDays
                    .stream()
                    .sorted(Integer::compareTo)
                    .collect(Collectors.toList());
            Integer adjustOverStartDay = daysSorted
                    .stream()
                    .filter(c -> c >= startDayOfWeekCalendar)
                    .findFirst().orElse(null);
            Integer adjustUnderStartDay = daysSorted
                    .stream()
                    .filter(c -> c < startDayOfWeekCalendar)
                    .findFirst().orElse(null);

            // prioritise any past the current date
            if (adjustOverStartDay != null) {
                start = start.plusDays(adjustOverStartDay - startDayOfWeekCalendar);
            } else {
                if (adjustUnderStartDay != null) {
                    start = start.plusDays((7 + adjustUnderStartDay) - startDayOfWeekCalendar);
                }
            }
        }

        return start;
    }

}
