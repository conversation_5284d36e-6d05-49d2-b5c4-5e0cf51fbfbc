package com.ecco.calendar.dom;

import com.ecco.dom.IdName;

import java.util.HashMap;
import java.util.Map;

public enum EventType implements IdName<Integer> {

    Other(-1), Interview(0), Review(1), Target(2), Expires(3), Meeting(4);

    private int value;

    private static class MapHolder {
        private final static Map<Integer, EventType> valueMap;

        static {
            valueMap = new HashMap<>(EventType.values().length);
            for (EventType eventType : EventType.values()) {
                valueMap.put(eventType.toInt(), eventType);
            }
        }
    }

    EventType(int value) {
        this.value = value;
    }

    public int toInt() {return value;}

    public static EventType fromInt(int value) {
        EventType eventType = MapHolder.valueMap.get(value);
        if (eventType == null) {
            eventType = Other;
        }
        return eventType;
    }

    @Override
    public Integer getId() {
        return value;
    }
    @Override
    public String getName() {
        if (this != Other) {
            return name();
        }
        return "other";
    }

}

/*
@javax.persistence.Entity
@javax.persistence.Table(name="eventtypes")
public class EventType extends IdName implements Serializable {

    public static EventType TARGETDATE = new EventType(1L, "target date");
    public static EventType REVIEW = new EventType(2L, "review");

    public EventType() {}
    public EventType(Long id, String name) {
        super(id, name);
    }

}
*/
