package com.ecco.calendar.event;

import com.ecco.calendar.core.RecurringEntry;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import org.jspecify.annotations.Nullable;
import java.time.Instant;

/**
 * When anything related to a rota demand change is made, this is triggered before the change is made.
 * This can therefore represent a deleted change.
 */
public class RotaDemandPostChangeEvent extends ApplicationEvent {

    @Getter
    int serviceRecipientId;
    @Getter
    Instant from;
    @Getter
    boolean skipReset;
    @Getter
    RecurringEntry.RecurringEntryHandle demandHandle;

    /**
     * 'from' allows us to reset the CalendarEventSnapshot cache from the relevant time of the change in the audit.
     * NB We could just use 'now' (DateTime) which should mean nothing will trip up 'canReset' (because no evidence will be done),
     * but this may still leave some invalid cache in the past which could cause fail in a fk, and wouldn't be accurate for visit verification.
     * @param skipReset Don't try to reset existing recurrences - skip them, because we'll be adding new schedules
     * @return the instant to reset the cache from
     */
    public RotaDemandPostChangeEvent(Object source, int serviceRecipientId, @Nullable Instant from, RecurringEntry.@Nullable RecurringEntryHandle demandHandle, boolean skipReset) {
        super(source);
        this.serviceRecipientId = serviceRecipientId;
        this.from = from;
        this.demandHandle = demandHandle;
        this.skipReset = skipReset;
    }

}
