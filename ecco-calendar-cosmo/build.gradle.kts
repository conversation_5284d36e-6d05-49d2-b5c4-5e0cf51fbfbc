/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

dependencies {
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-config"))
    implementation(project(":ecco-contacts"))
    implementation(project(":ecco-calendar-core"))
    implementation(project(":ecco-security"))
    implementation(project(":ecco-security-core"))
    implementation(project(":ecco-service-config"))

    implementation("com.github.eccosolutions:cosmo-core")
    implementation("com.google.guava:guava")
    implementation("joda-time:joda-time:2.10.8")
    implementation("org.mnode.ical4j:ical4j")

    testImplementation(project(":test-support"))
}

description = "ecco-calendar-cosmo"
