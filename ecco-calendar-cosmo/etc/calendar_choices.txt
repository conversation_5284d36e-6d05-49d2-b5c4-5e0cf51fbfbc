some spring intergration - mq http://www.bedework.org/trac/bedework/wiki/JbossConfig/integrateActivemq/broker-confi.xml

27/10/2011
Reply  ?
 Adam <PERSON>er
To <EMAIL>
bedework....java + hibernate...
http://www.jasig.org/bedework/features

outlook - http://www.bedework.org/bedework/update.do
could integrate - 'fully enabled in outlook' by enabling outlook to use calDav the open connector?
could integrate - 'seen' in outlook by subscribing to public calendar

funambol?



In a nutshell....either we become a 'client' to a calendaring server, or we adopt an exchange-like calendar solution (or buy our own cloud from funambol/sogo?) , or we persuade the organisations to use our simpler calendar solution with the open source connector to 'correct' outlooks icalendar formats...


for synching....(a feed is different, see below)
========

seems ppl write an 'exchange' interface for outlook to fool it and allow all sorts of (industry standard) devices to then connect to their server, of which outlook is just another client.

but we don't want a full blown exchange server? ... like these... or is cosmo/bede like that anyway!
http://www.sogo.nu/about/overview.html uses http://www.openchange.org/ (openchange is a exchange impl)
funambol (have their own? exchange impl?)
zimbra (have their own exchange impl)
bedework uses http://openconnector.org/
if we used an exchange-style program, could outlook connect to 2 exchanges??

the alternative is to get outlook to talk these standards - such as calDAV [calDav is a way to access scheduling info using the format icalendar]
such as http://openconnector.org/ and 
but that relies on the organisation changing things...
[syncML is a way of syncing data - standard to avoid vendor-lockin. also, its focus is on contact and calendar etc.]


feed
===
whilst I am sure cosmo can provide a feed (it appears not from this link)
http://chandlerproject.org/Projects/GetStarted#Subscribe%20to%20ICAL%20(Google,%20.Mac)
and here it seems possible but tricky: http://markmail.org/message/nwlpxk3xdqktydr5#query:+page:1+mid:nwlpxk3xdqktydr5+state:results
in a nutshell Outlook adheres to a different standard for free/busy...

outlook itself doesn't do great at being icalendar compatible! icalendar is the format of the message.

bedework like cosmo have no problems with lightning, apple ical etc... but bedework does say they provide a sync to outlook which may be simpler than cosmo ("This can be used to subscribe to a bedework calendar from Google or Outlook" http://www.bedework.org/bedework/update.do) and of course zimbra etc...but often licence restrictions will play their part.
