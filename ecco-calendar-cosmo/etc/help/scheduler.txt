the Scheduler holds users schedules in memory
where a schedule is a username against some Properties
which is a set of preferences stored in the db users preferences - cosmo.scheduler.job.[name].[key] = [value]
the Scheduler starts and submits a job first to refresh users schedules every hour
a user adds to a schedule by ...??? 
a getUsersWithSchedules calls UserPreferencesScheduleDao which runs users.withSchedules defined in cosmo.model.hibernate.package-info.java
which runs select u from HibUser u join u.preferences preference where preference.key like 'cosmo.scheduler.job.%.enabled' and preference.value='true' 
the scheduler then calls the SchedulerImpl.refreshSchedules and checks for changes
a schedule is added to an actual job through org.osaf.cosmo.scheduler.SchedulerImpl.scheduleUserJob
which looks at the property 'type' (ie forward for forward looking scheduler) and adds a job to it
so org.osaf.cosmo.scheduler.ForwardLookingJobTypeScheduler is responsible for configuring the job
and the properties referenced there are timezone, locale, cronexp, reportType (daily weekly) and collection holding the collections to schedule
the ForwardLookingJobTypeScheduler sets up the job: org.osaf.cosmo.scheduler.ForwardLookingNotificationJob
which runs and gets the current time as a start date and adds a day or week for the end time then gets the items

SO ACTUALLY...the approach is different to med...
cosmo sets up the ability to configure their own schedule, eg, users can receive weekly or daily etc (or custom cron job)...
which basically means that this is the ability to inform users of their tasks - NOT any reminder feature of an event
although in ranking the events in ForwardLookingNotificationJob the alarm is used to rank
 - where 'alarm' is typically a duration relative to the event
[NB I have not determined if alarms are included in getting events within a time period - see ForwardLookingNotificationJob.getCollectionResults]

MED however has a schedule every 5 mins and picks out what is needed to send...
this allows us to specify the logic of when and who to - especially useful when rotas come?
we would just need to utilise the same as ForwardLookingNotificationJob if we wanted to include calendar style reminders
