package com.ecco.calendar.cosmo;

import net.fortuna.ical4j.model.property.Priority;
import org.junit.Test;

import java.util.Arrays;

import static org.junit.Assert.assertArrayEquals;

/**
 * @since 20/06/15
 */
public class PriorityComparatorTest {
    @Test
    public void testOrdering() {
        Priority[] priorities = { Priority.LOW, Priority.HIGH, Priority.UNDEFINED, Priority.MEDIUM, new Priority(0), new Priority(9), new Priority(7) };
        Arrays.sort(priorities, new PriorityComparator());
        assertArrayEquals(new Priority[] { Priority.HIGH, Priority.MEDIUM, new Priority(7), Priority.LOW, Priority.LOW, Priority.UNDEFINED, Priority.UNDEFINED }, priorities);
    }
}
