package com.ecco.calendar.msgraph;

import com.ecco.calendar.msgraph.data.Calendar;
import com.ecco.calendar.msgraph.data.Event;
import org.springframework.cloud.square.retrofit.core.RetrofitClient;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.time.Instant;
import java.util.List;

@RetrofitClient(value = "office365", url = "https://graph.microsoft.com/")
public interface MsGraphCalendarService {

    @GET("/v1.0/me/calendars")
    Call<Response<List<Calendar>>> listCalendars();

    @GET("/v1.0/me/calendarview")
    Call<Response<List<Event>>> calendarView(
            @Query("startdatetime") Instant startDateTime,
            @Query("enddatetime") Instant endDateTime);

    @POST("/v1.0/me/events")
    Call<Response<Void>> createEvent(@Body Event event);
}
