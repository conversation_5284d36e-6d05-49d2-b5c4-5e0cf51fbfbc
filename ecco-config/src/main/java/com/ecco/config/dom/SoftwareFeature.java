package com.ecco.config.dom;

import java.io.Serializable;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;

import com.ecco.config.service.FeatureEnablementVoter.Vote;

@Entity
@Table(name = "cfg_feature")
@Access(AccessType.FIELD)
public class SoftwareFeature implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(nullable=false, length=63)
    private String name;

    private String description;

    @Enumerated(EnumType.STRING)
    private Vote defaultVote;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Vote getDefaultVote() {
        return defaultVote;
    }

    public void setDefaultVote(Vote defaultVote) {
        this.defaultVote = defaultVote;
    }
}
