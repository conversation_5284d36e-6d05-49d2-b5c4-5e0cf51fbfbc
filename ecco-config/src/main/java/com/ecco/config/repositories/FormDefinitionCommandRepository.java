package com.ecco.config.repositories;

import com.ecco.infrastructure.spring.data.BaseCommandRepository;
import org.springframework.data.jpa.repository.Query;

import com.ecco.config.dom.FormDefinitionCommand;

import java.util.List;
import java.util.UUID;

/**
 * Persistence for form definition commands
 */
public interface FormDefinitionCommandRepository extends BaseCommandRepository<FormDefinitionCommand, Long> {

    <TFormDefinitionCommand extends FormDefinitionCommand> TFormDefinitionCommand save(TFormDefinitionCommand command);

//    @Query("SELECT ru FROM FormDefinitionUpdateCommand ru WHERE ru.formDefUuid = ?")
//    public List<FormDefinitionUpdateCommand> findAllUpdatesByFormDefUuid(UUID formDefUuid); // client or server side filter adequate

    @Query("SELECT ru FROM FormDefinitionCommand ru")
    List<FormDefinitionCommand> findAllUpdates();

    FormDefinitionCommand findOneByUuid(UUID uuid);

}
