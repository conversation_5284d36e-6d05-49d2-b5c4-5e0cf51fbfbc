package com.ecco.dom.contacts;

import static com.ecco.infrastructure.util.EccoStringUtils.appendIfExists;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Transient;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Type;
import org.springframework.util.Assert;

import com.ecco.infrastructure.hibernate.HibTypeNames;


@Embeddable
//@Access(AcessType.FIELD) TODO finish migrating Embeddable
public class Address implements AddressLike, Serializable {

    private static final long serialVersionUID = 1L;

    @Transient
    Long projectId;
    @Transient
    public Long getProjectId() {
        return projectId;
    }
    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    @Column(name="addressline1")
    private String line1;
    @Column(name="addressline2")
    private String line2;
    @Column(name="addressline3")
    private String line3;
    @Column(name="addresstown")
    private String town;
    @Column(name="addresscounty")
    private String county;
    @Column(name="addresspostcode")
    private String postCode;
    @Column(name="addresscountry")
    private String country;

    @Override
    @Column(name="addresscounty") // TODO Remove property annotations when all on field access
    public String getCounty() {
        return county;
    }

    @Override
    @Type(type=HibTypeNames.ENCRYPTED_STRING)
    @Column(name="addressline1")
    public String getLine1() {
        return line1;
    }
    @Override
    @Type(type=HibTypeNames.ENCRYPTED_STRING)
    @Column(name="addressline2")
    public String getLine2() {
        return line2;
    }
    @Override
    @Type(type=HibTypeNames.ENCRYPTED_STRING)
    @Column(name="addressline3")
    public String getLine3() {
        return line3;
    }
    @Override
    @Column(name="addresspostcode")
    public String getPostCode() {
        return postCode;
    }
    @Override
    @Column(name="addresstown")
    public String getTown() {
        return town;
    }
    @Override
    @Column(name="addresscountry")
    public String getCountry() {
        return country;
    }

    @Override
    public String toCommaSepString() {
        final String separator = ", ";
        StringBuilder builder = new StringBuilder()
            .append(appendIfExists(getLine1(), separator))
            .append(appendIfExists(getLine2(), separator))
            .append(appendIfExists(getLine3(), separator))
            .append(appendIfExists(getTown(), separator))
            .append(appendIfExists(getCounty(), separator))
            .append(appendIfExists(getPostCode(), separator))
            .append(appendIfExists(getCountry(), separator));

        // lose the last separator and space
        String commandStr = builder.toString();
        if (commandStr.length() > 2) {
            commandStr = StringUtils.left(commandStr, commandStr.length()-2);
        }
        return commandStr;
    }

    @Override
    public String toString() {
        return toCommaSepString();
    }

    public void setCounty(String county) {
        this.county = county;
    }
    public void setLine1(String line1) {
        this.line1 = line1;
    }
    public void setLine2(String line2) {
        this.line2 = line2;
    }
    public void setLine3(String line3) {
        this.line3 = line3;
    }
    public void setPostCode(String postCode) {
        this.postCode = AddressLike.formatPostcode(postCode);
    }

    /** Sets the postcode to exactly this value without cleaning up format - for search only */
    public void setPostCodeNoFormat(String postCode) {
        this.postCode = postCode;
    }

    public void setTown(String town) {
        this.town = town;
    }
    public void setCountry(String country) {
        this.country = country;
    }

    public Address copy() {
        Address newA = new Address();
        newA.setLine1(getLine1());
        newA.setLine2(getLine2());
        newA.setLine3(getLine3());
        newA.setTown(getTown());
        newA.setPostCode(getPostCode());
        newA.setCounty(getCounty());
        newA.setCountry(getCountry());
        return newA;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Address)) {
            return false;
        }

        Address address = (Address) o;

        if (country != null ? !country.equals(address.country) : address.country != null) {
            return false;
        }
        if (county != null ? !county.equals(address.county) : address.county != null) {
            return false;
        }
        if (line1 != null ? !line1.equals(address.line1) : address.line1 != null) {
            return false;
        }
        if (line2 != null ? !line2.equals(address.line2) : address.line2 != null) {
            return false;
        }
        if (line3 != null ? !line3.equals(address.line3) : address.line3 != null) {
            return false;
        }
        if (postCode != null ? !postCode.equals(address.postCode) : address.postCode != null) {
            return false;
        }
        if (town != null ? !town.equals(address.town) : address.town != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = line1 != null ? line1.hashCode() : 0;
        result = 31 * result + (line2 != null ? line2.hashCode() : 0);
        result = 31 * result + (line3 != null ? line3.hashCode() : 0);
        result = 31 * result + (town != null ? town.hashCode() : 0);
        result = 31 * result + (county != null ? county.hashCode() : 0);
        result = 31 * result + (postCode != null ? postCode.hashCode() : 0);
        result = 31 * result + (country != null ? country.hashCode() : 0);
        return result;
    }

    public Address withStreetAddress(String... lines) {
        Assert.state(lines.length > 0 && lines.length <= 3, "between 1 and 3 lines must be specified");
        this.line1 = lines[0];
        if (lines.length > 1) {
            this.line2 = lines[1];
        }
        if (lines.length > 2) {
            this.line3 = lines[2];
        }
        return this;
    }

    public Address withTown(String town) {
        this.town = town;
        return this;
    }

    public Address withCounty(String county) {
        this.county = county;
        return this;
    }

    public Address withCountry(String country) {
        this.country = country;
        return this;
    }
    public void inheritUnsetFieldsFrom(Address other) {
        if (line1 == null) { line1 = other.line1; }
        if (line2 == null) { line2 = other.line2; }
        if (line3 == null) { line3 = other.line3; }
        if (town == null) { town = other.town; }
        if (county == null) { county = other.county; }
        if (postCode == null) { postCode = other.postCode; }
        if (country == null) { country = other.country; }
    }

    public static Address from(AddressLike src) {
        if (src == null) {
            return null;
        }
        Address addr = new Address();
        addr.line1 = src.getLine1();
        addr.line2 = src.getLine2();
        addr.line3 = src.getLine3();
        addr.town = src.getTown();
        addr.postCode = src.getPostCode();
        addr.county = src.getCounty();
        addr.country = src.getCountry();
        return addr;
    }
}
