package com.ecco.dom.contracts;

import com.ecco.dom.BaseServiceRecipientEvidence;
import com.ecco.dom.Individual;
import com.ecco.dom.ServiceRecipientAttachment;
import com.ecco.dom.contacts.AddressLike;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.jspecify.annotations.NonNull;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Entity
@Getter
@Setter
@DiscriminatorValue(ContractServiceRecipient.DISCRIMINATOR)
public class ContractServiceRecipient extends BaseServiceRecipientEvidence {

    private static final long serialVersionUID = 1L;
    public static final String DISCRIMINATOR = "cont";
    public static final String PREFIX = "ct"; // see BaseServiceRecipient.getPrefix, and service-recipient-dto.ts

    @OneToOne(mappedBy="serviceRecipient", fetch=FetchType.LAZY, cascade= CascadeType.REMOVE)
    private Contract contract;

    @OneToMany(mappedBy = "serviceRecipient", orphanRemoval = true, cascade = CascadeType.REMOVE, fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @OrderBy("id DESC")
    private Set<ServiceRecipientAttachment> attachments = new HashSet<>(); // see https://hibernate.atlassian.net/browse/HHH-9940

    @Override
    public String getDisplayName() {
        return contract.getName();
    }

    @Override
    public AddressLike getAddress() {
        return null;
    }

    @Override
    public String getCalendarId() {
        return null;
    }

    @Override
    public Individual getContact() {
        return null;
    }

    @Override
    public Map<String,String> getTextMap() {
        return null;
    }

    @Override
    public String getParentCode() {
        return contract.getId().toString();
    }

    @Override
    public Long getParentId() {
        return contract.getId().longValue();
    }

    @Override
    public Contract getTargetEntity() {
        return contract;
    }

    @NonNull
    @Override
    public String getPrefix() {
        return PREFIX;
    }
}
