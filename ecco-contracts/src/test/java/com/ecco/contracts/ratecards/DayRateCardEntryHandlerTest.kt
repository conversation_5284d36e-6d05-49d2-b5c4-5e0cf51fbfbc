package com.ecco.contracts.ratecards

import com.ecco.dom.contracts.RateCardEntry
import com.google.common.collect.Range
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import java.math.BigDecimal
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.stream.Stream

class DayRateCardEntryHandlerTest {

    companion object {
        private val LONDON_ZONE = ZoneId.of("Europe/London")

        @JvmStatic
        @Suppress("ktlint:standard:property-naming")
        fun dateRangeTestCases(): Stream<Arguments> {
            // Define test cases with start and end dates
            val jan1 = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, LONDON_ZONE)
            val jan2 = ZonedDateTime.of(2023, 1, 2, 0, 0, 0, 0, LONDON_ZONE)
            val jan6 = ZonedDateTime.of(2023, 1, 6, 0, 0, 0, 0, LONDON_ZONE)
            val jan1Noon = ZonedDateTime.of(2023, 1, 1, 12, 0, 0, 0, LONDON_ZONE)

            val mar1_00 = ZonedDateTime.of(2023, 3, 1, 0, 0, 0, 0, LONDON_ZONE)
            val mar1_13 = ZonedDateTime.of(2023, 3, 1, 13, 0, 0, 0, LONDON_ZONE)
            val apr1_00 = ZonedDateTime.of(2023, 4, 1, 0, 0, 0, 0, LONDON_ZONE)
            val apr1_15 = ZonedDateTime.of(2023, 4, 1, 15, 0, 0, 0, LONDON_ZONE)
            val may1_17 = ZonedDateTime.of(2023, 5, 1, 17, 0, 0, 0, LONDON_ZONE)
            val oct1_15 = ZonedDateTime.of(2023, 10, 1, 15, 0, 0, 0, LONDON_ZONE)
            val nov1_17 = ZonedDateTime.of(2023, 11, 1, 17, 0, 0, 0, LONDON_ZONE)

            return Stream.of(
                // 1 Mar 00:00 GMT to 1 Apr 00:00 BST (31 days & 2 hours)
                Arguments.of(
                    "1 Mar 00:00 GMT to 1 Apr 00:00 BST",
                    mar1_00,
                    apr1_00,
                    BigDecimal("10.00"),
                    1,
                    BigDecimal("310.00"),
                    Range.closedOpen(apr1_00, apr1_00),
                ),
                // Simple 1-day case
                Arguments.of(
                    "Simple 1-day period",
                    jan1,
                    jan2,
                    BigDecimal("10.00"),
                    1,
                    BigDecimal("10.00"),
                    Range.closedOpen(jan2, jan2),
                ),
                // 5-day case
                Arguments.of(
                    "5-day period",
                    jan1,
                    jan6,
                    BigDecimal("10.00"),
                    1,
                    BigDecimal("50.00"),
                    Range.closedOpen(jan6, jan6),
                ),
                // Partial day (should still count as 0 days due to Duration.toDays() truncating)
                Arguments.of(
                    "Partial day period",
                    jan1Noon,
                    jan2,
                    BigDecimal("10.00"),
                    1,
                    BigDecimal("0.00"),
                    Range.closedOpen(jan1Noon, jan2),
                ),
                // 1 Apr 15:00 BST to 1 May 17:00 BST (30 days)
                Arguments.of(
                    "1 Apr 15:00 BST to 1 May 17:00 BST",
                    apr1_15,
                    may1_17,
                    BigDecimal("10.00"),
                    1,
                    BigDecimal("300.00"),
                    Range.closedOpen(may1_17.minusHours(2), may1_17),
                ),
                // Custom units case (5 days with 2 units)
                Arguments.of(
                    "Custom units case",
                    jan1,
                    jan6,
                    BigDecimal("20.00"),
                    2,
                    BigDecimal("50.00"),
                    Range.closedOpen(jan1.plusDays(5), jan6),
                ),
                // 1 Mar 15:00 GMT to 1 Apr 17:00 BST (31 days & 2 hours)
                Arguments.of(
                    "1 Mar 13:00 GMT to 1 Apr 15:00 BST",
                    mar1_13,
                    apr1_15,
                    BigDecimal("10.00"),
                    1,
                    BigDecimal("310.00"),
                    Range.closedOpen(apr1_15.minusHours(2), apr1_15),
                ),
                // 1 Oct 15:00 BST to 1 Nov 17:00 GMT (31 days & 2 hours)
                Arguments.of(
                    "1 Oct 15:00 BST to 1 Nov 17:00 GMT",
                    oct1_15,
                    nov1_17,
                    BigDecimal("10.00"),
                    1,
                    BigDecimal("310.00"),
                    Range.closedOpen(nov1_17.minusHours(2), nov1_17),
                ),
            )
        }
    }

    @ParameterizedTest(name = "{0} ({1} to {2})")
    @MethodSource("dateRangeTestCases")
    @DisplayName("Should calculate correct charge for date range")
    fun shouldCalculateCorrectChargeForDateRange(
        testName: String,
        startDateTime: ZonedDateTime,
        endDateTime: ZonedDateTime,
        unitCharge: BigDecimal,
        units: Int,
        expectedCharge: BigDecimal,
        expectedRange: Range<ZonedDateTime>,
    ) {
        // Given
        val handler = DayRateCardEntryHandler()
        val rateCardEntry = mock(RateCardEntry::class.java)
        val chargePeriod = Range.closedOpen(startDateTime, endDateTime)

        `when`(rateCardEntry.unitCharge).thenReturn(unitCharge)
        `when`(rateCardEntry.units).thenReturn(units)

        // When
        val result = handler.handle(rateCardEntry, chargePeriod)

        // Then
        assertEquals(expectedCharge, result.first, "The calculated charge should match the expected value")
        assertEquals(expectedRange, result.second, "The remaining range should be correctly calculated")
    }
}