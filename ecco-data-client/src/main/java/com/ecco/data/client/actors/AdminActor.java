package com.ecco.data.client.actors;

import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

/**
 * High level interactions which equate to an action a user might make via a UI, except this does it directly
 * on the API.
 */
public class AdminActor extends BaseActor {

    public AdminActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<Boolean> isAclEnabled() {
        ResponseEntity<Boolean> response = restTemplate.getForEntity(apiBaseUrl + "acls/enabled/", Boolean.class);
        return response;
    }

    public ResponseEntity<Boolean> isAclConfigEnabled() {
        ResponseEntity<Boolean> response = restTemplate.getForEntity(apiBaseUrl + "acls/enabledConfig/", Boolean.class);
        return response;
    }

}
