package com.ecco.data.client.actors;

import com.ecco.data.client.ServiceOptions;
import com.ecco.data.client.WebApiSettings;
import com.ecco.rota.webApi.dto.AgreementResource;
import com.ecco.rota.webApi.dto.RecurringDemandScheduleDto;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.listsConfig.AppointmentTypeViewModel;
import com.ecco.webApi.rota.ServiceAgreementCommandDto;
import com.ecco.webApi.rota.ServiceRecipientAppointmentScheduleCommandDto;
import com.ecco.webApi.viewModels.Result;
import com.google.gson.JsonObject;
import org.hamcrest.Matchers;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Optional;

import static com.ecco.webApi.evidence.BaseCommandViewModel.OPERATION_UPDATE;
import static java.util.stream.Collectors.toList;

/**
 * API actions relating to agreements of appointments.
 *
 * @since 22/08/2014
 */
public class AgreementActor extends BaseActor {
    private static final DateTimeFormatter ISODATEFORMAT = ISODateTimeFormat.date();
    private static final String agreementsBaseUrl = WebApiSettings.APPLICATION_URL + "/api/rota/agreements/";

    public AgreementActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public Result createAgreement(int serviceRecipientId, LocalDate start, LocalDate end, Integer contractId) {
        var cmd = new ServiceAgreementCommandDto(serviceRecipientId, start, end, contractId, 1099f, 80f);
        var response = restTemplate.postForEntity(
                agreementsBaseUrl + "serviceRecipient/" + serviceRecipientId + "/", cmd, Result.class);
        Assert.state(Matchers.equalTo(HttpStatus.CREATED).matches(response.getStatusCode()), "Allocation failed");
        log.info("created Service Agreement for srId: {}", serviceRecipientId);
        return response.getBody();
    }

    public void editAgreementEnd(int serviceRecipientId, long agreementId, ChangeViewModel<LocalDate> endChange) {
        ServiceAgreementCommandDto cmd = new ServiceAgreementCommandDto(serviceRecipientId, OPERATION_UPDATE);
        cmd.setAgreementId(agreementId);
        cmd.setEnd(endChange);
        ResponseEntity<String> response = restTemplate.postForEntity(
                agreementsBaseUrl + "serviceRecipient/" + serviceRecipientId + "/", cmd, String.class);
        Assert.state(Matchers.equalTo(HttpStatus.CREATED).matches(response.getStatusCode()), "Allocation failed");
        log.info("updated Service Agreement for srId: {}", serviceRecipientId);
    }

    public String createAppointmentType(ServiceOptions service, String name, int duration) {
        JsonObject newAppointmentType = new JsonObject();
        newAppointmentType.addProperty("name", name);
        newAppointmentType.addProperty("service", service.getServiceName());
        newAppointmentType.addProperty("recommendedDurationInMinutes", duration);
        final HttpHeaders postHeaders = new HttpHeaders();
        postHeaders.setContentType(MediaType.APPLICATION_JSON);
        var postRequest = new HttpEntity<>(newAppointmentType.toString(), postHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(apiBaseUrl + "appointmentTypes/", postRequest, String.class);
        Assert.state(Matchers.equalTo(HttpStatus.CREATED).matches(response.getStatusCode()), "Successfully created appointment type");

        log.info("created appt type: {}", name);
        return name;
    }

    public ResponseEntity<RecurringDemandScheduleDto[]> getAppointmentSchedulesFromAgreementForClient(int serviceRecipientId, DateTime now) {
        String appointmentScheduleUri = getFirstAgreement(serviceRecipientId, now).getAppointmentsLink().getHref();
        return restTemplate.getForEntity(appointmentScheduleUri, RecurringDemandScheduleDto[].class);
    }

    public ResponseEntity<List<RecurringDemandScheduleDto>> getAppointmentSchedulesFromAgreement(int serviceRecipientId, DateTime now) {
        var typeRef = new ParameterizedTypeReference<List<RecurringDemandScheduleDto>>() {};
        String appointmentScheduleUri = getFirstAgreement(serviceRecipientId, now).getAppointmentsLink().getHref();
        return restTemplate.exchange(appointmentScheduleUri, HttpMethod.GET, null, typeRef);
    }

    public AgreementResource getFirstAgreement(int serviceRecipientId, DateTime now) {
        List<AgreementResource> matchingAgreements = getAgreements(serviceRecipientId, now);
        Assert.state(Matchers.<AgreementResource>iterableWithSize(1).matches(matchingAgreements), "Expect a single agreement to be returned");

        return matchingAgreements.get(0);
    }

    public List<AgreementResource> getAgreements(int serviceRecipientId, DateTime atDateTime) {
        List<AgreementResource> agreements = this.getActiveAgreementsOnDate(atDateTime);

        // HATEOAS: find the link to the appointment schedule list for our client.
        return agreements.stream()
                .filter(agreement -> agreement.getServiceRecipientId() == serviceRecipientId)
                .collect(toList());
    }

    public List<AgreementResource> getActiveAgreementsOnDate(int serviceRecipientId, DateTime now) {
        final List<AgreementResource> activeAgreementsOnDate = getActiveAgreementsOnDate(now);
        return Optional.of(activeAgreementsOnDate.stream()
                .filter(a -> serviceRecipientId == a.getServiceRecipientId())
                .collect(toList()))
                .orElseThrow(() -> new AssertionError("Expect client agreement to be found"));
    }

    public List<AgreementResource> getActiveAgreementsOnDate(DateTime now) {
        UriComponents agreementListUri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl + "rota/workers:all/agreements/")
                .queryParam("startDate", ISODATEFORMAT.print(now))
                .queryParam("demandFilter", "referrals:all") // FIXME: will need bldg here I suspect
                .build();

        var typeRef = new ParameterizedTypeReference<List<AgreementResource>>() {};

        ResponseEntity<List<AgreementResource>> response = restTemplate.exchange(agreementListUri.toUri(),
                HttpMethod.GET, null, typeRef);
        return response.getBody();
    }

    public List<AppointmentTypeViewModel> getAppointmentTypes() {
        var typeRef = new ParameterizedTypeReference<List<AppointmentTypeViewModel>>() {};

        ResponseEntity<List<AppointmentTypeViewModel>> response = restTemplate.exchange(
                apiBaseUrl + "appointmentTypes/",
                HttpMethod.GET, null, typeRef);
        return response.getBody();
    }

    public void deleteSchedule(RecurringDemandScheduleDto schedule, int serviceRecipientId) {
        ServiceRecipientAppointmentScheduleCommandDto cmd = new ServiceRecipientAppointmentScheduleCommandDto(BaseCommandViewModel.OPERATION_REMOVE, serviceRecipientId);
        cmd.eventRef = schedule.getEventRef();
        executeCommand(cmd);
    }
}
