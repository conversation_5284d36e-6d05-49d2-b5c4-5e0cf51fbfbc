package com.ecco.data.client.actors;

import static org.hamcrest.CoreMatchers.is;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import com.ecco.webApi.contacts.address.AddressViewModel;
import com.ecco.webApi.contacts.ClientViewModel;
import com.ecco.webApi.viewModels.Result;
import com.google.common.collect.ImmutableMap;

public class ClientActor extends BaseActor {

    public ClientActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<Result> createClient(ClientViewModel cvm) {
        return postAsJson(apiBaseUrl + "clients/", cvm);
    }

    /**
     * Retrieve the client via its id
     * @param clientId
     * @return ClientViewModel
     */
    public ResponseEntity<ClientViewModel> getClientById(long clientId) {
        return restTemplate.getForEntity(apiBaseUrl + "clients/{id}/", ClientViewModel.class, ImmutableMap.of("id", clientId));
    }

    public ResponseEntity<ClientViewModel[]> getClientsByExternalRef(String ref) {
        return restTemplate.getForEntity(apiBaseUrl + "clients/byExternalRef/{code}/", ClientViewModel[].class, ImmutableMap.of("code", ref));
    }

    public long createClient(String firstName, String lastName) {

        AddressViewModel avm = new AddressViewModel();
        avm.address[0] = "non blank";
        avm.address[1] = "space";
        avm.address[2] = "required for JSONArray line 217";
        avm.postcode = "SE13";
        avm.town = "Lewisham";

        ClientViewModel cvm = new ClientViewModel();
        cvm.setFirstName(firstName);
        cvm.setLastName(lastName);
        cvm.setGenderId(115);
        cvm.address = avm;

        ResponseEntity<Result> response = this.createClient(cvm);
        Assert.state(is(HttpStatus.CREATED).matches(response.getStatusCode()));
        Assert.state(response.getBody().getId() != null);

        return Long.parseLong(response.getBody().getId());
    }

}
