package com.ecco.data.client.actors;

import java.util.List;
import java.util.UUID;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.serviceConfig.viewModel.RiskAreaViewModel;
import com.google.common.collect.ImmutableMap;

public class OutcomeActor extends BaseActor {


    public OutcomeActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<OutcomeViewModel> findNeedByUUID(UUID uuid) {
        return restTemplate.getForEntity(apiBaseUrl + "outcomes/{outcomeUuid}/", OutcomeViewModel.class, ImmutableMap.of("outcomeUuid", uuid));
    }

    public ResponseEntity<RiskAreaViewModel> findThreatByUUID(UUID uuid) {
        return restTemplate.getForEntity(apiBaseUrl + "riskAreas/{outcomeUuid}/", RiskAreaViewModel.class, ImmutableMap.of("outcomeUuid", uuid));
    }

    public ResponseEntity<OutcomeViewModel[]> findAllSupportOutcomes() {
        return restTemplate.getForEntity(apiBaseUrl + "outcomes/support/", OutcomeViewModel[].class);
    }

    public ResponseEntity<RiskAreaViewModel[]> findAllThreatOutcomes() {
        return restTemplate.getForEntity(apiBaseUrl + "outcomes/threat/", RiskAreaViewModel[].class);
    }

    public ResponseEntity<List<OutcomeViewModel>> findAllByServiceId(long serviceId) {
        // getForEntity doesn't support Iterable, so use exchange - see http://stackoverflow.com/questions/6173182/spring-json-convert-a-typed-collection-like-listmypojo
        ParameterizedTypeReference<List<OutcomeViewModel>> typeRef = new ParameterizedTypeReference<>() {
        };
        ResponseEntity<List<OutcomeViewModel>> response = restTemplate.exchange(apiBaseUrl + "outcomes/byServiceId/{serviceId}", HttpMethod.GET, null, typeRef, ImmutableMap.of("serviceId", serviceId));
        return response;
    }

    public ResponseEntity<List<OutcomeViewModel>> findAllByServiceName(String serviceName) {
        ParameterizedTypeReference<List<OutcomeViewModel>> typeRef = new ParameterizedTypeReference<>() {
        };
        ResponseEntity<List<OutcomeViewModel>> response = restTemplate.exchange(apiBaseUrl + "outcomes/byServiceName/{serviceName}", HttpMethod.GET, null, typeRef, ImmutableMap.of("serviceName", serviceName));
        return response;
    }

    public ResponseEntity<List<OutcomeViewModel>> findAllOutcomeThreatsByServiceName(String serviceName) {
        ParameterizedTypeReference<List<OutcomeViewModel>> typeRef = new ParameterizedTypeReference<>() {
        };
        ResponseEntity<List<OutcomeViewModel>> response = restTemplate.exchange(apiBaseUrl + "outcomeThreats/byServiceName/{serviceName}", HttpMethod.GET, null, typeRef, ImmutableMap.of("serviceName", serviceName));
        return response;
    }

}
