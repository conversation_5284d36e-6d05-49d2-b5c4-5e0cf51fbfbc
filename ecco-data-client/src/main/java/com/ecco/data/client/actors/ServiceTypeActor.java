package com.ecco.data.client.actors;

import static com.ecco.dto.ChangeViewModel.changeNullTo;
import static java.util.stream.Collectors.toList;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

import com.ecco.webApi.serviceConfig.TaskDefinitionEntryCommandViewModel;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.serviceConfig.ServiceTypeChangeCommandViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionEntrySettingCommandViewModel;
import com.ecco.webApi.viewModels.Result;
import com.google.common.collect.ImmutableMap;

public class ServiceTypeActor extends BaseActor {

    public ServiceTypeActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    /**
     * Retrieve the servicetype with this id - force no caching (still cache on server repo side)
     * @return ServiceTypeViewModel
     */
    public ResponseEntity<ServiceTypeViewModel> getServiceTypeById(long id) {
        return restTemplate.getForEntity(
                apiBaseUrl + "servicetype/{id}/",
                ServiceTypeViewModel.class,
                ImmutableMap.of("id", id));
    }

    public ResponseEntity<ServiceTypeViewModel> getServiceTypeByName(String name) {
        return restTemplate.getForEntity(
                apiBaseUrl + "servicetype/byName/{name}/",
                ServiceTypeViewModel.class,
                ImmutableMap.of("name", name));
    }

    public ResponseEntity<ServiceTypeViewModel[]> getAllServiceTypes() {
        return restTemplate.getForEntity(
                apiBaseUrl + "servicetypes/",
                ServiceTypeViewModel[].class);
    }

    public ServiceTypeViewModel createServiceType(String serviceTypeName, boolean child, String... taskNames) {
        var serviceType = createServiceType(serviceTypeName, child);

        AtomicInteger position = new AtomicInteger();
        Stream.of(taskNames).forEach(t -> {
            addTaskDefinitionEntry(serviceType.id, position.addAndGet(5), t);
        });

        return serviceType;
    }

    public void addTaskDefinitionEntry(int serviceTypeId, int position, String t) {
        TaskDefinitionEntryCommandViewModel tvm;
        tvm = new TaskDefinitionEntryCommandViewModel(TaskDefinitionEntryCommandViewModel.OPERATION_ADD, serviceTypeId, t);
        tvm.orderbyChange = ChangeViewModel.changeNullTo(position);
        executeCommand(tvm);
    }

    public ServiceTypeViewModel createServiceType(String serviceTypeName, boolean child) {
        ServiceTypeChangeCommandViewModel stvm = new ServiceTypeChangeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, null);
        stvm.nameChange = ChangeViewModel.create(null, serviceTypeName);
        stvm.childChange = ChangeViewModel.create(null, child);
        Result result = executeCommand(stvm).getBody();
        Assert.isTrue(result.isCommandSuccessful(), "command not successful");
        return getServiceType(serviceTypeName).get(0);
    }

    /**
     * Create servicetype with:
     *  - overview 0
     *  - clientWithContact 5
     *  - decideFinal 10
     *  - close 15
     *  - endFlow 20
     */
    public ServiceTypeViewModel createServiceTypeWithBasicWorkflow(String serviceTypeName) {
        String[] taskNames = new String[]{"clientWithContact", "referralView", "decideFinal", "close", "endFlow"};
        var vm = createServiceType(serviceTypeName, false, taskNames);

        // allowNext on decideFinal
        TaskDefinitionEntryCommandViewModel tvm;
        tvm = new TaskDefinitionEntryCommandViewModel(TaskDefinitionEntryCommandViewModel.OPERATION_UPDATE, vm.id, "decideFinal");
        tvm.allowNextChange = ChangeViewModel.changeNullTo(true);
        executeCommand(tvm);

        return getServiceTypeById(vm.id).getBody();
    }

    public List<ServiceTypeViewModel> getServiceType(String name) {
        Stream<ServiceTypeViewModel> vms = (Arrays.stream(getAllServiceTypes().getBody()));
        return vms.filter(f -> f.name.equals(name))
                .collect(toList());
    }


    public void removeServiceTypes(String... serviceTypeNames) {
        List<ServiceTypeViewModel> vms = List.of(getAllServiceTypes().getBody());
        vms.stream()
                .filter(e -> Arrays.stream(serviceTypeNames).anyMatch(name -> e.name.equals(name)))
                .forEach(serviceType -> {
            ServiceTypeChangeCommandViewModel cmd = new ServiceTypeChangeCommandViewModel(BaseCommandViewModel.OPERATION_REMOVE, serviceType.id);
            executeCommand(cmd);
        });
    }

    public void changeTaskSetting(Integer serviceTypeId, String taskName, String settingName, String... values) {
        TaskDefinitionEntrySettingCommandViewModel cmd = new TaskDefinitionEntrySettingCommandViewModel(serviceTypeId, taskName, settingName);
        cmd.valueChange = changeNullTo(joinedWithComma(values));
        executeCommand(cmd);
    }

    String joinedWithComma(String[] values) {
        return String.join(",", values);
    }

}
