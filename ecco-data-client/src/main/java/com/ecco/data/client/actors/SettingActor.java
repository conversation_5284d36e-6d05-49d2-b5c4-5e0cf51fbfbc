package com.ecco.data.client.actors;

import com.ecco.webApi.viewModels.Result;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

public class SettingActor extends BaseActor {

    public SettingActor(RestTemplate restTemplate) {
        super(restTemplate);
    }


    public ResponseEntity<Result> changeSetting(String namespace, String keyName, String keyValue) {
        String dto = "{\"keyValue\": \"" + keyValue + "\"}";
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                        "settings/{namespace}/{keyName}/")
                .buildAndExpand(namespace, keyName);
        return postAsJson(uri.toUriString(), dto);
    }

}
