package com.ecco.data.client.actors;

import com.ecco.webApi.serviceConfig.TaskDefinitionViewModel;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class TaskDefinitionActor extends BaseActor {

    public TaskDefinitionActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<TaskDefinitionViewModel[]> getAllTaskDefinitions() {
        return restTemplate.getForEntity(
                apiBaseUrl + "taskDefinitions/",
                TaskDefinitionViewModel[].class);
    }

}
