package com.ecco.data.client.model.evidence;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;

@EqualsAndHashCode(callSuper = true)
@Data
public class QuestionnaireBasedImportViewModel extends BaseEvidenceImportViewModel {

    /**
     * Map from question name to answer
     * Allows us to build a picture of each answer in one piece of work.
     * eg answers["question name here"]
     */
    public HashMap<String, String> answers = new HashMap<>();

}
