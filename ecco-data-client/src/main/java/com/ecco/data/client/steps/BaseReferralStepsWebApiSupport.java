package com.ecco.data.client.steps;

import com.ecco.data.client.actors.*;
import com.ecco.webApi.evidence.BaseServiceRecipientCommandViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;

import org.jspecify.annotations.Nullable;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;

/**
 * Provides a base class for handling the functional flow of referral aspects/tasks/steps through the web api.
 * Subclasses provide the data handling.
 */
public abstract class BaseReferralStepsWebApiSupport implements ReferralStepDefinitions {
    protected final AgreementActor agreementActor;
    protected final CacheActor cacheActor;
    protected final SessionDataActor sessionDataActor;
    protected final ServiceActor serviceActor;
    protected final ReferralActor referralActor;
    protected final ClientActor clientActor;
    protected final ContactActor contactActor;
    protected final CalendarActor calendarActor;
    protected final UserActor userActor;
    protected final ListDefActor listDefActor;

    public BaseReferralStepsWebApiSupport(ReferralActor referralActor, AgreementActor agreementActor,
                                          SessionDataActor sessionDataActor, ContactActor contactActor,
                                          <PERSON>lientA<PERSON> clientActor, CalendarActor calendarActor,
                                          UserActor userActor, ListDefActor listDefActor,
                                          ServiceActor serviceActor, CacheActor cacheActor) {
        this.referralActor = referralActor;
        this.agreementActor = agreementActor;
        this.sessionDataActor = sessionDataActor;
        this.serviceActor = serviceActor;
        this.contactActor = contactActor;
        this.clientActor = clientActor;
        this.calendarActor = calendarActor;
        this.cacheActor = cacheActor;
        this.userActor = userActor;
        this.listDefActor = listDefActor;
    }

    protected <T extends BaseServiceRecipientCommandViewModel> CommandStepDefinition<T> simpleStep(
            BiFunction<Integer, String, T> commandFactory,
            BiFunction<ReferralData, T, T> commandProcessor) {
        return new CommandStepDefinition<>(commandFactory, commandProcessor, null);
    }

    protected <T extends BaseServiceRecipientCommandViewModel> CommandStepDefinition<T> simpleStep(
            BiFunction<Integer, String, T> commandFactory,
            BiFunction<ReferralData, T, T> commandProcessor,
            BiConsumer<ReferralData, T> commandResultValidator) {
        return new CommandStepDefinition<>(commandFactory, commandProcessor, commandResultValidator);
    }

    private class CommandStepDefinition<T extends BaseServiceRecipientCommandViewModel> implements BiFunction<ReferralData, String, ReferralData> {
        private final BiFunction<Integer, String, T> commandCreator;
        private final BiFunction<ReferralData, T, T> commandProcessor;
        @Nullable
        private final BiConsumer<ReferralData, T> commandResultValidator;

        private CommandStepDefinition(BiFunction<Integer, String, T> commandCreator, BiFunction<ReferralData, T, T> commandProcessor,
                                      @Nullable BiConsumer<ReferralData, T> commandResultValidator) {
            this.commandCreator = commandCreator;
            this.commandProcessor = commandProcessor;
            this.commandResultValidator = commandResultValidator;
        }

        private T getCommand(Integer serviceRecipientId, String taskHandle) {
            return commandCreator.apply(serviceRecipientId, taskHandle);
        }

        private BiFunction<ReferralData, T, T> getCommandProcessor() {
            return commandProcessor;
        }

        public ReferralData apply(ReferralData referralData, String taskHandle) {
            final T updatedCommand = commandProcessor.apply(referralData, getCommand(referralData.referralViewModel.serviceRecipientId, taskHandle));

            ReferralData result = executeAndReturn(referralData, updatedCommand);
            if (commandResultValidator != null) {
                commandResultValidator.accept(result, updatedCommand);
            }
            return result;
        }

    }

    protected <T extends BaseServiceRecipientCommandViewModel> ReferralData executeAndReturn(ReferralData referralData, T updatedCommand) {
        referralActor.executeCommand(updatedCommand);
        ReferralViewModel rvm = referralActor.getReferralById(referralData.referralViewModel.referralId).getBody();
        // TODO update ReferralData with workflowActor.getTasksByTaskName(referralViewModel)
        return new ReferralData(rvm, referralData.serviceOptions, referralData.referralOptions, referralData.tasks);
    }
}
