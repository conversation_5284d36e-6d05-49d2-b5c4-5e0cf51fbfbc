package com.ecco.data.client.steps;

import com.ecco.data.client.actors.*;
import com.ecco.dom.EvidenceGroup;
import com.ecco.webApi.evidence.CommentCommandViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.webApi.taskFlow.*;
import com.google.common.collect.ImmutableMap;

import java.util.UUID;
import java.util.function.BiFunction;

import static com.ecco.dto.ChangeViewModel.changeNullTo;

/**
 * Manipulates the supplied (empty) commands using ReferralData.
 * This class is designed to be the go-to implementation for applying referral commands.
 * For usages, see usages of {@link BaseReferralStepsWebApiSupport}.
 */
public class DefaultReferralStepsWebApiSupport extends BaseReferralStepsWebApiSupport {

    public DefaultReferralStepsWebApiSupport(AgreementActor agreementActor, SessionDataActor sessionDataActor,
                                      ReferralActor referralActor, ClientActor clientActor,
                                      <PERSON>A<PERSON> contactActor, CalendarActor calendarActor,
                                      UserActor userActor, ListDefActor listDefActor, ServiceActor serviceActor,
                                      CacheActor cacheActor) {
        super(referralActor, agreementActor, sessionDataActor, contactActor, clientActor, calendarActor, userActor, listDefActor, serviceActor, cacheActor);
    }

    @Override
    public void registerStepDefinitions(ImmutableMap.Builder<String, BiFunction<ReferralData, String, ReferralData>> builder) {
        builder
                .put(SOURCE_OF_REFERRAL, simpleStep(ReferralTaskEditSourceCommandViewModel::new, this::from))
                .put(DESTINATION, simpleStep(ReferralTaskEditDestinationCommandViewModel::new, this::destination))
                .put(REFERRAL_DETAILS, simpleStep(ReferralTaskReferralDetailsCommandViewModel::new, this::referralDetails))
                .put(WAITING_LIST_CRITERIA, simpleStep(ReferralTaskWaitingListScoreCommandViewModel::new, this::waitingListCriteria))
                .put(DATA_PROTECTION, simpleStep(ReferralTaskEditDataProtectionCommandViewModel::new, this::dataProtection))
                .put(EMERGENCY_DETAILS, simpleStep(ReferralTaskEditEmergencyDetailsCommandViewModel::new, this::emergencyDetails))
                .put(DELIVERED_BY, simpleStep(ReferralTaskEditDeliveredByCommandViewModel::new, this::deliveredBy))
                .put(FUNDING, simpleStep(ReferralTaskFundingCommandViewModel::new, this::funding))
                .put(SETUP_INITIAL_ASSESSMENT, simpleStep(ReferralTaskAssessmentDateCommandViewModel::new, this::initialAssessment))
                .put(ACCEPT_ON_SERVICE, simpleStep(ReferralTaskAcceptOnServiceCommandViewModel::new, this::acceptOnService))
                .put(START, simpleStep(ReferralTaskEditStartOnServiceCommandViewModel::new, this::start))
                .put(START_ACCOMMODATION, simpleStep(ReferralTaskEditStartOnServiceCommandViewModel::new, this::start))
                .put(NEEDS_ASSESSMENT, needsAssessmentStep())
                .put(EXIT, simpleStep(ReferralTaskExitCommandViewModel::new, this::exit))

                // TODO implement these as required (eg by ReferralScenarioGenerator, see ReferralStepsWebApiSupport for the other implementation
                .put(ACCOMMODATION, dummyStep())
                .put(CASE_NOTES, dummyStep())
                .put(PENDING_STATUS, dummyStep())
                .put(APPROPRIATE_REFERRAL, dummyStep())
                .put(AGREEMENT_OF_APPOINTMENTS, dummyStep())
                .put(THREAT_ASSESSMENT_REDUCTION, dummyStep())
                .put(ROTA_VISIT, dummyStep());
    }

    private BiFunction<ReferralData, String, ReferralData> dummyStep() {
        return (referralData, taskHandle) -> referralData; // Just complete the workflow task without doing anything.
    }

    private ReferralTaskEditSourceCommandViewModel from(ReferralData referralData, ReferralTaskEditSourceCommandViewModel c) {
        c = referralData.referralOptions.withSourceSelf
                ? c.withSelfReferral()
                : c.withAgency(referralData.referralOptions.withSourceAgency);
        return c;
    }

    private ReferralTaskEditDestinationCommandViewModel destination(ReferralData referralData, ReferralTaskEditDestinationCommandViewModel c) {
        // assume there is a project in the options provided, and choose the first - neither do we don't check what is already on the referral
        int projectId = referralData.serviceOptions.getServiceViewModel().projects.iterator().next().id;
        c.projectChange = changeNullTo(projectId);
        return c;
    }

    private ReferralTaskReferralDetailsCommandViewModel referralDetails(ReferralData referralData, ReferralTaskReferralDetailsCommandViewModel c) {
        return c.withReceivedDate(referralData.referralOptions.withDetailsReceivedDate);
    }

    private ReferralTaskWaitingListScoreCommandViewModel waitingListCriteria(ReferralData referralData, ReferralTaskWaitingListScoreCommandViewModel c) {
        return c.withScore(referralData.referralOptions.withWaitingListScore);
    }

    private ReferralTaskEditDataProtectionCommandViewModel dataProtection(ReferralData referralData, ReferralTaskEditDataProtectionCommandViewModel c) {
        c.signedDateChange = changeNullTo(referralData.referralOptions.withDataProtectionDate);
        c.signatureSvgXml = referralData.referralOptions.withDataProtectionSignature;
        return c;
    }

    private ReferralTaskEditEmergencyDetailsCommandViewModel emergencyDetails(ReferralData referralData, ReferralTaskEditEmergencyDetailsCommandViewModel cmd) {
        //cmd.descriptionDetails = changeNullTo(descriptionDetails);
        //cmd.communicationNeeds = changeNullTo(communicationNeeds);
        //cmd.emergencyKeyword = changeNullTo(emergencyKeyword);
        //cmd.emergencyDetails = changeNullTo(emergencyDetails);
        cmd.medicationDetails = changeNullTo(referralData.referralOptions.withEmergencyMedicationDetails);
        //cmd.doctorDetails = changeNullTo(doctorDetails);
        //cmd.dentistDetails = changeNullTo(dentistDetails);
        return cmd;
    }

    private ReferralTaskFundingCommandViewModel funding(ReferralData referralData, ReferralTaskFundingCommandViewModel cmd) {
        cmd.fundingDecisionDate = changeNullTo(referralData.referralOptions.withFundingDecisionDate); // TODO: change to just a date?? Check what legacy UI does
        //cmd.fundingPaymentRef = changeNullTo(paymentRef);
        cmd.fundingSource = changeNullTo(referralData.referralOptions.withFundingSourceId);
        //cmd.fundingReviewDate = changeNullTo(fundingReviewDate);
        cmd.fundingAmount = changeNullTo(referralData.referralOptions.withFundingAmount);
        //cmd.fundingAccepted = changeNullTo(fundingAccepted);
        //cmd.hoursOfSupport = changeNullTo(fundingHoursOfSupport);
        return cmd;
    }

    private ReferralTaskAssessmentDateCommandViewModel initialAssessment(ReferralData referralData, ReferralTaskAssessmentDateCommandViewModel cmd) {
        cmd.firstOfferedInterviewDate = changeNullTo(referralData.referralOptions.withInitialAssessmentFirstOfferedInterviewDate);
        cmd.decisionDate = changeNullTo(referralData.referralOptions.withInitialAssessmentDecisionDate);
        //cmd.interviewDnaComments = changeNullTo(referralData.referralOptions.withIntialAssessmentInterviewDnaComments);
        //cmd.interviewDna = changeNullTo(interviewDna);
        //cmd.interviewSetupComments = changeNullTo(interviewSetupComments);
        cmd.interviewer1 = changeNullTo(referralData.referralOptions.withInitialAssessmentInterviewer1Id);
        cmd.interviewer2 = changeNullTo(referralData.referralOptions.withInitialAssessmentInterviewer12d);
        //cmd.location = changeNullTo(location);
        return cmd;
    }

    private ReferralTaskAcceptOnServiceCommandViewModel acceptOnService(ReferralData referralData, ReferralTaskAcceptOnServiceCommandViewModel c) {
        c.acceptedState = changeNullTo(referralData.referralOptions.withAcceptOnServiceState);
        c.signpostedBack = changeNullTo(referralData.referralOptions.withAcceptOnServiceSignpostBack);
        c.signpostedAgency = changeNullTo(referralData.referralOptions.withAcceptOnServiceSignpostAgencyId);
        c.signpostedComment = changeNullTo(referralData.referralOptions.withAcceptOnServiceSignpostComment);
        c.signpostedReason = changeNullTo(referralData.referralOptions.withAcceptOnServiceSignpostReasonId);
        c.acceptedDate = changeNullTo(referralData.referralOptions.withAcceptOnServiceDate);
        return c;
    }

    private ReferralTaskEditStartOnServiceCommandViewModel start(ReferralData referralData, ReferralTaskEditStartOnServiceCommandViewModel c) {
        c.receivingServiceDate = changeNullTo(referralData.referralOptions.withStartReceivingServiceDate);
        c.allocatedWorkerContactId = changeNullTo(referralData.referralOptions.withStartAllocatedWorkerContactId);
        return c;
    }

    private ReferralTaskEditDeliveredByCommandViewModel deliveredBy(ReferralData referralData, ReferralTaskEditDeliveredByCommandViewModel cmd) {
        cmd.deliveredBy = changeNullTo(referralData.referralOptions.withDeliveredById);
        cmd.deliveredByStartDate = changeNullTo(referralData.referralOptions.withDeliveredByStartDate);
        return cmd;
    }


    private BiFunction<ReferralData, String, ReferralData> needsAssessmentStep() {
        return (referralData, taskHandle) -> {
            CommentCommandViewModel cmd = new CommentCommandViewModel(UUID.randomUUID(),
                    referralData.referralViewModel.serviceRecipientId, EvidenceGroup.NEEDS, EvidenceTask.NEEDS_ASSESSMENT);
            cmd.comment = changeNullTo(referralData.referralOptions.withNeedsAssessmentComment);
            cmd.workDate = changeNullTo(referralData.referralOptions.withNeedsAssessmentWorkDate.toLocalDateTime());
            return executeAndReturn(referralData, cmd);
        };
    }

    private ReferralTaskExitCommandViewModel exit(ReferralData referralData, ReferralTaskExitCommandViewModel c) {
        c.exitedDateChange = changeNullTo(referralData.referralOptions.withExitedDate);
        return c;
    }

}
