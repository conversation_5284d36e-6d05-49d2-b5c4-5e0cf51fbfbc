package com.ecco.data.client.dataimport.csv.mappers

import org.springframework.expression.spel.standard.SpelExpressionParser
import kotlin.test.*

class CsvExpressionMapperTest {
    private val parser = SpelExpressionParser()

    @Test
    fun emptyListNoExpressions() {
        val mapper = CsvExpressionMapper(emptyList())
        assertEquals(emptyList(), mapper(emptyList()))
    }

    @Test
    fun noExpressions() {
        val mapper = CsvExpressionMapper(emptyList())
        assertEquals(listOf("a", "b", "c"), mapper(listOf("a", "b", "c")))
    }

    @Test
    fun allExpressions() {
        val expression = parser.parseExpression("""#this + "a"""")
        val mapper = CsvExpressionMapper(listOf(expression, expression, expression))
        assertEquals(listOf("aa", "ba", "ca"), mapper(listOf("a", "b", "c")))
    }

    @Test
    fun expressionAtEnd() {
        val expression = parser.parseExpression("""#this + "a"""")
        val mapper = CsvExpressionMapper(listOf(null, null, expression))
        assertEquals(listOf("a", "b", "ca"), mapper(listOf("a", "b", "c")))
    }

    @Test
    fun expressionAfterEnd() {
        val expression = parser.parseExpression("""#this + "a"""")
        val mapper = CsvExpressionMapper(listOf(null, null, null, expression))
        assertEquals(listOf("a", "b", "c"), mapper(listOf("a", "b", "c")))
    }

    @Test
    fun expressionAppliedToString() {
        // SpEL expressions are applied BEFORE type conversion, so
        // `#this + 1` appends the string `"1"` to the string value of `#this`.
        val plusOne = parser.parseExpression("""#this + 1""")
        val plusTwo = parser.parseExpression("""#this + 2""")
        val mapper = CsvExpressionMapper(listOf(plusOne, plusTwo))
        assertEquals(listOf("171", "392"), mapper(listOf("17", "39")))
    }
}