package com.ecco.data.client.dataimport.csv.mappers

import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException
import kotlinx.serialization.serializer
import kotlin.test.*

class CsvKSerializationMapperTest {
    @Serializable
    private class TestData(val a: Int, val b: String = "", val c: Int = 0, val d: String? = null, val e: E? = null)

    @Serializable
    private class E(val a: Int)

    @Test
    fun emptyList() {
        val mapper = CsvKSerializationMapper(serializer<TestData>())
        assertFailsWith<SerializationException> {
            mapper(listOf())
        }
    }

    @Test
    fun setA() {
        val mapper = CsvKSerializationMapper(serializer<TestData>())
        val result = mapper(listOf(Pair("a", "715")))
        assertEquals(715, result.a)
        assertEquals("", result.b)
        assertEquals(0, result.c)
        assertNull(result.d)
        assertNull(result.e)
    }

    @Test
    fun setAbcd() {
        val mapper = CsvKSerializationMapper(serializer<TestData>())
        val result = mapper(
            listOf(
                Pair("a", "320"),
                Pair("b", "hello"),
                Pair("c", "688"),
                Pair("d", "world"),
            ),
        )
        assertEquals(320, result.a)
        assertEquals("hello", result.b)
        assertEquals(688, result.c)
        assertEquals("world", result.d)
        assertNull(result.e)
    }

    @Test
    fun setAe() {
        val mapper = CsvKSerializationMapper(serializer<TestData>())
        val result = mapper(
            listOf(
                Pair("a", "228"),
                Pair("e.a", "401"),
            ),
        )
        assertEquals(228, result.a)
        assertEquals("", result.b)
        assertEquals(0, result.c)
        assertNull(result.d)
        assertEquals(401, result.e?.a)
    }

    @Test
    fun setInvalidIgnored() {
        val mapper = CsvKSerializationMapper(serializer<TestData>())
        val result = mapper(
            listOf(
                Pair("a", "946"),
                Pair("invalid", "wobble"),
            ),
        )
        assertEquals(946, result.a)
        assertEquals("", result.b)
        assertEquals(0, result.c)
        assertNull(result.d)
        assertNull(result.e)
    }
}