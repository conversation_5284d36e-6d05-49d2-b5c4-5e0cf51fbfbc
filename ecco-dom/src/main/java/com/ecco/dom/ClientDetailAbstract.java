package com.ecco.dom;

import com.ecco.calendar.dom.MedDate;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.infrastructure.entity.CodedEntity;
import com.ecco.infrastructure.hibernate.HibTypeNames;
import com.ecco.infrastructure.util.EccoMessageUtils;
import com.ecco.calendar.core.util.DateTimeUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.*;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.joda.time.Period;

import org.jspecify.annotations.NonNull;
import javax.persistence.CascadeType;
import javax.persistence.*;
import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;

@MappedSuperclass
@Getter
@Setter
public abstract class ClientDetailAbstract extends RequestDeleteEntityImpl<Long> implements CodedEntity, Contactable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    @GeneratedValue(generator="clientDetailsTableGenerator")
    @TableGenerator(
            name = "clientDetailsTableGenerator", initialValue = 1, pkColumnValue = "clientdetails",
            allocationSize = 1, table = "hibernate_sequences")
    private Long id = null;


    // this data should remain static
    // if there are changes, it could affect prior reports
    // which is why most 'flags' etc appear on the referral
    private String code;

    // always load the contact (and save)
    //         <many-to-one name="contact" unique="true" column="contactsId" entity-name="Contact" cascade="all" not-null="true" not-found="exception" lazy="false" fetch="join"/>
    // we would quite like not to update the contact every time - but we do offer the edit screen every time
    // removed delete cascade because this abstract class is shared by the Worker, whose contact could be set to the same as the user contact (so deleting the worker would cause user problems)
    // this means logic of deleting a client should be updated to include deleting its contact (or that could be done through the contacts menu?)
    @OneToOne(cascade={CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH}, fetch=FetchType.EAGER)
    // disallow changing the contactid
    @JoinColumn(name="contactsId", updatable=false)
    @NotFound(action=NotFoundAction.EXCEPTION)
    // needs to be Super because entity-name has no equivalent in annotations
    // https://forum.hibernate.org/viewtopic.php?t=992246
    @Valid
    @NonNull
    private Individual contact;

    // we need to specify hour and minute in the db because of this issue
    // see https://forum.hibernate.org/viewtopic.php?f=9&t=961990&view=previous
    @Embedded
    @AttributeOverrides( {
        @AttributeOverride(name="day", column = @Column(name="birthday")),
        @AttributeOverride(name="month", column = @Column(name="birthmonth")),
        @AttributeOverride(name="year", column = @Column(name="birthyear"))
    })
    private MedDate birthDate = new MedDate(); // Used for form backing object. Must be set here, not lazily otherwise Hibernate thinks it's a change

    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentLocalDate")
    private LocalDate dateOfDeath;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "genderId")
    private ListDefinitionEntry gender;
    public static String GENDER_LISTNAME = "gender";

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "genderAtBirthId")
    private ListDefinitionEntry genderAtBirth;
    public static String GENDERATBIRTH_LISTNAME = "genderAtBirth";

    @Type(type=HibTypeNames.SEARCHABLE_ENCRYPTED_STRING)
    private String mothersFirstName;

    @ManyToOne(fetch=FetchType.EAGER)
    @JoinColumn(name="ethnicoriginsId")
    private ListDefinitionEntry ethnicOrigin;
    public static String ETHNICORIGIN_LISTNAME = "ethnicOrigin";

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "nationalityId")
    private ListDefinitionEntry nationality;
    public static String NATIONALITY_LISTNAME = "nationality";

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "maritalStatusId")
    private ListDefinitionEntry maritalStatus;
    public static String MARITALSTATUS_LISTNAME = "maritalStatus";

    @ManyToOne(fetch=FetchType.EAGER)
    @JoinColumn(name="religionsId")
    @Fetch(FetchMode.SELECT)
    private ListDefinitionEntry religion;
    public static String RELIGION_LISTNAME = "religion";

    @ManyToOne(fetch=FetchType.EAGER)
    @JoinColumn(name="languagesId")
    private ListDefinitionEntry firstLanguage;
    public static String LANGUAGE_LISTNAME = "language";

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "sexualityId")
    private ListDefinitionEntry sexuality;
    public static String SEXUALITY_LISTNAME = "sexualOrientation";

    @Lob
    private String emergencyDetails;

    private String emergencyKeyword;

    @Lob
    private String doctorDetails;

    @Lob
    private String dentistDetails;

    @Lob
    private String medicationDetails;

    @Lob
    private String risksAndConcerns;

    @Column
    LocalDateTime completeAt;

    // unused
    private String keyCode;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "disabilityId")
    private ListDefinitionEntry disability;
    public static String DISABILITY_LISTNAME = "disability";

    @Lob
    private String descriptionDetails;

    @Lob
    private String communicationNeeds;

    @Lob
    private String personality;

    @Lob
    private String behaviour;

    @Lob
    private String manners;

    @Lob
    private String significantEvents;

    // simply a ui property
    @Transient
    private String username;

    // two letters, six digits, and one further letter or a space
    // from http://regexlib.com/REDetails.aspx?regexp_id=527
    // we uppercase and remove spaces in the setter
    // The first letter may not be D, F, I, Q, U or V; the second letter may not be D, F, I, O, Q, U or V
    // however its silly to ask them to put a space on the end when we remove spaces in the setter!
    // should be ^[ABCEGHJKLMNOPRSTWXYZ][ABCEGHJKLMNPRSTWXYZ][0-9]{6}[A-D ]$
    // was ^[ABCEGHJKLMNOPRSTWXYZ][ABCEGHJKLMNPRSTWXYZ][0-9]{6}[A-D]?$
    // but now we wanted to drop the last letter restriction (temporary numbers sometimes given with X?)
    // NB also see validation.ts isValidNi
    public static final String NINO_REGEX = "^[ABCEGHJKLMNOPRSTWXYZ][ABCEGHJKLMNPRSTWXYZ][0-9]{6}[A-Z]?$";
    @Pattern(regexp=NINO_REGEX, message="{mustMatch.ni}")
    @Type(type=HibTypeNames.SEARCHABLE_ENCRYPTED_STRING)
    private String ni;

    // regex's?
    @Type(type=HibTypeNames.SEARCHABLE_ENCRYPTED_STRING)
    private String militaryNumber;

    // changing the default message can be done in usual ways
    // so required.nhs, Size.nhs etc
    public static final String NHS_REGEX = "[0-9]+";
    @Pattern(regexp=NHS_REGEX, message="{mustMatch.nhs}")
    // @Length is hibernate specific so we use @Size
    // we don't customise the error message= here because we can just use our global customised Size error message
    // however it seems the 'code' lookup is now only {javax.validation.constraints.Size.message}, should be Size, Size.etc...
    // but the javax...Size.message also doesn't come with any arguments for min or max
    // breakpoint on AbstractMessageSource.getMessage line 141
    // and we are correct in our config - validationMessageSource is set on the global validation configuration (in app context web-common)
    // so we need to specify the error message on spring's normal messageSource's
    // but it seems we still need a message= because we can't use the usual 'Size' or javax.validation...
    @Size(min=10, max=10, message="{size.nhs}")
    //@NotEmpty is implied in size
    @Type(type=HibTypeNames.SEARCHABLE_ENCRYPTED_STRING)
    private String nhs;

    // paris reference is used by social services
    @Type(type=HibTypeNames.SEARCHABLE_ENCRYPTED_STRING)
    private String paris;

    // transient, but easier to access in ui using 'bean' style
    @Transient
    private String displayName = null;

    @Lob
    @Column(name="textMap2")
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private HashMap<String,String> textMap = new HashMap<>();

    @Lob
    @Column(name="dateMap2")
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToLocalDateMap")
    private HashMap<String, LocalDate> dateMap = new HashMap<>();

    // useful for debugging
    @Override
    public String toString() {
        return "client: "+super.toString();
    }

    // originally we had this as a get - but then in the view
    // its useful to set the same property for display purposes
    public Integer getAge() {
        DateTime birthDate = DateTimeUtils.convertToDateTime(getBirthDate(), true, DateTimeZone.UTC);
        if (birthDate == null) {
            return null;
        }
        DateTime nowOrDeath = getDateOfDeath() != null
                ? getDateOfDeath().toDateTimeAtStartOfDay(DateTimeZone.UTC)
                : new DateTime(DateTimeZone.UTC);
        Period period = new Period(birthDate, nowOrDeath);
        return period.getYears();
    }

    public String getDisplayName() {
        // we did have null, but too strict - a new contact would be become empty
        // and then get saved in the birthday as ""
        if (StringUtils.isBlank(displayName)) {
            displayName = contact == null ? ""
                    : Individual.joinNonBlanksWithSpace(contact.getFirstName(), contact.getLastName());
        }
        return displayName;
    }

    public String getIdCode() {
        if (StringUtils.isEmpty(code)) {
            if (isNewEntity()) {
                return "";
            }
            String id = "" + getId();
            String lastClientID = EccoMessageUtils.getUiMessageSource().getMessage("lastClientID");
            if (StringUtils.isNotEmpty(lastClientID)) {
                long lastClientIDnum = Long.parseLong(lastClientID);
                long thisClientIDnum = lastClientIDnum + getId();
                id = "" + thisClientIDnum;
            }
            return id;
        } else {
            return code;
        }
    }
/*
    public MedDate getBirthDate() {
        // required to be not-null, since when saving to the dbase we simply use 'day month year'
        // and if those properties are null, the date gets loaded as null [there appears to be no hibernate usertype]
        // the clientDetail form maps to the underlying property - and hence gets null
        return birthDate;
    }
*/

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public void setNi(String ni) {
        ni = StringUtils.upperCase(ni);
        this.ni = StringUtils.remove(ni, " ");
    }

    /*
    private Risk risk;
    public Risk getRisk() {
        return risk;
    }
    public void setRisk(Risk risk) {
        this.risk = risk;
    }
    */

    /*
    //private boolean disability;
    private DisabilityClass disabilityClass;
    private boolean specialDietaryRequirements;
    private boolean foodAllergies;
    private String foodAllergiesDetails;

    private boolean gpRegistered;
    private String gpName;
    private String gpPractice;
    private String gpPhoneNumber;

    private boolean children;
    private String childrenDetails;
    private String nextOfKinName;
    private Address nextOfKinAddress;
    private String nextOfKinPhoneNumber;
    private Relation nextOfKinRelation;

    //private boolean hasAliases;
    private String aliases;
    */
    /*
    private Religion religion;
    private boolean pets;
    private String petsDetails;

    private String localCouncilReference;
    private String birthPlace;
    */
    /*
    private NiReason niReason;
    private boolean armedForces;
    private String armedForcesRank;
    private MedDate armedForcesIn;
    private MedDate armedForcesOut;
    */

    // there was no problem keeping these null, until we went to find the nextCollectionId
    // so we did new HashSet, however with hibernate sorting on, it expected a sortedset
    // so we try a sortedset, but the comparison is done when we add to the set
    // but we can't sort in jsp - forEach needs the set to be sorted already, so we sort in spring
    // at the moment we simply provide another getter which is sorted
/*
    private Set<ClientAddressHistory> clientAddressHistories = new HashSet<ClientAddressHistory>();
    private Set<ClientIncome> clientIncomes = new HashSet<ClientIncome>();

    // for querying only...but we still need a reference to store the info somewhere
    private Set<ClientReferral> clientReferrals = new HashSet<ClientReferral>();
    private Set<Client_Service> client_Services = new HashSet<Client_Service>();
    // replaced by individual assessments
    //private Set<ClientAssessment> clientAssessments = new HashSet<ClientAssessment>();
    private Set<ClientAssessmentSPAccommodation> clientAssessmentSPAccommodations = new HashSet<ClientAssessmentSPAccommodation>();
    private Set<ClientAssessmentDropIn> clientAssessmentDropIns = new HashSet<ClientAssessmentDropIn>();
    private Set<ClientAssessmentNeed> clientAssessmentNeeds = new HashSet<ClientAssessmentNeed>();
    private Set<ClientAssessmentNightShelter> clientAssessmentNightShelters = new HashSet<ClientAssessmentNightShelter>();
    private Set<ClientAssessmentRisk> clientAssessmentRisks = new HashSet<ClientAssessmentRisk>();
    private Set<ClientSupportPlan> clientSupportPlans = new HashSet<ClientSupportPlan>();
    private Set<ClientCrisisPlan> clientCrisisPlans = new HashSet<ClientCrisisPlan>();
    private Set<Client_Agency> client_Agencies = new HashSet<Client_Agency>();
    private Set<ClientWarning> clientWarnings = new HashSet<ClientWarning>();
    private Set<ClientLog> clientLogs = new HashSet<ClientLog>();
    //private Set<Client_Worker> workers = new HashSet<Client_Worker>();

    private Worker worker;

    private String comments;
*/
    /*
    public ClientReferral getReferral(long id) {
        for (Iterator iterator = clientReferrals.iterator(); iterator.hasNext();) {
            ClientReferral referral = (ClientReferral) iterator.next();
            if (referral.getId() == id)
                return referral;
        }
        return null;
    }
    */
/*
    public List<ClientReferral> getSortedClientReferrals() {
        List<ClientReferral> sortedClientReferrals = new ArrayList<ClientReferral>();
        sortedClientReferrals.addAll(clientReferrals);
        Collections.sort(sortedClientReferrals);
        return sortedClientReferrals;
    }
    public List<ClientAssessmentNeed> getSortedClientAssessmentNeeds() {
        List<ClientAssessmentNeed> sortedClientNeeds = new ArrayList<ClientAssessmentNeed>();
        sortedClientNeeds.addAll(clientAssessmentNeeds);
        Collections.sort(sortedClientNeeds);
        return sortedClientNeeds;
    }
    public List<ClientAssessmentRisk> getSortedClientAssessmentRisks() {
        List<ClientAssessmentRisk> sortedClientRisks = new ArrayList<ClientAssessmentRisk>();
        sortedClientRisks.addAll(clientAssessmentRisks);
        Collections.sort(sortedClientRisks);
        return sortedClientRisks;
    }
    public List<ClientSupportPlan> getSortedClientSupportPlans() {
        List<ClientSupportPlan> sortedClientSupportPlans = new ArrayList<ClientSupportPlan>();
        sortedClientSupportPlans.addAll(clientSupportPlans);
        Collections.sort(sortedClientSupportPlans);
        return sortedClientSupportPlans;
    }
    public List<ClientCrisisPlan> getSortedClientCrisisPlans() {
        List<ClientCrisisPlan> sortedClientCrisisPlans = new ArrayList<ClientCrisisPlan>();
        sortedClientCrisisPlans.addAll(clientCrisisPlans);
        Collections.sort(sortedClientCrisisPlans);
        return sortedClientCrisisPlans;
    }
    public List<ClientWarning> getSortedClientWarnings() {
        List<ClientWarning> sortedClientWarnings = new ArrayList<ClientWarning>();
        sortedClientWarnings.addAll(clientWarnings);
        Collections.sort(sortedClientWarnings);
        return sortedClientWarnings;
    }
    public List<ClientAssessmentSPAccommodation> getSortedClientAssessmentSPAccommodations() {
        List<ClientAssessmentSPAccommodation> sortedClientAssessmentSPAccommodations = new ArrayList<ClientAssessmentSPAccommodation>();
        sortedClientAssessmentSPAccommodations.addAll(clientAssessmentSPAccommodations);
        Collections.sort(sortedClientAssessmentSPAccommodations);
        return sortedClientAssessmentSPAccommodations;
    }

    public Set<ClientReferral> getClientReferrals() {
        return clientReferrals;
    }

    public Set<ClientCrisisPlan> getClientCrisisPlans() {
        return clientCrisisPlans;
    }
    public void setClientCrisisPlans(Set<ClientCrisisPlan> clientCrisisPlans) {
        this.clientCrisisPlans = clientCrisisPlans;
    }
    public void setClientReferrals(Set<ClientReferral> clientReferrals) {
        this.clientReferrals = clientReferrals;
    }

    public Set<Client_Service> getClient_Services() {
        return client_Services;
    }

    public void setClient_Services(Set<Client_Service> services) {
        this.client_Services = services;
    }

    public Set<ClientAssessmentSPAccommodation> getClientAssessmentSPAccommodations() {
        return clientAssessmentSPAccommodations;
    }
    public void setClientAssessmentSPAccommodations(
            Set<ClientAssessmentSPAccommodation> clientAssessmentSPAccommodations) {
        this.clientAssessmentSPAccommodations = clientAssessmentSPAccommodations;
    }
    public Set<ClientAssessmentDropIn> getClientAssessmentDropIns() {
        return clientAssessmentDropIns;
    }
    public void setClientAssessmentDropIns(
            Set<ClientAssessmentDropIn> clientAssessmentDropIns) {
        this.clientAssessmentDropIns = clientAssessmentDropIns;
    }

    public Set<ClientAssessmentNeed> getClientAssessmentNeeds() {
        return clientAssessmentNeeds;
    }
    public void setClientAssessmentNeeds(
            Set<ClientAssessmentNeed> clientAssessmentNeeds) {
        this.clientAssessmentNeeds = clientAssessmentNeeds;
    }
    public Set<ClientAssessmentNightShelter> getClientAssessmentNightShelters() {
        return clientAssessmentNightShelters;
    }
    public void setClientAssessmentNightShelters(
            Set<ClientAssessmentNightShelter> clientAssessmentNightShelters) {
        this.clientAssessmentNightShelters = clientAssessmentNightShelters;
    }
    public Set<ClientAssessmentRisk> getClientAssessmentRisks() {
        return clientAssessmentRisks;
    }
    public void setClientAssessmentRisks(
            Set<ClientAssessmentRisk> clientAssessmentRisks) {
        this.clientAssessmentRisks = clientAssessmentRisks;
    }
    public Set<ClientSupportPlan> getClientSupportPlans() {
        return clientSupportPlans;
    }

    public void setClientSupportPlans(Set<ClientSupportPlan> clientSupportPlans) {
        this.clientSupportPlans = clientSupportPlans;
    }

    public Set<Client_Agency> getClient_Agencies() {
        return client_Agencies;
    }

    public void setClient_Agencies(Set<Client_Agency> agencies) {
        this.client_Agencies = agencies;
    }

    *
    // this should be internationalised too
    public String toCommandString(CommandProjection properties, Map<String,String> inits) {
        // if null, everything is specified
        boolean everything = false;
        if (properties == null)
            everything = true;

        StrBuilder builder = new StrBuilder();
        if (everything || (properties.contains("firstName") || properties.contains("lastName")))
            builder.append(getDisplayName()).append(" ");

        if (everything || properties.contains("phoneNumber"))
            builder.append(inits.get("p")).append(getPhoneNumber()).append(" ");

        if (everything || properties.contains("mobileNumber"))
            builder.append(inits.get("m")).append(getMobileNumber()).append(" ");

        // we assume that all requests for email are both emails - so concatenate
        if (everything || properties.contains("email")) {
            builder.append(inits.get("e"));
            builder.append(StringUtils.appendIfExists(getEmail(), " "));
        }

        if (everything || properties.containsPart("address."))
            builder.append(inits.get("a")).append(getAddress().toCommandString(inits));

        return builder.toString();
    }
    *

    public Religion getReligion() {
        return religion;
    }

    public void setReligion(Religion religion) {
        this.religion = religion;
    }

    public DisabilityClass getDisabilityClass() {
        return disabilityClass;
    }

    public void setDisabilityClass(DisabilityClass disabilityClass) {
        this.disabilityClass = disabilityClass;
        if (disabilityClass != null)
            if (disabilityClass.getId() < 0)
                this.disabilityClass = null;
    }

    public Relation getNextOfKinRelation() {
        return nextOfKinRelation;
    }

    public void setNextOfKinRelation(Relation nextOfKinRelation) {
        this.nextOfKinRelation = nextOfKinRelation;
        if (nextOfKinRelation != null)
            if (nextOfKinRelation.getId() < 0)
                this.nextOfKinRelation = null;
    }

    public NiReason getNiReason() {
        return niReason;
    }

    public void setNiReason(NiReason niReason) {
        this.niReason = niReason;
        if (niReason != null)
            if (niReason.getId() < 0)
                this.niReason = null;
    }

    public boolean isSpecialDietaryRequirements() {
        return specialDietaryRequirements;
    }

    public void setSpecialDietaryRequirements(boolean specialDietaryRequirements) {
        this.specialDietaryRequirements = specialDietaryRequirements;
    }

    public boolean isFoodAllergies() {
        return foodAllergies;
    }

    public void setFoodAllergies(boolean foodAllergies) {
        this.foodAllergies = foodAllergies;
    }

    public String getFoodAllergiesDetails() {
        return foodAllergiesDetails;
    }

    public void setFoodAllergiesDetails(String foodAllergiesDetails) {
        this.foodAllergiesDetails = foodAllergiesDetails;
    }

    public boolean isPets() {
        return pets;
    }

    public void setPets(boolean pets) {
        this.pets = pets;
    }

    public String getPetsDetails() {
        return petsDetails;
    }

    public void setPetsDetails(String petsDetails) {
        this.petsDetails = petsDetails;
    }

    public boolean isGpRegistered() {
        return gpRegistered;
    }

    public void setGpRegistered(boolean gpRegistered) {
        this.gpRegistered = gpRegistered;
    }

    public String getGpName() {
        return gpName;
    }

    public void setGpName(String gpName) {
        this.gpName = gpName;
    }

    public String getGpPractice() {
        return gpPractice;
    }

    public void setGpPractice(String gpPractice) {
        this.gpPractice = gpPractice;
    }

    public String getGpPhoneNumber() {
        return gpPhoneNumber;
    }

    public void setGpPhoneNumber(String gpPhoneNumber) {
        this.gpPhoneNumber = gpPhoneNumber;
    }

    public boolean isChildren() {
        return children;
    }

    public void setChildren(boolean children) {
        this.children = children;
    }

    public String getChildrenDetails() {
        return childrenDetails;
    }

    public void setChildrenDetails(String childrenDetails) {
        this.childrenDetails = childrenDetails;
    }

    public String getNextOfKinName() {
        return nextOfKinName;
    }

    public void setNextOfKinName(String nextOfKinName) {
        this.nextOfKinName = nextOfKinName;
    }

    public Address getNextOfKinAddress() {
        return nextOfKinAddress;
    }

    public void setNextOfKinAddress(Address nextOfKinAddress) {
        this.nextOfKinAddress = nextOfKinAddress;
    }

    public String getNextOfKinPhoneNumber() {
        return nextOfKinPhoneNumber;
    }

    public void setNextOfKinPhoneNumber(String nextOfKinPhoneNumber) {
        this.nextOfKinPhoneNumber = nextOfKinPhoneNumber;
    }

    public String getAliases() {
        return aliases;
    }

    public void setAliases(String aliases) {
        this.aliases = aliases;
    }

    public String getLocalCouncilReference() {
        return localCouncilReference;
    }

    public void setLocalCouncilReference(String localCouncilReference) {
        this.localCouncilReference = localCouncilReference;
    }

    public String getBirthPlace() {
        return birthPlace;
    }

    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public boolean isArmedForces() {
        return armedForces;
    }

    public void setArmedForces(boolean armedForces) {
        this.armedForces = armedForces;
    }

    public String getArmedForcesRank() {
        return armedForcesRank;
    }

    public void setArmedForcesRank(String armedForcesRank) {
        this.armedForcesRank = armedForcesRank;
    }

    public MedDate getArmedForcesIn() {
        return armedForcesIn;
    }

    public void setArmedForcesIn(MedDate armedForcesIn) {
        this.armedForcesIn = armedForcesIn;
    }

    public MedDate getArmedForcesOut() {
        return armedForcesOut;
    }

    public void setArmedForcesOut(MedDate armedForcesOut) {
        this.armedForcesOut = armedForcesOut;
    }

    public Set<ClientAddressHistory> getClientAddressHistories() {
        return clientAddressHistories;
    }
    public List<ClientAddressHistory> getSortedClientAddressHistories() {
        List<ClientAddressHistory> sorted = new ArrayList<ClientAddressHistory>(clientAddressHistories.size());
        for (Iterator<ClientAddressHistory> iterator = clientAddressHistories.iterator(); iterator.hasNext();) {
            ClientAddressHistory entity = iterator.next();
            sorted.add(entity);
        }
        Collections.sort(sorted);
        return sorted;
    }

    public void setClientAddressHistories(Set<ClientAddressHistory> addressHistories) {
        this.clientAddressHistories = addressHistories;
    }

    public Set<ClientIncome> getClientIncomes() {
        return clientIncomes;
    }

    public void setClientIncomes(Set<ClientIncome> incomes) {
        this.clientIncomes = incomes;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Worker getWorker() {
        return worker;
    }

    public void setWorker(Worker worker) {
        this.worker = worker;
    }

    public Set<ClientLog> getClientLogs() {
        return clientLogs;
    }

    public void setClientLogs(Set<ClientLog> logs) {
        this.clientLogs = logs;
    }
    public Set<ClientWarning> getClientWarnings() {
        return clientWarnings;
    }
    public void setClientWarnings(Set<ClientWarning> clientWarnings) {
        this.clientWarnings = clientWarnings;
    }
*/

    /*
    public boolean isDisability() {
        return disability;
    }
    public void setDisability(boolean disability) {
        this.disability = disability;
    }
    */

    /*
    public boolean isHasAliases() {
        return hasAliases;
    }
    public void setHasAliases(boolean hasAliases) {
        this.hasAliases = hasAliases;
    }
    */

    /*
    public Set<Referral_Service> getServices() {
        return client_Services;
    }
    public List<Referral_Service> getSortedServices() {
        List<Referral_Service> sorted = new ArrayList<Referral_Service>(client_Services.size());
        for (Iterator<Referral_Service> iterator = client_Services.iterator(); iterator.hasNext();) {
            Referral_Service entity = iterator.next();
            sorted.add(entity);
        }
        Collections.sort(sorted);
        return sorted;
    }

    public void setServices(Set<Referral_Service> client_Services) {
        this.services = client_Services;
    }
    */

    /*
    public Set<ClientReferral> getReferrals() {
        return clientReferrals;
    }
    public List<ClientReferral> getSortedReferrals() {
        List<ClientReferral> sorted = new ArrayList<ClientReferral>(clientReferrals.size());
        for (Iterator<ClientReferral> iterator = clientReferrals.iterator(); iterator.hasNext();) {
            ClientReferral entity = iterator.next();
            sorted.add(entity);
        }
        Collections.sort(sorted);
        return sorted;
    }
    public void setReferrals(Set<ClientReferral> clientReferrals) {
        this.referrals = clientReferrals;
    }
    */

    /*
    public Set<Client_Worker> getWorkers() {
        return workers;
    }

    public void setWorkers(Set<Client_Worker> workers) {
        this.workers = workers;
    }
    */

    /*
    public Set<Client_Agency> getAgencies() {
        return client_Agencies;
    }
    public List<Client_Agency> getSortedAgencies() {
        List<Client_Agency> sorted = new ArrayList<Client_Agency>(client_Agencies.size());
        for (Iterator<Client_Agency> iterator = client_Agencies.iterator(); iterator.hasNext();) {
            Client_Agency entity = iterator.next();
            sorted.add(entity);
        }
        Collections.sort(sorted);
        return sorted;
    }

    public void setAgencies(Set<Client_Agency> client_Agencies) {
        this.agencies = client_Agencies;
    }
    */


    // ******************************************************************
    // COMPLETE GRAPH COLLECTIONS
/*
    public Class[] getCollectionClasses() {
        return new Class[] {};
    }
    public Set getCollection(Class clazz) {
        return null;
    }
    public void addElement(Class clazz, Object element) {
    }

    AbstractCollectionEntity collectionEntity = new AbstractCollectionEntityImpl();
    public void setAbstractCollectionEntity(AbstractCollectionEntityImpl collectionEntity) {
        this.collectionEntity = collectionEntity;
    }

    public boolean removePersistentElement(Class clazz, Long id) {
        if (clazz == ClientAddressHistory.class)
            return collectionEntity.removePersistentElement(clientAddressHistories, ClientAddressHistory.class, id);
        if (clazz == ClientIncome.class)
            return collectionEntity.removePersistentElement(clientIncomes, ClientIncome.class, id);
        //if (clazz == Client_Agency.class)
            //return removePersistentElementMultiId(client_Agencies, Client.class, Agency.class, getId(), id);
        throw new IllegalArgumentException("illegal class");
    }

    public boolean removeNewElement(Class clazz, Integer collectionId) {
        if (clazz == ClientAddressHistory.class)
            return collectionEntity.removeNewElement(clientAddressHistories, collectionId);
        if (clazz == ClientIncome.class)
            return collectionEntity.removeNewElement(clientIncomes, collectionId);
        //if (clazz == Client_Agency.class)
            //return removeNewElement(client_Agencies, collectionId);
        throw new IllegalArgumentException("illegal class");
    }

    public int findHighestCollectionId(Class clazz) {
        if (clazz == ClientAddressHistory.class)
            return collectionEntity.findHighestCollectionId(clientAddressHistories);
        if (clazz == ClientIncome.class)
            return collectionEntity.findHighestCollectionId(clientIncomes);
        //if (clazz == Client_Agency.class)
            //return findHighestCollectionId(client_Agencies);
        throw new IllegalArgumentException("illegal class");
    }

    public int findNextCollectionId(Class clazz) {
        if (clazz == ClientAddressHistory.class)
            return collectionEntity.findNextCollectionId(clientAddressHistories);
        if (clazz == ClientIncome.class)
            return collectionEntity.findNextCollectionId(clientIncomes);
        //if (clazz == Client_Agency.class)
            //return findNextCollectionId(client_Agencies);
        throw new IllegalArgumentException("illegal class");
    }

    public ElementEntity findNewElement(Class clazz, int elementId) {
        if (clazz == ClientAddressHistory.class)
            return collectionEntity.findNewElement(clientAddressHistories, elementId);
        if (clazz == ClientIncome.class)
            return collectionEntity.findNewElement(clientIncomes, elementId);
        //if (clazz == Client_Agency.class)
            //return findNewElement(client_Agencies, elementId);
        throw new IllegalArgumentException("illegal class");
    }

    public ElementEntity findPersistentElement(Class clazz, long elementId) {
        if (clazz == ClientAddressHistory.class)
            return collectionEntity.findPersistentElement(clientAddressHistories, clazz, elementId);
        if (clazz == ClientIncome.class)
            return collectionEntity.findPersistentElement(clientIncomes, clazz, elementId);
        //if (clazz == Client_Agency.class)
            //return findPersistentElementMultiId(client_Agencies, Client.class, Agency.class, getId(), elementId);
        throw new IllegalArgumentException("illegal class");
    }

    public ElementEntity newTransientElement(Class clazz) {
        ElementEntity element = newElement(clazz, 0L, null, null);
        Integer collectionId = findNextCollectionId(clazz);
        element.setCollectionId(collectionId);
        if (clazz == ClientAddressHistory.class)
            clientAddressHistories.add((ClientAddressHistory) element);
        if (clazz == ClientIncome.class)
            clientIncomes.add((ClientIncome) element);
        //if (clazz == Client_Agency.class)
            //client_Agencies.add((Client_Agency) element);
        return element;
    }

    //public ElementEntity newElement(Class clazz, Long addId, Object parameters) {
    public ElementEntity newElement(Class clazz, long usersId, Long addId, Object parameters) {

        //long usersId = Utils.getUserId();

        if (clazz == ClientAddressHistory.class) {
            ClientAddressHistory elementEntity = new ClientAddressHistory();
            elementEntity.setId(addId);
            elementEntity.setClientDetail(this);
            return elementEntity;
        }

        if (clazz == ClientIncome.class) {
            ClientIncome elementEntity = new ClientIncome();
            elementEntity.setId(addId);
            elementEntity.setClientDetail(this);
            return elementEntity;
        }

        *
        if (clazz == Client_Agency.class) {
            Client_Agency elementEntity = new Client_Agency();
            Client_Agency.MultiId multiId = new Client_Agency.MultiId(getId(), addId);
            elementEntity.setMultiId(multiId);
            elementEntity.setClient(this);
            return elementEntity;
        }
        *

        throw new IllegalArgumentException("illegal class");
    }
*/

}
