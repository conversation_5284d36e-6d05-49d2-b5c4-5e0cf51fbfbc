package com.ecco.dom;

import com.ecco.infrastructure.entity.AbstractUnidentifiedVersionedEntity;
import com.ecco.infrastructure.entity.RequestDelete;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * Entity which can be marked as deletion requested
 */
@MappedSuperclass
public abstract class RequestDeleteEntityImpl<PK extends Serializable> extends AbstractUnidentifiedVersionedEntity<PK>
        implements RequestDelete {
    @Column
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime requestedDelete;

    public DateTime getRequestedDelete() {
        return requestedDelete;
    }

    public void setRequestedDelete(DateTime requestedDelete) {
        this.requestedDelete = requestedDelete;
    }

    @Override
    public boolean isRequestedDelete() {
        return this.requestedDelete != null;
    }

    @Override
    public void requestDelete() {
        this.requestedDelete = new DateTime();
    }

    @Override
    public void cancelDeleteRequest() {
        this.requestedDelete = null;
    }
}
