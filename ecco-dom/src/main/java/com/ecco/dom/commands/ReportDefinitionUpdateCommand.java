package com.ecco.dom.commands;

import java.util.UUID;

import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.*;

@Entity
@DiscriminatorValue("defUpdate") // TODO rename to reportDef
public class ReportDefinitionUpdateCommand extends ReportDefinitionCommand {

    public ReportDefinitionUpdateCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                         long userId, @NonNull String body, @NonNull UUID reportDefUuid) {
        super(uuid, remoteCreationTime, userId, body, reportDefUuid);
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected ReportDefinitionUpdateCommand() {
        super();
    }

}
