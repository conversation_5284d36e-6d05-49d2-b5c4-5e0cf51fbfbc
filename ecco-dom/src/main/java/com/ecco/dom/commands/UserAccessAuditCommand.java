package com.ecco.dom.commands;

import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.*;
import java.util.UUID;

@Entity
@DiscriminatorValue("userAccessAudit")
public class UserAccessAuditCommand extends ServiceRecipientTaskCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public UserAccessAuditCommand() {
    }

    public UserAccessAuditCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                  long userId, @NonNull String body, int serviceRecipientId, @NonNull String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName);
    }
}
