package com.ecco.dom;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.*;

import com.ecco.config.dom.Setting;
import org.junit.Test;

public class SettingTest {

    public enum FooBar {
        FOO, BAR;
    }

    @Test
    public void canGetAsEnum() {
        Setting setting = new Setting("namespace", "key", "BAR", "The description", Setting.Type.ENUM, FooBar.class);

        FooBar asEnum = setting.<FooBar>getAsEnum(); //JDK8 type inference
        assertThat(asEnum, is(FooBar.BAR));
    }
}
