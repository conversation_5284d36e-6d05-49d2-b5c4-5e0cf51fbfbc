package com.ecco.dom.commands;

import java.util.UUID;

import org.jspecify.annotations.NonNull;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;

@Entity
@DiscriminatorValue("questionanswer")
public class QuestionAnswerCommand extends ServiceRecipientEvidenceCommand {

    @Nullable
    @Column(nullable = true)
    protected Long questionDefId;

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public QuestionAnswerCommand() {
        super();
    }

    public QuestionAnswerCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                 long userId, @NonNull String body, int serviceRecipientId,
                                 @NonNull Long questionDefId, @NonNull String evidenceGroupKey, String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName, evidenceGroupKey);
        this.questionDefId = questionDefId;
    }

    public long getQuestionDefId() {
        return questionDefId;
    }

}
