package com.ecco.dom.commands;

import java.util.UUID;

import org.jspecify.annotations.NonNull;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;

@Entity
@DiscriminatorValue("taskUpdate")
public class ReferralTaskUpdateCommand extends ServiceRecipientTaskCommand {
    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public ReferralTaskUpdateCommand() {
    }

    public ReferralTaskUpdateCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                                     long userId, @NonNull String body, int serviceRecipientId,
                                     @NonNull String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName);
    }
}
