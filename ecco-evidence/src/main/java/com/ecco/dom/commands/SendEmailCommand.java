package com.ecco.dom.commands;

import java.util.UUID;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

@Entity
@DiscriminatorValue("sendEmail")
public class SendEmailCommand extends ServiceRecipientEvidenceCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public SendEmailCommand() {
    }

    public SendEmailCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                            long userId, @NonNull String body, int serviceRecipientId,
                            @NonNull String evidenceGroupKey, String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName, evidenceGroupKey);
    }
}
