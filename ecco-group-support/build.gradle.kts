/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id("org.eccosolutions.querydsl-java-library")
}

dependencies {
    implementation(project(":ecco-config"))
    implementation(project(":ecco-dao"))
    implementation(project(":ecco-dom"))
    implementation(project(":ecco-evidence"))
    implementation(project(":ecco-hr"))
    implementation(project(":ecco-service"))
    implementation(project(":ecco-service-config"))

    implementation("joda-time:joda-time:2.10.8")
    implementation("com.google.guava:guava")
}

description = "ecco-group-support"
