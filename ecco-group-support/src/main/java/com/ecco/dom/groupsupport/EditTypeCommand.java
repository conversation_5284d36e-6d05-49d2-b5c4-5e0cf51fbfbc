package com.ecco.dom.groupsupport;

import com.ecco.infrastructure.dom.ConfigCommand;
import org.joda.time.Instant;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue("grouptype")
public class EditTypeCommand extends ConfigCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public EditTypeCommand() {
        super();
    }

    public EditTypeCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                               long userId, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

}
