package com.ecco.dom.groupsupport;

import java.io.Serializable;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import com.ecco.dom.Referral;
import com.querydsl.core.annotations.QueryInit;
import lombok.Getter;
import lombok.Setter;

@Embeddable
@Getter
@Setter
public class GroupActivity_Referral_MultiId implements Serializable {

    private static final long serialVersionUID = 1L;

    @QueryInit("*.*")
    @ManyToOne(fetch=FetchType.LAZY, optional=false)
    private GroupSupportActivity activity;

    @ManyToOne(fetch=FetchType.LAZY, optional=false)
    private Referral referral;

    @Override
    public String toString() {
        return "" + activity.getId() + ", " + getReferral().getId();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        GroupActivity_Referral_MultiId that = (GroupActivity_Referral_MultiId) o;

        if (activity != null ? !activity.equals(that.activity) : that.activity != null) {
            return false;
        }
        if (referral != null ? !referral.equals(that.referral) : that.referral != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result;
        result = (activity != null ? activity.hashCode() : 0);
        result = 31 * result + (referral != null ? referral.hashCode() : 0);
        return result;
    }

}
