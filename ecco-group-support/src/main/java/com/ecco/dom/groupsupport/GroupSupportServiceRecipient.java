package com.ecco.dom.groupsupport;

import com.ecco.dom.BaseServiceRecipientEvidence;
import com.ecco.dom.Individual;
import com.ecco.dom.ServiceRecipientAttachment;
import com.ecco.dom.contacts.AddressLike;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.jspecify.annotations.NonNull;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Entity
@Getter
@Setter
@DiscriminatorValue(GroupSupportServiceRecipient.DISCRIMINATOR)
public class GroupSupportServiceRecipient extends BaseServiceRecipientEvidence {

    private static final long serialVersionUID = 1L;
    public static final String DISCRIMINATOR = "gs";
    public static final String PREFIX = "gs"; // see BaseServiceRecipient.getPrefix, and service-recipient-dto.ts

    @OneToOne(mappedBy="serviceRecipient", fetch=FetchType.LAZY, cascade= CascadeType.REMOVE)
    private GroupSupportActivity groupSupport;

    @OneToMany(mappedBy = "serviceRecipient", orphanRemoval = true, cascade = CascadeType.REMOVE, fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @OrderBy("id DESC")
    private Set<ServiceRecipientAttachment> attachments = new HashSet<>(); // see https://hibernate.atlassian.net/browse/HHH-9940

    @Override
    public String getDisplayName() {
        return groupSupport.displayName();
    }

    @Override
    public AddressLike getAddress() {
        return null;
    }

    @Override
    public String getCalendarId() {
        return null;
    }

    @Override
    public Individual getContact() {
        return null;
    }

    @Override
    public Map<String,String> getTextMap() {
        return null;
    }

    @Override
    public String getParentCode() {
        return null;
    }

    @Override
    public Long getParentId() {
        return groupSupport.getId();
    }

    @Override
    public GroupSupportActivity getTargetEntity() {
        return groupSupport;
    }

    @NonNull
    @Override
    public String getPrefix() {
        return PREFIX;
    }
}
