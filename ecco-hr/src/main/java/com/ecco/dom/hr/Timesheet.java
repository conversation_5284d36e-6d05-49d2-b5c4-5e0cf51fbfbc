package com.ecco.dom.hr;

import javax.persistence.*;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.springframework.format.annotation.DateTimeFormat;

// we use the same table but not the abstract class
// because we require time on the dates when displaying an individual record

@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name="hr_fromtos")
@Inheritance(strategy=InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="discriminator_orm", discriminatorType=DiscriminatorType.STRING)
@DiscriminatorValue("timesheets")
public class Timesheet extends AbstractLongKeyedEntity {

    @ManyToOne(cascade={}, fetch=FetchType.LAZY)
    @JoinColumn(name = "workerJobId")
    WorkerJob workerJob;

    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @DateTimeFormat(style="SS")
    DateTime fromDate;

    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @DateTimeFormat(style="SS")
    DateTime toDate;
}
