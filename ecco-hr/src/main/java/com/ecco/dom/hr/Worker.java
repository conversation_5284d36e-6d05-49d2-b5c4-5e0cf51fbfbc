package com.ecco.dom.hr;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.calendar.core.CalendarOwnerDefinition;
import com.ecco.calendar.dom.Calendarable;
import com.ecco.dom.*;
import com.ecco.dto.ClientDefinition;
import com.ecco.evidence.dom.Signature;
import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.security.dom.User;
import com.ecco.dom.contacts.Address;
import com.ecco.security.event.CalendarableCreated;
import lombok.Getter;
import lombok.Setter;

import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.context.ApplicationEvent;

import java.util.Set;

import javax.persistence.*;

@Setter
@Getter
@Entity
@Table(name="hr_workers")
@Configurable
@Slf4j
public class Worker extends ClientDetailAbstract implements Created, Calendarable {

    public static final int DEFAULT_SERVICE_ALLOCATION_ID = -200;

    @PersistenceContext
    private transient EntityManager em;

    @Autowired
    @Transient
    protected transient MessageBus<ApplicationEvent> messageBus;

    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime created;

    private String crbNumber;

    private String disclosureNumber;

    @OneToMany(mappedBy = "worker")
    private Set<WorkerJob> workerJobs;

    @Override
    public void setCreated(DateTime created) {
        if (this.created == null) {
            this.created = created;
        }
    }

    /*
    // the worker can't have 2 jobs at once - otherwise they need to be a bundled job
    @OneToMany(mappedBy="worker", fetch = FetchType.EAGER)
    @OrderBy("fromDate DESC")
    private Set<SickLeave> sickLeave = new HashSet<>();

    @OneToMany(mappedBy="worker", fetch = FetchType.EAGER)
    @OrderBy("fromDate DESC")
    private Set<Holiday> holidays = new HashSet<>();
    */

    @Nullable
    @ManyToOne(fetch=FetchType.EAGER)
    @JoinColumn(name = "primaryLocationId", insertable = false, updatable = false)
    private FixedContainer primaryLocation;

    @Column
    private Integer primaryLocationId;

    // ui only - our 'link' between user and worker is through the contact
    // because we don't want all users being a worker (eg client / commissioner)
    // and we don't want all workers to have to be users (eg volunteers)
    @Transient
    private User linkToUser;

    public String getCalendarId() {
        return getContact().getCalendarId();
    }

    public Worker() {
        injectServices();
    }

    public Object readResolve()  {
        injectServices();
        return this;
    }

    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    /**
     * Link this worker to an existing user by ensuring they share a contact.
     * The relationships to remember are: Worker -> Contact -> User (but User can set the contact)
     * At the end of this method we set the worker's contact user to the incoming user but also
     * replace the incoming user's contact with the workers contact, which is now loaded with all data:
     *      toWorkerContact.setUser(fromWorkerUser); -- replace this worker's contact's user
     *      fromWorkerUser.setContact(toWorkerContact); -- replace the user contact with the loaded-up one
     * This basically means the worker is set to the incoming user, but the worker's contact is primary.
     * This leaves behind the incoming user's contact, which is now empty and actually deleted in em.remove(fromUserContact).
     * It also leaves behind the worker's contact's existing user, which is orphaned, but the user is just really about permissions.
     * NB We can also do this the other way round... by orphaning the workers contact and replacing its contact with the desired user's contact.
     *      - at the moment this alternative approach is helpful when the worker is new, and this user has a lot of data that currently fails - see 20230623_linkUser.txt.
     * <ul>
     *     <li>if the worker was already linked to a user, that link is broken and the Comment, Referral, ReferralActivityWorker and Signature remain with the worker and its new linked user.</li>
     *     <li>any Comment, Referral, ReferralActivityWorker and Signature entities on the user's contact get moved onto the worker's contact and the user's contact removed</li>
     *     <li>the calendar from the user's contact moves onto the worker's contact (if the worker was already linked to a user then the existing calendar stays with that user)</li>
     * </ul>
     * @param fromWorkerUser a managed user entity
     */
    public void migrateExistingUserToWorker(final User fromWorkerUser) {
        if (!em.contains(fromWorkerUser)) {
            throw new IllegalStateException("User must be a managed entity.");
        }

        // toWorkerContact is the existing worker's contact with (maybe) a user
        // NB although WorkerController does 'w.setLinkToUser(u);' this doesn't change the worker's existing contact or existing user
        // so we get the worker's existing contact to move data to it
        final Individual toWorkerContact;
        // Cope with having a detached worker to start with
        if (!em.contains(this.getContact())) {
            toWorkerContact = em.getReference(this.getContact().getClass(), this.getContact().getId());
        } else {
            toWorkerContact = this.getContact();
        }

        // if 'this' worker's contact has an existing user, then create a new/empty contact for it
        // because it will be orphaned, and a user should have a contact
        if (toWorkerContact.getUser() != null) {
            // Create an alternative contact for the existing linked user - it will lose all associated items which
            // will remain with the worker. NOTE: We don't copy lots of detail as a user contact is relatively thin (for now).
            final Individual newUserContact = new Individual();
            newUserContact.setFirstName(toWorkerContact.getFirstName());
            newUserContact.setLastName(toWorkerContact.getLastName());
            newUserContact.setCalendarId(toWorkerContact.getCalendarId());
            // copy the existing user to the new contact
            newUserContact.setUser(toWorkerContact.getUser());
            // ASSUME HR is up to date and copy that data in
            newUserContact.setAddress(toWorkerContact.getAddress());
            newUserContact.setEmail(toWorkerContact.getEmail());
            newUserContact.setBirthDate(toWorkerContact.getBirthDate());
            newUserContact.setMobileNumber(toWorkerContact.getMobileNumber());
            newUserContact.setPhoneNumber(toWorkerContact.getPhoneNumber());
            newUserContact.setPreferredContactMethod(toWorkerContact.getPreferredContactMethod());
            newUserContact.setJobTitle(toWorkerContact.getJobTitle());

            // replace the existing worker's user with the new contact
            toWorkerContact.getUser().setContact(newUserContact);
            // it's the contact that holds the link to the user
            em.persist(newUserContact);
            log.debug("Created new contact for user: " + toWorkerContact.getUser().getUsername());
        }

        // The current user contact will effectively be lost.
        // It could have Comment, MinimalReferral, ReferralActivityWorker or Signature hanging off it.
        // So move all of these to the worker's contact instead.
        final Individual fromUserContact = fromWorkerUser.getContact();

        // TODO need to include more entities here as we fail when fromUserContact has data remaining on it
        log.debug("Moving related entities from contact " + fromUserContact.getId() + " to contact " + toWorkerContact.getId());
        switchContacts(toWorkerContact, fromUserContact, ReferralActivityWorker.BULK_UPDATE_INDIVIDUAL_QUERY);
        switchContacts(toWorkerContact, fromUserContact, Signature.BULK_UPDATE_INDIVIDUAL_QUERY);
        switchContacts(toWorkerContact, fromUserContact, Referral.BULK_UPDATE_SUPPORT_WORKER_QUERY);
        switchContacts(toWorkerContact, fromUserContact, Referral.BULK_UPDATE_REFERRER_QUERY);
        switchContacts(toWorkerContact, fromUserContact, Referral.BULK_UPDATE_INTERVIEWER1_QUERY);
        switchContacts(toWorkerContact, fromUserContact, Referral.BULK_UPDATE_INTERVIEWER2_QUERY);
        switchContactsNative(toWorkerContact, fromUserContact, Referral.BULK_UPDATE_CONTACTS_NATIVE_QUERY);
        switchContacts(toWorkerContact, fromUserContact, Contact_Event.BULK_UPDATE_CONTACT_QUERY);
        switchContacts(toWorkerContact, fromUserContact, EvidenceSupportAction.BULK_UPDATE_AUTHOR_QUERY);
        switchContacts(toWorkerContact, fromUserContact, EvidenceSupportAnswer.BULK_UPDATE_AUTHOR_QUERY);
        switchContacts(toWorkerContact, fromUserContact, EvidenceSupportOutcome.BULK_UPDATE_AUTHOR_QUERY);
        switchContacts(toWorkerContact, fromUserContact, EvidenceSupportWork.BULK_UPDATE_AUTHOR_QUERY);
        switchContacts(toWorkerContact, fromUserContact, EvidenceThreatAction.BULK_UPDATE_AUTHOR_QUERY);
        switchContacts(toWorkerContact, fromUserContact, EvidenceThreatOutcome.BULK_UPDATE_AUTHOR_QUERY);
        switchContacts(toWorkerContact, fromUserContact, EvidenceThreatWork.BULK_UPDATE_AUTHOR_QUERY);
        switchContacts(toWorkerContact, fromUserContact, ReferralBaseComment.BULK_UPDATE_AUTHOR_QUERY);
        switchContacts(toWorkerContact, fromUserContact, EvidenceSupportComment.BULK_UPDATE_AUTHOR_QUERY);
        switchContacts(toWorkerContact, fromUserContact, EvidenceThreatComment.BULK_UPDATE_AUTHOR_QUERY);
        toWorkerContact.setCalendarId(fromUserContact.getCalendarId());

        // Attach this worker to a user by removing the user's contact and joining up this one.
        em.remove(fromUserContact);
        log.trace("Removed contact " + fromUserContact.getId());
        toWorkerContact.setUser(fromWorkerUser);
        fromWorkerUser.setContact(toWorkerContact);
        log.debug("Switched user contact for " + fromWorkerUser.getUsername() + " to worker contact for worker " + this.getId());
    }

    private int switchContacts(Individual currentWorkerContact, Individual currentUserContact, final String updateQuery) {
        final Query commentQuery = em.createNamedQuery(updateQuery);
        commentQuery.setParameter("newContact", currentWorkerContact);
        commentQuery.setParameter("oldContact", currentUserContact);
        final int updateCount = commentQuery.executeUpdate();
        log.trace("Moved " + updateCount + " for " + updateQuery);
        return updateCount;
    }

    private int switchContactsNative(Individual currentWorkerContact, Individual currentUserContact, final String updateQuery) {
        final Query commentQuery = em.createNamedQuery(updateQuery);
        commentQuery.setParameter("newContactId", currentWorkerContact.getId());
        commentQuery.setParameter("oldContactId", currentUserContact.getId());
        final int updateCount = commentQuery.executeUpdate();
        log.trace("Moved " + updateCount + " for " + updateQuery);
        return updateCount;
    }

    public static ClientDefinition toClientDefinition(Worker input) {
        ClientDefinition.Builder builder = ClientDefinition.BuilderFactory.create()
                .localClientId(input.getId())
                .localContactId(input.getContact().getId())
                .firstName(input.getContact().getFirstName())
                .lastName(input.getContact().getLastName())
                .genderKey(input.getGender() != null ? input.getGender().getBusinessKey() : null)
                // TODO genderAtBirthKey
                .birthDate(input.getBirthDate() != null ? input.getBirthDate().toLocalDate() : null)
                .code(input.getCode())
                .ni(input.getNi())
                // TODO firstLanguageKey
                // TODO ethnicOriginKey
                // TODO nationalityKey
                // TODO maritalStatusKey
                // TODO religionKey
                // TODO disabilityKey
                // TODO sexualOrientationKey
                // TODO address
                .phoneNumber(input.getContact().getPhoneNumber())
                .mobileNumber(input.getContact().getMobileNumber())
                .email(input.getContact().getEmail())
                .assignedLocationId(input.primaryLocationId)
                .assignedLocationName(input.primaryLocation != null ? input.primaryLocation.getName() : null)
                .externalClientRef(10000 + input.getId() + "-w") // TODO: input.getExternalSystemRef())
                .externalClientSource("payroll");// TODO: Add to worker input.getExternalSystemSourceName());

        Address address = input.getContact().getAddress();
        if (address != null) {
            String[] lines = {address.getLine1()}; // only first line
            builder = builder
                    .postCode(address.getPostCode())
                    .address(lines);
        }
        return builder.build();
    }

    @PrePersist
    private void prePersist() {
        fireCalendarableCreated();
    }

    /** Handle the case where the contact is created as a straightforward entity. */
    public void fireCalendarableCreated() {
        if (getContact().getCalendarId() == null) {
            final CalendarableCreated event = new CalendarableCreated(this, this);
            messageBus.publishBeforeTxEnd(event);
        }
    }

    @Override
    public void setCalendarId(String createCalendar) {
        getContact().setCalendarId(createCalendar);
    }

    @Override
    public CalendarOwnerDefinition.Builder buildCalendarOwner(CalendarOwnerDefinition.Builder builder) {
        return getContact().buildCalendarOwner(builder);
    }

    @Override
    public CalendarOwnerDefinition.Builder buildCalendarNamesUpdate(CalendarOwnerDefinition.Builder builder) {
        return getContact().buildCalendarNamesUpdate(builder);
    }
}
