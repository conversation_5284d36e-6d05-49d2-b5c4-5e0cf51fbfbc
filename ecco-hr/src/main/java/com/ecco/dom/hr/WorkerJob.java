package com.ecco.dom.hr;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.HashMap;
import javax.persistence.*;

import com.ecco.dom.EvidenceCapable;
import com.ecco.dom.Identified;
import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.entity.AbstractUnidentifiedVersionedEntity;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.querydsl.core.annotations.QueryInit;
import lombok.*;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.Assert;

@javax.persistence.Entity
@Getter
@Setter
@Table(name="hr_workerjobs")
public class WorkerJob extends AbstractUnidentifiedVersionedEntity<Integer> implements EvidenceCapable, Created {

    @Id
    @Column(name = "id", nullable = false) // oracle doesn't like using unique=true
    @GeneratedValue(generator = "workersTableGenerator")
    @TableGenerator(
            name = "workersTableGenerator", initialValue = 1, pkColumnValue = "workerJobs",
            allocationSize = 1, table = "hibernate_sequences")
    private Integer id = null;

    @OneToOne(cascade= {CascadeType.PERSIST, CascadeType.REMOVE})
    @JoinColumn(name="serviceRecipientId")
    @QueryInit("*.*.*")
    private WorkerJobServiceRecipient serviceRecipient;

    @Setter(AccessLevel.PRIVATE)
    @Column(insertable = false, updatable = false)
    private Integer serviceRecipientId;

    // alternative id
    private String code;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workerId")
    @NotFound(action = NotFoundAction.EXCEPTION)
    @QueryInit("*.*")
    private Worker worker;

    @DateTimeFormat(style="S-")
    ZonedDateTime startDate; // receivingServiceDate on referral (? contractStartDate)
    @DateTimeFormat(style="S-")
    ZonedDateTime endDate; // exited on referral

    @Column(name="workCapacity")
    private Integer contractedWeeklyHours;
    private BigDecimal rate;
    private BigDecimal entitlement;
    boolean volunteer;

    @Lob
    @Column
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private HashMap<String, String> textMap = new HashMap<>();

    @PrePersist
    protected void createServiceRecipientIfNull() {
        // null check as we may have called this earlier to get access to serviceRecipient
        if (this.serviceRecipient == null) {
            this.serviceRecipient = new WorkerJobServiceRecipient();
            this.serviceRecipient.setWorkerJob(this);
        }
    }

    public void setServiceAllocation(ServiceCategorisation serviceAllocation) {
        Assert.notNull(serviceAllocation, "allocation cannot be null");
        createServiceRecipientIfNull();
        serviceRecipient.setServiceAllocation(serviceAllocation);
    }

    @Override
    public DateTime getCreated() {
        return serviceRecipient.getCreated();
    }

    @Override
    public void setCreated(DateTime created) {
        createServiceRecipientIfNull();
        this.serviceRecipient.setCreated(created);
    }

    @Override
    public Integer countSiblings() {
        // can't use the simpler repository method since its not in scope (could use findAllServiceRecipientIdsFromClientId)
        return this.getWorker().getWorkerJobs().size();
    }

    @Override
    public Identified getGrandParent() {
        return this.getWorker();
    }

    @Override
    public WorkerJob getParentEvidenceCapable() {
        return null;
    }

    /*
    private Set<Timesheet> timesheets = new HashSet<Timesheet>();

    @OneToMany(mappedBy = "workerJob", orphanRemoval = true, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    public Set<Timesheet> getTimesheets() {
        return timesheets;
    }
    public void setTimesheets(Set<Timesheet> timesheets) {
        this.timesheets = timesheets;
    }
    */

    /*
    private Set<Work> work = new HashSet<Work>();
    @OneToMany(mappedBy = "job", orphanRemoval = true, cascade = { CascadeType.ALL }, fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    public Set<Work> getWork() {
        return work;
    }
    public void setWork(Set<Work> work) {
        this.work = work;
    }
    */

    /*
    @OneToMany(mappedBy="workerJob", fetch = FetchType.EAGER)
    public Set<HolidaysAccrued> getHolidaysAccrued() {
        return holidaysAccrued;
    }
    public void setHolidaysAccrued(Set<HolidaysAccrued> holidaysAccrued) {
        this.holidaysAccrued = holidaysAccrued;
    }
    */

}
