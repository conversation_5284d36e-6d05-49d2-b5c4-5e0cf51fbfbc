package com.ecco.service.hr;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.hr.Worker;
import com.ecco.dto.ClientDefinition;
import com.ecco.hr.dao.WorkerRepository;
import com.ecco.infrastructure.config.root.EccoEnvironment;
import com.ecco.calendar.dom.MedDate;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.List;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.startsWithIgnoreCase;

@Service("searchHrService")
@AllArgsConstructor
public class SearchHrServiceImpl implements SearchHrService {

    @PersistenceContext
    protected EntityManager em;

    private final EccoEnvironment env;
    private final ListDefinitionRepository listDefinitionRepository;
    private final WorkerRepository workerRepository;


    @Override
    public List<ClientDefinition> getLocalHrMatches(ClientDefinition staff) {
        // find the first gender to match by name
        ListDefinitionEntry gender = staff.getGenderKey() != null
                ? this.listDefinitionRepository.findByBusinessKey(staff.getGenderKey()).orElseThrow(NullPointerException::new)
                : null;
        MedDate birthDate = MedDate.from(staff.getBirthDate());

        //EntityFilter<Worker> config;
        Stream<Worker> entities = Stream.<Worker>builder().build();
        if (env.shouldEncryptDatabaseFields()) {
            /*config = new EncryptedWorkerByExampleFilter(
                    staff.getCode(), staff.getLastName(), staff.getFirstName(),
                    birthDate, gender,
                    staff.getPostCode());
            entities = entityViewService.getEntities(config).stream();*/
        } else {
//            config = new WorkerByExampleFilter(staff.getCode(), staff.getLastName(), staff.getFirstName(),
//                    birthDate, gender,
//                    staff.getPostCode());
//            entities = entityViewService.getEntities(config).stream();
//            entities = workerRepository.findAllByContact_LastNameIsLike(staff.getLastName())
            entities = workerRepository.findAll().stream() // TODO: Use findAll(Example.of(worker))
                    .filter(w -> startsWithIgnoreCase(w.getContact().getFirstName(), staff.getFirstName())
                            && startsWithIgnoreCase(w.getContact().getLastName(), staff.getLastName()));
        }

        return entities.map(Worker::toClientDefinition).collect(toList());
    }

    @Override
    public List<ClientDefinition> secureLocalHrMatches(List<ClientDefinition> localStaff) {
        // Does nothing for now. See secureLocalClientMatches impl for potential future impl
        return localStaff;
    }
}
