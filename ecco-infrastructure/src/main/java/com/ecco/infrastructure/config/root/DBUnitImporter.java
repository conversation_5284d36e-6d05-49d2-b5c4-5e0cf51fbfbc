package com.ecco.infrastructure.config.root;

import javax.sql.DataSource;

import org.dbunit.database.DatabaseConnection;
import org.dbunit.database.IDatabaseConnection;
import org.dbunit.dataset.FilteredDataSet;
import org.dbunit.dataset.filter.ExcludeTableFilter;
import org.dbunit.dataset.xml.FlatXmlDataSetBuilder;
import org.dbunit.operation.DatabaseOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.io.Resource;
import org.springframework.jdbc.datasource.DataSourceUtils;

import java.io.InputStream;
import java.sql.Connection;

/**
 * Export dbunit files when containing {@link ApplicationContext} emits {@link ContextRefreshedEvent}.
 */
public class DBUnitImporter implements ApplicationListener<ContextRefreshedEvent> {

    private final ApplicationContext context;
    private final DataSource dataSource;

    @Autowired
    public DBUnitImporter(ApplicationContext context, DataSource dataSource) {
        this.context = context;
        this.dataSource = dataSource;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().equals(context)) {
            try {
                doClean_Import();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private void doClean_Import() throws Exception {

        IDatabaseConnection con;

        // disable foreign key on connection
        // because we want to remove existing data which might already have dependencies
        // (also liquibase DatabaseFactory can find the right db class)
        // we did use DatabaseSequenceFilter so that order is inserted correctly (used with a dtd file) to avoid FK problems on the import
        // but this results in CyclicTablesDependencyException: Table: referrals ([referralcomments]) - (after we ignored commenttargets)
        // referral and referralcomments need to be inserted in the same transaction (due to exitCommentId and referralId)
        // simply ignoring the cyclical errors and referralcomments, we get ref integrity on importing, inserting referralcomments before contacts
        // so actually we are best with foreign key

        // use spring class to be part of tx manager - see comment at end of http://archive.oreilly.com/pub/post/dbunit_made_easy.html
        Connection c = DataSourceUtils.getConnection(this.dataSource);
        AbstractSqlDatabaseConfig<?> dbConfig = this.context.getBean(AbstractSqlDatabaseConfig.class);
        ForeignKeyDisabling.removeReferentialIntegrityConn(c, dbConfig);

        // for large files - see streaming options at http://dbunit.sourceforge.net/faq.html
        con = new DatabaseConnection(c);
        // TODO do we need to avoid "Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'MySQL' (e.g. some datatypes may not be supported properly)."
        // determine database: Gareth did code to check hibernate determined database, liquibase does its own understanding too
        //DatabaseConfig config = con.getConfig();
        //config.setProperty(DatabaseConfig.PROPERTY_DATATYPE_FACTORY, new MySqlDataTypeFactory());

        // import file - based on AllColumnsDateSubstitutingDataSetLoader
        Resource resourceData = context.getResource("classpath:sql/dbunit-export.xml");
        // XSD DISABLED (using fk approach for now)
        //Resource resourceSchema = context.getResource("classpath:sql/dbunit-schema.dtd");
        FlatXmlDataSetBuilder builder = new FlatXmlDataSetBuilder();
        // with a dtd this isn't needed, but is no harm and I think it should be a default
        builder.setColumnSensing(true);

        // filter out the schema table
        ExcludeTableFilter filter = new ExcludeTableFilter(new String [] {"DATABASECHANGELOG"});

        InputStream inputData = resourceData.getInputStream();
        //InputStream inputSchema = resourceSchema.getInputStream();
        try {
            FilteredDataSet datasetFilter = new FilteredDataSet(filter, builder.build(inputData));
            // not sure if its right builder.setMetaDataSetFromDtd(inputSchema);

            // XSD DISABLED (using fk approach for now)
            //IDataSet datasetSchema = new FlatDtdDataSet(inputSchema);
            //IDataSet datasetCombined = new CompositeDataSet(datasetSchema, datasetFilter);

            // deletes all tables included in the dataset
            // which is why we need xsd shema so it can clear tables with otherwise unrelated constraints
            DatabaseOperation.CLEAN_INSERT.execute(con, datasetFilter);
        } finally {
            inputData.close();
            // XSD DISABLED (using fk approach for now)
            ///inputSchema.close();
            if (c != null) {
                DataSourceUtils.releaseConnection(c, this.dataSource);
            }
        }

    }

}
