package com.ecco.infrastructure.config.root;

public enum LiquibaseMode {

    /** NOT_SET - what to do if it's not set - useful for tests, also will be case for CloudFoundry */
    NOT_SET,

    /** Don't use Liquibase */
    DISABLED,

    /** Run all baseline and update scripts */
    CREATE,

    /** Drop schema, then do CREATE */
    DROP_CREATE,

    /** Run only scripts for the SchemaVersion onwards (e.g. 2021 will not run anything other than the 2021 changelogs
     * This can be used once CREATE has been used. */
    UPDATE,

    /** Same as disabled, but DUMP changelog of schema and data */
    // outputs to newDataChangeLog.xml where the code is ran from
    // can be useful with HibernatePropertiesConfig hibernate.hbm2ddl=update
    DUMP;

    public static LiquibaseMode valueFor(String liquibaseMode) {
        return liquibaseMode == null ? NOT_SET : valueOf(liquibaseMode.toUpperCase());
    }

    public boolean shouldCreateSchema() {
        switch(this) {
            case CREATE:
            case DROP_CREATE:
                return true;
            case NOT_SET:
            case DISABLED:
            case UPDATE:
            case DUMP:
                return false;
            default:
                throw new UnsupportedOperationException("Can't handle:" + this);
        }
    }
}
