package com.ecco.infrastructure.config.root;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.annotation.PropertySource;

@Configuration(proxyBeanMethods = false)
@Profile(Profiles.PROD_SQLSERVER)
@PropertySource({"classpath:properties/prod/clickatell.properties"
        , "classpath:properties/prod/email.properties"
        , "classpath:properties/prod/esendex.properties"
        , "classpath:properties/prod/misc.properties"
})
public class ProductionSqlServerPropertiesConfig implements EnvironmentPropertiesConfig {
}
