package com.ecco.infrastructure.config.root;

import org.springframework.core.env.Environment;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

public interface Profiles {

    String DEFAULT = "default";

    String CLOUDFOUNDRY = "cloud";

    String PROD = "prod";

    String PROD_ORACLE = "prod-oracle";

    String EMBEDDED = "embedded";

    String TEST_FIXTURE = "test-fixture";

    String DEV = "dev";

    String DEV_ORACLE = "dev-oracle"; // -Denv=dev -Ddb=oracle

    String DEV_SQLSERVER = "dev-sqlserver"; // -Denv=dev -Ddb=sqlserver

    String PROD_SQLSERVER = "prod-sqlserver";

    String EXPORT_LIQUIBASE = "liquibase-dump";

    String EXPORT_DBUNIT = "dbunit-dump";

    String IMPORT_DBUNIT = "dbunit-import";

    class Validator {
        static final Set<String> allProfiles;
        static {
            // Get all string fields in Profiles
            Field[] fields = Profiles.class.getFields();
            Set<String> introspectedProfiles = new HashSet<>(fields.length);
            for (Field field : fields) {
                try {
                    if ((!field.isSynthetic()) && field.getType().equals(String.class)) {
                        introspectedProfiles.add((String) field.get(null));
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }

            // always add 'test' to available profiles
            introspectedProfiles.add("test"); // Because we use "test" for application-test.yml when testing
            allProfiles = Collections.unmodifiableSet(introspectedProfiles);
        }

        public static void validate(Environment environment) {
            for (String  profile : environment.getActiveProfiles()) {
                Assert.state(allProfiles.contains(profile), profile + " is not a valid profile");
            }
        }
    }}
