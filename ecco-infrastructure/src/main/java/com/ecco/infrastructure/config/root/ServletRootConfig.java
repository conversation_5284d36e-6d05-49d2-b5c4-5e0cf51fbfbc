package com.ecco.infrastructure.config.root;


import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.web.SetRequestAttributeFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;

/**
 * Along with RootConfig.java, makes up the components of the application.
 *
 * Used in ecco-web-api spring.factories and ecco-war web.xml (contextConfigLocation)
 */
@Configuration
public class ServletRootConfig {


    @Bean
    public Filter resourceVersionKeyFilter(ApplicationProperties applicationProperties) {
        return new SetRequestAttributeFilter("resourceRootPath", applicationProperties.getResourceRootPath());
    }


}
