package com.ecco.infrastructure.hibernate;

import java.sql.Types;

import org.hibernate.dialect.H2Dialect;

/**
 * Provides various workaround for deficiences in H2 and Liquibase.
 * <p>
 * H2 allows longvarchar (SQL Type = -1) to be created but reads back as
 * varchar (SQL Type = 12) which causes validation to fail.
 * <p>
 * Liquibase doesn't map CLOB and BLOB to those types for H2 but overrides them
 * to break the behaviour.  We handle it by mapping Hibernate to the result.
 */
public class H2DialectWithH2AndLiquibaseWorkarounds extends H2Dialect {

    public H2DialectWithH2AndLiquibaseWorkarounds() {
        super();
        registerColumnType(Types.LONGVARCHAR, "clob"); // Liquibase tells H2 TEXT, and H2 gives clob

    }
}
