package com.ecco.infrastructure.hibernate;

import com.ecco.infrastructure.config.web.ConvertersConfig;

import java.io.Serializable;

public class JSONUserTypeStringToString extends JSONUserTypeToStringColumn implements Serializable {

    private static JSONClob converter = new JSONClobString(ConvertersConfig.getObjectMapper());

    public JSONClob getConverter() {
        return converter;
    }

}