package com.ecco.infrastructure.liquibase;

import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.sql.Blob;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.util.ISO8601DateFormat;

/**
 * Convert a Java HashMap in a table blob to JSON.
 */
public class StringHashMapToJsonChange extends UpdateableResultSetChange {

    private static ObjectMapper mapper = new Jackson2ObjectMapperBuilder()
            .featuresToDisable(SerializationFeature.WRITE_DATE_KEYS_AS_TIMESTAMPS)
            .dateFormat(new ISO8601DateFormat())
            .build();


    private String dstColumnName;

    private String srcColumnName;


    public String getDstColumnName() {
        return dstColumnName;
    }

    public String getSrcColumnName() {
        return srcColumnName;
    }

    public void setDstColumnName(String columnName) {
        this.dstColumnName = columnName;
    }

    public void setSrcColumnName(String sourceColumnName) {
        this.srcColumnName = sourceColumnName;
    }

    @Override
    protected String getAdditionalColumns() {
        return srcColumnName + "," + dstColumnName;
    }

    @Override
    protected void computeChange(ResultSet rs) throws SQLException {
        Blob original = rs.getBlob(srcColumnName);
        if (original == null) {
            rs.updateString(dstColumnName, null); // which JSONUserType should handle
            return;
        }

        Object map = null;
        try (   InputStream binaryStream = original.getBinaryStream();
                ObjectInputStream ois = new ObjectInputStream(binaryStream) ) {
            map = ois.readObject();
            String result = mapper.writeValueAsString(map);

            // Note: it might be nice to replace {} with null at this point, but this isn't what JSONUserType
            // deals with, and it's an easy thing to fix later.

            rs.updateString(dstColumnName, result);
        } catch (ClassNotFoundException | IOException e) {
            e.printStackTrace();
        }
    }
}
