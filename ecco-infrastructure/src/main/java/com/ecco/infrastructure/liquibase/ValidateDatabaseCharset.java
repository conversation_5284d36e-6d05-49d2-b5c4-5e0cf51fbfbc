package com.ecco.infrastructure.liquibase;

import liquibase.database.Database;
import liquibase.database.core.MySQLDatabase;
import liquibase.exception.CustomPreconditionErrorException;
import liquibase.exception.CustomPreconditionFailedException;
import liquibase.exception.DatabaseException;
import liquibase.executor.ExecutorService;
import liquibase.precondition.CustomPrecondition;
import liquibase.statement.core.RawSqlStatement;

public class ValidateDatabaseCharset implements CustomPrecondition {

    private String charset;
    private String collation;

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public void setCollation(String collation) {
        this.collation = collation;
    }

    @Override
    public void check(Database database) throws CustomPreconditionFailedException, CustomPreconditionErrorException {

        if (database instanceof MySQLDatabase) {
            String defaultCatalogName = database.getDefaultCatalogName();

            String sql = String.format("select DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME " +
                            "from information_schema.SCHEMATA " +
                            "where SCHEMA_NAME = '%s'",
                    defaultCatalogName);

            RawSqlStatement statement = new RawSqlStatement(sql);

            try {
                var results = ExecutorService.getInstance().getExecutor(database).queryForList(statement);
                var databaseCharset = results.get(0).get("DEFAULT_CHARACTER_SET_NAME");
                if (!databaseCharset.equals(charset)) {
                    throw new CustomPreconditionFailedException("Database "
                            + defaultCatalogName + " has default charset " + databaseCharset + " but expecting "
                            + charset);
                }

                var databaseCollation = results.get(0).get("DEFAULT_COLLATION_NAME");
                if (!databaseCollation.equals(collation)) {
                    throw new CustomPreconditionFailedException("Database "
                            + defaultCatalogName + " has default collation " + databaseCollation + " but expecting "
                            + collation);
                }
            } catch (DatabaseException e) {
                throw new CustomPreconditionErrorException("Failed to execute SQL:", e);
            }
        }
        else {
            throw new CustomPreconditionErrorException("Unsupported database for " + getClass().getName()
                    + " precondition: " + database.getDatabaseProductName() + ", mysql and mariadb are only supported");
        }
    }
}