package com.ecco.infrastructure.rest;

import com.ecco.infrastructure.config.web.ConvertersConfig;
import org.apache.http.client.protocol.HttpClientContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.function.Supplier;

/**
 * Copy-paste from http://stackoverflow.com/a/11688712/1998186
 */
public class RestClient {
    private final String applicationUrl;
    public static String apiPath = "api";
    private String loginPath = "nav/secure/j_acegi_security_check";
    private String logoutPath = "nav/secure/logout.html";
    private final String usernameInputFieldName = "j_username";
    private final String passwordInputFieldName = "j_password";
    private RestTemplate template;

    private String username;

    public RestClient(String baseUrl) {
        applicationUrl = baseUrl;
        this.template = new StatefulRestTemplate();
    }

    /**
     * DEBUGIP:
     * Determine if we're using a stale IP address (JVM, Azure?) - because we can't know unless we log it somewhere
     * except to know is actually quite difficult and requires getting the underlying connection.
     * - https://forums.oracle.com/ords/apexds/post/getting-the-ip-address-of-an-httpurlconnection-2224
     * - https://stackoverflow.com/questions/47441172/java-inspect-an-open-url-connection-and-obtain-ip-address
     *
     * Other resources:
     // Source code available at https://github.com/spring-projects/spring-framework/tree/v5.3.30/spring-web/src/main/java/org/springframework/http/client
     // https://stackoverflow.com/questions/2108072/how-do-i-get-the-remote-ip-address-with-httpurlconnection
     //      x since the JVM is caching DNS lookups, you can use InetAddress.getByName(serverName) to find the actual IP address being used unless you've set the system property "networkaddress.cache.ttl" to disable the cache.
     // https://www.baeldung.com/spring-resttemplate-logging
     //     logging.level.org.apache.http=DEBUG
     //     logging.level.org.springframework.web.client.RestTemplate=DEBUG
     // https://stackoverflow.com/questions/7952154/spring-resttemplate-how-to-enable-full-debugging-logging-of-requests-responses
     // https://github.com/markhobson/spring-rest-template-logger
     //     or networkaddress.cache.ttl
     //     or some "Connection: close" header - https://github.com/spring-projects/spring-boot/issues/19376
     // https://medium.com/@avocadi/dont-overlook-this-critical-spring-configuration-understanding-connectionrequesttimeout-could-22d0324df09b
     // https://dev.to/akdevcraft/never-use-spring-resttemplate-default-implementation-2ghj
     // this isn't using a connection pool - https://stackoverflow.com/a/44189539
     */
    public RestClient(String baseUrl, boolean debugIP) {
        applicationUrl = baseUrl;
        if (debugIP) {
            // NB we don't use any benefits of StatefulRestTemplate, its RestClient that has configureMessageConverters
            // NB StatefulRestTemplate uses HttpComponentsClientHttpRequestFactory, but we struggle to get access
            // to see the underlying connection, so we just use SimpleClientHttpRequestFactory for requests wanting debug
            // and it also removes a potential issue - using StatefulRestTemplate in the first place.

            // The debug code indicates that .connect() is required first, and in MailGunService exchange this occurs
            // in restTemplate.doExecute which calls its createRequest to 'execute' on it (org.springframework.http.client.ClientHttpRequest)
            // For a SimpleClientHttpRequestFactory, its createRequest is either SimpleBufferingClientHttpRequest or SimpleStreamingClientHttpRequest.
            // However, both of them have a property called 'connection', and it does appear that execute itself doesn't do a lot else,
            // so we could inspect the connection after execute.

            // Other options could be to:
            //  - log at the exception, which is in RestTemplate.doExecute
            //  - use the response object from 'response = request.execute()'
            //  - provide an alternative URLConnection, but that isn't an interface which therefore risks lasing functionality
            //  - use a proxy solution to observe the IP traffic - https://www.telerik.com/fiddler
            //  - seen some uses of getInterceptors().add(...)
            //  - RestTemplate seems to be using HttpURLConnection which means sun.net.www.protocol.http.HttpURLConnection.level=FINE could be used - https://confluence.atlassian.com/jirakb/how-to-enable-debug-logging-for-outgoing-http-connections-in-jira-952073102.html

            this.template = new RestTemplate();
            var factory = new SimpleClientHttpRequestFactoryIPLogger();
            this.template.setRequestFactory(factory);
        } else {
            this.template = new StatefulRestTemplate();
        }
    }

    public Supplier<String> getLastLoggedInUsernameSupplier()  {
        return () -> this.username;
    }

    /**
     * This method logs into a service by doing an standard http using the
     * configuration in this class.
     *
     * @param username
     *            the username to log into the application with
     * @param password
     *            the password to log into the application with
     *
     * @return the url that the login redirects to
     */
    public String login(String username, String password) {
        this.username = username;
        MultiValueMap<String, String> form = new LinkedMultiValueMap<>(); // We might want to add headers using
        var headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        var request = new HttpEntity<>(form, headers);
        form.add(usernameInputFieldName, username);
        form.add(passwordInputFieldName, password);
        var entity = this.template.postForEntity(loginUrl(), request, String.class);
        var location = entity.getHeaders().getLocation();
        Assert.notNull(location, () -> "Are you sure you're running ecco at the right path?  Attempting to login at: " + loginUrl());
        if (location.getQuery() != null && location.getQuery().contains("login_error")) {
            throw new BadCredentialsException("*** ERROR *** login failed for user: " + username
                    + " with password: " + password); // Are you sure you're running against clean H2 instance?
        }
        return location.toString();
    }

    /**
     * Logout by doing an http get on the logout url
     *
     * @return result of the get as ResponseEntity
     */
    public ResponseEntity<String> logout() {
        final ResponseEntity<String> entity = this.template.getForEntity(logoutUrl(), String.class); // TODO: Shouldn't this be POST?
        // Clear the redirect locations after logging out, otherwise they persist in the context indefinitely and the
        // next time you logout it will be treated as a circular redirect. Really this should be cleared at the end of
        // each and every request.
        ((StatefulRestTemplate) this.template).getHttpContext().removeAttribute(HttpClientContext.REDIRECT_LOCATIONS);
        return entity;
    }

    public String applicationUrl(String relativePath) {
        return applicationUrl + "/" + checkNotNull(relativePath);
    }

    @SuppressWarnings("deprecation")
    private String checkNotNull(String str) {
        Assert.notNull(str);
        return str;
    }

    public String apiUrl(String relativePath) {
        return applicationUrl(apiPath + "/" + checkNotNull(relativePath));
    }

    public RestTemplate template() {
        template.getMessageConverters().clear();
        ConvertersConfig.addWebApiConvertersTo(template.getMessageConverters());
        template.setErrorHandler(new ThrowOnlyOn400And5xxErrorHandler());
        return template;
    }

    /**
     * For login/logout, requires {@link StatefulRestTemplate}
     */
    public void setTemplate(RestTemplate template) {
        this.template = template;
    }

    public String loginUrl() {
        return applicationUrl(loginPath);
    }

    public String logoutUrl() {
        return applicationUrl(logoutPath);
    }

    public String apiUrl() {
        return applicationUrl(apiPath);
    }

    public void setLogoutPath(String logoutPath) {
        this.logoutPath = logoutPath;
    }

    public String getApiPath() {
        return apiPath;
    }

    public void setApiPath(String apiPath) {
        this.apiPath = apiPath;
    }

    public String getLoginPath() {
        return loginPath;
    }

    public void setLoginPath(String loginPath) {
        this.loginPath = loginPath;
    }

    public String getLogoutPath() {
        return logoutPath;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("RestClient [\n loginUrl()=");
        builder.append(loginUrl());
        builder.append(", \n logoutUrl()=");
        builder.append(logoutUrl());
        builder.append(", \n apiUrl()=");
        builder.append(apiUrl());
        builder.append("\n]");
        return builder.toString();
    }
}
