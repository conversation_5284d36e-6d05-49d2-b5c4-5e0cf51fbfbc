package com.ecco.infrastructure.rest;

import lombok.Getter;
import org.apache.http.client.CookieStore;
import org.apache.http.client.HttpClient;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.protocol.HttpContext;
import org.springframework.web.client.RestTemplate;

/**
 * Initially copy-paste from http://stackoverflow.com/a/11688712/1998186
 */
public class StatefulRestTemplate extends RestTemplate {
    @Getter
    private final HttpClient httpClient;
    @Getter
    private final CookieStore cookieStore;
    @Getter
    private final HttpContext httpContext;
    private final StatefullHttpComponentsClientHttpRequestFactory statefullHttpComponentsClientHttpRequestFactory;

    public StatefulRestTemplate() {
        super();
        httpClient = HttpClientBuilder.create().useSystemProperties().build(); // to proxy with https://github.com/HotelsDotCom/network-plugin use: -Dhttp.proxyHost=localhost -Dhttp.proxyPort=8999 -Dhttp.nonProxyHosts=
        cookieStore = new BasicCookieStore();
        httpContext = new BasicHttpContext();
        httpContext.setAttribute(HttpClientContext.COOKIE_STORE, getCookieStore());
        statefullHttpComponentsClientHttpRequestFactory = new StatefullHttpComponentsClientHttpRequestFactory(
                httpClient, httpContext);
        super.setRequestFactory(statefullHttpComponentsClientHttpRequestFactory);
        // Override the handling of URL behaviour so that we don't end up double encoding correctly output URLs
        // that we deal with from Spring HATEOAS, for example.
//        var defaultUriBuilderFactory = new DefaultUriBuilderFactory();
//        defaultUriBuilderFactory.setEncodingMode(EncodingMode.VALUES_ONLY);
//        setUriTemplateHandler(defaultUriBuilderFactory);
    }

    public StatefullHttpComponentsClientHttpRequestFactory getStatefulHttpClientRequestFactory() {
        return statefullHttpComponentsClientHttpRequestFactory;
    }
}