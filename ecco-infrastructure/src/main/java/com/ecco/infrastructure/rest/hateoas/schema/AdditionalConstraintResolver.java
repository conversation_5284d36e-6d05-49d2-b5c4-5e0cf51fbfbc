package com.ecco.infrastructure.rest.hateoas.schema;

import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.jsonFormatVisitors.JsonValueFormat;
import com.fasterxml.jackson.module.jsonSchema.validation.ValidationConstraintResolver;
import lombok.extern.slf4j.Slf4j;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.context.ApplicationContext;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionException;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.net.URI;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;

/**
 * Catches the stuff that {@link ValidationConstraintResolver} doesn't.
 * In particular, it doesn't distinguish between inclusive and exclusive numeric constraints.
 */
@Slf4j
class AdditionalConstraintResolver {

    private ArrayList<Consumer<StandardEvaluationContext>> contextConfigurers = new ArrayList<>(2);


    AdditionalConstraintResolver(ApplicationContext applicationContext) {

        // Add the default
        Consumer<StandardEvaluationContext> applyCurrentUser = context -> Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication())
                .map(Authentication::getPrincipal)
                .ifPresent(currentUser -> context.setVariable("currentUser", currentUser));
        contextConfigurers.add(applyCurrentUser);

        contextConfigurers.add( context -> context.setBeanResolver(new BeanFactoryResolver(applicationContext)));
    }

    public void addContextConfigurer(Consumer<StandardEvaluationContext> configurer) {
        contextConfigurers.add(configurer);
    }

    public Optional<Boolean> getNumberExclusiveMinimum(BeanProperty prop) {
        DecimalMin decimalMinAnnotation = prop.getAnnotation(DecimalMin.class);
        return Optional.ofNullable(decimalMinAnnotation).map(an -> !an.inclusive());
    }

    public Optional<Boolean> getNumberExclusiveMaximum(BeanProperty prop) {
        DecimalMax decimalMaxAnnotation = prop.getAnnotation(DecimalMax.class);
        return Optional.ofNullable(decimalMaxAnnotation).map(an -> !an.inclusive());
    }

    public Optional<JsonValueFormat> getValueFormat(final BeanProperty prop, SerializerProvider provider) {
        JsonSchemaProperty jsonSchemaAnnotation = prop.getAnnotation(JsonSchemaProperty.class);
        return Optional.ofNullable(jsonSchemaAnnotation)
                .map(an -> {
                    if (an.format().length > 0) {
                        return an.format()[0];
                    } else {
                        return null;
                    }
                });
    }

    public Optional<Boolean> getReadOnly(BeanProperty prop) {
        JsonSchemaProperty jsonSchemaAnnotation = prop.getAnnotation(JsonSchemaProperty.class);
        return Optional.ofNullable(jsonSchemaAnnotation).map(an -> an.readOnly() ? Boolean.TRUE : null);
    }

    public Optional<Boolean> getNotNull(BeanProperty prop) {
        NotNull notNullAnnotation = prop.getAnnotation(NotNull.class);
        return Optional.ofNullable(notNullAnnotation).map(n -> true);
    }

    public Optional<String> getPattern(BeanProperty prop) {
        JsonSchemaProperty jsonSchemaAnnotation = prop.getAnnotation(JsonSchemaProperty.class);
        return Optional.ofNullable(jsonSchemaAnnotation).map(JsonSchemaProperty::pattern).filter(p -> !p.isEmpty());
    }

    public Optional<Map<String, String>> getEnumConstants(BeanProperty prop) {
        JsonSchemaProperty jsonSchemaAnnotation = prop.getAnnotation(JsonSchemaProperty.class);
        return Optional.ofNullable(jsonSchemaAnnotation)
                .map(an -> {
                    return schemaAsMap(an);
                });
    }

	private Map<String, String> schemaAsMap(JsonSchemaProperty an) {
		if (an.enumValues().length > 0) {
		    return Arrays.stream(an.enumValues())
		            .collect(Collectors.toMap(EnumConstant::value,
		                    ev -> (ev.title().isEmpty()? ev.value() : ev.title()),
		                    (a,b) -> a, LinkedHashMap::new));
		}
		else if (!an.enumExpression().isEmpty()) {
            final Expression expression = new SpelExpressionParser().parseExpression(an.enumExpression(), new TemplateParserContext());

            StandardEvaluationContext evalContext = buildEvaluationContext();
            Object map = expression.getValue(evalContext);
            return (Map<String, String>) map;
        }
        else {
		    return null;
		}
	}

    public Optional<URI> getEnumRef(BeanProperty prop) {
        JsonSchemaProperty jsonSchemaAnnotation = prop.getAnnotation(JsonSchemaProperty.class);
        return Optional.ofNullable(jsonSchemaAnnotation)
                .map(an -> {
                    if (!an.enumRef().isEmpty()) {
                        try {
                            return getUri(an);
                        } catch (ExpressionException e) {
                            log.warn("Ignoring unprocessable enumRef expression: " + an.enumRef(), e);
                        }
                    }
                    return null;
                });
    }

    @Nullable
    URI getUri(JsonSchemaProperty an) {
        final Expression expression = new SpelExpressionParser().parseExpression(an.enumRef(), new TemplateParserContext());

        StandardEvaluationContext evalContext = buildEvaluationContext();
        String value = expression.getValue(evalContext, String.class);
        if (value != null) {
            return URI.create(value);
        } else {
            return null;
        }
    }

    @NonNull
    StandardEvaluationContext buildEvaluationContext() {
        StandardEvaluationContext evalContext = new StandardEvaluationContext();
        contextConfigurers.forEach(configurer -> configurer.accept(evalContext));
        return evalContext;
    }
}
