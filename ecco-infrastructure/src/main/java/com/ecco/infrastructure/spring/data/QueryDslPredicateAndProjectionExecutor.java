package com.ecco.infrastructure.spring.data;

import com.querydsl.core.types.Expression;
import com.querydsl.core.types.FactoryExpression;
import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.NoRepositoryBean;

import java.io.Serializable;
import java.util.List;

/**
 * Need to be enabled using
 * @EnableJpaRepositories(
 *      repositoryFactoryBeanClass=CustomQueryDslJpaRepositoryFactoryBean.class)

 * @see http://stackoverflow.com/questions/18300465/spring-data-jpa-and-querydsl-to-fetch-subset-of-columns-using-bean-constructor-p
 */
@NoRepositoryBean
public interface QueryDslPredicateAndProjectionExecutor<T, ID extends Serializable>
        extends JpaRepository<T, ID>, QuerydslPredicateExecutor<T> {


    <PROJ> List<PROJ> findAllWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate);

    /**
     * Returns a {@link org.springframework.data.domain.Page} of entities matching the given {@link com.querydsl.core.types.Predicate}.
     * This also uses provided projections ( can be JavaBean or constructor or anything supported by QueryDSL
     * @param factoryExpression this constructor expression will be used for transforming query results
     */
    <PROJ> Page<PROJ> findAllWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate, Pageable pageable);

    <PROJ, T> List<PROJ> findAllWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate,
                                            QueryModifier<T> queryModifier);

    <PROJ, T> Page<PROJ> findAllWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate, Pageable pageable,
                                            QueryModifier<T> queryModifier);

    <PROJ> Page<PROJ> findAllWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate, Pageable pageable,
            Expression<?>... groupBy);

    /**
     * Returns the entity matching the given {@link com.querydsl.core.types.Predicate}.
     * This also uses provided projections ( can be JavaBean or constructor or anything supported by QueryDSL
     * @param factoryExpression this constructor expression will be used for transforming query results
     */
    <PROJ> PROJ findOneWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate);
//    <PROJ> PROJ findOneWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate, JpaEntityGraph entityGraph);

}