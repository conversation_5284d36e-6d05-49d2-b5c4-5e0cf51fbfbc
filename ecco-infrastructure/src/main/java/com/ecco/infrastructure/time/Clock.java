package com.ecco.infrastructure.time;

import java.io.Serializable;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

/**
 * Allows an object (replaceable for testing say) that accesses system time.
 */
public interface Clock extends Serializable {

    Clock DEFAULT = new Clock() {
        @Override public DateTime now() { return new DateTime(DateTimeZone.UTC); }
        @Override public ZonedDateTime nowJdk() { return ZonedDateTime.now(ZoneOffset.UTC); }

        @Override public DateTime nowWithoutMillies() { return new DateTime(DateTimeZone.UTC).withMillisOfSecond(0); }
        @Override public ZonedDateTime nowJdkWithoutMillies() { return ZonedDateTime.now(ZoneOffset.UTC).withNano(0); }
    };

    @Deprecated
    DateTime now();

    ZonedDateTime nowJdk();

    @Deprecated
    DateTime nowWithoutMillies();

    ZonedDateTime nowJdkWithoutMillies();

}
