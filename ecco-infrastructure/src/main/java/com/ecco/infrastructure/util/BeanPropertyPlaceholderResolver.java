package com.ecco.infrastructure.util;

import org.springframework.beans.BeanWrapper;
import org.springframework.beans.PropertyAccessorFactory;
import org.springframework.util.PropertyPlaceholderHelper.PlaceholderResolver;

import org.jspecify.annotations.NonNull;

public final class BeanPropertyPlaceholderResolver implements PlaceholderResolver {

    private final BeanWrapper beanWrapper;

    public BeanPropertyPlaceholderResolver(Object instance) {
        this.beanWrapper = PropertyAccessorFactory.forBeanPropertyAccess(instance);
    }

    @Override
    public String resolvePlaceholder(@NonNull String placeholderName) {
        Object propertyValue = beanWrapper.getPropertyValue(placeholderName);
        return propertyValue == null ? "" : propertyValue.toString();
    }
}
