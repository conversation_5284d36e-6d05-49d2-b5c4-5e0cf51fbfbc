package liquibase.ext;

import liquibase.database.Database;
import liquibase.database.core.DerbyDatabase;
import liquibase.database.core.H2Database;
import liquibase.database.core.HsqlDatabase;
import liquibase.database.core.MSSQLDatabase;
import liquibase.datatype.DataTypeFactory;
import liquibase.exception.ValidationErrors;
import liquibase.sql.Sql;
import liquibase.sql.UnparsedSql;
import liquibase.sqlgenerator.SqlGeneratorChain;
import liquibase.sqlgenerator.core.AbstractSqlGenerator;
import liquibase.structure.core.Column;
import liquibase.structure.core.Table;

public class DropAutoIncrementGenerator extends AbstractSqlGenerator<DropAutoIncrementStatement> {

    public DropAutoIncrementGenerator() {
    }

    @Override
    public int getPriority() {
        return PRIORITY_DEFAULT;
    }

    @Override
    public boolean supports(DropAutoIncrementStatement statement, Database database) {
        return (database.supportsAutoIncrement()
                && !(database instanceof DerbyDatabase)
                && !(database instanceof MSSQLDatabase)
                && !(database instanceof HsqlDatabase)
                && !(database instanceof H2Database));
    }

    @Override
    public ValidationErrors validate(DropAutoIncrementStatement statement, Database database, SqlGeneratorChain sqlGeneratorChain) {
        ValidationErrors validationErrors = new ValidationErrors();

        validationErrors.checkRequiredField("columnName", statement.getColumnName());
        validationErrors.checkRequiredField("tableName", statement.getTableName());

        return validationErrors;
    }

    @Override
    public Sql[] generateSql(DropAutoIncrementStatement statement, Database database, SqlGeneratorChain sqlGeneratorChain) {
        String sql = "ALTER TABLE "
                + database.escapeTableName(null, statement.getSchemaName(), statement.getTableName())
                + " MODIFY "
                + database.escapeColumnName(null, statement.getSchemaName(), statement.getTableName(), statement.getColumnName())
                + " "
                + DataTypeFactory.getInstance().fromDescription(statement.getColumnDataType(), database).toDatabaseDataType(database);

        // Modify column with specifying auto increment takes it off (at least in MySQL).

        return new Sql[]{
                new UnparsedSql(sql,
                        new Column()
                            .setRelation(new Table()
                                .setName(statement.getTableName())
                                .setSchema(statement.getSchemaName(), statement.getSchemaName()))
                            .setName(statement.getColumnName()))
        };
    }
}
