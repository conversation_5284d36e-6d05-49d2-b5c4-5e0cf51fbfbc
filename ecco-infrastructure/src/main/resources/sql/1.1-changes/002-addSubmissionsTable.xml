<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- v3 corrected the LONGTEXT to CLOB on responseRaw -->
    <changeSet author="adamjhamer" id="ECCO-90-1-v3">
        <preConditions onFail="MARK_RAN">
        <not>
            <or>
            <!-- If we ran v2, then we were successful, so ignore this change -->
            <changeSetExecuted id="ECCO-90-1-v2" author="adamjhamer" changeLogFile="classpath:sql/1.1-changes/002-addSubmissionsTable.xml"/>
            <!-- If we ran v1, then we will run ECCO-90-2 later, which will end up with the same result -->
            <changeSetExecuted id="ECCO-90-1" author="adamjhamer" changeLogFile="classpath:sql/1.1-changes/002-addSubmissionsTable.xml"/>
            </or>
        </not>
        </preConditions>
        <createTable tableName="submissions">
            <column name="discriminator_orm" type="VARCHAR(31)">
                <constraints nullable="false"/>
            </column>
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="responseRaw" type="CLOB"/>
            <column name="responseStatus" type="VARCHAR(255)"/>
            <column name="submitted" type="DATETIME"/>
            <column name="bytesId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="referralId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

</databaseChangeLog>
