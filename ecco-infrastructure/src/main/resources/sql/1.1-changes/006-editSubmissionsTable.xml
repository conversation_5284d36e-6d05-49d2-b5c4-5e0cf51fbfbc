<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="ECCO-90-2" author="adamjhamer" onValidationFail="MARK_RAN">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="ECCO-90-1" author="adamjhamer" changeLogFile="classpath:sql/1.1-changes/002-addSubmissionsTable.xml"/>
        </preConditions>
        <customChange class="com.ecco.infrastructure.liquibase.DropAutoIncrementChange" columnName="id" columnDataType="BIGINT" tableName="submissions"/>
    </changeSet>

</databaseChangeLog>
