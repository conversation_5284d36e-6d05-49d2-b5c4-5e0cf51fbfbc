<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- create 'senior manager' -->
    <!-- we are creating here also for tests - since we are 1.1-changes -->
    <changeSet author="adamjhamer" id="ECCO-270-1-v1">
        <validCheckSum>8:e6a81bbcfd02147cdc03da29c46e86ca</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <!-- if we ran original then we were successful so ignore this changeset -->
            <!-- the reason this changeset was modified is to cater for the change in sqlCheck expression for oracle -->
            <not>
                <changeSetExecuted id="ECCO-270-1" author="adamjhamer" changeLogFile="classpath:sql/1.1-changes/014-addSeniorManager.xml"/>
            </not>
            <!-- and 'senior manager' doesn't exist -->
            <!-- Removed preCondition for migration of old sites where we may already have added this groupId manually -->
        </preConditions>

        <insert tableName="groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="11"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="senior manager"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/>
            <column name="authority" value="ROLE_ADMIN"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/>
            <column name="authority" value="ROLE_ADMINREFERRAL"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/>
            <column name="authority" value="ROLE_EDITREFERRAL"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/>
            <column name="authority" value="ROLE_HR"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/>
            <column name="authority" value="ROLE_INTERVIEWER"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/>
            <column name="authority" value="ROLE_LOGIN"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/>
            <column name="authority" value="ROLE_STAFF"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/>
            <column name="authority" value="ROLE_USER"/>
        </insert>

    </changeSet>


    <!-- reduce 'manager' role (and rename from 'management') -->
    <changeSet author="adamjhamer" id="ECCO-270-2-v1">
        <validCheckSum>8:8f44dbaaf21ffcad748d003774af704f</validCheckSum>
        <!-- its easier with 'ids' so lets enforce that -->
        <preConditions onFail="MARK_RAN">
            <!-- if we ran original then we were successful so ignore this changeset -->
            <!-- the reason this changeset was modified is to cater for the change in sqlCheck expression for oracle -->
            <not>
                <changeSetExecuted id="ECCO-270-2" author="adamjhamer" changeLogFile="classpath:sql/1.1-changes/014-addSeniorManager.xml"/>
            </not>
        </preConditions>
        <!-- Removed preCondition for migration of old sites where we may already have added this groupId manually -->
        <update tableName="groups">
            <column name="group_name" value="manager"/>
            <where>id=1</where>
        </update>
        <delete tableName="group_authorities">
            <where>group_id=1 and authority='ROLE_ADMIN'</where>
        </delete>
        <delete tableName="group_authorities">
            <where>group_id=1 and authority='ROLE_HR'</where>
        </delete>
    </changeSet>

    <!-- sysadmin wants to be upgraded to senior manager from manager -->
    <changeSet author="adamjhamer" id="ECCO-270-5">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="group_members" columnName="username"/>
        </preConditions>
        <update tableName="group_members">
            <column name="group_id" valueNumeric="11"/>
            <where>group_id=1 and username='sysadmin'</where>
        </update>
    </changeSet>

    <changeSet author="bodeng" id="ECCO-270-5-v2">
        <preConditions>
            <not>
                <or>
                    <columnExists tableName="group_members" columnName="username"/>
                    <changeSetExecuted id="ECCO-270-5" author="bodeng" changeLogFile="classpath:sql/1.1-changes/014-addSeniorManager.xml"/>
                </or>
            </not>
        </preConditions>
        <update tableName="group_members">
            <column name="group_id" valueNumeric="11"/>
            <where>group_id=1 and member_id=1</where>
        </update>
    </changeSet>

</databaseChangeLog>
