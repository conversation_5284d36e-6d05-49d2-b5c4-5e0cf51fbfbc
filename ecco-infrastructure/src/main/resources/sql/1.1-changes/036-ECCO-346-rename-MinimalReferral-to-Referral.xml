<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet author="nealeu" id="ECCO-346-rename-in-act-ru-variable">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="ACT_RU_VARIABLE"/>
        </preConditions>
        <update tableName="ACT_RU_VARIABLE">
        <column name="TEXT_">com.ecco.dom.Referral</column>
        <where>TEXT_ = 'com.ecco.dom.MinimalReferral'</where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="ECCO-346-rename-in-act-hi-varinst">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="ACT_HI_VARINST"/>
        </preConditions>
        <update tableName="ACT_HI_VARINST">
        <column name="TEXT_">com.ecco.dom.Referral</column>
        <where>TEXT_ = 'com.ecco.dom.MinimalReferral'</where>
        </update>
    </changeSet>

</databaseChangeLog>
