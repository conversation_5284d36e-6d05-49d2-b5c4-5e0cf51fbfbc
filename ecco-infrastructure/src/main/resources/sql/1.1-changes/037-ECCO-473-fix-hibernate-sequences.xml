<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet author="nealeu" id="ECCO-473-set-default-to-max-sequence">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
        </preConditions>
        <sql>
            UPDATE hibernate_sequences AS h1
            INNER JOIN
                (SELECT max(h2.next_val) max FROM hibernate_sequences h2) AS t2
            SET h1.next_val = t2.max
            WHERE h1.sequence_name = 'default';
        </sql>
    </changeSet>

</databaseChangeLog>
