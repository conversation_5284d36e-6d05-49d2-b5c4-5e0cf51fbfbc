<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="ECCO-509-fundingSources" author="adamjhamer">
        <createTable tableName="fundingsources">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BIT">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet id="ECCO-509-referral" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="fundingSourceId2" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-509-constraint" author="adamjhamer">
        <addForeignKeyConstraint baseColumnNames="fundingSourceId2" baseTableName="referrals" constraintName="FKC8E0F87679BDDB74" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="fundingsources" referencesUniqueColumn="false"/>
    </changeSet>

    <!-- the above has already been run on some live sites, so we don't touch it -->
    <!-- but now on main we need to migrate existing sites to the new way otherwise their funding will appear blank -->
    <!-- then we can remove the now old columns -->
    <!-- we restrict to mysql since oracle isn't using this feature, so requires no migration -->

    <!-- SQL process to help verify
    - BEFORE
    - check migrate - if 0, carry on
    select count(1) from fundingsources;
    - list SELFFUND
    select id from referrals where selfFunding=1;
    - list AGENCYs
    select c.* from referrals r inner join contacts c on c.id=r.fundingsourceId;

    - RUN CODE

    - AFTER
    - check SELFFUND has same rids as initial run
    select id from referrals where fundingsourceId2=1;
    - check AGENCYs count is the same as initial run, and the ids are as before but +1, and names match up
    select f.*,r.id,r.fundingsourceId2,r.acceptedfunding from referrals r inner join fundingsources f on f.id=r.fundingsourceId2;
    -->

    <!-- migrate self funding -->
    <changeSet author="adamjhamer" id="migrate_fundingSource_selfReferral">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
            <!-- only allow to create 'self' fundingsource if nothing exists already (so its unused) -->
            <sqlCheck expectedResult="0">select count(1) from fundingsources</sqlCheck>
        </preConditions>
        <insert tableName="fundingsources">
            <column name="id" valueNumeric="1"/>
            <!-- use -1 version so we know later if it was this code that set it -->
            <column name="version" valueNumeric="-1"/>
            <column name="disabled" valueBoolean="false"/>
            <column name="name" value="self"/>
        </insert>
        <!-- migrate the selfFunding to the new list item 'self' -->
        <update tableName="referrals">
            <column name="fundingsourceId2" valueNumeric="1"/>
            <where>selfFunding=1</where>
        </update>
    </changeSet>

    <!-- migrate agencies -->
    <changeSet author="adamjhamer" id="migrate_fundingSource_agencies">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
            <!-- don't migrate if we have started using the new fundingsources -->
            <!-- which we can only tell if we test the -1 version property -->
            <sqlCheck expectedResult="0">select count(1) from referrals r inner join fundingsources fs on r.fundingsourceId2=fs.id where fs.version>-1</sqlCheck>
        </preConditions>
        <!-- migrate the ids, but safeguard the id 1 above by increasing all the ids by 1 -->
        <!-- multi-line sql had troubles and didn't appear in debug on logging, so simply split them -->
        <sql>
            INSERT INTO fundingsources (id, version, disabled, name) SELECT DISTINCT c.id+1, 0, 0, c.companyname FROM referrals r INNER JOIN contacts c ON r.fundingsourceId=c.id;
        </sql>
        <sql>
            UPDATE referrals set fundingsourceId2=fundingsourceId+1 where fundingsourceId is not null;
        </sql>
    </changeSet>

    <!-- remove the FK to the contacts table - fundingSources used to use 'agencies' -->
    <changeSet author="adamjhamer" id="remove-1366042567388-275">
        <preConditions>
            <foreignKeyConstraintExists foreignKeyName="FKC8E0F8764AA5E33B"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="referrals" constraintName="FKC8E0F8764AA5E33B"/>
    </changeSet>

    <!-- remove the old fundingsource column -->
    <changeSet author="adamjhamer" id="remove-old-fundingsourceId">
        <dropColumn tableName="referrals" columnName="fundingsourceId"/>
    </changeSet>

    <!-- drop selfFunding default value for MSSQL before we drop the column (mssql creates a constraint)-->
    <changeSet author="adamjhamer" id="remove-old-selfFunding-default">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql"/>
        </preConditions>
        <dropDefaultValue tableName="referrals" columnName="selfFunding"/>
    </changeSet>

    <!-- drop selfFunding column -->
    <changeSet author="adamjhamer" id="remove-old-selfFunding">
        <dropColumn tableName="referrals" columnName="selfFunding"/>
    </changeSet>

</databaseChangeLog>
