<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="ECCO-148-dataProtectionSignatures" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="dataprotectionsignatureId" type="BIGINT"/>
        </addColumn>
        <addColumn tableName="referrals">
            <column name="dataprotectionsigned" type="DATETIME"/>
        </addColumn>
        <addColumn tableName="referrals">
            <column name="consentsignatureId" type="BIGINT"/>
        </addColumn>
        <addColumn tableName="referrals">
            <column name="consentsigned" type="DATETIME"/>
        </addColumn>
    </changeSet>

    <!-- migrate those who have got past the older data protection/consent which was simply assumed -->
    <changeSet id="ECCO-148-markAsSigned-dataprotection" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
        </preConditions>
        <sql>
        update referrals r
        inner join services s
        on r.referredServiceId=s.id
        inner join servicetypes_referralaspects stra
        on s.servicetypeId=stra.servicetypeId
        set r.dataprotectionsigned=r.created
        where stra.referralaspectId in (63)
        and r.receivedDate is not null;
        </sql>
    </changeSet>
    <changeSet id="ECCO-148-markAsSigned-consent" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
        </preConditions>
        <sql>
        update referrals r
        inner join services s
        on r.referredServiceId=s.id
        inner join servicetypes_referralaspects stra
        on s.servicetypeId=stra.servicetypeId
        set r.consentsigned=r.created
        where stra.referralaspectId in (73)
        and r.receivedDate is not null;
        </sql>
    </changeSet>
    <!-- this oracle update does the same, but updates a lot more records - but sets null where the inner joins don't match -->
    <changeSet id="ECCO-148-markAsSigned-dataProtection-oracle" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <sql>
        update referrals r
        set r.dataprotectionsigned=(select r2.created
            from referrals r2
            inner join services s
            on r2.referredServiceId=s.id
            inner join servicetypes_referralaspects stra
            on s.servicetypeId=stra.servicetypeId
            where stra.referralaspectId in (63)
            and r2.receivedDate is not null
            and r.id=r2.id)
        where r.receivedDate is not null
        </sql>
    </changeSet>
    <changeSet id="ECCO-148-consent-oracle" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <sql>
        update referrals r
        set r.consentsigned=(select r2.created
            from referrals r2
            inner join services s
            on r2.referredServiceId=s.id
            inner join servicetypes_referralaspects stra
            on s.servicetypeId=stra.servicetypeId
            where stra.referralaspectId in (73)
            and r2.receivedDate is not null
            and r.id=r2.id)
        where r.receivedDate is not null
        </sql>
    </changeSet>

    <!-- move data protection/consent pages after a referral/client has been created -->
    <!-- if we have signatures before the client is created the client doesn't exist to get their contactId -->
    <!-- and if we simply 'save' the page to accept the referral is saved without the client causing problems after the client page -->
    <!-- therefore we need to move the signature pages after 'referralView' aspect which is the referral home page -->
    <changeSet id="ECCO-148-ensure-order-mysql" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
        </preConditions>
        <sql>
        update servicetypes_referralaspects a
        left join servicetypes_referralaspects b
        on b.servicetypeId=a.servicetypeId
        set a.orderby=b.orderby+1
        where b.referralaspectId=18 and a.referralaspectId in (63, 73);
        </sql>
    </changeSet>
    <changeSet id="ECCO-148-ensure-order-oracle" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <sql>
        UPDATE servicetypes_referralaspects t1
           SET orderby = (SELECT t2.orderby+1
                                 FROM servicetypes_referralaspects t2
                                WHERE t1.servicetypeid = t2.servicetypeid
                                AND t2.referralaspectId=18
                         )
        WHERE t1.referralaspectId in (63,73);
        </sql>
    </changeSet>

</databaseChangeLog>
