<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="ECCO-557-groupsupport" author="adamjhamer">
        <update tableName="menuitem">
            <column name="url" value="/dynamic/secure/entities/activities/get.html"/>
            <where>id=5</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-557-communitysupport-report" author="adamjhamer">
        <update tableName="menuitem">
            <column name="linkText" value="menu.linktext.community_support"/>
            <where>id=21</where>
        </update>
    </changeSet>


    <!-- a new menuitem hidden by default with model name 'dummy' -->
    <changeSet id="ECCO-557-communitysupport-softwaremodule" author="adamjhamer">
        <insert tableName="softwaremodule">
            <column name="name" value="community"/>
            <column name="enabled" valueNumeric="0"/>
        </insert>
    </changeSet>
    <changeSet id="ECCO-557-communitysupport-hide" author="adamjhamer">
        <insert tableName="menuitem">
            <column name="id" valueNumeric="35"/>
            <column name="imageUrl" value="/icons/crystal/users/png/48.png"/>
            <column name="linkText" value="menu.linktext.community_support"/>
            <column name="roles" value="ROLE_STAFF"/>
            <column name="url" value="/dynamic/secure/menuActivities.html"/>
            <column name="module_name" value="community"/>
        </insert>
    </changeSet>
    <changeSet id="ECCO-557-communitysupport-welcome" author="adamjhamer">
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome"/>
            <column name="menuItems_id" valueNumeric="35"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-557-communitysupport-report-softwaremodule" author="adamjhamer">
        <update tableName="menuitem">
            <column name="module_name" value="community"/>
            <where>id=21</where>
        </update>
    </changeSet>

    <!-- BASEDATA change wants to show it -->
    <changeSet id="ECCO-557-communitysupport-show" author="adamjhamer" context="1.1-base-data">
        <update tableName="softwaremodule">
            <column name="enabled" valueNumeric="1"/>
            <where>name='community'</where>
        </update>
    </changeSet>

</databaseChangeLog>
