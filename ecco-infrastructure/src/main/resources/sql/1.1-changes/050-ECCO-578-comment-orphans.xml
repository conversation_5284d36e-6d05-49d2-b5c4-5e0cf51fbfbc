<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- clear orphaned comments -->
    <changeSet id="ECCO-578-remove-exitedComments" author="adamjhamer">
        <delete tableName="referralcomments">
            <where>discriminator_orm='exit' and referralId is null</where>
        </delete>
    </changeSet>
    <changeSet id="ECCO-578-remove-signpostedComments" author="adamjhamer">
        <delete tableName="referralcomments">
            <where>discriminator_orm='signpost' and referralId is null</where>
        </delete>
    </changeSet>

</databaseChangeLog>
