<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="ECCO-656-relationship-referralaspect" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="98"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="false"/>
            <column name="isexternal" valueBoolean="true"/>
            <column name="friendlyName" value="relationshipToPrimary"/>
            <column name="internal" valueBoolean="true"/>
            <column name="name" value="relationshipToPrimary"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-656-relationshipToPrimaryReferral" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="relationshipToPrimary" type="VARCHAR(50)"/>
        </addColumn>
    </changeSet>

    <!-- hide the 'newMultipleReferral' from the list -->
    <changeSet id="ECCO-656-hide-newMultipleReferral" author="adamjhamer">
        <update tableName="referralaspects">
            <column name="displayOverview" valueBoolean="false"/>
            <where>id=78</where>
        </update>
    </changeSet>

</databaseChangeLog>
