<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="ECCO-832-financeBand" author="adamjhamer">
        <createTable tableName="financebands">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="disabled" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet id="ECCO-832-demandScheduleColumn" author="adamjhamer">
        <addColumn tableName="appointmentschedules">
            <column name="financeBandId" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-832-demandScheduleFK" author="adamjhamer">
        <addForeignKeyConstraint baseTableName="appointmentschedules" baseColumnNames="financeBandId" constraintName="fk_as_financeband" referencedTableName="financebands" referencedColumnNames="id"/>
    </changeSet>

</databaseChangeLog>
