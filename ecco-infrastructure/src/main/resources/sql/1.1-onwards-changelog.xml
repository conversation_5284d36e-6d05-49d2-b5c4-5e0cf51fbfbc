<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="oracle-varchar2-length-semantics" author="bodeng" dbms="oracle" runAlways="true">
        <comment>Oracle VARCHAR2 sizes default to BYTE, but we want to specify size in CHAR units.</comment>
        <sql>ALTER SESSION SET NLS_LENGTH_SEMANTICS = 'CHAR';</sql>
    </changeSet>

    <include file="classpath:sql/test-schema-changelog.xml" context="test-schema" />

    <!-- 1.1 - Current baseline -->
    <!-- Per bounded domain changelogs.  Should support int tests, and work for full webapp for both 1.0 and 1.1 -->
    <include file="classpath:sql/2020-and-earlier-from-1.1/configDomainChangeLog.xml"/>

    <include file="classpath:sql/2020-and-earlier-from-1.1/securityDomainChangeLog.xml"/>

    <include file="classpath:sql/2020-and-earlier-from-1.1/evidenceDomainChangeLog.xml"/>

    <include file="classpath:sql/2020-and-earlier-from-1.1/financeDomainChangeLog.xml"/>

    <include file="classpath:sql/2020-and-earlier-from-1.1/generalDomainChangeLog.xml"/>

    <include file="classpath:sql/2020-and-earlier-from-1.1/securityDomainChangeLogPost.xml"/>

    <include file="classpath:sql/data-reset.xml" context="data-reset,data-reset-evidence,data-reset-calendar"/> <!-- For context to clear referrals if context active on restart -->
    <include file="classpath:sql/data-anonymise.xml" context="data-test-site, data-anonymise"/> <!-- For context to clear referrals if context active on restart -->

    <!-- 2021-onwards-changelog will then link to 2022-onwards-changelog -->
    <include file="classpath:sql/2021/2021-onwards-changeLog.xml"/>
    <!-- *** STOP:
     DO NOT ADD ANYTHING MORE HERE
     - USE A CHANGELOG
     in the correct YEAR folder
     *** -->
</databaseChangeLog>
