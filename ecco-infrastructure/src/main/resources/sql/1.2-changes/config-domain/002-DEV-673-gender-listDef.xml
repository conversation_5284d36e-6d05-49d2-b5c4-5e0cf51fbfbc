<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd"
        logicalFilePath="classpath:sql/1.2-changes/config-domain/002-DEV-673-gender-listDef.xml">

<changeSet id="DEV-673-gender-listDef" author="adamjhamer">
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="115"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="gender"/>
        <column name="name" value="male"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="116"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="gender"/>
        <column name="name" value="female"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="117"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="gender"/>
        <column name="name" value="transgender"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="118"/>
        <column name="version" valueNumeric="0"/>
        <column name="listName" value="gender"/>
        <column name="name" value="unknown"/>
        <column name="disabled" valueBoolean="false"/>
        <column name="metadata">{}</column>
    </insert>
</changeSet>

</databaseChangeLog>
