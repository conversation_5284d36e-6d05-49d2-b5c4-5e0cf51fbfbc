<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd"
        logicalFilePath="classpath:sql/1.2-changes/config-domain/005-DEV-965-resourceType-listDef.xml">

<changeSet id="DEV-965-resourceType-listDef" author="nealeu">
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="132"/>
        <column name="listName" value="resourceType"/>
        <column name="name" value="building"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="133"/>
        <column name="listName" value="resourceType"/>
        <column name="name" value="resource"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="134"/>
        <column name="listName" value="resourceType"/>
        <column name="name" value="own property"/>
        <column name="parentId" valueNumeric="132"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="135"/>
        <column name="listName" value="resourceType"/>
        <column name="name" value="leased property"/>
        <column name="parentId" valueNumeric="132"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="136"/>
        <column name="listName" value="resourceType"/>
        <column name="name" value="bed"/>
        <column name="parentId" valueNumeric="133"/>
        <column name="metadata">{}</column>
    </insert>
    <insert tableName="cfg_list_definitions">
        <column name="id" valueNumeric="137"/>
        <column name="listName" value="resourceType"/>
        <column name="name" value="dayService"/>
        <column name="parentId" valueNumeric="133"/>
        <column name="metadata">{}</column>
    </insert>
</changeSet>

</databaseChangeLog>
