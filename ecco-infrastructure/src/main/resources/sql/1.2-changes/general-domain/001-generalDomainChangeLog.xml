<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="DEV-746-svcrec_commands-index-commandname" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <comment>Want an index for api/service-recipients/*/commands/tasks/latest/ which takes 25 secs.</comment>
        <output target="WARN">Creating index on svcrec_commands. This may take a while</output>
        <createIndex tableName="svcrec_commands" indexName="idx_svcrec_cmd_name_srid_ctd">
            <column name="serviceRecipientId"/>
            <column name="commandname"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-746-svcrec_commands-index-more" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
    <comment>Want an index for api/service-recipients/*/commands/tasks/latest/ which takes 25 secs.</comment>
        <output target="WARN">Creating another index on svcrec_commands. This may take a while</output>
        <createIndex tableName="svcrec_commands" indexName="idx_svcrec_cmd_name_srid_ctd2">
            <column name="serviceRecipientId"/>
            <column name="commandname"/>
            <column name="taskName"/>
            <column name="created"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-797-addHousingBenefit" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="clientdetails">
            <column name="housingBenefit" type="VARCHAR(32)"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-797-add-clientDetailOptionalFields" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <update tableName="setting">
            <column name="description" value="Common fields (ni,militaryNumber,nhs,housingBenefit etc), custom [text|date:{field name}](e.g.text:hair colour,date:partner date of death)"/>
            <where>id=22</where>
        </update>
    </changeSet>

    <!--
     - If this fails, then find and fudge with:
     - find the culprit
    select stra.* from servicetypes_referralaspects stra left join st_referralaspectsettings stras on stra.servicetypeId=stras.servicetypeId and stra.referralaspectId=stras.referralaspectId and stras.name='questions' where stras.name is null and stra.referralaspectid in (68,69,70,71,79,86,87,88,89,109);
     - get the questiongroups that it shows now
    select group_concat(qg.name) from questiongroups qg inner join servicetypes_questiongroups stqg on stqg.questiongroupid=qg.id and stqg.servicetypeid=<>;
     - in the ui (after this fudge) find the config for the service type and referral aspect and apply the questiongroups above, or a subset
     - or potentially clear and start again
    delete from servicetypes_questiongroups where servicetypeId in ();

    select max(orderexecuted) from DATABASECHANGELOG;
insert into DATABASECHANGELOG (id, author, filename, dateexecuted, orderexecuted, exectype, md5sum, description, comments, tag, liquibase)
values ('DEV-1539-hact-surveys2', 'adamjhamer', 'classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml', now(), 2098, 'EXECUTED', null, 'manual ignored', '', null, '3.5.3');
    -->
    <changeSet id="DEV-726-force-correct-questionnaire-config" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <!-- ensures that 'questions' are configured correctly -->
        <preConditions onFail="HALT">
            <sqlCheck expectedResult="0"><![CDATA[select count(*) from servicetypes_referralaspects stra left join st_referralaspectsettings stras on stra.servicetypeId=stras.servicetypeId and stra.referralaspectId=stras.referralaspectId and stras.name='questions' where stras.name is null and stra.referralaspectid in (68,69,70,71,79,86,87,88,89,109)]]></sqlCheck>
        </preConditions>
    </changeSet>

    <changeSet id="DEV-750-hact-drop-notnull-actiondefid" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <dropNotNullConstraint tableName="hactoutcomemappings" columnName="actionDefId" columnDataType="BIGINT"/>
    </changeSet>
    <changeSet id="DEV-750-hact-add-questiondefid" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="hactoutcomemappings">
            <column name="questionDefId" type="BIGINT"/>
        </addColumn>
        <addForeignKeyConstraint
                constraintName="fk_hactmapping_qns"
                baseTableName="hactoutcomemappings" baseColumnNames="questionDefId"
                referencedTableName="questions" referencedColumnNames="id" />
    </changeSet>
    <changeSet id="DEV-750-hact-add-questiondefid-idx" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <createIndex unique="false" tableName="hactoutcomemappings" indexName="idx_hactmappings_qns">
            <column name="questionDefId"/>
            <column name="hactOutcomeDefCode"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-759-add-actiongroups.orderby-col" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="risks">
            <column name="orderby" type="int" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-759-compute-actiongroups.orderby" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <update tableName="risks">
            <column name="orderby" type="int" valueComputed="id * 4"/>
        </update>
    </changeSet>

    <changeSet id="DEV-772-add-groupsupport-commenttype" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="grp_activities_referrals">
            <column name="evidenceType" type="int">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <!-- TODO - referential integrity to commenttypes -->
    </changeSet>
    <changeSet id="DEV-772-add-groupsupport-workUuid-rename" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <renameColumn tableName="grp_activities_referrals" oldColumnName="workUuid" newColumnName="supportWorkUuid" columnDataType="CHAR(36)"/>
    </changeSet>
    <changeSet id="DEV-772-add-groupsupport-workUuid-FK" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addForeignKeyConstraint baseTableName="grp_activities_referrals" constraintName="grp_act_ref_workUuid"
                                 baseColumnNames="supportWorkUuid" referencedTableName="supportplanwork" referencedColumnNames="uuid"
                                 referencesUniqueColumn="true"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"/>
    </changeSet>

    <changeSet id="DEV-779-add-sr-latestClientStatusDateTime" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="servicerecipients">
            <column name="latestClientStatusDateTime" type="DATETIME">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-779-populate-sr-latestClientStatusDateTime" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <preConditions onFail="MARK_RAN">
            <not>
                <sqlCheck expectedResult="0">
                    SELECT count(1) FROM supportplancomments WHERE clientStatusId IS NOT NULL
                </sqlCheck>
            </not>
        </preConditions>
        <sql>
            UPDATE servicerecipients sr
            JOIN (SELECT spw.serviceRecipientid, spw.workdate FROM supportplanwork spw
            INNER JOIN supportplancomments spc on spc.workUuid=spw.uuid
            WHERE spc.id IN
            (SELECT max(spc2.id) from supportplancomments spc2
            WHERE spc2.clientStatusId IS NOT null
            GROUP BY spc2.serviceRecipientId)) pairs
            ON sr.id = pairs.servicerecipientId
            SET latestClientStatusDateTime = pairs.workdate;
        </sql>
    </changeSet>

    <changeSet id="DEV-785-sr-contacts-command" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="svcrec_commands">
            <column name="contactId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-785-sr-contacts-command-archive" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="svcrec_commands_archive">
            <column name="contactId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-785-referral-contacts-archive" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="referrals_contacts">
            <column name="archived" type="DATE">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-673-gender" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="clientdetails">
            <column name="genderId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="workers">
            <column name="genderId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-673-gender-listDef-ref" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addForeignKeyConstraint constraintName="fk_cd_gender_lists" baseTableName="clientdetails"
                                 baseColumnNames="genderId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
        <addForeignKeyConstraint constraintName="fk_wk_gender_lists" baseTableName="workers"
                                 baseColumnNames="genderId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-673-gender-listDef-update" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <update tableName="clientdetails">
            <column name="genderId" valueNumeric="115"/>
            <where>gender='M'</where>
        </update>
        <update tableName="clientdetails">
            <column name="genderId" valueNumeric="116"/>
            <where>gender='F'</where>
        </update>
        <update tableName="clientdetails">
            <column name="genderId" valueNumeric="117"/>
            <where>gender='T'</where>
        </update>
        <update tableName="clientdetails">
            <column name="genderId" valueNumeric="118"/>
            <where>gender='Unknown'</where>
        </update>
        <update tableName="workers">
            <column name="genderId" valueNumeric="115"/>
            <where>gender='M'</where>
        </update>
        <update tableName="workers">
            <column name="genderId" valueNumeric="116"/>
            <where>gender='F'</where>
        </update>
        <update tableName="workers">
            <column name="genderId" valueNumeric="117"/>
            <where>gender='T'</where>
        </update>
        <update tableName="workers">
            <column name="genderId" valueNumeric="118"/>
            <where>gender='Unknown'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-674-disability" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="clientdetails">
            <column name="disabilityId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="workers">
            <column name="disabilityId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-674-disability-listDef-ref" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addForeignKeyConstraint constraintName="fk_cd_disability_lists" baseTableName="clientdetails"
                                 baseColumnNames="disabilityId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
        <addForeignKeyConstraint constraintName="fk_wk_disability_lists" baseTableName="workers"
                                 baseColumnNames="disabilityId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-674-disability-listDef-update" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <update tableName="clientdetails">
            <column name="disabilityId" valueNumeric="123"/>
            <where>disability=-1</where>
        </update>
        <update tableName="clientdetails">
            <column name="disabilityId" valueNumeric="119"/>
            <where>disability=0</where>
        </update>
        <update tableName="clientdetails">
            <column name="disabilityId" valueNumeric="120"/>
            <where>disability=1</where>
        </update>
        <update tableName="clientdetails">
            <column name="disabilityId" valueNumeric="121"/>
            <where>disability=2</where>
        </update>
        <update tableName="clientdetails">
            <column name="disabilityId" valueNumeric="122"/>
            <where>disability=3</where>
        </update>
        <update tableName="workers">
            <column name="disabilityId" valueNumeric="123"/>
            <where>disability=-1</where>
        </update>
        <update tableName="workers">
            <column name="disabilityId" valueNumeric="119"/>
            <where>disability=0</where>
        </update>
        <update tableName="workers">
            <column name="disabilityId" valueNumeric="120"/>
            <where>disability=1</where>
        </update>
        <update tableName="workers">
            <column name="disabilityId" valueNumeric="121"/>
            <where>disability=2</where>
        </update>
        <update tableName="workers">
            <column name="disabilityId" valueNumeric="122"/>
            <where>disability=3</where>
        </update>
    </changeSet>

    <changeSet id="DEV-674-sexorient" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="clientdetails">
            <column name="sexualityId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="workers">
            <column name="sexualityId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-674-sexuality-listDef-ref" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addForeignKeyConstraint constraintName="fk_cd_sexuality_lists" baseTableName="clientdetails"
                                 baseColumnNames="sexualityId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
        <addForeignKeyConstraint constraintName="fk_wk_sexuality_lists" baseTableName="workers"
                                 baseColumnNames="sexualityId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-674-sexuality-listDef-update" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <update tableName="clientdetails">
            <column name="sexualityId" valueNumeric="124"/>
            <where>sexuality='Unknown'</where>
        </update>
        <update tableName="clientdetails">
            <column name="sexualityId" valueNumeric="125"/>
            <where>sexuality='Hetrosexual' or sexuality='He'</where>
        </update>
        <update tableName="clientdetails">
            <column name="sexualityId" valueNumeric="126"/>
            <where>sexuality='Homosexual' or sexuality='Ho'</where>
        </update>
        <update tableName="clientdetails">
            <column name="sexualityId" valueNumeric="127"/>
            <where>sexuality='Lesbian' or sexuality='Lb'</where>
        </update>
        <update tableName="clientdetails">
            <column name="sexualityId" valueNumeric="128"/>
            <where>sexuality='Gay' or sexuality='gay'</where>
        </update>
        <update tableName="clientdetails">
            <column name="sexualityId" valueNumeric="129"/>
            <where>sexuality='Bisexual' or sexuality='Bi'</where>
        </update>
        <update tableName="clientdetails">
            <column name="sexualityId" valueNumeric="130"/>
            <where>sexuality='notdisclosed' or sexuality='ND'</where>
        </update>
        <update tableName="clientdetails">
            <column name="sexualityId" valueNumeric="131"/>
            <where>sexuality='nocapacitytoanswer' or sexuality='NC'</where>
        </update>

        <update tableName="workers">
            <column name="sexualityId" valueNumeric="124"/>
            <where>sexuality='Unknown'</where>
        </update>
        <update tableName="workers">
            <column name="sexualityId" valueNumeric="125"/>
            <where>sexuality='Hetrosexual' or sexuality='He'</where>
        </update>
        <update tableName="workers">
            <column name="sexualityId" valueNumeric="126"/>
            <where>sexuality='Homosexual' or sexuality='Ho'</where>
        </update>
        <update tableName="workers">
            <column name="sexualityId" valueNumeric="127"/>
            <where>sexuality='Lesbian' or sexuality='Lb'</where>
        </update>
        <update tableName="workers">
            <column name="sexualityId" valueNumeric="128"/>
            <where>sexuality='Gay' or sexuality='gay'</where>
        </update>
        <update tableName="workers">
            <column name="sexualityId" valueNumeric="129"/>
            <where>sexuality='Bisexual' or sexuality='Bi'</where>
        </update>
        <update tableName="workers">
            <column name="sexualityId" valueNumeric="130"/>
            <where>sexuality='notdisclosed' or sexuality='ND'</where>
        </update>
        <update tableName="workers">
            <column name="sexualityId" valueNumeric="131"/>
            <where>sexuality='nocapacitytoanswer' or sexuality='NC'</where>
        </update>
    </changeSet>
    <changeSet id="DEV-926-supportedFields-update" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <update tableName="setting">
            <column name="description" value="supported fields (firstLanguageId, ethnicOriginId, religionId, disabilityId, sexualOrientationId, ni, housingBenefit, nhs)"/>
            <where>id=22</where>
        </update>
        <update tableName="setting">
            <column name="description" value="supported fields (birthDate, firstLanguageId, ethnicOriginId, religionId, disabilityId, sexualOrientationId, ni, housingBenefit, nhs)"/>
            <where>id=30</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1141-supportedFields-update" author="adamjhamer">
        <update tableName="setting">
            <column name="description" value="supported fields (firstLanguageId, ethnicOriginId, nationalityId, religionId, disabilityId, sexualOrientationId, ni, housingBenefit, nhs)"/>
            <where>id=22</where>
        </update>
        <update tableName="setting">
            <column name="description" value="supported fields (birthDate, firstLanguageId, ethnicOriginId, nationalityId, religionId, disabilityId, sexualOrientationId, ni, housingBenefit, nhs)"/>
            <where>id=30</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1141-nationality" author="adamjhamer">
        <addColumn tableName="clientdetails">
            <column name="nationalityId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="workers">
            <column name="nationalityId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1141-nationality-listDef-ref" author="adamjhamer">
        <addForeignKeyConstraint constraintName="fk_cd_nationality_lists" baseTableName="clientdetails"
                                 baseColumnNames="nationalityId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
        <addForeignKeyConstraint constraintName="fk_wk_nationality_lists" baseTableName="workers"
                                 baseColumnNames="nationalityId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>


    <changeSet id="DEV-1151-supportedFields-update" author="adamjhamer">
        <update tableName="setting">
            <column name="description" value="supported fields (firstLanguageId, ethnicOriginId, nationalityId, religionId, disabilityId, sexualOrientationId, maritalStatusId, ni, housingBenefit, nhs)"/>
            <where>id=22</where>
        </update>
        <update tableName="setting">
            <column name="description" value="supported fields (birthDate, firstLanguageId, ethnicOriginId, nationalityId, religionId, disabilityId, sexualOrientationId, maritalStatusId, ni, housingBenefit, nhs)"/>
            <where>id=30</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1151-maritalStatus" author="adamjhamer">
        <addColumn tableName="clientdetails">
            <column name="maritalStatusId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="workers">
            <column name="maritalStatusId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1151-maritalStatus-listDef-ref" author="adamjhamer">
        <addForeignKeyConstraint constraintName="fk_cd_marital_lists" baseTableName="clientdetails"
                                 baseColumnNames="maritalStatusId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
        <addForeignKeyConstraint constraintName="fk_wk_marital_lists" baseTableName="workers"
                                 baseColumnNames="maritalStatusId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <!-- TODO
    <changeSet id="DEV-673-gender-drop" author="adamjhamer">
        <dropColumn tableName="clientdetails">
            <column name="gender"/>
        </dropColumn>
        <dropColumn tableName="workers">
            <column name="gender"/>
        </dropColumn>
    </changeSet>
    <changeSet id="DEV-674-disability-drop" author="adamjhamer">
        <dropColumn tableName="clientdetails">
            <column name="disability"/>
        </dropColumn>
        <dropColumn tableName="workers">
            <column name="disability"/>
        </dropColumn>
    </changeSet>
    <changeSet id="DEV-674-sexxorient-drop" author="adamjhamer">
        <dropColumn tableName="clientdetails">
            <column name="sexuality"/>
        </dropColumn>
        <dropColumn tableName="workers">
            <column name="sexuality"/>
        </dropColumn>
    </changeSet>
    -->

    <changeSet id="DEV-800-singlevaluehistory-encoding" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <validCheckSum>7:c775e8eb30387ebdaf8c12c975266ccc</validCheckSum>
        <sql>
            update singlevaluehistory set keyname=REPLACE(REPLACE(keyname, '%5B', '['), '%5D', ']');
        </sql>
        <sql>
            update svcrec_commands set singleValueKey=REPLACE(REPLACE(singleValueKey, '%5B', '['), '%5D', ']'), body=REPLACE(REPLACE(REPLACE(REPLACE(body, '%25', '%'), '%5B', '['), '%5D', ']'), '%27', '''') where singleValueKey is not null;
        </sql>
    </changeSet>

    <changeSet id="DEV-808-services-allowInboundReferrals" author="adamjhamer">
        <sql>
            update services set parameters=REPLACE(parameters, 'allowExternalReferrals', 'allowInboundReferrals') where parameters is not null;
        </sql>
    </changeSet>

    <changeSet author="nealeu" id="DEV-1262-drop-loneworkersnapshot">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="loneworkersnapshot"/>
        </preConditions>
        <dropTable tableName="loneworkersnapshot"/>
    </changeSet>

    <changeSet author="nealeu" id="DEV-1262-cal_eventstatus_table">
        <createTable tableName="cal_eventstatus">
            <column name="eventUid" type="VARCHAR(255)">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="contactId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="startInstant" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="plannedDurationMins" type="INT">
                <!-- probably best not-null -->
                <constraints nullable="true"/>
            </column>
            <!-- we shouldn't need a clob for this small json object -->
            <column name="location" type="VARCHAR(256)">
                <constraints nullable="true"/>
            </column>
            <column name="endInstant" type="DATETIME">
                <constraints nullable="true"/>
            </column>
            <column name="serviceRecipientId" type="INT">
                <!-- we may just want to record entries independent to work -->
                <constraints nullable="true"/>
            </column>
            <column name="workUuid" type="CHAR(36)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addForeignKeyConstraint constraintName="FK_eventstatus_eventuid"
                                 baseColumnNames="eventUid" baseTableName="cal_eventstatus"
                                 referencedColumnNames="item_uid" referencedTableName="cosmo_item"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
        <addForeignKeyConstraint constraintName="FK_lonework_svcrec"
                                 baseColumnNames="servicerecipientId" baseTableName="cal_eventstatus"
                                 referencedColumnNames="id" referencedTableName="servicerecipients"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
        <addForeignKeyConstraint constraintName="FK_lonework_contacts"
                                 baseColumnNames="contactId" baseTableName="cal_eventstatus"
                                 referencedColumnNames="id" referencedTableName="contacts"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
        <addForeignKeyConstraint constraintName="FK_lonework_work"
                                 baseColumnNames="workUuid" baseTableName="cal_eventstatus"
                                 referencedColumnNames="uuid" referencedTableName="supportplanwork"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
        <createIndex tableName="cal_eventstatus" indexName="idx_lonework_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
        <createIndex tableName="cal_eventstatus" indexName="idx_lonework_startEnd">
            <column name="startInstant"/>
            <column name="endInstant"/>
        </createIndex>
        <createIndex tableName="cal_eventstatus" indexName="idx_lonework_work">
            <column name="workUuid"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-833-smartstep-hierarchy" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="supportplanactions">
            <column name="parentActionInstanceUuid" type="CHAR(36)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <!-- parent may not be available depending on the command order, but this isn't critical -->
        <!--
        <addForeignKeyConstraint constraintName="FK_aiuuid_parent"
                                 baseColumnNames="parentActionInstanceUuid" baseTableName="supportplanactions"
                                 referencedColumnNames="actionInstanceUuid" referencedTableName="supportplanactions"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
        -->
        <!-- don't bother with an index as its only relevant within the same piece of work (at the moment) -->
    </changeSet>

    <changeSet id="DEV-833-smartstep-hierarchy-more" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="supportplanactions">
            <column name="hierarchy" type="SMALLINT">
                <constraints nullable="true"/>
            </column>
            <column name="orderby" type="SMALLINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-833-smartstep-hierarchy-position" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <dropColumn tableName="supportplanactions" columnName="orderby"/>
        <addColumn tableName="supportplanactions">
            <column name="position" type="VARCHAR(10)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-898-projectcalendar-menuitem" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <insert tableName="cfg_menuitem">
            <column name="id" valueNumeric="82" /> <!-- after lone worker -->
            <column name="imageUrl" value="/icons/crystal/import/png/48.png" />
            <column name="linkText" value="menu.linktext.projectcalendar" />
            <column name="roles" value="ROLE_EVANGELIST" /> <!-- for now, restrict -->
            <column name="url" value="/online/project-calendar/" />
            <column name="module_name" value="core" />
        </insert>
    </changeSet>
    <changeSet id="DEV-898-projectcalendar-menu-menuitem" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <insert tableName="cfg_menu_cfg_menuitem">
            <column name="cfg_menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="82" />
        </insert>
    </changeSet>

    <changeSet id="DEV-817-referralaspects-add-customForms4" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="126"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm4"/>
            <column name="name" value="customForm4"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1218-referralaspects-add-customForms5" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="128"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm5"/>
            <column name="name" value="customForm5"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1218-referralaspects-add-customForms6" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="129"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm6"/>
            <column name="name" value="customForm6"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1218-referralaspects-add-customForms7" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="130"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm7"/>
            <column name="name" value="customForm7"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1218-referralaspects-add-customForms8" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="131"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm8"/>
            <column name="name" value="customForm8"/>
        </insert>
    </changeSet>


    <changeSet id="DEV-1218-referralaspects-add-customForms9" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="132"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm9"/>
            <column name="name" value="customForm9"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1218-referralaspects-add-customForms10" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="133"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm10"/>
            <column name="name" value="customForm10"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1218-referralaspects-add-customForms11" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="134"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm11"/>
            <column name="name" value="customForm11"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1218-referralaspects-add-customForms12" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="135"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm12"/>
            <column name="name" value="customForm12"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1218-referralaspects-add-customForms13" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="136"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm13"/>
            <column name="name" value="customForm13"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1218-referralaspects-add-customForms14" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="137"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm14"/>
            <column name="name" value="customForm14"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1218-referralaspects-add-customForms15" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="138"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm15"/>
            <column name="name" value="customForm15"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1218-referralaspects-add-customForms16" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="139"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm16"/>
            <column name="name" value="customForm16"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1218-referralaspects-add-customForms17" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="140"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_CUSTOMFORM"/>
            <column name="friendlyName" value="customForm17"/>
            <column name="name" value="customForm17"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-976-eventCategory" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
    <addColumn tableName="events">
            <column name="eventCategoryId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-976-eventCategory-listDef-ref" author="adamjhamer"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addForeignKeyConstraint constraintName="fk_eventcategory_lists" baseTableName="events"
                                 baseColumnNames="eventCategoryId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-975-appt-schedules-time-nullable" author="nealeu"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <dropNotNullConstraint tableName="appointmentschedules" columnName="timeForDays" columnDataType="TIME"/>
    </changeSet>

    <changeSet id="DEV-963-add-bldgId-to-projects" author="nealeu"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="projects">
            <column name="buildingId" type="INT"/>
        </addColumn>
        <addForeignKeyConstraint baseTableName="projects"
                                 baseColumnNames="buildingId"
                                 constraintName="FK_projects_buildingId"
                                 referencedTableName="bldg_fixed"
                                 referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-963-add-buildingId-to-accommodation" author="nealeu"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <addColumn tableName="accommodations">
            <column name="buildingId" type="INT"/>
        </addColumn>
        <addForeignKeyConstraint baseTableName="accommodations"
                                 baseColumnNames="buildingId"
                                 constraintName="FK_accomms_buildingId"
                                 referencedTableName="bldg_fixed"
                                 referencedColumnNames="id"/>
    </changeSet>


    <changeSet id="DEV-963-rfrl-accomId-to-client-residenceId" author="nealeu"
               dbms="mysql"
               logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <!-- This one needs to run after the Java based migration which we can test by
             seeing if buildingId has been populated -->
        <preConditions onFail="WARN">
            <sqlCheck expectedResult="0">
                SELECT count(1) FROM accommodations WHERE buildingId IS NULL
            </sqlCheck>
        </preConditions>
        <sql>
            UPDATE clientdetails c
            JOIN referrals ON referrals.clientId = c.id
            SET residenceId = (SELECT buildingId FROM accommodations WHERE referrals.accommodationId = accommodations.id)
            WHERE c.residenceId IS NULL AND EXISTS (SELECT * FROM accommodations WHERE referrals.accommodationId = accommodations.id);
        </sql>
    </changeSet>

    <include file="classpath:sql/1.2-changes/general-domain/002-reportdefs.xml"/>

    <changeSet id="DEV-997-rename-referralattachments" author="nealeu"
        logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <renameTable newTableName="svcrec_attachments" oldTableName="referralattachments"/>
    </changeSet>

    <changeSet id="DEV-998-move-worker-attachments-to-svcrec-attachments" author="nealeu"
        logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <sql>
            INSERT INTO svcrec_attachments (id, version, filename, upload_size, bytesid,
            showOnReferral, evidencePage, evidencePageGroup, serviceRecipientId)
            SELECT id, version, filename, upload_size, bytesid,
            showOnWorker, evidencePage, evidencePageGroup, serviceRecipientId FROM workerattachments;
        </sql>
    </changeSet>

    <changeSet id="DEV-998-drop-workerattachments-table" author="nealeu"
        logicalFilePath="classpath:sql/1.2-changes/001-generalDomainChangeLog.xml">
        <dropTable tableName="workerattachments"/>
    </changeSet>

    <!-- use only ecco-specific columns in repeating events that go beyond standard ical -->
    <!-- so we rely on the underlying calendar for recurring details -->
    <!-- but can still utilise our ecco-specific 'events' table for specific info -->
    <changeSet id="DEV-1023-recurring-ecco-specifics" author="adamjhamer">
        <createTable tableName="eventsrecurring">
            <column defaultValueNumeric="0" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="eventtype" type="INT"/>
            <column name="eventCategoryId" type="INT">
                <constraints nullable="true"/>
            </column>
            <column name="isGenerated" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="cal_uid" type="VARCHAR(255)"/>
            <!-- 'events' have this as optional since "events can have diff associations" -->
            <!-- but recurring entries that need 'additional properties to a standard calendar' will have an srId -->
            <column name="serviceRecipientId" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet id="DEV-1023-recurring-ecco-specifics-idx-fk" author="adamjhamer">
        <createIndex tableName="eventsrecurring" indexName="idx_eventsrecurring_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
        <addForeignKeyConstraint constraintName="fk_eventsrecurring_svcrec"
                                 baseTableName="eventsrecurring" baseColumnNames="serviceRecipientId"
                                 referencedTableName="servicerecipients" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
        <addForeignKeyConstraint constraintName="fk_eventsrecurring_listdef" baseTableName="eventsrecurring"
                                 baseColumnNames="eventCategoryId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <!-- outcomes and risks already have disable in the schema -->
    <changeSet id="DEV-1044-servicetype-disable" author="adamjhamer">
        <addColumn tableName="actions">
            <column name="disabled" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="actionfakes">
            <column name="disabled" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1058-agreements-status" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="dataProtectionStatus" type="BOOLEAN" defaultValue="NULL">
                <constraints nullable="true"/>
            </column>
            <column name="consentStatus" type="BOOLEAN" defaultValue="NULL">
                <constraints nullable="true"/>
            </column>
            <column name="agreementStatus" type="BOOLEAN" defaultValue="NULL">
                <constraints nullable="true"/>
            </column>
            <column name="agreement2Status" type="BOOLEAN" defaultValue="NULL">
                <constraints nullable="true"/>
            </column>
            <column name="agreement3Status" type="BOOLEAN" defaultValue="NULL">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <update tableName="referrals">
            <column name="dataProtectionStatus" valueBoolean="true"/>
            <where>dataProtectionSigned is not null</where>
        </update>
        <update tableName="referrals">
            <column name="consentStatus" valueBoolean="true"/>
            <where>consentSigned is not null</where>
        </update>
        <update tableName="referrals">
            <column name="agreementStatus" valueBoolean="true"/>
            <where>agreementSigned is not null</where>
        </update>
        <update tableName="referrals">
            <column name="agreement2Status" valueBoolean="true"/>
            <where>agreement2Signed is not null</where>
        </update>
        <update tableName="referrals">
            <column name="agreement3Status" valueBoolean="true"/>
            <where>agreement3Signed is not null</where>
        </update>
    </changeSet>

    <changeSet author="nealeu" id="DEV-1066-add-spa-targetSchedule">
        <addColumn tableName="supportplanactions">
            <column name="targetSchedule" type="VARCHAR(63)"/>
        </addColumn>
    </changeSet>

    <include file="classpath:sql/1.2-changes/general-domain/003-DEV-1090-multiple-smartstep-risk.xml"/>
    <include file="classpath:sql/1.2-changes/general-domain/003-DEV-1090-multiple-smartstep-hr.xml"/>

    <!-- SQL Server uniq index see https://forum.liquibase.org/topic/how-to-create-conditional-indexes-using-createindex -->
    <changeSet id="DEV-1029-add-work-unique-eventId" author="adamjhamer">
        <createIndex tableName="supportplanwork" indexName="idx_supp_eventid" unique="true">
            <column name="eventId"/>
        </createIndex>
        <modifySql dbms="mssql">
            <append value=" WHERE eventId is not null"/>
        </modifySql>
    </changeSet>

    <changeSet id="DEV-1120-svcrec_cmdarc-commandname-created-index" author="adamjhamer">
        <createIndex tableName="svcrec_commands_archive" indexName="idx_svcrec_cmdarc_ctdname">
            <column name="created"/>
            <column name="commandname"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-1115-referralaspects-add-attachments" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="127"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="EVIDENCE_SUPPORT"/>
            <column name="friendlyName" value=""/>
            <column name="name" value="needsAttachment"/>
        </insert>
    </changeSet>

    <!--
      - See 075-Dec-2015-until-next:4196 for a previous repeat of this issue (H2 failing tests due to changeset not executed since wrong domain).
      - 1.2-onwards-changelog dictates the order of schema/data changes and 'securityDomainChangeLog' is before any general .
      - The 014-addSeniorManager is a general change that this changeset relies on - so silently fails when all changesets are ran together (h2).
      - TODO Ideally the schema and data for security changes are moved together in the security domain.
    -->
    <!-- ROLE_DELETEREFERRAL - add to senior manager since delete menu only available to ROLE_SOFTDELETE which is on senior managers -->
    <!-- but the userManagementFlow suggests this is only for request/unrequest deletion, so we can could allow ROLE_SOFTDELETE to be used by staff -->
    <changeSet author="adamjhamer" id="z3041-deletereferral-for-seniormanagers">
        <validCheckSum>8:7683d3be33a29ce7fceb6890ad7582bd</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/security-domain/001-securityDomainChangeLog.xml" author="adamjhamer" id="z3041-deletereferral-for-seniormanagers"/>
            </not>
            <!-- Removed preCondition for migration of old sites where we may already have added this groupId manually -->
        </preConditions>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_DELETEREFERRAL"/>
        </insert>
    </changeSet>

    <changeSet id="z3058-services_projects-disabled" author="adamjhamer">
        <addColumn tableName="services_projects">
            <column name="disabled" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1129-add-uuid-actions" author="adamjhamer">
        <addColumn tableName="actions">
            <column name="uuid" type="CHAR(36)">
                <constraints nullable="true" />
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1129-populate-actions-uuid" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.AddUuidColumnChange"
                      tableName="actions" uniqueKeyColumns="id"
                      newUuidColumn="uuid" />
    </changeSet>
    <changeSet id="DEV-1129-nonull-on-actions-uuid" author="adamjhamer">
        <addNotNullConstraint tableName="actions" columnName="uuid" columnDataType="CHAR(36)"/>
    </changeSet>
    <changeSet id="DEV-1129-actions-uuid-idx" author="adamjhamer">
        <comment>Index for actions by uuid (for good measure)</comment>
        <createIndex unique="true" tableName="actions" indexName="idx_action_uuid">
            <column name="uuid"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-1129-rename-actionGroups" author="adamjhamer">
        <renameTable oldTableName="risks" newTableName="actiongroups"/>
    </changeSet>

    <changeSet id="DEV-1129-add-uuid-actiongroups" author="adamjhamer">
        <addColumn tableName="actiongroups">
            <column name="uuid" type="CHAR(36)">
                <constraints nullable="true" />
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1129-populate-actiongroups-uuid" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.AddUuidColumnChange"
                      tableName="actiongroups" uniqueKeyColumns="id"
                      newUuidColumn="uuid" />
    </changeSet>
    <changeSet id="DEV-1129-nonull-on-actiongroups-uuid" author="adamjhamer">
        <addNotNullConstraint tableName="actiongroups" columnName="uuid" columnDataType="CHAR(36)"/>
    </changeSet>
    <changeSet id="DEV-1129-actiongroups-uuid-idx" author="adamjhamer">
        <comment>Index for actiongroups by uuid (for good measure)</comment>
        <createIndex unique="true" tableName="actiongroups" indexName="idx_actiongrp_uuid">
            <column name="uuid"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-1172-referral-contacts-associatedTypeId" author="adamjhamer">
        <addColumn tableName="referrals_contacts">
            <column name="associatedTypeId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_referralcontacts_listdef" baseTableName="referrals_contacts"
                                 baseColumnNames="associatedTypeId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-1171-referral-contacts-associatedTypeId-migration" author="adamjhamer" context="">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
        </preConditions>
        <sql>
            UPDATE referrals_contacts AS rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            SET rc.associatedTypeId=142
            WHERE c.individualType='carer';
        </sql>
        <sql>
            UPDATE referrals_contacts AS rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            SET rc.associatedTypeId=143
            WHERE c.individualType='relation';
        </sql>
        <sql>
            UPDATE referrals_contacts AS rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            SET rc.associatedTypeId=144
            WHERE c.individualType='volunteer';
        </sql>
        <sql>
            UPDATE referrals_contacts AS rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            SET rc.associatedTypeId=145
            WHERE c.individualType='partner';
        </sql>
        <sql>
            UPDATE referrals_contacts AS rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            SET rc.associatedTypeId=146
            WHERE c.individualType='sibling';
        </sql>
        <sql>
            UPDATE referrals_contacts AS rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            SET rc.associatedTypeId=147
            WHERE c.individualType='parent';
        </sql>
        <sql>
            UPDATE referrals_contacts AS rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            SET rc.associatedTypeId=148
            WHERE c.individualType='child';
        </sql>
        <sql>
            UPDATE referrals_contacts AS rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            SET rc.associatedTypeId=149
            WHERE c.individualType='friend';
        </sql>
        <sql>
            UPDATE referrals_contacts AS rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            SET rc.associatedTypeId=150
            WHERE c.individualType='nextofkin';
        </sql>
        <sql>
            UPDATE referrals_contacts AS rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            SET rc.associatedTypeId=151
            WHERE c.individualType='GP';
        </sql>
    </changeSet>

    <changeSet id="DEV-1171-referral-contacts-associatedTypeId-migration-mssql" author="adamjhamer" dbms="mssql">
        <sql>
            UPDATE rc
            SET rc.associatedTypeId=142
            FROM svcrec_contacts rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            WHERE c.individualType='carer';
        </sql>
        <sql>
            UPDATE rc
            SET rc.associatedTypeId=143
            FROM svcrec_contacts rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            WHERE c.individualType='relation';
        </sql>
        <sql>
            UPDATE rc
            SET rc.associatedTypeId=144
            FROM svcrec_contacts rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            WHERE c.individualType='volunteer';
        </sql>
        <sql>
            UPDATE rc
            SET rc.associatedTypeId=145
            FROM svcrec_contacts rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            WHERE c.individualType='partner';
        </sql>
        <sql>
            UPDATE rc
            SET rc.associatedTypeId=146
            FROM svcrec_contacts rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            WHERE c.individualType='sibling';
        </sql>
        <sql>
            UPDATE rc
            SET rc.associatedTypeId=147
            FROM svcrec_contacts rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            WHERE c.individualType='parent';
        </sql>
        <sql>
            UPDATE rc
            SET rc.associatedTypeId=148
            FROM svcrec_contacts rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            WHERE c.individualType='child';
        </sql>
        <sql>
            UPDATE rc
            SET rc.associatedTypeId=149
            FROM svcrec_contacts rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            WHERE c.individualType='friend';
        </sql>
        <sql>
            UPDATE rc
            SET rc.associatedTypeId=150
            FROM svcrec_contacts rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            WHERE c.individualType='nextofkin';
        </sql>
        <sql>
            UPDATE rc
            SET rc.associatedTypeId=151
            FROM svcrec_contacts rc
            INNER JOIN individual_individualTypes AS c on rc.contactId=c.individualId
            WHERE c.individualType='GP';
        </sql>
    </changeSet>

    <changeSet id="DEV-1171-referral-contacts-associatedTypeId-migration-oracle" author="adamjhamer" dbms="oracle">
        <sql>
            update svcrec_contacts rc
            set rc.associatedTypeId=142
            where rc.contactId in (select individualid from individual_individualTypes where individualType='carer');
        </sql>
        <sql>
            update svcrec_contacts rc
            set rc.associatedTypeId=143
            where rc.contactId in (select individualid from individual_individualTypes where individualType='relation');
        </sql>
        <sql>
            update svcrec_contacts rc
            set rc.associatedTypeId=144
            where rc.contactId in (select individualid from individual_individualTypes where individualType='volunteer');
        </sql>
        <sql>
            update svcrec_contacts rc
            set rc.associatedTypeId=145
            where rc.contactId in (select individualid from individual_individualTypes where individualType='partner');
        </sql>
        <sql>
            update svcrec_contacts rc
            set rc.associatedTypeId=146
            where rc.contactId in (select individualid from individual_individualTypes where individualType='sibling');
        </sql>
        <sql>
            update svcrec_contacts rc
            set rc.associatedTypeId=147
            where rc.contactId in (select individualid from individual_individualTypes where individualType='parent');
        </sql>
        <sql>
            update svcrec_contacts rc
            set rc.associatedTypeId=148
            where rc.contactId in (select individualid from individual_individualTypes where individualType='child');
        </sql>
        <sql>
            update svcrec_contacts rc
            set rc.associatedTypeId=149
            where rc.contactId in (select individualid from individual_individualTypes where individualType='friend');
        </sql>
        <sql>
            update svcrec_contacts rc
            set rc.associatedTypeId=150
            where rc.contactId in (select individualid from individual_individualTypes where individualType='nextofkin');
        </sql>
        <sql>
            update svcrec_contacts rc
            set rc.associatedTypeId=151
            where rc.contactId in (select individualid from individual_individualTypes where individualType='GP');
        </sql>
    </changeSet>

    <changeSet id="DEV-1200-appttype-required-tbl" author="nealeu">
        <createTable tableName="svccfg_appttype_required">
            <column name="version" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="appointmentTypeId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="requirementId" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addForeignKeyConstraint
                constraintName="fk_sc_at_rq_appttypeid"
                baseTableName="svccfg_appttype_required"
                baseColumnNames="appointmentTypeId"
                referencedTableName="appointmenttypes"
                referencedColumnNames="id"
        />
        <addForeignKeyConstraint
                constraintName="fk_sc_at_rq_listdefid"
                baseTableName="svccfg_appttype_required"
                baseColumnNames="requirementId"
                referencedTableName="cfg_list_definitions"
                referencedColumnNames="id"
        />
    </changeSet>

    <changeSet id="DEV-1200-appttype-required-data" context="base-data" author="nealeu">
        <insert tableName="svccfg_appttype_required">
            <column name="appointmentTypeId" valueNumeric="1"/>
            <column name="requirementId" valueNumeric="152" />
        </insert>
        <insert tableName="svccfg_appttype_required">
            <column name="appointmentTypeId" valueNumeric="1"/>
            <column name="requirementId" valueNumeric="153" />
        </insert>
        <insert tableName="svccfg_appttype_required">
            <column name="appointmentTypeId" valueNumeric="1"/>
            <column name="requirementId" valueNumeric="154" />
        </insert>
    </changeSet>

    <changeSet id="DEV-1203-svcrec-provides-attrs-tbl" author="nealeu">
        <createTable tableName="svcrec_provides_attrs">
            <column name="version" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="attributeId" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addForeignKeyConstraint
                constraintName="fk_sr_pr_at_svcrecid"
                baseTableName="svcrec_provides_attrs"
                baseColumnNames="serviceRecipientId"
                referencedTableName="servicerecipients"
                referencedColumnNames="id"
        />
        <addForeignKeyConstraint
                constraintName="fk_sr_pr_at_listdefid"
                baseTableName="svcrec_provides_attrs"
                baseColumnNames="attributeId"
                referencedTableName="cfg_list_definitions"
                referencedColumnNames="id"
        />
    </changeSet>

    <changeSet id="DEV-1203-svcrec-requires-attrs-tbl" author="nealeu">
        <createTable tableName="svcrec_requires_attrs">
            <column name="version" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="attributeId" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addForeignKeyConstraint
                constraintName="fk_sr_rq_at_svcrecid"
                baseTableName="svcrec_requires_attrs"
                baseColumnNames="serviceRecipientId"
                referencedTableName="servicerecipients"
                referencedColumnNames="id"
        />
        <addForeignKeyConstraint
                constraintName="fk_sr_rq_at_listdefid"
                baseTableName="svcrec_requires_attrs"
                baseColumnNames="attributeId"
                referencedTableName="cfg_list_definitions"
                referencedColumnNames="id"
        />
    </changeSet>

    <changeSet id="DEV-890-drop-BLOB-date-and-textmaps" author="nealeu">
        <dropColumn tableName="clientdetails" columnName="textMap"/>
        <dropColumn tableName="clientdetails" columnName="dateMap"/>
        <dropColumn tableName="referrals" columnName="dateMap"/>
        <dropColumn tableName="workers" columnName="textMap"/>
        <dropColumn tableName="workers" columnName="dateMap"/>
    </changeSet>

    <changeSet id="DEV-1231-genderAtBirth" author="adamjhamer">
        <addColumn tableName="clientdetails">
            <column name="genderAtBirthId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="workers">
            <column name="genderAtBirthId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1231-genderAtBirth-listDef-ref" author="adamjhamer">
        <addForeignKeyConstraint constraintName="fk_cd_genderBth_lists" baseTableName="clientdetails"
                                 baseColumnNames="genderAtBirthId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
        <addForeignKeyConstraint constraintName="fk_wk_genderBth_lists" baseTableName="workers"
                                 baseColumnNames="genderAtBirthId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-1277-appointmenttypes-more" author="adamjhamer" context="base-data">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(1) FROM appointmenttypes
                WHERE id in (2,3)
            </sqlCheck>
        </preConditions>
        <insert tableName="appointmenttypes">
            <column name="id" valueNumeric="2" />
            <column name="version" valueNumeric="0" />
            <column name="serviceId" valueNumeric="99" />
            <column name="disabled" valueBoolean="false" />
            <column name="name" value="medication" />
            <column name="recommendedDuration" value="30" />
            <column name="isDefault" valueBoolean="false" />
        </insert>
        <insert tableName="appointmenttypes">
            <column name="id" valueNumeric="3" />
            <column name="version" valueNumeric="0" />
            <column name="serviceId" valueNumeric="99" />
            <column name="disabled" valueBoolean="false" />
            <column name="name" value="shopping" />
            <column name="recommendedDuration" value="240" />
            <column name="isDefault" valueBoolean="false" />
        </insert>
    </changeSet>

    <changeSet id="DEV-1277-building-availability" author="adamjhamer">
        <insert tableName="appointmenttypes">
            <column name="id" valueNumeric="10" />
            <column name="version" valueNumeric="0" />
            <column name="serviceId" valueNumeric="-100" />
            <column name="disabled" valueBoolean="false" />
            <column name="name" value="availability" />
            <column name="recommendedDuration" value="0" />
            <column name="isDefault" valueBoolean="false" />
        </insert>
    </changeSet>

    <!-- Let's see if we can re-use existing with customsation
    <changeSet id="DEV-1308-referralaspects-add-workerDetails" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="141"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="DEDICATED_TASK"/>
            <column name="friendlyName" value="customForm17"/>
            <column name="name" value="customForm17"/>
        </insert>
    </changeSet>
    -->

    <changeSet id="DEV-1308-st-referralaspects-hr" author="neale" context="1.1-base-data">
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="0"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="45"/> <!-- clientWithContact => worker -->
            <column name="servicetypeId" valueNumeric="-200"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="15"/> <!-- from / sourceWithIndividual -->
            <column name="servicetypeId" valueNumeric="-200"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="18"/>  <!-- referralView -->
            <column name="servicetypeId" valueNumeric="-200"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1308-st-referralaspects-hr-endFlow" author="neale" context="1.1-base-data">
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="50"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="50"/>  <!-- close -->
            <column name="servicetypeId" valueNumeric="-200"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="11"/>  <!-- endFlow -->
            <column name="servicetypeId" valueNumeric="-200"/>
        </insert>
    </changeSet>

    <include file="classpath:sql/1.2-changes/finance-domain/002-rateCards-fk.xml"/>

    <changeSet id="ECCO-1460-demandschedule-ratecards" author="adamjhamer">
        <!-- TODO drop or migrate 'charge' -->
        <!-- TODO drop or migrate 'financeBand' -->
        <addColumn tableName="appointmentschedules">
            <column name="agreedDurationUnitId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="appointmentschedules">
            <column name="rateCardName" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="appointmentschedules">
            <column name="rateCardId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1460-demandschedule-ratecards-ref" author="adamjhamer">
        <addForeignKeyConstraint constraintName="fk_appsched_units" baseTableName="appointmentschedules"
                                 baseColumnNames="agreedDurationUnitId" referencedTableName="fin_unitofmeasurements" referencedColumnNames="id" />
        <addForeignKeyConstraint constraintName="fk_appsched_ratecard" baseTableName="appointmentschedules"
                                 baseColumnNames="rateCardId" referencedTableName="fin_ratecards" referencedColumnNames="id" />
    </changeSet>
    <changeSet id="ECCO-1460-demandschedule-ratecards-data" author="adamjhamer">
        <update tableName="appointmentschedules">
            <column name="agreedDurationUnitId" valueNumeric="1"/>
        </update>
    </changeSet>

    <changeSet id="ECCO-1460-appointmentagreements-charge-rename" author="adamjhamer">
        <renameColumn tableName="appointmentagreements" oldColumnName="agreementCharge" newColumnName="agreedCharge" columnDataType="DECIMAL(9,2)"/>
    </changeSet>
    <changeSet id="ECCO-1460-appointmentagreements-parent" author="adamjhamer">
        <addColumn tableName="appointmentagreements">
            <column name="parentServiceAgreementId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="ECCO-1460-appointmentagreements-parent-ref" author="adamjhamer">
        <addForeignKeyConstraint constraintName="fk_appsagree_parent" baseTableName="appointmentagreements"
                                 baseColumnNames="parentServiceAgreementId" referencedTableName="appointmentagreements" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-1328-remove-contracts-actions" author="adamjhamer">
        <dropTable tableName="contracts_actions"/>
    </changeSet>
    <changeSet id="DEV-1328-remove-contracts-referral" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="referrals" constraintName="FKC8E0F8764B4CA0CE"/>
        <dropColumn tableName="referrals" columnName="contractId"/>
    </changeSet>
    <changeSet id="DEV-1328-remove-contracts" author="adamjhamer">
        <dropTable tableName="contracts"/>
    </changeSet>

    <include file="classpath:sql/1.2-changes/finance-domain/003-contracts-fk.xml"/>

    <changeSet id="DEV-1347-questionanswerchoices-value" author="adamjhamer">
        <!-- this was just belt and braces, but causes issues with mysql ".DatabaseException: You can't specify target table 'questionanswerchoices' for update in FROM clause"
        <update tableName="questionanswerchoices">
            <column name="value" valueComputed="(SELECT id FROM questionanswerchoices)"/>
            <where>value is null</where>
        </update>
        -->
        <addNotNullConstraint tableName="questionanswerchoices" columnName="value" columnDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="DEV-1326-events-eventStatusRateId" author="adamjhamer">
        <addColumn tableName="events">
            <column name="eventStatusRateId" type="INTEGER">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseTableName="events" baseColumnNames="eventStatusRateId" constraintName="events_ratest_lists"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-1318-add_formevidence_sigUuid" author="adamjhamer">
        <addColumn tableName="evdnc_form_snapshot">
            <column name="signatureUuid" type="CHAR(36)"/><!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1318-add_formevidence_sigUuid-PK" author="adamjhamer">
        <addForeignKeyConstraint baseTableName="evdnc_form_snapshot" baseColumnNames="signatureUuid"
                                 constraintName="fk_evdnc_frm_snp_sigs" referencedTableName="signature" referencedColumnNames="uuid"/>
    </changeSet>

    <changeSet id="DEV-1357-form-work" author="adamjhamer">
        <renameTable oldTableName="evdnc_form_snapshot" newTableName="evdnc_form_work"/>
    </changeSet>

    <changeSet id="DEV-1357-evdnc_form_snapshot" author="adamjhamer">
        <createTable tableName="evdnc_form_snapshots">
            <column name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="workUuid" type="CHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="created" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="json" type="CLOB"/>
        </createTable>
    </changeSet>

    <!-- MYSQL MIGRATION -->
    <changeSet id="DEV-1357-evdnc_form_snapshot-mysql" author="adamjhamer" dbms="mysql">
        <addAutoIncrement tableName="evdnc_form_snapshots" columnName="id" startWith="1" columnDataType="INT"/>
    </changeSet>
    <changeSet id="DEV-1357-evdnc_form_snapshot-populateid" author="adamjhamer" dbms="mysql">
        <sql>
            INSERT INTO evdnc_form_snapshots (workUuid, version, created, serviceRecipientId, json)
            SELECT uuid, version, created, serviceRecipientId, json FROM evdnc_form_work;
        </sql>
    </changeSet>
    <!-- drops auto increment -->
    <changeSet id="DEV-1357-evdnc_form_snapshot-id" author="adamjhamer" dbms="mysql">
        <modifyDataType tableName="evdnc_form_snapshots" columnName="id" newDataType="INT"/>
    </changeSet>
    <!-- check sequences are okay - fail if not, and do manual update -->
    <!-- select max(id) from evdnc_form_snapshots; -->
    <!-- select next_val from hibernate_sequences where sequence_name='id_name'; -->
    <!-- update hibernate_sequences set next_val=<ID> where sequence_name='id_name' limit 1; -->
    <changeSet id="DEV-1463-check-sequences" author="adamjhamer" dbms="mysql">
        <preConditions onFail="HALT">
            <sqlCheck expectedResult="0">
                SELECT
                CASE WHEN MAX(id) > b.next_val THEN 1 ELSE 0 END
                FROM
                evdnc_form_snapshots a,
                hibernate_sequences b
                WHERE
                b.sequence_name='id_name';
            </sqlCheck>
        </preConditions>
    </changeSet>
    <!-- MYSQL MIGRATION -->

    <changeSet id="DEV-1357-evdnc_form_snapshot-fk3" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <or>
                    <!-- If we ran v1, then we don't need v2 -->
                    <changeSetExecuted id="DEV-1357-evdnc_form_snapshot-fk" author="adamjhamer" changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml"/>
                    <!-- If we ran v2, then we don't need v3 or v1 -->
                    <changeSetExecuted id="DEV-1357-evdnc_form_snapshot-fk2" author="adamjhamer" changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml"/>
                </or>
            </not>
        </preConditions>
        <addForeignKeyConstraint baseTableName="evdnc_form_snapshots" baseColumnNames="workUuid" constraintName="fk-evd_frmsnp_workUuid"
                                 referencedTableName="evdnc_form_work" referencedColumnNames="uuid"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="false"/>
        <createIndex tableName="evdnc_form_snapshots" indexName="idx_evdnc_frmsnp_srId">
            <column name="serviceRecipientId"/>
        </createIndex>
        <createIndex tableName="evdnc_form_snapshots" indexName="idx_evdnc_frmsnp_srId_crt">
            <column name="serviceRecipientId"/>
            <column name="created"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-1357-evdnc_form_snapshot-dropcols" author="adamjhamer">
        <dropColumn tableName="evdnc_form_work" columnName="json"/>
    </changeSet>

    <changeSet id="DEV-1357-form-comment" author="adamjhamer">
        <createTable tableName="evdnc_form_comments">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="workUuid" type="CHAR(36)"/>
            <column name="serviceRecipientId" type="INT"/>
            <column name="bc_comment" type="CLOB"/>
            <column name="created" type="DATETIME"/>
            <column name="minutesspent" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="contactid" type="BIGINT"/>
            <column name="typeid" type="BIGINT"/>
        </createTable>
    </changeSet>

    <changeSet id="DEV-1357-form-comment-constraints" author="adamjhamer">
        <addNotNullConstraint tableName="evdnc_form_comments" columnName="workUuid" columnDataType="CHAR(36)"/>
        <addNotNullConstraint tableName="evdnc_form_comments" columnName="serviceRecipientId" columnDataType="INT"/>

        <addForeignKeyConstraint baseTableName="evdnc_form_comments" baseColumnNames="contactid" constraintName="fk-evd_frmc_contactId"
                                 referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"/>
        <addForeignKeyConstraint baseTableName="evdnc_form_comments" baseColumnNames="typeid" constraintName="fk-evd_frmc_cmttypeId"
                                 referencedColumnNames="id" referencedTableName="commenttypes" referencesUniqueColumn="false"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"/>
        <addForeignKeyConstraint baseTableName="evdnc_form_comments" baseColumnNames="workUuid" constraintName="fk-evd_frmc_workUuid"
                                 referencedTableName="evdnc_form_work" referencedColumnNames="uuid"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="false"/>
        <addForeignKeyConstraint baseTableName="evdnc_form_comments" baseColumnNames="serviceRecipientId" constraintName="fk_evd_frmc_svcrecId"
                                 referencedTableName="servicerecipients" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
        <createIndex tableName="evdnc_form_comments" indexName="idx_evdc_frm_idx_srid">
            <column name="serviceRecipientId"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-1294-referralaspects-add-allocateTo" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="141"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="DEDICATED_TASK"/>
            <column name="friendlyName" value="allocateWorker"/>
            <column name="name" value="allocateWorker"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1390-ref-details-cust-form-accom-test" author="nealeu" context="acceptanceTests">
        <insert tableName="cfg_form_definitions">
            <column name="uuid" value="00200000-0000-babe-babe-dadafee1600d"/>
            <column name="created" valueDate="2018-02-27T09:36:12"/>
            <column name="name" value="referral-details-default"/>
            <column name="body" type="CLOB"><![CDATA[{"meta": {"label": "json-schema-form-layout", "version": "1"},
    "schema":
    {"schema":
        {"definitions": {
            "custom_relationship": {
                "type": "integer",
                    "enum": [
                    1000,
                    1001,
                    1002
                ],
                    "enumNames": [
                    "father",
                    "mother",
                    "other"
                ]
            },
            "custom_yesno": {
                "type": "integer",
                    "enum": [
                    1008,
                    1009
                ],
                    "enumNames": [
                    "yes",
                    "no"
                ]
            },
            "custom_scale": {
                "type": "integer",
                    "enum": [
                    1003,
                    1004,
                    1005,
                    1006,
                    1007
                ],
                    "enumNames": [
                    "none",
                    "low",
                    "moderate",
                    "high",
                    "extremely high"
                ]
            }
        },
            "title": "referral details",
            "description": "SECTION 1: about the person",
            "type": "object",
            "properties": {
            "parents/carers name (under 25s only)": {
                "type": "string"
            },
            "relationship": {
                "$ref": "#/definitions/custom_relationship"
            },
            "referee mobile": {
                "type": "string"
            },
            "preferred language (if not English)": {
                "type": "string"
            },
            "adults children and young people in the household (name age relationship)": {
                "type": "string"
            },
            "britishCitizen": {
                "title": "british citizen",
                    "$ref": "#/definitions/custom_yesno"
            },
            "legalUK": {
                "title": "if you are not a british citizen do you have a legal right to remain in the UK",
                    "$ref": "#/definitions/custom_yesno"
            },
            "UE": {
                "title": "are you an EU member citizen",
                    "$ref": "#/definitions/custom_yesno"
            }
        }}
    ,
        "uiSchema":
        {
            "adults children and young people in the household (name age relationship)": {
            "ui:widget": "textarea"
        },
            "relationship": {
            "ui:widget": "radio",
                "ui:options": {
                "inline": true
            }
        },
            "britishCitizen": {
            "ui:widget": "radio",
                "ui:options": {
                "inline": true
            }
        },
            "legalUK": {
            "ui:widget": "radio",
                "ui:options": {
                "inline": true
            }
        },
            "UE": {
            "ui:widget": "radio",
                "ui:options": {
                "inline": true
            }
        }
    }}}
]]></column>

        </insert>
        <insert tableName="st_referralaspectsettings">
            <column name="id" valueNumeric="331"/>
            <column name="name" value="formDefinition"/>
            <column name="value" value="00200000-0000-babe-babe-dadafee1600d"/>
            <column name="referralaspectId" valueNumeric="32"/>
            <column name="servicetypeId" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1222-demandschedule-parent" author="adamjhamer">
        <addColumn tableName="appointmentschedules">
            <column name="parentScheduleId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_sched_parent"
                                 baseColumnNames="parentScheduleId" baseTableName="appointmentschedules"
                                 referencedColumnNames="id" referencedTableName="appointmentschedules"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="DEV-1411-demandschedule-fortasks" author="adamjhamer">
        <addColumn tableName="appointmentschedules">
            <column name="parameters" type="CLOB">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1461-referralaspects-verifyWork" author="nealeu">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="142"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="DEDICATED_TASK"/>
            <column name="friendlyName" value="verifyWork"/>
            <column name="name" value="verifyWork"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1503-strasettings-outcomeId" author="nealeu">
        <addColumn tableName="st_referralaspectsettings">
            <column name="outcomeId" type="BIGINT"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1503-strasettings-outcomeIdFK" author="nealeu">
        <addForeignKeyConstraint baseTableName="st_referralaspectsettings" baseColumnNames="outcomeId"
                                 constraintName="fk_stras_outcomeId"
                                 referencedTableName="outcomes" referencedColumnNames="id"/>
    </changeSet>
    <changeSet id="DEV-1503-deduplicate-strasettings" author="nealeu">
        <delete tableName="st_referralaspectsettings">
            <where>id in (22,50)</where>
        </delete>
    </changeSet>


    <!-- Would be nice in security-domain, but SchemaVersion defaults to 1.2 and pulls in 1.1 and that first loads -->
    <!-- config and security domains (since they are independent) - and we need to rely on existing users/contacts tables -->
    <!-- which we created as part of portable baseline and so comes later - the baseline could come before, allowing -->
    <!-- us to continue using separate domains, or the better solution would be to extract the relevant baseline into domains -->
    <include file="classpath:sql/1.2-changes/general-domain/004-dataImportUser.xml"/>

    <!-- Would be nice in building-domain, but SchemaVersion defaults to 1.2 and pulls in 1.1 and that first loads -->
    <!-- config and security domains (since they are independent) - and we need to rely on bldg_fixed tables in general domain -->
    <!-- we would need to extract the sql to separate domains -->
    <changeSet id="DEV-1496-bldg_externalRef" author="adamjhamer">
        <addColumn tableName="bldg_fixed">
            <column name="externalRef" type="VARCHAR(255)"/>
            <column name="externalSource" type="VARCHAR(20)"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1496-bldg_externalRef-uniq" author="adamjhamer">
        <addUniqueConstraint tableName="bldg_fixed" columnNames="externalRef" constraintName="uniq_bldg_externalRef"/>
    </changeSet>
    <changeSet id="DEV-1496-bldg_externalRef-fk" author="adamjhamer">
        <addForeignKeyConstraint constraintName="fk_bldg_extrnalsystem_name"
                                 baseColumnNames="externalSource" baseTableName="bldg_fixed"
                                 referencedColumnNames="name" referencedTableName="cfg_externalsystem"/>
    </changeSet>

    <changeSet id="DEV-1373-evdnc_form_snapshot-formDefinitionUuid" author="adamjhamer">
        <addColumn tableName="evdnc_form_snapshots">
            <column name="formDefinitionUuid" type="CHAR(36)"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1373-evdnc_form_snapshot-formDefinitionUuid-fk" author="adamjhamer">
        <addForeignKeyConstraint
                constraintName="fk_formdefinitions_uuid"
                baseTableName="evdnc_form_snapshots" baseColumnNames="formDefinitionUuid"
                referencedTableName="cfg_form_definitions" referencedColumnNames="uuid" />
    </changeSet>

    <changeSet id="DEV-1503-support.showActionComponents-remaining" author="adamjhamer">
        <insert tableName="cfg_feature">
            <column name="name" value="support.showActionComponents.status"/>
            <column name="description" value="Determines if the status shows on each SMART step"/>
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
        </insert>
        <insert tableName="cfg_feature">
            <column name="name" value="support.showActionComponents.link"/>
            <column name="description" value="Determines if the link shows on each SMART step"/>
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1417-no-fixed-abode-address" author="nealeu">
        <insert tableName="bldg_addresses">
            <column name="id" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
            <column name="line1" value="No fixed abode"/>
            <column name="postcode" value="NFA"/>
            <column name="uuid" value="11100001-0000-babe-babe-dadafee1600d"/>
        </insert>
        <insert tableName="bldg_addresses">
            <column name="id" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
            <column name="line1" value="Withheld"/>
            <column name="postcode" value="WHLD"/>
            <column name="uuid" value="11100002-0000-babe-babe-dadafee1600d"/>
        </insert>
        <insert tableName="bldg_addresses">
            <column name="id" valueNumeric="3"/>
            <column name="version" valueNumeric="0"/>
            <column name="line1" value="Unknown"/>
            <column name="postcode" value="UNK"/>
            <column name="uuid" value="11100003-0000-babe-babe-dadafee1600d"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1373-remove-poc-customForms" author="adamjhamer">
        <delete tableName="referralaspects">
            <where>id in (112,113,114,115,116)</where>
        </delete>
    </changeSet>
    <changeSet id="DEV-1373-remove-poc-customForms-myProfile" author="adamjhamer">
        <delete tableName="referralaspects">
            <where>id in (110)</where>
        </delete>
    </changeSet>
    <changeSet id="DEV-1524-add-disabled-to-choices" author="adamjhamer">
        <addColumn tableName="questionanswerchoices">
            <column name="disabled" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- deadweight is in the svb, so we include - but it may not be used -->
    <changeSet id="DEV-1539-hact-svb-deadweight" author="adamjhamer" context="acceptanceTests,hact">
        <!-- its been added to one customer already, so check -->
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="hactsocialvaluebank" columnName="deadweight"/>
            </not>
        </preConditions>
        <addColumn tableName="hactsocialvaluebank">
            <column name="deadweight" type="DECIMAL(9,2)"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1539-hact-svb-length" author="adamjhamer" context="acceptanceTests,hact">
        <modifyDataType tableName="hactsocialvaluebank" columnName="outcomeCategory" newDataType="VARCHAR(50)"/>
    </changeSet>
    <changeSet id="DEV-1539-hact-svb" author="adamjhamer" context="acceptanceTests,hact">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from hactsocialvaluebank</sqlCheck>
        </preConditions>
        <sqlFile path="classpath:sql/1.2-changes/general-domain/hact-svb.sql"/>
    </changeSet>
    <changeSet id="DEV-1539-hact-surveys3" author="adamjhamer" context="acceptanceTests,hact">
        <preConditions onFail="MARK_RAN">
            <!-- previous preCondition is incorporated in this new preCondition:
                <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml" author="adamjhamer" id="DEV-1539-hact-surveys"/>
            -->
            <sqlCheck expectedResult="0">select count(1) from questiongroups where name='hact' and id=31</sqlCheck>
        </preConditions>
        <sqlFile path="classpath:sql/1.2-changes/general-domain/hact-surveys.sql"/>
    </changeSet>
    <changeSet id="DEV-1539-hact-surveys-correct-original" author="adamjhamer" context="acceptanceTests,hact">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml" author="adamjhamer" id="DEV-1539-hact-surveys"/>
        </preConditions>

        <update tableName="questiongroups_questions">
            <column name="orderby" valueNumeric="25"/>
            <where>questiongroupId=31 and questionId=376 and orderby=0</where>
        </update>
        <update tableName="questiongroups_questions">
            <column name="orderby" valueNumeric="26"/>
            <where>questiongroupId=31 and questionId=377 and orderby=0</where>
        </update>
        <update tableName="questiongroups_questions">
            <column name="orderby" valueNumeric="27"/>
            <where>questiongroupId=31 and questionId=378 and orderby=0</where>
        </update>
        <update tableName="questiongroups_questions">
            <column name="orderby" valueNumeric="28"/>
            <where>questiongroupId=31 and questionId=379 and orderby=0</where>
        </update>
        <update tableName="questiongroups_questions">
            <column name="orderby" valueNumeric="29"/>
            <where>questiongroupId=31 and questionId=380 and orderby=0</where>
        </update>
        <update tableName="questiongroups_questions">
            <column name="orderby" valueNumeric="30"/>
            <where>questiongroupId=31 and questionId=381 and orderby=0</where>
        </update>
    </changeSet>
    <changeSet id="DEV-1539-hact-surveys-correct2" author="adamjhamer" context="acceptanceTests,hact">
        <!-- if we've ran the last dodgy code -->
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml" author="adamjhamer" id="DEV-1539-hact-surveys2"/>
        </preConditions>
        <!--insert into hactoutcomeevidencesurvey (id, version, hactOutcomeDefCode, questionDefId) values (18, 0, 'ENV1607', 365);-->
        <insert tableName="hactoutcomeevidencesurvey">
            <column name="id" valueNumeric="18"/>
            <column name="version" valueNumeric="0"/>
            <column name="hactOutcomeDefCode" value="ENV1607"/>
            <column name="questionDefId" valueNumeric="365"/>
        </insert>
        <!--insert into hactsurvey_valuableanswers (hactSurveyId, questionanswerchoiceId) values (18, 380), (18, 381);-->
        <insert tableName="hactsurvey_valuableanswers">
            <column name="hactSurveyId" valueNumeric="18"/>
            <column name="questionanswerchoiceId" valueNumeric="380"/>
        </insert>
        <insert tableName="hactsurvey_valuableanswers">
            <column name="hactSurveyId" valueNumeric="18"/>
            <column name="questionanswerchoiceId" valueNumeric="381"/>
        </insert>
        <!--insert into questionanswerchoices (id ,version, displayImage, displayValue,hiddenDefault,value) values (413, 0, null, 'Not applicable', 0, 8);-->
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="413"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage" valueComputed="NULL"/>
            <column name="displayValue" value="Not applicable"/>
            <column name="hiddenDefault" valueNumeric="0"/>
            <column name="value" valueNumeric="8"/>
        </insert>
        <!--insert into questions_questionanswrchoices (questionId, questionanswerchoiceId) values (367, 413);-->
        <insert tableName="questions_questionanswrchoices">
            <column name="questionid" valueNumeric="367"/>
            <column name="questionanswerchoiceId" valueNumeric="413"/>
        </insert>
        <!--insert into questionanswerchoices (id ,version, displayImage, displayValue,hiddenDefault,value) values (373, 0, null, 'Not applicable', 0, 5);-->
        <insert tableName="questionanswerchoices">
            <column name="id" valueNumeric="414"/>
            <column name="version" valueNumeric="0"/>
            <column name="displayImage" valueComputed="NULL"/>
            <column name="displayValue" value="Not applicable"/>
            <column name="hiddenDefault" valueNumeric="0"/>
            <column name="value" valueNumeric="5"/>
        </insert>
        <!--insert into questions_questionanswrchoices (questionId, questionanswerchoiceId) values (369, 414);-->
        <insert tableName="questions_questionanswrchoices">
            <column name="questionid" valueNumeric="369"/>
            <column name="questionanswerchoiceId" valueNumeric="414"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1539-hact-mapping-test" author="adamjhamer" context="acceptanceTests">
        <insert tableName="hactoutcomemappings">
            <column name="id" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
            <column name="hactOutcomeDefCode" value="EMP1401"/>
            <column name="actionDefId" valueNumeric="119"/> <!-- first actionDefId in accommodation's 'use of time' -->
        </insert>
    </changeSet>

    <changeSet id="DEV-1626-hact-mapping-test-hactOnlyInnerJoin" author="adamjhamer" context="acceptanceTests">
        <insert tableName="hactoutcomemappings">
            <column name="id" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
            <column name="hactOutcomeDefCode" value="FIN1605"/>
            <column name="actionDefId" valueNumeric="113"/> <!-- economic wellbeing -> Support to maximise income eg - correct welfare benefits, application for grants etc. -->
        </insert>
    </changeSet>

    <changeSet id="DEV-1541-formdefs-ensureVersion" author="adamjhamer">
        <update tableName="cfg_form_definitions">
            <column name="version" valueNumeric="0"/>
            <where>version is null</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1557-ensureAcceptIfStarted" author="adamjhamer">
        <update tableName="referrals">
            <!-- ignore anything related to signposted as we won't include those cases due to 'decisionMadeOn is null' -->

            <!-- ignore anything related to exit as they may be exited but we still want to say they are accepted -->

            <!-- ignore anything related to 'appropriate referral' columns as we don't want to override status here -->
            <!--  - if its not set this won't cause a major problem and could be helpful in identifying this changeset -->
            <!--  - this is acceptedReferral and decisionReferralMadeOn -->

            <column name="decision" valueBoolean="true"/>
            <column name="acceptedOnService" valueBoolean="true"/>
            <column name="decisionMadeOn" valueComputed="receivingServiceDate"/> <!-- best we can do without loading the date of the command -->

            <!-- decisionMadeOn is for 'accept on service' or any rejection, so will be blank for those who have 'start on service' without 'accept on service' or rejected -->
            <where>decisionMadeOn is null and acceptedOnService=0 and receivingServiceDate is not null</where>
        </update>
    </changeSet>

    <changeSet id="DEV-283-relationshipAsListDef" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="relationshipToPrimaryId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_referralsrel_lists" baseTableName="referrals"
                                 baseColumnNames="relationshipToPrimaryId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <changeSet id="DEV-283-migrate-to-relationshipListDef2" author="adamjhamer">
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="161"/>
            <where>relationshipToPrimary='father' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="162"/>
            <where>relationshipToPrimary='mother' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="163"/>
            <where>relationshipToPrimary='grandmother' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="164"/>
            <where>relationshipToPrimary='grandfather' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="165"/>
            <where>relationshipToPrimary='step father' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="166"/>
            <where>relationshipToPrimary='step mother' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="167"/>
            <where>relationshipToPrimary='guardian' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="168"/>
            <where>relationshipToPrimary='partner' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="169"/>
            <where>relationshipToPrimary='foster mother' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="170"/>
            <where>relationshipToPrimary='foster father' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="171"/>
            <where>relationshipToPrimary='aunt' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="172"/>
            <where>relationshipToPrimary='uncle' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="173"/>
            <where>relationshipToPrimary='brother' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="174"/>
            <where>relationshipToPrimary='sister' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="175"/>
            <where>relationshipToPrimary='step brother' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="176"/>
            <where>relationshipToPrimary='step sister' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="177"/>
            <where>relationshipToPrimary='half brother' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="178"/>
            <where>relationshipToPrimary='half sister' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="179"/>
            <where>relationshipToPrimary='cousin' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="180"/>
            <where>relationshipToPrimary='carer' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="181"/>
            <where>relationshipToPrimary='son' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="182"/>
            <where>relationshipToPrimary='daughter' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="183"/>
            <where>relationshipToPrimary='step son' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="184"/>
            <where>relationshipToPrimary='step daughter' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="185"/>
            <where>relationshipToPrimary='foster brother' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="186"/>
            <where>relationshipToPrimary='foster sister' and relationshipToPrimaryId is null</where>
        </update>
        <update tableName="referrals">
            <column name="relationshipToPrimaryId" valueNumeric="187"/>
            <where>relationshipToPrimary='ex partner' and relationshipToPrimaryId is null</where>
        </update>
    </changeSet>

    <changeSet id="z3477-unset-dataprotectionstatus" author="adamjhamer">
        <update tableName="referrals">
            <column name="dataProtectionStatus" valueComputed="NULL"/>
            <where>dataProtectionSigned is null and dataProtectionStatus=0</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1576-referralaspects-agreement4" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="143"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="AGREEMENT"/>
            <column name="friendlyName" value="agreement4"/>
            <column name="name" value="agreement4"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1576-add_svcrec_agreement4_signature_uuids" author="adamjhamer">
        <addColumn tableName="servicerecipients">
            <column name="agreement4SignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1576-add_agreement4_date" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="agreement4signed" type="DATETIME"/>
        </addColumn>
        <addColumn tableName="referrals">
            <column name="agreement4Status" type="BOOLEAN" defaultValue="NULL">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1576-referralaspects-agreement5" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="144"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="AGREEMENT"/>
            <column name="friendlyName" value="agreement5"/>
            <column name="name" value="agreement5"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1576-add_svcrec_agreement5_signature_uuids" author="adamjhamer">
        <addColumn tableName="servicerecipients">
            <column name="agreement5SignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1576-add_agreement5_date" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="agreement5signed" type="DATETIME"/>
        </addColumn>
        <addColumn tableName="referrals">
            <column name="agreement5Status" type="BOOLEAN"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1576-referralaspects-agreement6" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="145"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="AGREEMENT"/>
            <column name="friendlyName" value="agreement6"/>
            <column name="name" value="agreement6"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1576-add_svcrec_agreement6_signature_uuids" author="adamjhamer">
        <addColumn tableName="servicerecipients">
            <column name="agreement6SignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1576-add_agreement6_date" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="agreement6signed" type="DATETIME"/>
        </addColumn>
        <addColumn tableName="referrals">
            <column name="agreement6Status" type="BOOLEAN"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1576-referralaspects-agreement7" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="146"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="AGREEMENT"/>
            <column name="friendlyName" value="agreement7"/>
            <column name="name" value="agreement7"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1576-add_svcrec_agreement7_signature_uuids" author="adamjhamer">
        <addColumn tableName="servicerecipients">
            <column name="agreement7SignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1576-add_agreement7_date" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="agreement7signed" type="DATETIME"/>
        </addColumn>
        <addColumn tableName="referrals">
            <column name="agreement7Status" type="BOOLEAN"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1576-referralaspects-agreement8" author="adamjhamer">
        <insert tableName="referralaspects">
            <column name="id" valueNumeric="147"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="AGREEMENT"/>
            <column name="friendlyName" value="agreement8"/>
            <column name="name" value="agreement8"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1576-add_svcrec_agreement8_signature_uuids" author="adamjhamer">
        <addColumn tableName="servicerecipients">
            <column name="agreement8SignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1576-add_agreement8_date" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="agreement8signed" type="DATETIME"/>
        </addColumn>
        <addColumn tableName="referrals">
            <column name="agreement8Status" type="BOOLEAN"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1610-rename-contactcommands" author="adamjhamer">
        <renameTable oldTableName="contactcommands" newTableName="cont_commands"/>
    </changeSet>

    <changeSet id="DEV-1597-event-add-contactId" author="adamjhamer">
        <addColumn tableName="events">
            <column name="contactId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseTableName="events" baseColumnNames="contactId" constraintName="events_contactid"
                                 referencedTableName="contacts" referencedColumnNames="id"/>
    </changeSet>
    <changeSet id="DEV-1597-eventrecurring-add-contactId" author="adamjhamer">
        <addColumn tableName="eventsrecurring">
            <column name="contactId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseTableName="eventsrecurring" baseColumnNames="contactId" constraintName="eventsrec_contactid"
                                 referencedTableName="contacts" referencedColumnNames="id"/>
    </changeSet>

    <!-- auto increment equivalent for surrogate id column -->
    <!--
    <changeSet id="DEV-1517-referral-contacts-surrogateId-oracle" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="oracle"/>
        </preConditions>
        <sql>
            UPDATE referrals_contacts SET id = ROWNUM;
        </sql>
    </changeSet>
    <changeSet id="DEV-1517-referral-contacts-surrogateId-mssql" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql"/>
        </preConditions>
        <sql>
            WITH t_rownum AS
            (
            SELECT *, ROW_NUMBER() as c_rownum
            FROM dbo.referrals_contacts
            )
            UPDATE t_rownum SET id = c_rownum
        </sql>
    </changeSet>
    <changeSet id="DEV-1517-referral-contacts-surrogateId-mysql" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql"/>
        </preConditions>
        <sql>
            update referrals_contacts set id=@id:=@id+1;
        </sql>
    </changeSet>

    <changeSet id="DEV-1517-referral-contacts-surrogateId-pk" author="adamjhamer">
        <addPrimaryKey tableName="referrals_contacts" columnNames="id"/>
    </changeSet>
    -->

    <changeSet id="DEV-1639-referrals-contacts-cId-case" author="adamjhamer" dbms="!oracle">
        <renameColumn tableName="referrals_contacts" oldColumnName="contactid" newColumnName="contactId" columnDataType="BIGINT"/>
    </changeSet>

    <changeSet id="DEV-1639-referral-contacts-add_srId" author="adamjhamer">
        <addColumn tableName="referrals_contacts">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
        <update tableName="referrals_contacts">
            <column name="serviceRecipientId"
                    valueComputed="(SELECT serviceRecipientId FROM referrals WHERE referrals.id = referrals_contacts.referralid)"/>
        </update>
        <addNotNullConstraint tableName="referrals_contacts" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-1639-referral-contacts-change-pk" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="referrals_contacts" constraintName="FKD622BDFC581BA917"/>
        <dropForeignKeyConstraint baseTableName="referrals_contacts" constraintName="FKD622BDFC2DAEC673"/>
        <dropPrimaryKey tableName="referrals_contacts"/>
        <dropNotNullConstraint tableName="referrals_contacts" columnName="referralid" columnDataType="BIGINT"/>
        <addPrimaryKey tableName="referrals_contacts" columnNames="serviceRecipientId, contactId"/>
        <addForeignKeyConstraint constraintName="fk_svcrec_contacts_cId"
                                 baseTableName="referrals_contacts" baseColumnNames="contactId"
                                 referencedTableName="contacts" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
        <addForeignKeyConstraint constraintName="fk_svcrec_contacts_srId"
                                 baseTableName="referrals_contacts" baseColumnNames="serviceRecipientId"
                                 referencedTableName="servicerecipients" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
    </changeSet>
    <changeSet id="DEV-1639-referrals-contacts-rename" author="adamjhamer">
        <renameTable oldTableName="referrals_contacts" newTableName="svcrec_contacts"/>
    </changeSet>
    <!-- don't drop referralId until we remove Referral.contacts
    <changeSet id="DEV-1639-referral-contacts-drop-referralId" author="adamjhamer">
        <dropColumn tableName="referrals_contacts" columnName="referralid"/>
    </changeSet>
    -->

    <changeSet id="DEV-1639-svcrec-contacts-assocType" author="adamjhamer">
        <createTable tableName="svcrec_contacts_assocs">
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="contactId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="associatedTypeId" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey columnNames="serviceRecipientId, contactId, associatedTypeId" tableName="svcrec_contacts_assocs"/>
        <addForeignKeyConstraint constraintName="fk_svcrec_assoc_listdef" baseTableName="svcrec_contacts_assocs"
                                 baseColumnNames="associatedTypeId" referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseColumnNames="contactId" baseTableName="svcrec_contacts_assocs" constraintName="fk_svcrec_assoc_cid"
                                 referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"/>
        <addForeignKeyConstraint constraintName="fk_svcrec_assoc_srId"
                                 baseTableName="svcrec_contacts_assocs" baseColumnNames="serviceRecipientId"
                                 referencedTableName="servicerecipients" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencesUniqueColumn="true"/>
    </changeSet>
    <changeSet id="DEV-1639-svcrec-contacts-assocType-migration" author="adamjhamer">
        <sql>
            insert into svcrec_contacts_assocs (servicerecipientId, contactId, associatedTypeId)
            select rc.serviceRecipientId,rc.contactId,rc.associatedTypeId
            from svcrec_contacts rc
            where rc.associatedTypeId is not null
        </sql>
    </changeSet>
    <!-- don't risk dropping in the same release - in case we have issues with 'insert' above
    <changeSet id="DEV-1639-referral-contacts-drop-assocType" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="referrals_contacts" constraintName="fk_referralcontacts_listdef"/>
        <dropColumn tableName="referrals_contacts" columnName="associatedTypeId"/>
    </changeSet>
    -->


    <!-- MIGRATE IDNAME -->

    <changeSet id="DEV-1674-ensure-idname-sequence" author="nealeu">
        <validCheckSum>7:2ffeb8124937b4b8121b103125b1ddf8</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">SELECT count(*) from hibernate_sequences where sequence_name='id_name'</sqlCheck>
        </preConditions>
        <comment>Add id_name sequence if it doesn't exist</comment>
        <insert tableName="hibernate_sequences">
            <column name="sequence_name" value="id_name"/>
            <!-- set to AbstractIntKeyedEntity initialValue -->
            <column name="next_val" valueNumeric="1000"/>
        </insert>
    </changeSet>

    <!-- ethnicorigins has:
        <column defaultValueNumeric="0" name="id" type="BIGINT">
            <constraints nullable="false" primaryKey="true"/>
        </column>
     - where mssql creates explicit constraints:
        - CONSTRAINT [PK_ETHNICORIGINS] PRIMARY KEY CLUSTERED ([id] ASC)
        - '[id] [bigint] NOT NULL CONSTRAINT [DF_ethnicorigins_id]  DEFAULT ((0))'
     - We need to drop these constraints before modifying the data type.
     - We can use dropDefaultValue to drop the default constraint.
     - We need to use dropPrimaryKey to drop the primary key constraint.
    -->
    <changeSet id="DEV-1674-listDef-ethnicorigins-prep-fk" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml" author="adamjhamer" id="DEV-1674-listDef-ethnicorigins-prep"/>
            </not>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="clientdetails" constraintName="FK12AD3417A88FE951"/>
        <dropForeignKeyConstraint baseTableName="workers" constraintName="FK5AE81CB5A88FE951"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-ethnicorigins-prep-mssql-remove" author="adamjhamer" dbms="!mysql">
        <dropDefaultValue tableName="ethnicorigins" columnName="id"/>
        <dropPrimaryKey tableName="ethnicorigins"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-ethnicorigins-prep2" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml" author="adamjhamer" id="DEV-1674-listDef-ethnicorigins-prep"/>
            </not>
        </preConditions>
        <modifyDataType tableName="ethnicorigins" columnName="id" newDataType="INT"/>
        <modifyDataType tableName="clientdetails" columnName="ethnicoriginsId" newDataType="INT"/>
        <modifyDataType tableName="workers" columnName="ethnicoriginsId" newDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-ethnicorigins-prep-mssql-reinstate" author="adamjhamer" dbms="!mysql">
        <addNotNullConstraint tableName="ethnicorigins" columnName="id" columnDataType="INT"/>
        <addPrimaryKey tableName="ethnicorigins" columnNames="id"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-ethnicorigins-migrationId" author="adamjhamer">
        <addColumn tableName="ethnicorigins">
            <column name="migrationId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <!-- STEP 2: fill that sequence -->
    <changeSet id="DEV-1674-listDef-ethnicOrigins-enable" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateHibernateSequenceChange"
                      tableName="ethnicorigins" uniqueKeyColumns="id"
                      newIdColumn="migrationId"
                      sequenceName="id_name"
        />
    </changeSet>
    <!-- select * from ethnicorigins group by name having count(*) > 1 -->
    <changeSet id="DEV-1674-listDef-ethnicOrigins-copy-rows" author="adamjhamer">
        <!-- ethnicOrigin businessKey base data is ethnicOrigin-id -->
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, businessKey)
            SELECT migrationId, 0, 'ethnicOrigin', name, concat('ethnicOrigin-', migrationId) FROM ethnicorigins;
        </sql>
    </changeSet>
    <changeSet id="DEV-1674-listDef-ethnicorigins-update-refs" author="adamjhamer">
        <update tableName="clientdetails">
            <column name="ethnicoriginsid" valueComputed="(SELECT eo.migrationId FROM ethnicorigins eo WHERE eo.id = clientdetails.ethnicoriginsid)"/>
        </update>
        <update tableName="workers">
            <column name="ethnicoriginsid" valueComputed="(SELECT eo.migrationId FROM ethnicorigins eo WHERE eo.id = workers.ethnicoriginsid)"/>
        </update>
        <addForeignKeyConstraint baseTableName="clientdetails" baseColumnNames="ethnicoriginsid" constraintName="FK_clientdet_ethId" referencedTableName="cfg_list_definitions"
                                 referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="workers" baseColumnNames="ethnicoriginsid" constraintName="FK_workdet_ethId" referencedTableName="cfg_list_definitions"
                                 referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-languages-prep-fk" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml" author="adamjhamer" id="DEV-1674-listDef-languages-prep"/>
            </not>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="clientdetails" constraintName="FK12AD341760C8AFD1"/>
        <dropForeignKeyConstraint baseTableName="workers" constraintName="FK5AE81CB560C8AFD1"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-languages-prep-mssql-remove" author="adamjhamer" dbms="!mysql">
        <dropDefaultValue tableName="languages" columnName="id"/>
        <dropPrimaryKey tableName="languages"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-languages-prep2" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml" author="adamjhamer" id="DEV-1674-listDef-languages-prep"/>
            </not>
        </preConditions>
        <modifyDataType tableName="languages" columnName="id" newDataType="INT"/>
        <modifyDataType tableName="clientdetails" columnName="languagesId" newDataType="INT"/>
        <modifyDataType tableName="workers" columnName="languagesId" newDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-languages-prep-mssql-reinstate" author="adamjhamer" dbms="!mysql">
        <addNotNullConstraint tableName="languages" columnName="id" columnDataType="INT"/>
        <addPrimaryKey tableName="languages" columnNames="id"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-languages-migrationId" author="adamjhamer">
        <addColumn tableName="languages">
            <column name="migrationId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <!-- STEP 2: fill that sequence -->
    <changeSet id="DEV-1674-listDef-languages-populate-seq" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateHibernateSequenceChange"
                      tableName="languages" uniqueKeyColumns="id"
                      newIdColumn="migrationId"
                      sequenceName="id_name"
        />
    </changeSet>
    <changeSet id="DEV-1674-listDef-languages-migrationID-add-3" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml"
                                   author="adamjhamer"
                                   id="DEV-1674-listDef-languages-copy-rows2"/>
            </not>
        </preConditions>
        <update tableName="languages">
            <column name="migrationId" valueComputed="migrationId + 3"/>
        </update>
        <update tableName="hibernate_sequences">
            <column name="next_val" valueComputed="next_val + 3"/>
            <where>sequence_name='id_name'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1674-listDef-languages-copy-rows2" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml" author="adamjhamer" id="DEV-1674-listDef-languages-copy-rows"/>
            </not>
        </preConditions>
        <!-- language businessKey base data is name -->
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, businessKey) SELECT migrationId, 0, 'language', name, name FROM languages;
        </sql>
    </changeSet>
    <changeSet id="DEV-1674-listDef-languages-update-refs" author="adamjhamer">
        <update tableName="clientdetails">
            <column name="languagesid" valueComputed="(SELECT eo.migrationId FROM languages eo WHERE eo.id = clientdetails.languagesid)"/>
        </update>
        <update tableName="workers">
            <column name="languagesid" valueComputed="(SELECT eo.migrationId FROM languages eo WHERE eo.id = workers.languagesid)"/>
        </update>
        <addForeignKeyConstraint baseTableName="clientdetails" baseColumnNames="languagesid"
                                 constraintName="FK_clientdet_langId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="workers" baseColumnNames="languagesid"
                                 constraintName="FK_workdet_langId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-religions-prep-fk" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml" author="adamjhamer" id="DEV-1674-listDef-religions-prep"/>
            </not>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="clientdetails" constraintName="FK12AD3417BF94B531"/>
        <dropForeignKeyConstraint baseTableName="workers" constraintName="FK5AE81CB5BF94B531"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-religions-prep-mssql-remove" author="adamjhamer" dbms="!mysql">
        <dropDefaultValue tableName="religions" columnName="id"/>
        <dropPrimaryKey tableName="religions"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-religions-prep2" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml" author="adamjhamer" id="DEV-1674-listDef-religions-prep"/>
            </not>
        </preConditions>
        <modifyDataType tableName="religions" columnName="id" newDataType="INT"/>
        <modifyDataType tableName="clientdetails" columnName="religionsId" newDataType="INT"/>
        <modifyDataType tableName="workers" columnName="religionsId" newDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-religions-prep-mssql-reinstate" author="adamjhamer" dbms="!mysql">
        <addNotNullConstraint tableName="religions" columnName="id" columnDataType="INT"/>
        <addPrimaryKey tableName="religions" columnNames="id"/>
    </changeSet>

    <changeSet id="DEV-1674-listDef-religions-migrationId" author="adamjhamer">
        <addColumn tableName="religions">
            <column name="migrationId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <!-- STEP 2: fill that sequence -->
    <changeSet id="DEV-1674-listDef-religions-populate-seq" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateHibernateSequenceChange"
                      tableName="religions" uniqueKeyColumns="id"
                      newIdColumn="migrationId"
                      sequenceName="id_name"
        />
    </changeSet>
    <changeSet id="DEV-1674-listDef-religions-copy-rows" author="adamjhamer">
        <!-- religion businessKey base data is religion-id -->
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, businessKey)
            SELECT migrationId, 0, 'religion', name, concat('religion-', migrationId) FROM religions;
        </sql>
    </changeSet>
    <changeSet id="DEV-1674-listDef-religions-update-refs" author="adamjhamer">
        <update tableName="clientdetails">
            <column name="religionsid" valueComputed="(SELECT eo.migrationId FROM religions eo WHERE eo.id = clientdetails.religionsid)"/>
        </update>
        <update tableName="workers">
            <column name="religionsid" valueComputed="(SELECT eo.migrationId FROM religions eo WHERE eo.id = workers.religionsid)"/>
        </update>
        <addForeignKeyConstraint baseTableName="clientdetails" baseColumnNames="religionsid"
                                 constraintName="FK_clientdet_religionId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="workers" baseColumnNames="religionsid"
                                 constraintName="FK_workdet_religionId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-1685-svrec_commands-scheduleId" author="adamjhamer">
        <addColumn tableName="svcrec_commands">
            <column name="scheduleId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1685-svcrec_commands-scheduleId-archive" author="adamjhamer">
        <addColumn tableName="svcrec_commands_archive">
            <column name="scheduleId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1685-svcrec_commands-scheduleId-index" author="adamjhamer">
        <createIndex tableName="svcrec_commands" indexName="idx_svcrec_cmd_schedid">
            <column name="serviceRecipientId"/>
            <column name="scheduleId"/>
            <column name="created"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-1685-svcrec_commands-previousScheduleId" author="adamjhamer">
        <addColumn tableName="appointmentschedules">
            <column name="previousScheduleId" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseTableName="appointmentschedules" baseColumnNames="previousScheduleId" constraintName="fk_as_prevschedId" referencedTableName="appointmentschedules" referencedColumnNames="id"/>
    </changeSet>


    <!-- this is basically part of a temporal database in disguise -->
    <changeSet id="DEV-1703-addressHistory" author="adamjhamer">
        <createTable tableName="addresshistory">
            <column defaultValueNumeric="0" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="serviceRecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="addressLocationId" type="INT"/>
            <column name="buildingLocationId" type="INT"/>
            <column name="validFrom" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="validTo" type="DATETIME"/>
        </createTable>
    </changeSet>

    <changeSet id="DEV-1703-addressHistory-idx" author="adamjhamer">
        <createIndex unique="true" tableName="addresshistory" indexName="idx_adrhst_srId_vF">
            <column name="serviceRecipientId"/>
            <column name="validFrom"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-1703-addressHistory-contactId" author="adamjhamer">
        <addColumn tableName="addresshistory">
            <column name="contactId" type="BIGINT"/>
        </addColumn>
        <addForeignKeyConstraint baseTableName="addresshistory" baseColumnNames="contactId" constraintName="adrhst_contacts"
                                 referencedTableName="contacts" referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-1703-addressHistory-contactsId-idx" author="adamjhamer">
        <createIndex unique="true" tableName="addresshistory" indexName="idx_adrhst_contId_vF">
            <column name="contactId"/>
            <column name="validFrom"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-1731-cosmo-idx-start-end-float" author="nealeu">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists tableName="cosmo_event_stamp" indexName="cos_es_start_end_float"/>
            </not>
        </preConditions>
        <createIndex tableName="cosmo_event_stamp" indexName="cos_es_start_end_float">
            <column name="startdate"/>
            <column name="enddate"/>
            <column name="isfloating"/>
        </createIndex>
        <dropIndex tableName="cosmo_event_stamp" indexName="IDX_STARTDT"/>
    </changeSet>

    <!-- ran this sql to retain backup -->
    <!-- mysqldump -u root -p <db>> &#45;&#45;set-gtid-purged=OFF referrals > ./client-backups/<customer>/<customer>_referral_with_flags.sql -->
    <changeSet id="DEV-1740-referral-flags-remove" author="adamjhamer">
        <dropColumn tableName="referrals" columnName="alcohol"/>
        <dropColumn tableName="referrals" columnName="drugs"/>
        <dropColumn tableName="referrals" columnName="violence"/>
        <dropColumn tableName="referrals" columnName="domesticViolence"/>
        <dropColumn tableName="referrals" columnName="sexOffender"/>
        <dropColumn tableName="referrals" columnName="arson"/>
        <dropColumn tableName="referrals" columnName="gambling"/>
        <dropColumn tableName="referrals" columnName="children"/>
        <dropColumn tableName="referrals" columnName="life"/>
        <dropColumn tableName="referrals" columnName="anger"/>
        <dropColumn tableName="referrals" columnName="mentalHealth"/>
        <dropColumn tableName="referrals" columnName="emotionalHealth"/>
        <dropColumn tableName="referrals" columnName="physicalHealth"/>
        <dropColumn tableName="referrals" columnName="offender"/>
        <dropColumn tableName="referrals" columnName="learningDifficulty"/>
        <dropColumn tableName="referrals" columnName="substance"/>
        <dropColumn tableName="referrals" columnName="debt"/>
        <dropColumn tableName="referrals" columnName="violenceFromOutside"/>
        <dropColumn tableName="referrals" columnName="asbo"/>
        <dropColumn tableName="referrals" columnName="mobility"/>
        <dropColumn tableName="referrals" columnName="visual"/>
        <dropColumn tableName="referrals" columnName="hearing"/>
        <dropColumn tableName="referrals" columnName="autistic"/>
        <dropColumn tableName="referrals" columnName="chronic"/>
        <dropColumn tableName="referrals" columnName="disabledOther"/>
        <dropColumn tableName="referrals" columnName="disabilityNotDisclosed"/>
        <dropColumn tableName="referrals" columnName="catTeenageParent"/>
        <dropColumn tableName="referrals" columnName="catLoneParent"/>
        <dropColumn tableName="referrals" columnName="catWorkless"/>
        <dropColumn tableName="referrals" columnName="catMinority"/>
        <dropColumn tableName="referrals" columnName="catDisabledFamilies"/>
        <dropColumn tableName="referrals" columnName="catFather"/>
        <dropColumn tableName="referrals" columnName="catTraveller"/>
        <dropColumn tableName="referrals" columnName="catTemporaryAccommodation"/>
        <dropColumn tableName="referrals" columnName="catMigrant"/>
    </changeSet>
    <changeSet id="DEV-1740-referral-flags-eligible-remove" author="adamjhamer">
        <dropColumn tableName="referrals" columnName="eligible"/>
        <dropColumn tableName="referrals" columnName="marac"/>
        <dropColumn tableName="referrals" columnName="mappa"/>
        <dropColumn tableName="referrals" columnName="mhastatus"/>
    </changeSet>
    <changeSet id="DEV-1740-referral-flags-offences-remove" author="adamjhamer">
        <update tableName="referrals">
            <column name="offenceId" valueComputed="NULL"/>
            <where>offenceId is not null</where>
        </update>
        <dropForeignKeyConstraint baseTableName="referrals" constraintName="FKC8E0F876306509AC"/>
        <dropColumn tableName="referrals" columnName="offenceId"/>
        <dropTable tableName="offences"/>
    </changeSet>

    <changeSet id="DEV-1639-remove-svcrec_contacts-cols" author="nealeu">
        <dropForeignKeyConstraint baseTableName="svcrec_contacts" constraintName="fk_referralcontacts_listdef"/>
        <dropColumn tableName="svcrec_contacts" columnName="referralid"/>
        <dropColumn tableName="svcrec_contacts" columnName="associatedTypeId"/>
    </changeSet>

    <!-- foreign keys can't be in evidenceDomainChangeLog when evidence-domain is before generalChangeLog where the tables are created -->
    <include file="classpath:sql/1.2-changes/evidence-domain/002-flags-fk.xml"/>

    <!-- TODO remove servicetypes_threatflags, since we don't enforce the config via referential integrity, just a list def name -->

    <!-- reference a rename here because we don't want to rename all the general changelogs -->
    <include file="classpath:sql/1.2-changes/security-domain/002-groups-rename.xml"/>

    <include file="classpath:sql/1.2-changes/finance-domain/001b-contracts-config.xml"/>
    <include file="classpath:sql/1.2-changes/finance-domain/001b-cmds-fk.xml"/>

    <include file="classpath:sql/1.2-changes/cosmo-domain/001-cosmoDomainChangeLog.xml"/>

    <changeSet id="DEV-1793-recurrenceAllocateMonths" author="nealeu">
        <insert tableName="setting">
            <column name="id" valueNumeric="57"/>
            <column name="version" valueNumeric="0"/>
            <column name="keyname" value="maxRecurringAllocateMonths"/>
            <column name="namespace" value="com.ecco.rota"/>
            <column name="consumedOnStartup" valueBoolean="false"/>
            <column name="keyvalue" value="4"/>
            <column name="description" value="Specifies maximum months for recurring allocate"/>
            <column name="type" value="NUMBER"/>
        </insert>
    </changeSet>
    <!-- *** STOP:
     DO NOT ADD ANYTHING MORE HERE
     - USE A CHANGELOG
     in the correct YEAR folder
     *** -->
</databaseChangeLog>