<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

<!-- update the body with escape xml -->
<changeSet id="DEV-899-repDef-projectcalendar-body6" author="adamjhamer">
    <update tableName="reportdefinitions">
        <column name="body">
            {
            &quot;description&quot;: &quot;&quot;,
            &quot;selectionCriteria&quot;: {
            &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
            &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
            &quot;fetchRelatedEntities&quot;: [
            &quot;referralEvents&quot;
            ],
            &quot;selectorType&quot;: &quot;byStartOfDay&quot;,
            &quot;relativeStartIndex&quot;: 0,
            &quot;relativeEndIndex&quot;: 1
            },
            &quot;stages&quot;: [
            {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
            &quot;analyserType&quot;: &quot;calendarEventsFromReferrals&quot;
            }
            },
            {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
            &quot;analyserType&quot;: &quot;calendarCountsByService&quot;
            }
            },
            {
            &quot;description&quot;: &quot;appointments by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
            {
            &quot;label&quot;: &quot;count&quot;,
            &quot;valuePath&quot;: &quot;count&quot;,
            &quot;renderMode&quot;: &quot;PIE&quot;
            }
            ]
            },
            {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
            &quot;analyserType&quot;: &quot;calendarCountsByType&quot;
            }
            },
            {
            &quot;description&quot;: &quot;appointments by event type&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
            {
            &quot;label&quot;: &quot;count&quot;,
            &quot;valuePath&quot;: &quot;count&quot;,
            &quot;renderMode&quot;: &quot;PIE&quot;
            }
            ]
            },
            {
            &quot;description&quot;: &quot;breakdown of events&quot;,
            &quot;stageType&quot;: &quot;CALENDAREVENT&quot;
            }
            ]
            }
        </column>
        <where>uuid='04800000-0000-babe-babe-dadafee1600d'</where>
    </update>
</changeSet>

<!-- create a placeholder so we only have one definition in the file that we can keep updating -->
<changeSet id="DEV-1059-repDef-tasksIncomplete-create" author="adamjhamer">
    <insert tableName="reportdefinitions">
        <column name="uuid" value="04900000-0000-babe-babe-dadafee1600d"/>
        <column name="version" valueNumeric="0"/>
        <column name="created" valueDate="2019-06-10T00:00:00"/>
        <column name="userId" valueNumeric="1"/>
        <column name="orderby" valueNumeric="45"/>
        <column name="name" value="tasks incomplete"/>
        <column name="body">
            {}
        </column>
    </insert>
</changeSet>
<!-- update the body with escape xml -->
<changeSet id="DEV-1059-repDef-tasksIncomplete-body" author="adamjhamer">
    <update tableName="reportdefinitions">
        <column name="body">
            {
            &quot;description&quot;: &quot;on all uncompleted tasks&quot;,
            &quot;selectionCriteria&quot;: {
            &quot;selectionRootEntity&quot;: &quot;TaskStatus&quot;
            },
            &quot;stages&quot;: [
            {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
            &quot;analyserType&quot;: &quot;filterByNotCompleted&quot;
            }
            },
            {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
            &quot;analyserType&quot;: &quot;groupByDue&quot;
            }
            },
            {
            &quot;description&quot;: &quot;by due date&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
            {
            &quot;label&quot;: &quot;value&quot;,
            &quot;valuePath&quot;: &quot;count&quot;,
            &quot;renderMode&quot;: &quot;PIE&quot;
            }
            ]
            },
            {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
            &quot;analyserType&quot;: &quot;groupByService&quot;
            }
            },
            {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
            {
            &quot;label&quot;: &quot;value&quot;,
            &quot;valuePath&quot;: &quot;count&quot;,
            &quot;renderMode&quot;: &quot;PIE&quot;
            }
            ]
            },
            {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
            &quot;analyserType&quot;: &quot;groupByAssignee&quot;
            }
            },
            {
            &quot;description&quot;: &quot;by assignee&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
            {
            &quot;label&quot;: &quot;value&quot;,
            &quot;valuePath&quot;: &quot;count&quot;,
            &quot;renderMode&quot;: &quot;PIE&quot;
            }
            ]
            },
            {
            &quot;description&quot;: &quot;breakdown of tasks&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
            &quot;className&quot;: &quot;TaskStatusOnlyColumns&quot;,
            &quot;columns&quot;: [
            &quot;srId&quot;,
            &quot;r-id&quot;,
            &quot;c-id&quot;,
            &quot;client&quot;,
            &quot;service&quot;,
            &quot;project&quot;,
            &quot;task&quot;,
            &quot;description&quot;,
            &quot;due&quot;,
            &quot;assignee&quot;
            ]
            }
            }
            ]
            }
        </column>
        <where>uuid='04900000-0000-babe-babe-dadafee1600d'</where>
    </update>
</changeSet>


    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <changeSet id="DEV-1741-repDef-hact-mgt-reports" author="adamjhamer" context="acceptanceTests,hact">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="05000000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2020-11-01T00:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="50"/>
            <column name="name" value="HACT Management Live"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>


    <!-- update the body with escape xml, or CDATA -->
    <changeSet id="DEV-1741-repDef-hact-mgt-reports-body" author="adamjhamer" context="acceptanceTests,hact">
        <comment>see hact-impl.txt for thoughts on this report</comment>
        <update tableName="reportdefinitions">
            <column name="body"><![CDATA[
{
    "description": "on HACT work this period",
    "selectionMultiCriteria": [
    {
        "selectionRootEntity": "Referral",
        "selectorType": "byStartOfQuarter",
        "relativeStartDate": "2016-04-01",
        "relativeStartIndex": 0,
        "relativeEndIndex": 1
    },
    {
        "selectionCriteriaSource": {
            "analyserType": "referralToClientId",
            "entityIdType": "clientId",
            "inheritDatesOfIndex": 0
        },
        "selectionRootEntity": "Referral",
        "hactSessionData": "true",
        "questionnaireEvidenceGroup": "hactQuestionnaire",
        "fetchRelatedEntities": [
            "client",
            "questionnaireWork",
            "supportWork"
        ]
    }
    ],
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "hactManagement"
            }
        },
        {
            "description": "breakdown of management",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "hactManagement",
                "columns": [
                    "c-id",
                    "questionName",
                    "pre survey support date",
                    "survey name",
                    "survey status",
                    "survey answer date",
                    "survey days expire"
                ]
            }
        }
    ]
}
            ]]></column>
            <where>uuid='05000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1741-repDef-hact-value-reports" author="adamjhamer" context="acceptanceTests,hact">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="05100000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2020-11-01T00:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="51"/>
            <column name="name" value="HACT Social Value"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <!-- update the body with escape xml, or CDATA -->
    <changeSet id="DEV-1741-repDef-hact-social-reports-body" author="adamjhamer" context="acceptanceTests,hact">
        <update tableName="reportdefinitions">
            <column name="body"><![CDATA[
{
    "description": "on HACT work this period",
    "selectionMultiCriteria": [
        {
            "selectionRootEntity": "Questionnaire",
            "selectorType": "byStartOfQuarter",
            "questionnaireEvidenceGroup": "hactQuestionnaire",
            "relativeStartDate": "2020-04-01",
            "relativeStartIndex": 0,
            "relativeEndIndex": 1
        },
        {
            "selectionCriteriaSource": {
                "analyserType": "questionWorkToSrIds",
                "entityIdType": "serviceRecipientId"
            },
            "selectionRootEntity": "Client"
        },
        {
            "selectionCriteriaSource": {
                "analyserType": "clientToClientId",
                "entityIdType": "clientId",
                "inheritDatesOfIndex": 0
            },
            "selectionRootEntity": "Referral",
            "hactSessionData": "true",
            "questionnaireEvidenceGroup": "hactQuestionnaire",
            "fetchRelatedEntities": [
               "client", "questionnaireWork"
            ]
        }],
    "stages": [
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": {
                "analyserType": "hactSocialValue"
            }
        },
        {
            "description": "-not visible-",
            "stageType": "ANALYSER",
            "analyserConfig": { "analyserType": "hactSocialValueTotalsByAge" }
        },
        {
            "description": "by age",
            "stageType": "CHART",
            "seriesDefs": [
                { "label": "value", "valuePath": "count", "renderMode": "PIE" }
            ]
        },
        {
            "description": "breakdown of social value",
            "stageType": "TABLE",
            "tableRepresentation": {
                "className": "hactSocialValue",
                "columns": [
                    "c-id",
                    "client name",
                    "age at answer",
                    "questionName",
                    "valuable",
                    "valuableHactOutcome",
                    "valuableHactValue",
                    "workDate1",
                    "answer1",
                    "workDate2",
                    "answer2"
                ]
            }
        }
    ]
}
            ]]></column>
            <where>uuid='05100000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>



    <!-- ******************** -->
    <!-- UPDATED HACT REPORTS -->
    <!-- ******************** -->

    <!-- update the body with escape xml, or CDATA -->
    <changeSet id="DEV-1741-repDef-hact-mgt-reports-liveAtEnd" author="adamjhamer" context="acceptanceTests,hact">
        <comment>see hact-impl.txt for thoughts on this report</comment>
        <update tableName="reportdefinitions">
            <column name="body"><![CDATA[
            {
                "description": "",
                "selectionMultiCriteria": [
                {
                    "selectionRootEntity": "Referral",
                    "referralStatus": "liveAtEnd"
                },
                {
                    "selectionCriteriaSource": {
                        "analyserType": "referralToClientId",
                        "entityIdType": "clientId",
                        "inheritDatesOfIndex": 0
                    },
                    "selectionRootEntity": "Referral",
                    "hactSessionData": "true",
                    "questionnaireEvidenceGroup": "hactQuestionnaire",
                    "fetchRelatedEntities": [
                        "client",
                        "questionnaireWork",
                        "supportWork"
                    ]
                }
                ],
                "stages": [
                    {
                        "description": "-not visible-",
                        "stageType": "ANALYSER",
                        "analyserConfig": {
                            "analyserType": "hactManagement"
                        }
                    },
                    {
                        "description": "breakdown of management",
                        "stageType": "TABLE",
                        "tableRepresentation": {
                            "className": "hactManagement",
                            "columns": [
                                "c-id",
                                "client name",
                                "questionName",
                                "pre survey support date",
                                "survey name",
                                "survey status",
                                "survey answer date",
                                "survey valuable change",
                                "survey days expire"
                            ]
                        }
                    }
                ]
            }
            ]]></column>
            <where>uuid='05000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <changeSet id="ECCO-1933-repDef-timeToInterview2" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                &quot;description&quot;: &quot;on referrals this quarter&quot;,
                &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: []
                },
                &quot;stages&quot;: [
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByService&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by service&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByProject&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by project&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByWorker&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by worker&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;breakdown of referrals&quot;,
                &quot;stageType&quot;: &quot;TABLE&quot;,
                &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [
                &quot;rid&quot;,
                &quot;cid&quot;,
                &quot;client&quot;,
                &quot;service&quot;,
                &quot;project&quot;,
                &quot;from&quot;,
                &quot;received&quot;,
                &quot;interview (offered)&quot;,
                &quot;interview&quot;,
                &quot;received to interview (offered)&quot;,
                &quot;received to interview&quot;,
                &quot;decided&quot;,
                &quot;start&quot;,
                &quot;worker&quot;
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='04200000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2023-repDef-projectcalendar-body-today-withEventCatogory" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                "description": "",
                "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "referralStatus": "liveAtEnd",
                "fetchRelatedEntities": [
                "referralEvents"
                ],
                "selectorType": "byStartOfDay",
                "relativeStartIndex": 0,
                "relativeEndIndex": 0
                },
                "stages": [
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "calendarEventsFromReferrals"
                }
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "calendarCountsByService"
                }
                },
                {
                "description": "appointments by service",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "calendarCountsByType"
                }
                },
                {
                "description": "appointments by event type",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "calendarCountsByCategory"
                }
                },
                {
                "description": "appointments by event category",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "breakdown of events",
                "stageType": "CALENDAREVENT"
                }
                ]
                }
            </column>
            <where>uuid='04800000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

</databaseChangeLog>
