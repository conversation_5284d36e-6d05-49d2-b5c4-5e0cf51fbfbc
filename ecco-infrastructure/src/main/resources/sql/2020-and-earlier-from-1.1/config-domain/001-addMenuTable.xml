<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
    logicalFilePath="classpath:sql/1.1-changes/001-addMenuTable.xml">
    <changeSet author="default" id="ECCO-172-table-menu">
        <createTable tableName="menu">
            <column name="name" type="VARCHAR(15)">
                <constraints nullable="false" primaryKey="true" />
            </column>
        </createTable>
    </changeSet>
    <changeSet author="default" id="ECCO-172-table-menu_menuitem">
        <createTable tableName="menu_menuitem">
            <column name="menu_name" type="VARCHAR(15)">
                <constraints nullable="false" />
            </column>
            <column name="menuItems_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
        </createTable>
    </changeSet>
    <changeSet author="default" id="ECCO-172-table-menuitem-v2">
        <preConditions onFail="MARK_RAN">
            <not>
                <!-- If we ran v1, then we will run ECCO-172-table-menuitem-fix later, which will end up with the same result -->
                <changeSetExecuted id="ECCO-172-table-menuitem" author="default" changeLogFile="classpath:sql/1.1-changes/001-addMenuTable.xml"/>
            </not>
        </preConditions>
        <createTable tableName="menuitem">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" />
            </column>
            <column name="acceleratorKey" type="VARCHAR(1)"/>
            <column name="imageUrl" type="VARCHAR(255)">
                <constraints nullable="false" />
            </column>
            <column name="linkText" type="VARCHAR(60)">
                <constraints nullable="false" />
            </column>
            <column name="roles" type="VARCHAR(255)"/>
            <column name="url" type="VARCHAR(255)">
                <constraints nullable="false" />
            </column>
            <column name="module_name" type="VARCHAR(15)">
                <constraints nullable="false" />
            </column>
        </createTable>
    </changeSet>
    <changeSet author="default" id="ECCO-172-table-softwaremodule">
        <createTable tableName="softwaremodule">
            <column name="name" type="VARCHAR(15)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="enabled" type="BIT">
                <constraints nullable="false" />
            </column>
        </createTable>
    </changeSet>
    <changeSet author="default" id="ECCO-172-fk-menu_menuitem-menu_name">
        <addForeignKeyConstraint baseColumnNames="menu_name" baseTableName="menu_menuitem" constraintName="FK18277FD2F5A3024D"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="name" referencedTableName="menu"
            referencesUniqueColumn="false" />
    </changeSet>
    <changeSet author="default" id="ECCO-172-fk-menumenuitem-menuItems_id">
        <addForeignKeyConstraint baseColumnNames="menuItems_id" baseTableName="menu_menuitem" constraintName="FK18277FD2E8FAC76E"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="menuitem"
            referencesUniqueColumn="false" />
    </changeSet>
    <changeSet author="default" id="ECCO-172-index-menuitem-module_name">
        <createIndex tableName="menuitem" indexName="FKDC0CC8F257968F34">
            <column name="module_name">
            </column>
        </createIndex>
    </changeSet>
    <changeSet author="default" id="ECCO-172-fk-menuitem-module-name">
        <addForeignKeyConstraint baseColumnNames="module_name" baseTableName="menuitem" constraintName="FKDC0CC8F257968F34"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="name" referencedTableName="softwaremodule"
            referencesUniqueColumn="false" />
    </changeSet>
    <changeSet author="default" id="ECCO-172-data-softwaremodules">
        <insert tableName="softwaremodule">
            <column name="name" value="core" />
            <column name="enabled" valueBoolean="true" />
        </insert>
        <insert tableName="softwaremodule">
            <column name="name" value="hr" />
            <column name="enabled" valueBoolean="true" />
        </insert>
        <insert tableName="softwaremodule">
            <column name="name" value="rota" />
            <column name="enabled" valueBoolean="true" />
        </insert>
        <insert tableName="softwaremodule">
            <column name="name" value="offline" />
            <column name="enabled" valueBoolean="true" />
        </insert>
        <insert tableName="softwaremodule">
            <column name="name" value="dev-only" />
            <column name="enabled" valueBoolean="false" />
        </insert>
    </changeSet>
    <changeSet author="default" id="ECCO-172-data-menus">
        <insert tableName="menu">
            <column name="name" value="welcome" />
        </insert>
        <insert tableName="menu">
            <column name="name" value="settings" />
        </insert>
        <insert tableName="menu">
            <column name="name" value="reports" />
        </insert>
    </changeSet>
    <changeSet author="default" id="ECCO-172-data-welcome-menu-items">
        <insert tableName="menuitem">
            <column name="id" valueNumeric="1" />
            <column name="acceleratorKey" value="r" />
            <column name="imageUrl" value="/icons/crystal/user/png/user-48.png" />
            <column name="linkText" value="menu.linktext.referrals" />
            <column name="roles" value="ROLE_USER,ROLE_COMMISSIONER" />
            <column name="url" value="/dynamic/secure/menuReferral.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="2" />
            <column name="acceleratorKey" value="s" />
            <column name="imageUrl" value="/icons/crystal/card/png/48.png" />
            <column name="linkText" value="menu.linktext.support_plans" />
            <column name="roles" value="ROLE_USER,ROLE_COMMISSIONER,ROLE_CLIENT" />
            <column name="url" value="/dynamic/secure/supportPlanFlow.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="3" />
            <column name="acceleratorKey" value="c" />
            <column name="imageUrl" value="/icons/crystal/calendar/png/48.png" />
            <column name="linkText" value="menu.linktext.calendar" />
            <column name="roles" value="ROLE_USER,ROLE_CLIENT" />
            <column name="url" value="/dynamic/secure/viewCalendar.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="4" />
            <column name="acceleratorKey" value="a" />
            <column name="imageUrl" value="/icons/crystal/addressbook/png/adressbook-48.png" />
            <column name="linkText" value="menu.linktext.contacts" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/entities/clients/get.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="5" />
            <column name="acceleratorKey" value="g" />
            <column name="imageUrl" value="/icons/crystal/users/png/48.png" />
            <column name="linkText" value="menu.linktext.group_support" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/menuActivities.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="6" />
            <column name="acceleratorKey" value="p" />
            <column name="imageUrl" value="/home.png" />
            <column name="linkText" value="menu.linktext.projects" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/menuProject.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="7" />
            <column name="acceleratorKey" value="h" />
            <column name="imageUrl" value="/home_yellow.png" />
            <column name="linkText" value="menu.linktext.hr" />
            <column name="roles" value="ROLE_USER,ROLE_HR-VOLUNTEER,ROLE_HR" />
            <column name="url" value="/dynamic/secure/entities/workers/get.html" />
            <column name="module_name" value="hr" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="8" />
            <column name="acceleratorKey" value="t" />
            <column name="imageUrl" value="/rota.png" />
            <column name="linkText" value="menu.linktext.rota" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/viewRota.html" />
            <column name="module_name" value="rota" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="9" />
            <column name="acceleratorKey" value="z" />
            <column name="imageUrl" value="/icons/gauge.png" />
            <column name="linkText" value="menu.linktext.reports" />
            <column name="roles" value="ROLE_ADMIN,ROLE_COMMISSIONER" />
            <column name="url" value="/dynamic/secure/menuReport.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="10" />
            <column name="acceleratorKey" value="x" />
            <column name="imageUrl" value="/gear.png" />
            <column name="linkText" value="menu.linktext.settings" />
            <column name="roles" value="ROLE_SYSADMIN,ROLE_ADMIN" />
            <column name="url" value="/dynamic/secure/menuSetting.html" />
            <column name="module_name" value="core" />
        </insert>
    </changeSet>

    <changeSet author="default" id="ECCO-172-data-settings-menu-items">
        <insert tableName="menuitem">
            <column name="id" valueNumeric="11" />
            <column name="imageUrl" value="/icons/crystal/user/png/user-48.png" />
            <column name="linkText" value="menu.linktext.logins" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/entities/users/get.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="12" />
            <column name="imageUrl" value="/icons/crystal/db/png/48.png" />
            <column name="linkText" value="menu.linktext.LDAP_group_mapping" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/settings/ldapGroupMapping/list.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="13" />
            <column name="imageUrl" value="/icons/crystal/text/png/48.png" />
            <column name="linkText" value="menu.linktext.lists" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/listIdNames.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="14" />
            <column name="imageUrl" value="/target-48.png" />
            <column name="linkText" value="menu.linktext.outcomes" />
            <column name="roles" value="ROLE_SYSADMIN" />
            <column name="url" value="/dynamic/secure/outcomesFlow.html" />
            <column name="module_name" value="dev-only" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="15" />
            <column name="imageUrl" value="/icons/crystal/user/png/user-48.png" />
            <column name="linkText" value="menu.linktext.referral_process" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/referralProcess.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="16" />
            <column name="imageUrl" value="/icons/crystal/emblem-photos/png/48.png" />
            <column name="linkText" value="menu.linktext.logo" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/settings/logo/upload.html" />
            <column name="module_name" value="core" />
        </insert>
    </changeSet>

    <changeSet author="default" id="ECCO-172-data-reports-menu-items">
        <insert tableName="menuitem">
            <column name="id" valueNumeric="17" />
            <column name="imageUrl" value="/icons/gauge.png" />
            <column name="linkText" value="menu.linktext.dashboard" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/reports/dashboard.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="18" />
            <column name="imageUrl" value="/icons/crystal/user/png/user-48.png" />
            <column name="linkText" value="menu.linktext.referrals" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/reports/referrals.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="19" />
            <column name="imageUrl" value="/icons/crystal/card/png/48.png" />
            <column name="linkText" value="menu.linktext.support_plans" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/reports/supportPlans.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="20" />
            <column name="imageUrl" value="/home_yellow.png" />
            <column name="linkText" value="menu.linktext.hr" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/reports/hr.html" />
            <column name="module_name" value="hr" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="21" />
            <column name="imageUrl" value="/icons/crystal/users/png/48.png" />
            <column name="linkText" value="menu.linktext.group_support" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/reports/supportPlanServices.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="22" />
            <column name="imageUrl" value="/icons/hand_share2.png" />
            <column name="linkText" value="menu.linktext.services" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/menuReportServices.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="23" />
            <column name="imageUrl" value="/icons/plug.png" />
            <column name="linkText" value="menu.linktext.submissions" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/menuReportSubmissions.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="24" />
            <column name="imageUrl" value="/icons/money.jpg" />
            <column name="linkText" value="menu.linktext.finances" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/menuReportFinances.html" />
            <column name="module_name" value="dev-only" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="25" />
            <column name="imageUrl" value="/icons/crystal/usergreen/png/48.png" />
            <column name="linkText" value="menu.linktext.staff" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/reports/staff.html" />
            <column name="module_name" value="core" />
        </insert>
        <insert tableName="menuitem">
            <column name="id" valueNumeric="26" />
            <column name="imageUrl" value="/icons/crystal/user/png/user-48.png" />
            <column name="linkText" value="menu.linktext.user_audit" />
            <column name="roles" value="ROLE_USER" />
            <column name="url" value="/dynamic/secure/reports/userAudit.html" />
            <column name="module_name" value="core" />
        </insert>
    </changeSet>

    <changeSet author="default" id="ECCO-172-data-menu_menuitem">
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="1" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="2" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="3" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="4" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="5" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="6" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="7" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="8" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="9" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="welcome" />
            <column name="menuItems_id" valueNumeric="10" />
        </insert>

        <insert tableName="menu_menuitem">
            <column name="menu_name" value="settings" />
            <column name="menuItems_id" valueNumeric="11" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="settings" />
            <column name="menuItems_id" valueNumeric="12" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="settings" />
            <column name="menuItems_id" valueNumeric="13" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="settings" />
            <column name="menuItems_id" valueNumeric="14" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="settings" />
            <column name="menuItems_id" valueNumeric="15" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="settings" />
            <column name="menuItems_id" valueNumeric="16" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="reports" />
            <column name="menuItems_id" valueNumeric="17" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="reports" />
            <column name="menuItems_id" valueNumeric="18" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="reports" />
            <column name="menuItems_id" valueNumeric="19" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="reports" />
            <column name="menuItems_id" valueNumeric="20" />
        </insert>

        <insert tableName="menu_menuitem">
            <column name="menu_name" value="reports" />
            <column name="menuItems_id" valueNumeric="21" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="reports" />
            <column name="menuItems_id" valueNumeric="22" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="reports" />
            <column name="menuItems_id" valueNumeric="23" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="reports" />
            <column name="menuItems_id" valueNumeric="24" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="reports" />
            <column name="menuItems_id" valueNumeric="25" />
        </insert>
        <insert tableName="menu_menuitem">
            <column name="menu_name" value="reports" />
            <column name="menuItems_id" valueNumeric="26" />
        </insert>
    </changeSet>
    <changeSet author="bodeng" id="ECCO-172-table-menuitem-fix" onValidationFail="MARK_RAN">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="ECCO-172-table-menuitem" author="default" changeLogFile="classpath:sql/1.1-changes/001-addMenuTable.xml"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="menu_menuitem" constraintName="FK18277FD2E8FAC76E"/>
        <customChange class="com.ecco.infrastructure.liquibase.DropAutoIncrementChange" columnName="id" columnDataType="BIGINT" tableName="menuitem"/>
        <addForeignKeyConstraint baseColumnNames="menuItems_id" baseTableName="menu_menuitem" constraintName="FK18277FD2E8FAC76E"
            deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="menuitem"
            referencesUniqueColumn="false" />
    </changeSet>

    <changeSet id="ECCO-328-rota-view-url-fix" author="nealeu">
        <update tableName="menuitem">
            <column name="url" value="/dynamic/secure/rota/" />
            <where>id=8</where>
        </update>
    </changeSet>
</databaseChangeLog>
