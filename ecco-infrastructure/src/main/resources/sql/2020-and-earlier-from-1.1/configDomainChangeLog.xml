<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="hibernate_sequences" author="bodeng">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="hibernate_sequences"/>
            </not>
        </preConditions>
        <createTable tableName="hibernate_sequences">
            <column name="sequence_name" type="VARCHAR(40)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="next_val" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="validate-utf8-database" author="nealeu" runAlways="true" dbms="mysql">
        <preConditions onFail="HALT">
            <!-- utf8 is shown as utf8mb3 since 8.0.28 so allow either -->
            <or>
                <customPrecondition className="com.ecco.infrastructure.liquibase.ValidateDatabaseCharset">
                    <param name="charset" value="utf8mb3"/>
                    <param name="collation" value="utf8mb3_general_ci"/>
                </customPrecondition>
                <customPrecondition className="com.ecco.infrastructure.liquibase.ValidateDatabaseCharset">
                    <param name="charset" value="utf8"/>
                    <param name="collation" value="utf8_general_ci"/>
                </customPrecondition>
            </or>
            <!--
            Also see what mess may already exist
            SELECT table_name,table_collation FROM information_schema.tables WHERE table_schema='test7';

            To set defaults add the following to mysql config (from https://stackoverflow.com/a/202276/1998186)
                [mysqld]
                skip-character-set-client-handshake
                collation_server     = utf8_general_ci
                character_set_server = utf8

                [client]
                default-character-set=utf8

            To alter after creation, but you'll need to check that no tables have been created with latin1:
                ALTER SCHEMA `testdomcare`  DEFAULT CHARACTER SET utf8  DEFAULT COLLATE utf8_general_ci ;
            -->
        </preConditions>
    </changeSet>

    <include file="classpath:sql/1.1-baseline/configDomainSchemaBaseline.xml" context="1.1-baseline"/>
    <include file="classpath:sql/1.1-baseline/configDomainDataBaseline.xml" context="1.1-baseline"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/001-addMenuTable.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/003-addClientAndWorkflowModules.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/005-menuitem-for-continuingCare.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/006-ECCO-85-setting-table-changes.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/007-addNewReferralForClientCentric.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/008-migrate-menuReferral-to-menu.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/013-updateMenuTable.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/015-editClientModules.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/016-editContactModules.xml"/>
    <include file="classpath:sql/1.1-changes/047-ECCO-557-groupsupport-menuitem.xml"/>
    <include file="classpath:sql/1.1-changes/048-ECCO-565-reports-tidy.xml"/>
    <include file="classpath:sql/1.1-changes/049-ECCO-566-reports-audits.xml"/>
    <include file="classpath:sql/1.1-changes/051-supportplan-report-rename.xml"/>
    <include file="classpath:sql/1.1-changes/061-ECCO-752-rotareport-menuitem.xml"/>
    <include file="classpath:sql/1.1-changes/065-ECCO-812-workload-menuItem.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/900-config-domain-2013-06-onwards-changes.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/901-config-domain-2016-06-onwards-changes.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/config-domain/072-Jan-2015-until-next-data.xml"/>

    <include file="classpath:sql/1.2-changes/config-domain/001-configDomainChangeLog.xml"/>

    <!-- Feel free to add to existing file above or do a new file, by year-month-onwards for larger changes
         We don't need to create a new changelog file for every change -->

</databaseChangeLog>
