<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">


    <!-- HANDLES:
     - anything NOT in config/security domain change logs files
    -->

    <!-- DOMAIN-SPECIFIC CHANGES
     - When making domain-specific changes, please put into the appropriate domain-specific file.
     - If this causes the ordering to go wrong - eg changesets a,b,c are needed but b is domain-specific
     - then place the b in a new independent file that can be referenced from here, and include it
    -->


    <include file="classpath:sql/1.1-changes/0001-generalDomainChangeLog.xml"/>

    <include file="classpath:sql/1.2-changes/general-domain/001-generalDomainChangeLog.xml"/>

    <!-- *** STOP: DO NOT ADD ANYTHING MORE HERE *** -->
</databaseChangeLog>
