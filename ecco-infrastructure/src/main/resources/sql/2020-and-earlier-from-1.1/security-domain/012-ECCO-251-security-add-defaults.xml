<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
    logicalFilePath="classpath:sql/1.1-changes/012-ECCO-251-security-add-defaults.xml">

    <changeSet id="ECCO-251-add-defaults-to-failedlogins-table" author="nealeu">
        <addDefaultValue tableName="failedlogins" columnName="faileddate" defaultValueComputed="CURRENT_TIMESTAMP"/>
    </changeSet>

    <changeSet id="ECCO-251-add-defaults-to-users-table" author="nealeu">
        <addDefaultValue tableName="users" columnName="activedirectoryuser" defaultValueBoolean="false"/>
        <addDefaultValue tableName="users" columnName="enabled" defaultValueBoolean="false"/>
<!-- MySQL doesn't allow more than one this way until 5.6.5
        <addDefaultValue tableName="users" columnName="registered" defaultValueComputed="CURRENT_TIMESTAMP"/> -->
    </changeSet>

</databaseChangeLog>
