<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
    logicalFilePath="2022/cosmo-domain">

    <changeSet id="DEV-1809-seriesColumn" author="adamjhamer">
        <addColumn tableName="ical_seriesrelatedentries">
            <column name="seriesHandle" type="CHAR(36)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_icalentries_series_self"
                                 baseTableName="ical_seriesrelatedentries" baseColumnNames="seriesHandle"
                                 referencedTableName="ical_seriesrelatedentries" referencedColumnNames="entryHandle" />
        <createIndex tableName="ical_seriesrelatedentries" indexName="idx_icalrelated_series_start">
            <column name="seriesHandle"/>
            <column name="startDate"/>
        </createIndex>
    </changeSet>
    <!-- 'not found' errors during tests if this is enforced - perhaps need to check tx boundaries? -->
    <changeSet id="DEV-1828-relatedentries-remove-seriesFK" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="ical_seriesrelatedentries" constraintName="fk_icalentries_series_self"/>
    </changeSet>

    <changeSet id="DEV-1809-createdColumn" author="adamjhamer">
        <addColumn tableName="ical_seriesrelatedentries">
            <column name="created" type="DATETIME">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>