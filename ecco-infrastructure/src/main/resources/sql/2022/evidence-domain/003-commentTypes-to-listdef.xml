<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd"
        logicalFilePath="2022/evidence-domain">

    <!-- NB COMMENT TYPES MIGRATION to listdef is cloned from 022-flags-fk.xml -->

    <!-- NB the below may now be better as dropDefaultValue tableName="" columnName="" -->
    <!-- for mssql, not oracle, we can't modify a foreign key easily without removing the hidden default first -->
    <!-- mssql creates its own constraints for 'default' values which don't appear in information_schema -->
    <!-- so we need a way of getting the name to delete them -->
    <!-- useful http://stackoverflow.com/questions/7663390/why-does-sql-keep-creating-a-df-constraint -->
    <!-- and http://stackoverflow.com/questions/1430456/how-to-drop-sql-default-constraint-without-knowing-its-name -->
    <!-- NB removed as there are no defaults on 'typeid' on these tables -->
    <!--<changeSet id="DEV-2352-listDef-commenttypes-types-mssql" author="adamjhamer" dbms="mssql">
        &lt;!&ndash; NOTE there are no ';' or GO apart from where we specifically want to break up the batch &ndash;&gt;
        &lt;!&ndash; and there are no line breaks between the batched statements &ndash;&gt;
        <sql>
            DECLARE @ObjectName NVARCHAR(100)
            SELECT @ObjectName = OBJECT_NAME([default_object_id]) FROM SYS.COLUMNS
            WHERE [object_id] = OBJECT_ID('[dbo].[evdnc_form_comments]') AND [name] = 'typeid'
            EXEC('ALTER TABLE [dbo].[evdnc_form_comments] DROP CONSTRAINT ' + @ObjectName)
            ;
            DECLARE @ObjectName NVARCHAR(100)
            SELECT @ObjectName = OBJECT_NAME([default_object_id]) FROM SYS.COLUMNS
            WHERE [object_id] = OBJECT_ID('[dbo].[supportthreatcomments]') AND [name] = 'typeid'
            EXEC('ALTER TABLE [dbo].[supportthreatcomments] DROP CONSTRAINT ' + @ObjectName)
            ;
            DECLARE @ObjectName NVARCHAR(100)
            SELECT @ObjectName = OBJECT_NAME([default_object_id]) FROM SYS.COLUMNS
            WHERE [object_id] = OBJECT_ID('[dbo].[supportplancomments]') AND [name] = 'typeid'
            EXEC('ALTER TABLE [dbo].[supportplancomments] DROP CONSTRAINT ' + @ObjectName)
            ;
            DECLARE @ObjectName NVARCHAR(100)
            SELECT @ObjectName = OBJECT_NAME([default_object_id]) FROM SYS.COLUMNS
            WHERE [object_id] = OBJECT_ID('[dbo].[supporthrcomments]') AND [name] = 'typeid'
            EXEC('ALTER TABLE [dbo].[supporthrcomments] DROP CONSTRAINT ' + @ObjectName)
            ;
        </sql>
    </changeSet>-->

    <!-- STEP 1: drop fks, rename to taskDefId, convert to int -->
    <changeSet id="DEV-2352-listDef-commenttypes-prep" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="evdnc_form_comments" constraintName="fk-evd_frmc_cmttypeId"/>
        <dropForeignKeyConstraint baseTableName="supportthreatcomments" constraintName="FKA8115B9D42BDAB2B"/>
        <dropForeignKeyConstraint baseTableName="supportplancomments" constraintName="FK2225716C42BDAB2B"/>
        <dropForeignKeyConstraint baseTableName="supporthrcomments" constraintName="FKE4F38A0D42BDAB2B"/>
    </changeSet>
    <changeSet id="DEV-2352-listDef-commenttypes-defs" author="adamjhamer">
        <renameColumn tableName="evdnc_form_comments" oldColumnName="typeid" newColumnName="typeDefId" columnDataType="BIGINT"/>
        <renameColumn tableName="supportthreatcomments" oldColumnName="typeid" newColumnName="typeDefId" columnDataType="BIGINT"/>
        <renameColumn tableName="supportplancomments" oldColumnName="typeid" newColumnName="typeDefId" columnDataType="BIGINT"/>
        <renameColumn tableName="supporthrcomments" oldColumnName="typeid" newColumnName="typeDefId" columnDataType="BIGINT"/>
    </changeSet>
    <changeSet id="DEV-2352-listDef-commenttypes-types" author="adamjhamer">
        <!-- NB casting long->int unlikely to work on other dbs -->
        <modifyDataType tableName="evdnc_form_comments" columnName="typeDefId" newDataType="INT"/>
        <modifyDataType tableName="supportthreatcomments" columnName="typeDefId" newDataType="INT"/>
        <modifyDataType tableName="supportplancomments" columnName="typeDefId" newDataType="INT"/>
        <modifyDataType tableName="supporthrcomments" columnName="typeDefId" newDataType="INT"/>
    </changeSet>

    <!-- STEP 2: fill migrationId from the sequence -->
    <changeSet id="DEV-2352-commenttypes-typeDefId" author="adamjhamer">
        <addColumn tableName="commenttypes">
            <column name="migrationId" type="INT"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-2352-listDef-commenttypes-populate-seq" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateHibernateSequenceChange"
                      tableName="commenttypes" uniqueKeyColumns="id"
                      newIdColumn="migrationId"
                      sequenceName="id_name"
        />
    </changeSet>

    <!-- STEP 3: migrate -->
    <!-- insert one list per servicetype (as is the case currently with commenttypes) by using the referralView -->
    <!-- this allows us to transition to how commenttypes currently behave, and avoid unique clashes -->
    <!-- some commenttypes are duplicated as they are disabled -->
    <!-- fix manually: update commenttypes set name=concat(name, concat(':',id)) where disabled=1; -->
    <changeSet id="DEV-2352-listDef-commenttypes-copy-rows" author="adamjhamer">
        <validCheckSum>8:8117688d1f1b953175596c5af0b99f81</validCheckSum>
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, businessKey, disabled)
            SELECT migrationId, 0, concat('migrate:commenttypes-', servicetypeId), name, concat('commenttypes-', id), disabled FROM commenttypes
            where disabled=0
            ;
        </sql>
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, businessKey, disabled)
            SELECT migrationId, 0, concat('migrate:commenttypes-', servicetypeId), concat(name, concat(':',id)), concat('commenttypes-', id), disabled FROM commenttypes
            where disabled=1
            ;
        </sql>
    </changeSet>
    <!-- STEP 4: fks -->
    <changeSet id="DEV-2352-listDef-commenttypes-update-refs" author="adamjhamer">
        <update tableName="evdnc_form_comments">
            <column name="typeDefId" valueComputed="(SELECT migrationId FROM commenttypes WHERE id = evdnc_form_comments.typeDefId)"/>
        </update>
        <update tableName="supportthreatcomments">
            <column name="typeDefId" valueComputed="(SELECT migrationId FROM commenttypes WHERE id = supportthreatcomments.typeDefId)"/>
        </update>
        <update tableName="supportplancomments">
            <column name="typeDefId" valueComputed="(SELECT migrationId FROM commenttypes WHERE id = supportplancomments.typeDefId)"/>
        </update>
        <update tableName="supporthrcomments">
            <column name="typeDefId" valueComputed="(SELECT migrationId FROM commenttypes WHERE id = supporthrcomments.typeDefId)"/>
        </update>
        <addForeignKeyConstraint baseTableName="evdnc_form_comments" baseColumnNames="typeDefId"
                                 constraintName="fk_evdnc_typeDefId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="supportthreatcomments" baseColumnNames="typeDefId"
                                 constraintName="fk_threat_typeDefId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="supportplancomments" baseColumnNames="typeDefId"
                                 constraintName="fk_plan_typeDefId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="supporthrcomments" baseColumnNames="typeDefId"
                                 constraintName="fk_hr_typeDefId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
    </changeSet>
    <!-- migrate the non-FKs -->
    <!-- TODO if we added some json for {"pre-migrationId": xx} then we could more easily show accurate audits -->
    <changeSet id="DEV-2352-listDef-commenttypes-populate-settings" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.CsvTransposeChange"
                      tableName="st_referralaspectsettings" primaryKeyColumns="id" whereClause="where name='commentTypesById'"
                      csvColumnName="value"
                      mappingTable="commenttypes" mappingSrcColumnName="id" mappingDstColumnName="migrationId">
        </customChange>
    </changeSet>

</databaseChangeLog>
