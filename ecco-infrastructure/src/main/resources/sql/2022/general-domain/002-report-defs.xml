<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
        logicalFilePath="2022/general-domain">

    <changeSet id="DEV-2334-repDef-refContacts" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="06300000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2022-08-09T20:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="22"/>
            <column name="name" value="referral 'contacts'"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <changeSet id="DEV-2334-repDef-refContacts-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                "description": "on contacts",
                "selectionCriteria": {
                "selectionRootEntity": "Referral",
                "referralStatus": "liveAtEnd",
                "fetchRelatedEntities": [
                "associatedContacts"
                ]
                },
                "stages": [
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "contactsFromReferrals"
                }
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "associatedContactsCountsByType"
                }
                },
                {
                "description": "by type",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "breakdown of contacts",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "AssociatedContactsWithReferralSummary",
                "columns": [
                "sr-id",
                "r: r-id",
                "r: name",
                "created",
                "archived",
                "type(s)",
                "i: contactId",
                "i: first name",
                "i: last name",
                "i: full address",
                "i: phone",
                "i: mobile",
                "i: email"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='06300000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <!-- clone of 04100000 but refreshed and includes risk work -->
    <changeSet id="DEV-2371-repDef-supportAndRisk-entry" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="06400000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2022-10-01T07:00:00"/>
            <column name="hidden" valueDate="2022-10-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="10"/>
            <column name="name" value="support and risk work by service, then by author, then referral breakdown"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>


    <changeSet id="DEV-2371-repDef-supportAndRisk-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                "description": "on support and risk work this period",
                "selectionCriteria": {
                "selectionRootEntity": "SupportRiskWork",
                "selectorType": "byStartOfWeekMonday",
                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": [
                "referral"
                ]
                },
                "stages": [
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "visitCountsByService"
                }
                },
                {
                "description": "by service",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "totalVisits",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "visitCountsByProject"
                }
                },
                {
                "description": "by project",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "totalVisits",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "visitCountsBySrId"
                }
                },
                {
                "description": "breakdown of visits",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "VisitWithReferralColumns",
                "columns": [
                "r: r-id",
                "r: c-id",
                "r: name",
                "r: worker",
                "time spent",
                "visits",
                "average time spent"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='06400000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2381-repDef-badge-tasks-user-body4" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="dashboard tasks"/>
            <column name="body">
                {
                "description": "tasks in the period",
                "selectionCriteria": {
                "selectionRootEntity": "TaskStatus",
                "selectorType": "byStartOfDay",
                "entityStatus": "dueDateOrCompleted",
                "relativeStartIndex": -7,
                "relativeEndIndex": 7
                },
                "stages": [
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "tasksToSingleSummary"
                }
                },
                {
                "description": "due",
                "stageType": "BADGE",
                "selectionAnalyser": "selectAllDue",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-tasks",
                "recordRepresentationClassName": "byTaskGroupAnalysis",
                "mainIndicatorValue": "due"
                }
                },
                {
                "description": "overdue",
                "stageType": "BADGE",
                "selectionAnalyser": "selectAllOverdue",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-tasks badge-amber",
                "recordRepresentationClassName": "byTaskGroupAnalysis",
                "mainIndicatorValue": "overdue"
                }
                },
                {
                "description": "completed",
                "stageType": "BADGE",
                "selectionAnalyser": "selectAllCompleted",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-tasks",
                "recordRepresentationClassName": "byTaskGroupAnalysis",
                "mainIndicatorValue": "completed"
                }
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "groupByService"
                }
                },
                {
                "description": "by service",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "-not visible-",
                "stageType": "ANALYSER",
                "analyserConfig": {
                "analyserType": "groupByAssignee"
                }
                },
                {
                "description": "by assignee",
                "stageType": "CHART",
                "seriesDefs": [
                {
                "label": "value",
                "valuePath": "count",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "breakdown of tasks",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "TaskStatusOnlyColumns",
                "columns": [
                "r-id",
                "c-id",
                "client",
                "service",
                "project",
                "task",
                "description",
                "due",
                "assignee",
                "created",
                "completed",
                "edit"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='05700000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- clone of 04100000 but refreshed and includes risk work -->
    <changeSet id="DEV-2355-repDef-professionals-entry" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="06500000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2022-10-01T07:00:00"/>
            <column name="hidden" valueDate="2022-10-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="50"/>
            <column name="name" value="professionals"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <changeSet id="DEV-2355-repDef-professionals-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                "description": "on all professionals",
                "selectionCriteria": {
                "selectionRootEntity": "Professional"
                },
                "stages": [
                {
                "description": "breakdown of professionals",
                "stageType": "TABLE",
                "tableRepresentation": {
                "className": "ProfessionalOnly",
                "columns": [
                "a: company name",
                "a: contactId",
                "a: category",
                "a: address",
                "a: town",
                "a: postcode",
                "a: phone",
                "a: email",
                "a: archived",
                "contactId",
                "first name",
                "last name",
                "address",
                "town",
                "postcode",
                "phone",
                "email",
                "archived"
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='06500000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

</databaseChangeLog>
