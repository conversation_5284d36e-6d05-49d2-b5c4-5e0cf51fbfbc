<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd"
    logicalFilePath="2023/repairs-domain"
>

    <changeSet id="DEV-2671-repair-rates" author="adamjhamer">
        <createTable tableName="repairs_rates">
            <column name="id" type="INT">
                <constraints nullable="false" primaryKey="true" />
            </column>
            <column name="code" type="VARCHAR(32)">
                <constraints unique="true"/>
            </column>
            <!-- tab name or probably the title in the tab -->
            <column name="area" type="VARCHAR(32)"/>
            <column name="ref" type="VARCHAR(32)"/>
            <column name="description" type="VARCHAR(1024)"/>
            <column name="unit" type="VARCHAR(32)"/>
            <!-- rate -->
            <column name="rate" type="DECIMAL(10,2)"/>
            <!-- OR these -->
            <column name="material" type="DECIMAL(10,2)"/>
            <column name="labour" type="DECIMAL(10,2)"/>
            <column name="plant" type="DECIMAL(10,2)"/>
            <column name="total" type="DECIMAL(10,2)"/>
        </createTable>
        <createIndex tableName="repairs_rates" indexName="idx_reprat_code">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet id="DEV-2671-repair-rates-version" author="adamjhamer">
        <validCheckSum>8:3e0500ff89740fc610cb05c110dec578</validCheckSum>
        <addColumn tableName="repairs_rates">
            <column name="version" type="INT" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2671-repair-rates-data" author="adamjhamer" context="rates">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from repairs_rates</sqlCheck>
        </preConditions>
        <sqlFile path="classpath:sql/2023/buildings-domain/003-repair-national-schedule-data.sql"/>
    </changeSet>

    <changeSet id="DEV-2671-repair-to-rates" author="adamjhamer">
        <addColumn tableName="repairs">
            <column name="rateId" type="INT"/>
        </addColumn>
        <addForeignKeyConstraint baseTableName="repairs" baseColumnNames="rateId" constraintName="fk_rates_rateId"
                                 referencedTableName="repairs_rates" referencedColumnNames="id"/>
    </changeSet>

</databaseChangeLog>
