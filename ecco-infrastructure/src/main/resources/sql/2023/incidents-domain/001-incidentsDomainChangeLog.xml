<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd"
    logicalFilePath="2023/incidents-domain"
>

    <!-- HANDLES: (based on search for <createTable)
     - see main incidentsDomainChangeLog.xml for what tables are involved im the domain
    -->

    <property name="now" value="CURRENT_TIMESTAMP()" dbms="h2"/>
    <property name="now" value="CURRENT_TIMESTAMP()" dbms="mssql"/>
    <property name="now" value="now()" dbms="mysql"/>
    <property name="now" value="sysdate" dbms="oracle"/>

    <!-- we need hibernate_sequences on first domain file -->
    <changeSet id="hibernate_sequences" author="bodeng">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="hibernate_sequences"/>
            </not>
        </preConditions>
        <createTable tableName="hibernate_sequences">
            <column name="sequence_name" type="VARCHAR(40)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="next_val" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="DEV-2618-inc-sequence" author="adamjhamer">
        <validCheckSum>8:5424eb1521572fe48ef36880b2c35f3e</validCheckSum>
        <insert tableName="hibernate_sequences">
            <column name="sequence_name" value="incidents"/>
            <column name="next_val" valueNumeric="100"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2618-incidents" author="adamjhamer">
        <createTable tableName="incidents">
            <column defaultValueNumeric="0" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"><constraints nullable="false"/></column>
            <column name="name" type="VARCHAR(63)"><constraints nullable="false"/></column>
            <column name="parentId" type="INT"/>
            <!--<column name="calendarId" type="VARCHAR(40)"/>-->
        </createTable>
    </changeSet>
    <changeSet id="DEV-2618-incidents-name" author="adamjhamer">
        <dropNotNullConstraint tableName="incidents" columnName="name" columnDataType="VARCHAR(63)"/>
    </changeSet>
    <changeSet id="DEV-2618-incidents-dropParent" author="adamjhamer">
        <dropColumn tableName="incidents" columnName="parentId"/>
    </changeSet>
    <changeSet id="DEV-2618-incidents-srId" author="adamjhamer">
        <addColumn tableName="incidents">
            <column name="servicerecipientId" type="INT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint
                constraintName="fk_inc_srId"
                baseTableName="incidents" baseColumnNames="servicerecipientId"
                referencedTableName="servicerecipients" referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-2618-inc-commands" author="adamjhamer">
        <createTable tableName="inc_commands">
            <column name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="commandname" type="VARCHAR(63)">
                <constraints nullable="false"/>
            </column>
            <column name="created" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="uuid" type="CHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="commandCreated" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="userid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="body" type="CLOB">
                <constraints nullable="false"/>
            </column>
            <column name="incidentId" type="INT">
                <constraints nullable="false"/> <!-- when creating, we get the id and inject it to the command -->
            </column>
        </createTable>

        <addForeignKeyConstraint
                constraintName="fk_inccmds_author"
                baseTableName="inc_commands" baseColumnNames="userid"
                referencedTableName="users" referencedColumnNames="id"/>
        <addForeignKeyConstraint
                constraintName="fk_inccmds_wId"
                baseTableName="inc_commands" baseColumnNames="incidentId"
                referencedTableName="incidents" referencedColumnNames="id"/>

        <createIndex tableName="inc_commands" indexName="idx_inccmds_uuid">
            <column name="uuid"/>
        </createIndex>
        <createIndex tableName="inc_commands" indexName="idx_inccmds_ctd">
            <column name="created"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="inc_commands" indexName="idx_inccmds_wid_crtd">
            <column name="incidentId"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="inc_commands" indexName="idx_inccmds_name_id_ctd">
            <column name="incidentId"/>
            <column name="commandname"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-2618-inc-commands-srId" author="adamjhamer">
        <addColumn tableName="inc_commands">
            <column name="serviceRecipientId" type="INT"/>
        </addColumn>
        <addForeignKeyConstraint
                constraintName="fk_inccmds_srId"
                baseTableName="inc_commands" baseColumnNames="serviceRecipientId"
                referencedTableName="servicerecipients" referencedColumnNames="id"/>
        <createIndex tableName="inc_commands" indexName="idx_inccmds_sr_ctd">
            <column name="serviceRecipientId"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
        <createIndex tableName="inc_commands" indexName="idx_inccmds_sr_name_ctd">
            <column name="serviceRecipientId"/>
            <column name="commandname"/>
            <column name="created"/>
            <column name="id"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-2619-incidents-received" author="adamjhamer">
        <addColumn tableName="incidents">
            <column name="receivedDate" type="DATE"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-2619-incidents-typeId" author="adamjhamer">
        <addColumn tableName="incidents">
            <column name="typeId" type="INT"/>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_inc_typeId" baseTableName="incidents"
                                 baseColumnNames="typeId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>
    <changeSet id="DEV-2619-incidents-levelId" author="adamjhamer">
        <validCheckSum>8:8dd87eed11487defa1f8babfaa0f0438</validCheckSum>
        <addColumn tableName="incidents">
            <column name="levelId" type="INT"/>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_inc_levelId" baseTableName="incidents"
                                 baseColumnNames="levelId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>
    <changeSet id="DEV-2619-incidents-categoryId" author="adamjhamer">
        <addColumn tableName="incidents">
            <column name="categoryId" type="INT"/>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_inc_catId" baseTableName="incidents"
                                 baseColumnNames="categoryId" referencedTableName="cfg_list_definitions" referencedColumnNames="id" />
    </changeSet>

    <!-- 2x Column with defaultValue without not null constraint -->
    <changeSet id="DEV-2619-incidents-signposting" author="adamjhamer">
        <validCheckSum>8:ee8b9e3e2934cef7fee829d08b40c781</validCheckSum>
        <addColumn tableName="incidents">
            <column name="decisionmadeon" type="DATETIME"/>
            <column name="signpostedcomment" type="LONGTEXT"/>
            <column name="signpostedreasonId" type="INT"/>
            <column name="signpostedagencyid" type="BIGINT"/>
            <column name="signpostedback" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="acceptedonservice" type="BOOLEAN" defaultValueBoolean="false"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2619-incidents-exit" author="adamjhamer">
        <renameColumn tableName="incidents" oldColumnName="signpostedcomment" newColumnName="signpostedexitcomment" columnDataType="LONGTEXT"/>
        <addColumn tableName="incidents">
            <column name="exited" type="DATE"/>
        </addColumn>
        <addColumn tableName="incidents">
            <column name="exitreasonId" type="INT"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2619-incidents-fks" author="adamjhamer">
        <addForeignKeyConstraint baseTableName="incidents" baseColumnNames="signpostedreasonid" constraintName="fk_inc_signrId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="incidents" baseColumnNames="signpostedagencyid" constraintName="fk_inc_signaId"
                                 referencedTableName="contacts" referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="incidents" baseColumnNames="exitreasonid" constraintName="fk_inc_exitrId"
                                 referencedTableName="cfg_list_definitions" referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-2619-incidents-exit-review" author="adamjhamer">
        <addColumn tableName="incidents">
            <column name="reviewDate" type="DATE"/>
        </addColumn>
    </changeSet>

    <!-- see DEV-1326 which created the 'contracts' -->
    <changeSet id="DEV-2620-incident-servicetype" author="adamjhamer" context="!test-data">
        <validCheckSum>8:74bd0ae891f1d785251d2f83f3f7543a</validCheckSum>
        <insert tableName="servicetypes">
            <column name="id" valueNumeric="-500"/>
            <column name="version" valueNumeric="0"/>
            <column name="multipleReferrals" valueBoolean="false"/>
            <column name="name" value="incident-default"/>
            <column name="type" value="incident"/>
        </insert>
        <insert tableName="services">
            <column name="id" valueNumeric="-500"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="incident"/>
            <column name="servicetypeid" valueNumeric="-500"/>
        </insert>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-500"/>
            <column name="serviceId" valueNumeric="-500"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="0"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="18"/> <!-- referralView -->
            <column name="servicetypeId" valueNumeric="-500"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="9"/>  <!-- decideFinal -->
            <column name="servicetypeId" valueNumeric="-500"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="50"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="50"/>  <!-- close -->
            <column name="servicetypeId" valueNumeric="-500"/>
        </insert>
        <insert tableName="servicetypes_taskdefinitions">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
            <column name="taskDefinitionId" valueNumeric="11"/>  <!-- endFlow -->
            <column name="servicetypeId" valueNumeric="-500"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2619-incidents-fields" author="adamjhamer">
        <validCheckSum>8:0fb6c9e53b84ec1b059f309f1756eec4</validCheckSum>

        <dropForeignKeyConstraint baseTableName="incidents" constraintName="fk_inc_levelId"/>
        <dropForeignKeyConstraint baseTableName="incidents" constraintName="fk_inc_typeId"/>
        <dropColumn tableName="incidents" columnName="typeId"/>
        <dropColumn tableName="incidents" columnName="levelId"/>

        <addColumn tableName="incidents">
            <column name="emergencyServicesInvolved" type="boolean">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="incidents">
            <column name="hospitalisationInvolved" type="boolean">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- alter table incidents change column receivingservice receivingservice bit not null default false; -->
    <changeSet id="DEV-2619-incidents-start" author="adamjhamer">
        <validCheckSum>8:af1abe8ffe01d5c24bcd98a0efbb5167</validCheckSum>
        <addColumn tableName="incidents">
            <column name="supportworkerid" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="receivingservice" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="receivingservicedate" type="DATETIME">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseColumnNames="supportworkerid" baseTableName="incidents" constraintName="fk_inc_suppwk" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
    </changeSet>
    <!--
    <changeSet id="DEV-2619-incidents-start2" author="adamjhamer">
        <dropDefaultValue tableName="incidents" columnName="receivingservice"/>
        <addDefaultValue tableName="incidents" columnName="receivingservice" columnDataType="BOOLEAN" defaultValueBoolean="false"/>
        <addNotNullConstraint tableName="incidents" columnName="receivingservice"/>
    </changeSet>
    -->

    <changeSet id="DEV-2619-incidents-servicetype2" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="DEV-2619-incidents-servicetype" author="adamjhamer" changeLogFile="2023/incidents-domain"/>
        </preConditions>
        <dropColumn tableName="incidents" columnName="servicetypeid"/>
    </changeSet>

    <changeSet id="DEV-2619-incidents-status" author="adamjhamer">
        <dropColumn tableName="incidents" columnName="receivingService"/>
    </changeSet>

    <changeSet id="DEV-614-commands-inc-draft" author="adamjhamer">
        <addColumn tableName="inc_commands">
            <column name="draft" type="boolean" valueBoolean="false"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-614-commands-inc-draft-idx" author="adamjhamer">
        <createIndex tableName="inc_commands" indexName="idx_inccmd_draft">
            <column name="draft"/>
            <column name="serviceRecipientId"/>
            <column name="userId"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-2620-incidents-fields" author="adamjhamer">
        <addColumn tableName="incidents">
            <column name="reportedById" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseColumnNames="reportedById" baseTableName="incidents" constraintName="fk_inc_repby" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="false"/>
        <addColumn tableName="incidents">
            <column name="reportedBy" type="VARCHAR(64)"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2620-incidents-contact-fields" author="adamjhamer">
        <renameColumn tableName="incidents" oldColumnName="name" newColumnName="reportedByContact" columnDataType="VARCHAR(63)"/>
    </changeSet>

</databaseChangeLog>
