
CREATE FUNCTION svcRecCommandsDaysAgo() RETURNS INT
READS SQL DATA
  BEGIN
    DECLARE latestCommandDate DATETIME;
    SELECT MAX(created) INTO latestCommandDate FROM svcrec_commands;

    RETURN DATEDIFF(NOW(), latestCommandDate);

  END;

DROP FUNCTION IF EXISTS supportWorkDaysAgo;
CREATE FUNCTION supportWorkDaysAgo() RETURNS INT
  READS SQL DATA
  BEGIN
    DECLARE latestCommandDate DATETIME;
    SELECT MAX(created) INTO latestCommandDate FROM supportplanwork;

    RETURN DATEDIFF(NOW(), latestCommandDate);

  END;

DROP PROCEDURE IF EXISTS advanceSupportWork;
CREATE PROCEDURE advanceSupportWork(daysToAdvance INT)
  MODIFIES SQL DATA
  BEGIN
    UPDATE supportplanwork SET created = DATE_ADD(created, INTERVAL daysToAdvance DAY);
    UPDATE supportplanwork SET workdate = DATE_ADD(workdate, INTERVAL daysToAdvance DAY);
    UPDATE supportplanactions SET created = DATE_ADD(created, INTERVAL daysToAdvance DAY);
    UPDATE supportplanactions SET workdate = DATE_ADD(workdate, INTERVAL daysToAdvance DAY);
    UPDATE supportplanactions SET target = DATE_ADD(target, INTERVAL daysToAdvance DAY);
    UPDATE supportplancomments SET created = DATE_ADD(created, INTERVAL daysToAdvance DAY);
    UPDATE supportplanoutcomes SET created = DATE_ADD(created, INTERVAL daysToAdvance DAY);
    UPDATE supportplanoutcomes SET statuschangedate = DATE_ADD(statuschangedate, INTERVAL daysToAdvance DAY);
    UPDATE supportplananswers SET created = DATE_ADD(created, INTERVAL daysToAdvance DAY);
    UPDATE supportplananswers SET workdate = DATE_ADD(workdate, INTERVAL daysToAdvance DAY);
    UPDATE supportplanrisks SET created = DATE_ADD(created, INTERVAL daysToAdvance DAY);
  END;

select supportWorkDaysAgo();
call advanceSupportWork(supportWorkDaysAgo());

select * from supportplanwork;
select * from svcrec_commands;