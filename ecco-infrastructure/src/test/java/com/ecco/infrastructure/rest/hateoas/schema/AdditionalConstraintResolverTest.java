package com.ecco.infrastructure.rest.hateoas.schema;

import com.ecco.infrastructure.config.web.WebApiConfigBase;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.jsonFormatVisitors.JsonValueFormat;
import com.google.common.collect.ImmutableMap;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

import static org.hamcrest.Matchers.contains;
import static org.junit.Assert.*;
import static org.junit.Assume.assumeTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * @since 15/10/15
 */
@RunWith(SpringRunner.class)
@ContextConfiguration
@SuppressWarnings("unused")
public class AdditionalConstraintResolverTest {

    static class Service {
        public Map<String, String> fruitEnums(int param) {
            return ImmutableMap.of(
                    "1", "Thing 1",
                    "2", "Thing 2");
        }
    }

    @Configuration
    static class Config {
        @Bean
        public Service service() {
            return new Service();
        }

        @Bean
        public AdditionalConstraintResolver additionalConstraintResolver(ApplicationContext applicationContext) {
            return new AdditionalConstraintResolver(applicationContext);
        }
    }

    @Autowired
    private AdditionalConstraintResolver additionalConstraintResolver;


    static class DummyBean {
        @JsonSchemaProperty(format = JsonValueFormat.DATE_TIME)
        private LocalDateTime temperamentalField;

        @JsonSchemaProperty(enumValues = {
                @EnumConstant("strawberry"),
                @EnumConstant("raspberry")
        })
        private String pickyField;

        @JsonSchemaProperty(enumValues = {
                @EnumConstant(value = "blueberry", title = "Muffin"),
                @EnumConstant(value = "gooseberry", title = "Crumble"),
                @EnumConstant(value = "blackcurrant", title = "Tart")
        })
        private String fruityField;

        @JsonSchemaProperty(enumExpression = "#{@service.fruitEnums(1)}")
        private Long fruitId;


                public LocalDateTime getTemperamentalField() {
            return temperamentalField;
        }

        public String getPickyField() {
            return pickyField;
        }

        public String getFruityField() {
            return fruityField;
        }
    }


    @Test
    public void whenStandardFormatSpecifiedThenEnumSerializerWorks() throws NoSuchFieldException, IOException {
        ObjectMapper objectMapper = new WebApiConfigBase(){}.objectMapper();

        BeanProperty prop = Mockito.mock(BeanProperty.class);
        SerializerProvider provider = objectMapper.getSerializerProvider();
        SerializationConfig config = provider.getConfig();
        JsonGenerator jsonGenerator = Mockito.mock(JsonGenerator.class);
        JsonSchemaProperty annotation = DummyBean.class.getDeclaredField("temperamentalField").getAnnotation(JsonSchemaProperty.class);

        when(prop.getAnnotation(JsonSchemaProperty.class)).thenReturn(annotation);
        final Optional<JsonValueFormat> result = additionalConstraintResolver.getValueFormat(prop, provider);
        assumeTrue("Assume value format returned", result.isPresent());
        final JsonValueFormat valueFormat = result.get();
        assumeTrue("Assume value format to match annotation", annotation.format()[0].equals(valueFormat));

        objectMapper.writeValue(jsonGenerator, valueFormat);

        verify(jsonGenerator).writeString(annotation.format()[0].toString());
    }

    @Test
    public void whenEnumConstantsSpecified_thenOrderedMapReturned() throws NoSuchFieldException {
        BeanProperty prop = Mockito.mock(BeanProperty.class);
        JsonSchemaProperty annotation = DummyBean.class.getDeclaredField("pickyField").getAnnotation(JsonSchemaProperty.class);

        when(prop.getAnnotation(JsonSchemaProperty.class)).thenReturn(annotation);

        Optional<Map<String, String>> result = additionalConstraintResolver.getEnumConstants(prop);
        assertTrue("Expect map returned", result.isPresent());
        Map<String, String> map = result.get();
        assertEquals("Expect " + annotation.enumValues().length + " enum constants", annotation.enumValues().length, map.size());
        assertThat("Expect map keys in order", map.keySet(), contains(Arrays.stream(annotation.enumValues()).map(EnumConstant::value).toArray()));
        for (EnumConstant enumConstant : annotation.enumValues()) {
            assertEquals("Expect " + enumConstant.value() + " constant to be eponymous", enumConstant.value(), map.get(enumConstant.value()));
        }
    }

    @Test
    public void whenEnumConstantsSpecifiedWithTitle_thenOrderedMapReturned() throws NoSuchFieldException {
        BeanProperty prop = Mockito.mock(BeanProperty.class);
        JsonSchemaProperty annotation = DummyBean.class.getDeclaredField("fruityField").getAnnotation(JsonSchemaProperty.class);

        when(prop.getAnnotation(JsonSchemaProperty.class)).thenReturn(annotation);

        Optional<Map<String, String>> result = additionalConstraintResolver.getEnumConstants(prop);
        assertTrue("Expect map returned", result.isPresent());
        Map<String, String> map = result.get();
        assertEquals("Expect " + annotation.enumValues().length + " enum constants", annotation.enumValues().length, map.size());
        assertThat("Expect map keys in order", map.keySet(), contains(Arrays.stream(annotation.enumValues()).map(EnumConstant::value).toArray()));
        for (EnumConstant enumConstant : annotation.enumValues()) {
            assertEquals("Expect " + enumConstant.value() + " constant to have title", enumConstant.title(), map.get(enumConstant.value()));
        }
    }

    @Test
    public void whenEnumExpressionSpecified_thenOrderedMapReturned() throws NoSuchFieldException {
        BeanProperty prop = Mockito.mock(BeanProperty.class);
        JsonSchemaProperty annotation = DummyBean.class.getDeclaredField("fruitId").getAnnotation(JsonSchemaProperty.class);

        when(prop.getAnnotation(JsonSchemaProperty.class)).thenReturn(annotation);

        Optional<Map<String, String>> result = additionalConstraintResolver.getEnumConstants(prop);
        assertTrue("Expect map returned", result.isPresent());
        Map<String, String> map = result.get();
        assertEquals("Expect " + annotation.enumValues().length + " enum constants", 2, map.size());
        assertThat("Expect map keys in order", map.keySet(), contains("1", "2"));
        assertThat("Expect map titles in order", map.values(), contains("Thing 1", "Thing 2"));
    }
}
