
Configuration:

1. Each section in the text below needs pasting into the "Outcome Definitions (List/Upload)" screen to create the outcomes
2. Need to associate the outcomes to the building servicetype (settings -> referral process -> service type -> buildings-default)
3. Then go into the "Service definition configuration" screen for the buildings service type
    and select
        the building checks outcomes by id for both the needsAssessment and needsReduction, and
        the checklist ones for the needsChecklist


=== CHECKS ===

---- outcome: 87 ----
emergency equipment
lift alarms
lift alarm tested

fire doors
fire door release tested

fire alarms
fire alarm tested

---- outcome: 88 ----
safety and hygiene
water checks
water temperature range check (< 41 or > 60 deg C must be reported to maint.)

===== CHECKLIST OUTCOMES =====

------------------- PASTE THIS IN TO ADMIN SCREEN TO CREATE OUTCOME ------------
---- outcome: 89 ----
Bin Stores
group1
Is the bin store locked?
Has any rubbish been dumped?

------------------- PASTE THIS IN TO ADMIN SCREEN TO CREATE OUTCOME ------------
---- outcome: 90 ----
Electrical
group1
Has all equipment been tested and labelled?
Are there any damaged electrical cables?
Are there any damaged sockets or plugs?
Are there any double adaptors or extension leads in use in the communal areas?
Are there any cables loose or trailing?
Are any light bulbs missing or defective?
Is there any personal untested electrical equipment in use in any communal areas?
Are electrical equipment ventilation ports clear of any blockages?


------------------- PASTE THIS IN TO ADMIN SCREEN TO CREATE OUTCOME ------------
---- outcome: 91 ----
Cleaning
group1
Is the cleanliness of the scheme kitchen and toilets satisfactory?
Are all chemicals stored and locked in a store cupboard?


------------------- PASTE THIS IN TO ADMIN SCREEN TO CREATE OUTCOME ------------
---- outcome: 92 ----
First Aid
group1
Is the first aid kit complete and up to date?
