package com.ecco.integration.core;

import static com.ecco.dto.ClientDefinition.BuilderFactory.create;

import java.util.Collection;
import java.util.Map;
import java.util.Properties;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import com.ecco.dto.ClientDefinition;

/**
 * Bean to run post startup operations common to ECCO integrations
 */
public class AfterStartup implements ApplicationRunner {

    private Logger log = LoggerFactory.getLogger(getClass());

    @Value("${git.commit.id}")
    private String gitCommitId;

    @Value("${git.commit.time}")
    private String gitCommitTime;

    @Autowired
    @Qualifier("referenceDataMapping")
    Properties referenceDataMapping;

    private Function<ClientDefinition, Collection<ClientDefinition>> searchClients;

    public AfterStartup(Function<ClientDefinition, Collection<ClientDefinition>> searchClients) {
        this.searchClients = searchClients;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        String SEP = System.getProperty("line.separator");
        log.info(SEP + "============================================================" + SEP +
                "ECCO integration build version: " + gitCommitTime + " (" + gitCommitId + ")" + SEP +
                "============================================================");

        log.info("Listing first few mapping.properties to verify: ");
        var propsSet = this.referenceDataMapping.entrySet();
        var i = 0;
        for (Map.Entry<Object, Object> p : propsSet) {
            if (i <= 5) {
                log.info(" - mapping.properties: {}={}", p.getKey(), p.getValue());
            }
            i++;
        }

        ClientDefinition exemplar = create().firstName("Donald").lastName("Trump").build();
        Collection<ClientDefinition> clients = searchClients.apply(exemplar);
        log.info("Startup check resulted in {} clients named Donald Trump being found.", clients.size());

        exemplar = create().externalClientRef("11111").build();
        clients = searchClients.apply(exemplar);
        log.info("Startup check resulted in {} clients with ref 11111 being found.", clients.size());
    }
}
