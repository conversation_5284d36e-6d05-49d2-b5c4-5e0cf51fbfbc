package com.ecco.integration.core;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * Config for integrations (eg ql etc).
 *
 * Used by spring's WebSecurityConfigurerAdapter.
 *
 * Allows access from localhost to the web api without authentication.
 *
 * @since 25/11/14
 */
@Configuration
@EnableWebSecurity
public class WebSecurityConfigurer extends WebSecurityConfigurerAdapter {
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            .httpBasic().and()
            .csrf().disable()
            .authorizeRequests()
                // will permit if ext client url is configured as http://127.0.0.1:8090/something/ (but not without last segment)
                .antMatchers("/*/clients/query").permitAll()
                .antMatchers("/*/clients/").permitAll()
                .antMatchers("/*/client/update").permitAll()
                .antMatchers("/*/client/*/flags/").permitAll()
                .antMatchers("/*/events/").permitAll()
                .antMatchers("/*/events/flags/").permitAll()
                .anyRequest().authenticated();
    }
}
