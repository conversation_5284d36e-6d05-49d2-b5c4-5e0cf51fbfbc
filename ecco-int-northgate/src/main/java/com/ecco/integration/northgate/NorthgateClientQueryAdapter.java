package com.ecco.integration.northgate;

import static org.springframework.http.HttpStatus.OK;
import static org.springframework.util.StringUtils.hasText;

import com.ecco.dto.ClientDefinition;
import com.ecco.integration.api.TooManyResultsException;

import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * Provides an implementation of the default REST API which delegates to Northgate to return its results.
 *
 * @see <a href='https://eccosolutions.atlassian.net/secure/attachment/11900/northgate_pfp_specifics2.txt'>query details</a>.
 */
@RestController
@RequestMapping("/northgate")
public class NorthgateClientQueryAdapter {

    private Logger log = LoggerFactory.getLogger(getClass());

    private static final String REF_ALIAS = "Ref";
    private static final String CODE_ALIAS = "Code";
    private static final String SURNAME_ALIAS = "Surname";
    private static final String FORENAME_ALIAS = "Firstname";
    private static final String GENDER_ALIAS = "Gender";
    private static final String DOB_ALIAS = "DOB";
    private static final String NI_ALIAS = "NI";
    private static final String LANGUAGE_ALIAS = "Language";
    private static final String DISABILITY_ALIAS = "Disability";
    private static final String ETHNICITY_ALIAS = "Ethnicity";
    private static final String RELIGION_ALIAS = "Religion";
    private static final String SEXUALITY_ALIAS = "SO";
    private static final String ADDRESS_ALIAS = "Address";

    private static final String REF_PARAM = "ref";
    private static final String LASTNAME_PARAM = "lastname";
    private static final String FIRSTNAME_PARAM = "firstname";
    private static final String GENDER_PARAM = "gender";
    private static final String BIRTHDATE_PARAM = "birthdate";
    private static final String GENDER_DOMAIN = "SEX";
    private static final String LANGUAGE_DOMAIN = "SUPPORTED_NLD";
    private static final String DISABILITY_DOMAIN = "CENDISABILTY";
    private static final String ETHNICITY_DOMAIN = "ETHNIC2";
    private static final String RELIGION_DOMAIN = "RELIGION";
    private static final String SEXUALITY_DOMAIN = "SEXUAL_ORIENTATION";
    protected static final String ECCO_DOMAIN = "ECCO";
    private static final String FIELDS_CLAUSE = "ecco_parties.PAR_REFNO as " + REF_ALIAS + ", " +
            "ecco_parties.PAR_REUSABLE_REFNO as " + CODE_ALIAS + ", " +
            "ecco_parties.par_per_surname as " + SURNAME_ALIAS + ", " +
            "ecco_parties.par_per_forename as " + FORENAME_ALIAS + ", " +
            "ecco_parties.par_per_frv_fge_code as " + GENDER_ALIAS + ", " +
            "ecco_parties.PAR_PER_DATE_OF_BIRTH as " + DOB_ALIAS + ", " +
            "ecco_parties.PAR_PER_NI_NO as " + NI_ALIAS + ", " +
            "ecco_parties.PAR_PER_FRV_FNL_CODE as " + LANGUAGE_ALIAS + ", " +
            "ecco_parties.PAR_PER_HOU_DISABLED_IND as " + DISABILITY_ALIAS + ", " +
            "ecco_parties.PAR_PER_FRV_FEO_CODE as " + ETHNICITY_ALIAS + ", " +
            "ecco_parties.PAR_PER_HOU_HRV_RLGN_CODE as " + RELIGION_ALIAS + ", " +
            "ecco_parties.PAR_PER_HOU_HRV_SEXO_CODE as " + SEXUALITY_ALIAS + ", " +
            "ecco_addresses.ADR_LINE_ALL as " + ADDRESS_ALIAS + " ";

    private static final String BASE_CLIENT_QUERY =
            "FROM ecco_parties " +
            "LEFT OUTER JOIN ecco_address_usages " +
            "ON ecco_parties.par_refno = ecco_address_usages.aus_par_refno and ecco_address_usages.aus_aut_far_code = 'CONTACT' " +
            "LEFT OUTER JOIN ecco_addresses " +
            "ON ecco_address_usages.aus_adr_refno = ecco_addresses.adr_refno " +
            "WHERE ";
    private static final String CLIENT_QUERY_EXTERNALREF_CLAUSE = "lower(ecco_parties.PAR_REFNO) = :" + REF_PARAM + " ";
    private static final String CLIENT_QUERY_LASTNAME_CLAUSE = "lower(ecco_parties.par_per_surname) LIKE :" + LASTNAME_PARAM + " ";
    private static final String CLIENT_QUERY_FIRSTNAME_CLAUSE = "AND lower(ecco_parties.par_per_forename) LIKE :" + FIRSTNAME_PARAM + " ";
    private static final String CLIENT_QUERY_GENDER_CLAUSE = "AND ecco_parties.par_per_frv_fge_code = :" + GENDER_PARAM + " ";
    private static final String CLIENT_QUERY_BIRTHDATE_CLAUSE = "AND ecco_parties.par_per_date_of_birth = :" + BIRTHDATE_PARAM + " ";

    @Value("${max.results}")
    private int maxResults = 100;

    private final NamedParameterJdbcTemplate jdbcTemplate;
    private final Properties referenceDataMapping;

    @Autowired
    public NorthgateClientQueryAdapter(NamedParameterJdbcTemplate jdbcTemplate, @Qualifier("referenceDataMapping") Properties referenceDataMapping) {
        this.jdbcTemplate = jdbcTemplate;
        this.referenceDataMapping = referenceDataMapping;
    }

    @PostJson(value = "/clients/", produces = APPLICATION_JSON_VALUE)
    public ClientDefinition createOrUpdateClient(@RequestBody ClientDefinition newClient) {

        throw new UnsupportedOperationException("This isn't implemented");
    }

    @PostJson(value = "/clients/query", produces = APPLICATION_JSON_VALUE)
    @ResponseStatus(OK)
    public Collection<ClientDefinition> queryClientsByExample(@RequestBody final ClientDefinition exemplar) {
        final StringBuilder query = new StringBuilder(BASE_CLIENT_QUERY);
        final Map<String, Object> queryParams = new HashMap<>(4);

        if (!hasText(exemplar.getExternalClientRef())) {
            Assert.state((hasText(exemplar.getLastName())), "Either externalClientRef or lastName must be specified");
            query.append(CLIENT_QUERY_LASTNAME_CLAUSE);
            queryParams.put(LASTNAME_PARAM, exemplar.getLastName().toLowerCase() + "%");
        } else {
            query.append(CLIENT_QUERY_EXTERNALREF_CLAUSE);
            queryParams.put(REF_PARAM, exemplar.getExternalClientRef().toLowerCase());
        }

        if (hasText(exemplar.getFirstName())) {
            query.append(CLIENT_QUERY_FIRSTNAME_CLAUSE);
            queryParams.put(FIRSTNAME_PARAM, exemplar.getFirstName().toLowerCase() + "%");
        }
        if (exemplar.getGenderKey() != null) {
            query.append(CLIENT_QUERY_GENDER_CLAUSE);
            String mappedValue = referenceDataMapping.getProperty(ECCO_DOMAIN + "." + GENDER_DOMAIN + "." + exemplar.getGenderKey());
            queryParams.put(GENDER_PARAM, mappedValue);
        }
        if (exemplar.getBirthDate() != null) {
            query.append(CLIENT_QUERY_BIRTHDATE_CLAUSE);
            queryParams.put(BIRTHDATE_PARAM, exemplar.getBirthDate().toDate());
        }

        Integer count = jdbcTemplate.queryForObject("select count(*) " + query, queryParams, Integer.class);
        if (count > maxResults) {
            throw new TooManyResultsException();
        }

        return jdbcTemplate.query("select " + FIELDS_CLAUSE + query, queryParams,
                (rs, rowNum) -> {

                    final ClientDefinition.Builder builder = ClientDefinition.BuilderFactory.create()
                    .externalClientRef(rs.getString(REF_ALIAS))
                    .externalClientSource(exemplar.getExternalClientSource())
                    .code(rs.getString(CODE_ALIAS)) // beware: this could clash iwth other systems
                    .lastName(rs.getString(SURNAME_ALIAS))
                    .firstName(rs.getString(FORENAME_ALIAS))
                    .genderKey(getMappedReferenceData(GENDER_DOMAIN, rs.getString(GENDER_ALIAS)))
                    // TODO genderAtBirthKey
                    .birthDate(new LocalDate(rs.getDate(DOB_ALIAS)))
                    // TODO code
                    .ni(rs.getString(NI_ALIAS))
                    .firstLanguageKey(getMappedReferenceData(LANGUAGE_DOMAIN, rs.getString(LANGUAGE_ALIAS)))
                    .ethnicOriginKey(getMappedReferenceData(ETHNICITY_DOMAIN, rs.getString(ETHNICITY_ALIAS)))
                    // TODO nationalityKey
                    // TODO maritalStatusKey
                    .religionKey(getMappedReferenceData(RELIGION_DOMAIN, rs.getString(RELIGION_ALIAS)))
                    .disabilityKey(getMappedReferenceData(DISABILITY_DOMAIN, rs.getString(DISABILITY_ALIAS)))
                    .sexualOrientationKey(getMappedReferenceData(SEXUALITY_DOMAIN, rs.getString(SEXUALITY_ALIAS)))
                    .address(new String[]{rs.getString(ADDRESS_ALIAS)}) // TODO: multiple lines?
                    ;
                    // TODO phoneNumber / mobileNumber / email
                    return builder.build();
                });
    }

    /** Attempt to resolve the returned value using the supplied prefix and returned value as the suffix */
    private String getMappedReferenceData(String prefix, String key) {
        String result = referenceDataMapping.getProperty(prefix + "." + key);
        if (result == null) {
            log.warn("No mapping found for database value '" + key + "' with prefix: " + prefix);
        }
        return result;
    }

}
