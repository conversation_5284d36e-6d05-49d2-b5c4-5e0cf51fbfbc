# Reference data mapping between external system and ecco
# NB We have an ECCO_FIRST_REF_VALUES table available to us

# CONVERTING FROM external system (eg result of queryClientsByExample)
# external system <DOMAIN>.<VALUE> = ecco business key

# CONVERTING FROM ecco (eg parameter of queryClientsB<PERSON>Example)
# obtains business key from ecco
# <ECCO>.<DOMAIN>.<BUSKEY> = external system <VALUE>

# CONVERTING FROM ecco (eg create - NB updateClient just passes the BUSKEY through to create)
# obtains business key from ecco
# <ECCO>.<DOMAIN>.<BUSKEY> = external system <VALUE>

# CONVERTING FROM ecco NULL if needed to force overwrite (eg update<PERSON><PERSON> needs a BUSKEY through to create)
# NB This is effectively a double lookup, since the update passes through create, which gets the ECCO.DOMAIN.BUSKEY
# <ECCO>.<DOMAIN>.DEFAULT = <BUSKEY>


CENDISABILTY.YES=YES
CENDISABILTY.NO=NO
CENDISABILTY.Y=YES
CENDISABILTY.N=NO
CENDISABILTY.REFUSED=REFUSED
ECCO.CENDISABILTY.DEFAULT=REFUSED

# Non-current values
#ETHNIC2.BLACK - no mapping
#ETHNIC2.MIXED\ RACE     Mixed: Other
#ETHNIC2.OTHERRACE       Mixed: Other
ETHNIC2.WHITE=WHITE
#ETHNIC2.MIXEDRACE       Mixed: Other
#ETHNIC2.NOTONACAD       Missing
#ETHNIC2.OTHER           Chinese/Other: Other
#ETHNIC2.MIXED           Mixed: Other
ETHNIC2.ASIAN=ASIAN
ETHNIC2.CHINOTHER=CHINOTHER
# Current values
#ETHNIC2.REFUSED         Do not wish to disclose
ETHNIC2.WHITEBRIT=WHITEBRIT
ETHNIC2.WHITEIRISH=WHITEIRISH
ETHNIC2.OTHERWHITE=OTHERWHITE
ETHNIC2.CHINESE=CHINESE
#ETHNIC2.INDIAN          Asian/Asian British: Indian
#ETHNIC2.PAKISTANI       Asian/Asian Birtish: Pakistani
#ETHNIC2.BANGLADESH      Asian/Asian British: Bangladeshi
#ETHNIC2.OTHERASIAN      Asian/Asian British: Other
#ETHNIC2.CARIBBEAN       Black/Black British: Caribbean
#ETHNIC2.AFRICAN         Black/Black British: African
#ETHNIC2.OTHERBLACK      Black/Black British: Other
#ETHNIC2.MIXEDWBCAR      Mixed: White & Black Caribbean
#ETHNIC2.MIXEDWBAFR      Mixed: White & Black African
#ETHNIC2.MIXEDWHIAS      Mixed: White & Asian
#ETHNIC2.OTHERMIXED      Mixed: Other
#ETHNIC2.OTHEROTHER      Chinese/Other: Other
#ETHNIC2.WHITESCOT       White: Other
#ETHNIC2.WHITEOBRIT      White: British

# used in testing ApplicationTests which use the liquibase changelog-master.xml
ETHNIC2.GYPSY=GYPSY

#ETHNIC2.NOTPERSON       Missing
#ETHNIC2.ARAB            Arabic: Other
ECCO.ETHNIC2.DEFAULT = REFUSED

RELIGION.CHRISTIAN=94
#RELIGION.HINDU      Hindu
#RELIGION.MUSLIM     Muslim
#RELIGION.SIKH       Sikh
RELIGION.JEWISH=JEWISH
RELIGION.BUDDHIST=BUDDHIST
#RELIGION.REFUSED    Not disclosed
#RELIGION.OTHER      Other
#RELIGION.NORELIGION None
#RELIGION.NOTPERSON  Missing
ECCO.RELIGION.DEFAULT=NOTPERSON

SEX.MALE=MALE
SEX.FEMALE=FEMALE
SEX.TRANGENDER=TRANGENDER
SEX.UNKNOWN=UNKNOWN
SEX.NOTPERSON=NOTPERSON

ECCO.SEX.MALE=MALE
ECCO.SEX.FEMALE=FEMALE
ECCO.SEX.TRANGENDER=TRANGENDER
ECCO.SEX.UNKNOWN=UNKNOWN
ECCO.SEX.NOTPERSON=NOTPERSON
ECCO.SEX.DEFAULT=UNKNOWN

# into ECCO: SEXUAL_ORIENTATION maps to "sexuality" list definition
SEXUAL_ORIENTATION.HETERO=HETERO
SEXUAL_ORIENTATION.GAY=GAY
SEXUAL_ORIENTATION.LESBIAN=LESBIAN
SEXUAL_ORIENTATION.BISEXUAL=BISEXUAL
# SEXUAL_ORIENTATION.TRANSGEND - no mapping
SEXUAL_ORIENTATION.NOTSTATED=NOTSTATED
SEXUAL_ORIENTATION.NOTPERSON=NOTPERSON
ECCO.SEXUAL_ORIENTATION.DEFAULT=NOTSTATED

# SUPPORTED_NLD.ALBANIAN - no mapping
# SUPPORTED_NLD.AMHARIC - no mapping
# SUPPORTED_NLD.ARABIC - no mapping
# SUPPORTED_NLD.BENGALI - no mapping
SUPPORTED_NLD.CANTONESE=CANTONESE
# SUPPORTED_NLD.CROATIAN - no mapping
# SUPPORTED_NLD.CZECH - no mapping
SUPPORTED_NLD.ENGLISH=ENGLISH
# SUPPORTED_NLD.FARSI - no mapping
# SUPPORTED_NLD.FRENCH - no mapping
# SUPPORTED_NLD.GERMAN - no mapping
# SUPPORTED_NLD.GREEK - no mapping
# SUPPORTED_NLD.GUJARATI - no mapping
#SUPPORTED_NLD.HINDI - no mapping
# SUPPORTED_NLD.ITALIAN - no mapping
# SUPPORTED_NLD.KURDISH - no mapping
SUPPORTED_NLD.MANDARIN=MANDARIN
# SUPPORTED_NLD.POLISH - no mapping
# SUPPORTED_NLD.PORTUGESE - no mapping
# SUPPORTED_NLD.PUNJABI - no mapping
# SUPPORTED_NLD.ROMANIAN - no mapping
# SUPPORTED_NLD.RUSSIAN - no mapping
# SUPPORTED_NLD.SERBIAN - no mapping
# SUPPORTED_NLD.SOMALI - no mapping
# SUPPORTED_NLD.SPANISH - no mapping
# SUPPORTED_NLD.TAMIL - no mapping
#SUPPORTED_NLD.TURKISH - no mapping
SUPPORTED_NLD.URDU=URDU
# SUPPORTED_NLD.VIETNAMESE - no mapping
# SUPPORTED_NLD.ARMENIAN - no mapping
# SUPPORTED_NLD.BULGARAIN - no mapping
# SUPPORTED_NLD.BURMESE - no mapping
# SUPPORTED_NLD.DUTCH - no mapping
# SUPPORTED_NLD.ESTONIAN - no mapping
# SUPPORTED_NLD.GAELIC - no mapping
# SUPPORTED_NLD.GEORGIAN - no mapping
# SUPPORTED_NLD.HAUSA - no mapping
# SUPPORTED_NLD.HUNGARIAN - no mapping
# SUPPORTED_NLD.IBO - no mapping
# SUPPORTED_NLD.INDONESIAN - no mapping
# SUPPORTED_NLD.LINGALA - no mapping
# SUPPORTED_NLD.MALAY - no mapping
# SUPPORTED_NLD.NEPALI - no mapping
# SUPPORTED_NLD.PUSHTO - no mapping
# SUPPORTED_NLD.SERBOCROAT - no mapping
# SUPPORTED_NLD.SLOVAK - no mapping
# SUPPORTED_NLD.SLOVENIAN - no mapping
# SUPPORTED_NLD.SWAHILI - no mapping
# SUPPORTED_NLD.TELUGU - no mapping
# SUPPORTED_NLD.THAI - no mapping
# SUPPORTED_NLD.TIGRINIA - no mapping
# SUPPORTED_NLD.WELSH - no mapping
# SUPPORTED_NLD.YORUBA - no mapping
# SUPPORTED_NLD.CHICHEWA - no mapping
# SUPPORTED_NLD.BSL - no mapping
# SUPPORTED_NLD.LITHUANIAN - no mapping
ECCO.SUPPORTED_NLD.DEFAULT=URDU
