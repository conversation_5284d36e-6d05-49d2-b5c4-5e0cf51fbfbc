package com.ecco.integration.ql;

import com.ecco.dto.BuildableDto;
import com.ecco.dto.DtoBuilder;
import com.ecco.dto.DtoBuilderProxy;
import com.ecco.dto.ProxyDtoBuilderProxy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Getter;

@JsonDeserialize(as = FlagAlertDefinition.Impl.class)
public interface FlagAlertDefinition extends BuildableDto<FlagAlertDefinition> {

    /** The external system name. */
    //String getExternalClientSource();

    /**
     * The reference to the client in the external system.
     * Required for the API call.
     */
    String getExternalClientRef();

    Boolean getAlert1();
    Boolean getAlert2();
    Boolean getAlert3();
    Boolean getAlert4();
    Boolean getAlert5();
    Boolean getAlert6();
    Boolean getAlert7();
    Boolean getAlert8();
    Boolean getAlert9();

    interface Builder extends DtoBuilder<FlagAlertDefinition> {
        //Builder externalClientSource(String externalClientSource);
        Builder externalClientRef(String externalClientRef);
        Builder alert1(Boolean alert);
        Builder alert2(Boolean alert);
        Builder alert3(Boolean alert);
        Builder alert4(Boolean alert);
        Builder alert5(Boolean alert);
        Builder alert6(Boolean alert);
        Builder alert7(Boolean alert);
        Builder alert8(Boolean alert);
        Builder alert9(Boolean alert);
    }

    final class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, FlagAlertDefinition.class);
        }

        public static Builder create(FlagAlertDefinition template) {
            return DtoBuilderProxy.newInstance(Builder.class, template);
        }
    }

    @SuppressWarnings("UnusedDeclaration")
    @Getter
    final class Impl implements FlagAlertDefinition {
        //private String externalClientSource;
        private String externalClientRef;
        private Boolean alert1;
        private Boolean alert2;
        private Boolean alert3;
        private Boolean alert4;
        private Boolean alert5;
        private Boolean alert6;
        private Boolean alert7;
        private Boolean alert8;
        private Boolean alert9;
    }

}
