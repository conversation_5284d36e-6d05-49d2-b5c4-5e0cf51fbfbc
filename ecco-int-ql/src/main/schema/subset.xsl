<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xsl:output encoding="UTF-8" indent="yes"/>

    <!-- By default, copy all nodes, applying templates to child nodes. -->
    <xsl:template match="node()|@*">
        <xsl:copy>
            <xsl:apply-templates select="node()|@*"/>
        </xsl:copy>
    </xsl:template>

    <!-- By default, exclude all wsdl:operation elements. -->
    <xsl:template match="wsdl:operation"/>

    <!-- Override the previous rule to copy these wsdl:operation elements and child elements verbatim. -->
    <!-- Child elements are copied without applying template rules. -->
    <xsl:template match="wsdl:operation[@name = 'CreateClient'
                                        or @name = 'CreateContact'
                                        or @name = 'CreateComplaintNotes'
                                        or @name = 'UpdateClientDetails'
                                        or @name = 'GetClientDetails'
                                        or @name = 'CreateClientImpairments'
                                        or @name = 'GetClientImpairments'
                                        or @name = 'DeleteClientImpair'
                                        ]">
        <xsl:apply-templates select="." mode="copy"/>
    </xsl:template>

    <!-- By default, exclude all wsdl:message elements. -->
    <xsl:template match="wsdl:message"/>

    <!-- Override the previous rule to copy these wsdl:message elements and child elements verbatim. -->
    <!-- Child elements are copied without applying template rules. -->
    <xsl:template match="wsdl:message[@name = 'IQLWCFService_CreateClient_InputMessage'
                                      or @name = 'IQLWCFService_CreateClient_OutputMessage'
                                      or @name = 'IQLWCFService_CreateContact_InputMessage'
                                      or @name = 'IQLWCFService_CreateContact_OutputMessage'
                                      or @name = 'IQLWCFService_CreateComplaintNotes_InputMessage'
                                      or @name = 'IQLWCFService_CreateComplaintNotes_OutputMessage'
                                      or @name = 'IQLWCFService_UpdateClientDetails_InputMessage'
                                      or @name = 'IQLWCFService_UpdateClientDetails_OutputMessage'
                                      or @name = 'IQLWCFService_GetClientDetails_InputMessage'
                                      or @name = 'IQLWCFService_GetClientDetails_OutputMessage'
                                      or @name = 'IQLWCFService_CreateClientImpairments_InputMessage'
                                      or @name = 'IQLWCFService_CreateClientImpairments_OutputMessage'
                                      or @name = 'IQLWCFService_GetClientImpairments_InputMessage'
                                      or @name = 'IQLWCFService_GetClientImpairments_OutputMessage'
                                      or @name = 'IQLWCFService_DeleteClientImpairments_InputMessage'
                                      or @name = 'IQLWCFService_DeleteClientImpairments_OutputMessage'
                                      ]">
        <xsl:apply-templates select="." mode="copy"/>
    </xsl:template>

    <!-- By default, exclude all xs:element elements. -->
    <xsl:template match="xs:element"/>

    <!-- Override the previous rule to copy these xs:element elements and child elements verbatim. -->
    <!-- Child elements are copied without applying template rules. -->
    <xsl:template match="xs:element[@name = 'CreateClient'
                                    or @name = 'CreateClientResponse'
                                    or @name = 'CreateContact'
                                    or @name = 'CreateContactResponse'
                                    or @name = 'CreateComplaintNotes'
                                    or @name = 'CreateComplaintNotesResponse'
                                    or @name = 'UpdateClientDetails'
                                    or @name = 'UpdateClientDetailsResponse'
                                    or @name = 'GetClientDetails'
                                    or @name = 'GetClientDetailsResponse'
                                    or @name = 'CreateClientImpairments'
                                    or @name = 'CreateClientImpairmentsResponse'
                                    or @name = 'GetClientImpairments'
                                    or @name = 'GetClientImpairmentsRequest'
                                    or @name = 'GetClientImpairmentsResponse'
                                    or @name = 'DeleteClientImpair'
                                    or @name = 'DeleteClientImpairResponse'
                                    ]">
        <xsl:apply-templates select="." mode="copy"/>
    </xsl:template>

    <!-- By default, exclude all xs:simpleType elements. -->
    <xsl:template match="xs:simpleType"/>

    <!-- By default, exclude all xs:complexType elements. -->
    <xsl:template match="xs:complexType"/>

    <!-- Override the previous rule to copy these xs:complexType elements and child elements verbatim. -->
    <!-- Child elements are copied without applying template rules. -->
    <xsl:template match="xs:complexType[@name = 'LoginToDB'
                                        or @name = 'Client'
                                        or @name = 'Contact'
                                        or @name = 'ArrayOfComplaintNotes'
                                        or @name = 'ComplaintNotes'
                                        or @name = 'iHSGUpdateClientDetails'
                                        or @name = 'iHSGGetClientDetails'
                                        or @name = 'iHSGClientDetails'
                                        or @name = 'ArrayOfGetClientImpairmentsResponse'
                                        or @name = 'GetClientImpairmentsRequest'
                                        or @name = 'GetClientImpairmentsResponse'
                                        ]">
        <xsl:apply-templates select="." mode="copy"/>
    </xsl:template>

    <!-- Copy entire schema for built-in .NET types verbatim, without applying any other template rules. -->
    <xsl:template match="xs:schema[@targetNamespace = 'http://schemas.microsoft.com/2003/10/Serialization/']">
        <xsl:apply-templates select="." mode="copy"/>
    </xsl:template>

    <!-- Copies nodes and child nodes verbatim, without applying any other template rules. -->
    <xsl:template match="node()|@*" mode="copy">
        <xsl:copy>
            <xsl:apply-templates select="node()|@*" mode="copy"/>
        </xsl:copy>
    </xsl:template>
</xsl:stylesheet>