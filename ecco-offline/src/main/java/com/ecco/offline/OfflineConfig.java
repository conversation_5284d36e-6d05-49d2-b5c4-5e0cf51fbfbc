package com.ecco.offline;

import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.config.root.EccoEnvironment;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Configuration
public class OfflineConfig implements WebMvcConfigurer {

    private static final String CLASSPATH_COM_ECCO_OFFLINE_STATIC_FILES = "classpath:/com/ecco/offline/staticFiles/";


//    @Bean
//    @ConditionalOnMissingBean(RequestMappingHandlerAdapter.class)
//    public RequestMappingHandlerAdapter requestMappingHandlerAdapter() {
//        RequestMappingHandlerAdapter requestMappingHandlerAdapter = new RequestMappingHandlerAdapter();
//        requestMappingHandlerAdapter.setIgnoreDefaultModelOnRedirect(true);
//        return requestMappingHandlerAdapter;
//    }


    @Bean
    public AlwaysNetworkResourceProvider alwaysNetworkResourceProvider(ApplicationProperties applicationProperties) {
        return new AlwaysNetworkResourceProvider(applicationProperties);
    }

    @Bean
    public ResourceHttpRequestHandler resourceRequestHandler(ApplicationContext applicationContext) {
        List<Resource> locations = Collections.singletonList(
                applicationContext.getResource(CLASSPATH_COM_ECCO_OFFLINE_STATIC_FILES));

        ResourceHttpRequestHandler resourceHandler = new ResourceHttpRequestHandler();
        resourceHandler.setLocations(locations);
        resourceHandler.setCacheSeconds(0);
        return resourceHandler;
    }

    @Bean
    public OfflineResourceController offlineResourceController(
            ApplicationContext applicationContext,
            Collection<OfflineResourceProvider> offlineResourceProviders,
            ResourceHttpRequestHandler resourceRequestHandler
            ) {

        return new OfflineResourceController(applicationContext, offlineResourceProviders, resourceRequestHandler);
    }

    @Bean
    public RarelyChangingStaticFileResourceProvider rarelyChangingStaticFileResourceProvider() {
        return new RarelyChangingStaticFileResourceProvider();
    }

    @Bean
    public OfflineResourceProvider templatedFileOfflineResourceProvider(ApplicationProperties applicationProperties,
                                                                        ApplicationContext context) throws IOException {
        // Get EccoEnvironment from root application context as we can't get it
        // as a bean because it's not registered and only our own environment will get injected
        ApplicationContext parent = context.getParent();
        if (parent == null) parent = context; // Hack for spring-boot
        EccoEnvironment eccoEnvironment = ((EccoEnvironment) parent.getEnvironment());
        return new TemplatedFileOfflineResourceProvider(applicationProperties, eccoEnvironment.isDevMode());
    }

    @ConditionalOnMissingBean
    @Bean
    public OfflineResourceProvider applicationPropertiesOfflineResourceProvider(ApplicationProperties applicationProperties) {
        return new ApplicationPropertiesOfflineResourceProvider(applicationProperties);
    }
}
