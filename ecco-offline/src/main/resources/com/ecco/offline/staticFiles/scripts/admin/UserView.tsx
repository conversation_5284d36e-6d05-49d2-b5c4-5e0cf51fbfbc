import {
    Change<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    useAppBarOptions,
    usePromise,
    UserForm,
    useServicesContext,
    useUser
} from "ecco-components";
import {showNotification} from "ecco-components-core";
import * as React from "react";
import {FC, ReactNode, useState} from "react";
import {AuditHistory} from "../service-recipients/components/AuditHistory";
import {
    Alert,
    Box,
    Button,
    Card,
    CardActions,
    CardContent,
    CardHeader,
    Grid,
    Typography
} from '@eccosolutions/ecco-mui';
import {AclAjaxRepository} from "ecco-dto";
import {AclEntryDto, getRelation, RECENT_USER_CHANGE, UserAjaxRepository, UserDto} from 'ecco-dto';
import {showModalWithSubmitCancel} from "../components/MUIConverterUtils";
import AclEntryControl from "./acls/AclEntryControl";
import {WebApiError} from "@eccosolutions/ecco-common";
import {applicationRootPath} from "application-properties";
import {UserMfaResetCommand} from "ecco-commands";
import * as applicationProperties from "application-properties";

type Props = {
    username: string
};

export function useAcls(username: string) {
    const {apiClient} = useServicesContext();
    const {resolved, error, loading, reload} = usePromise(
        () => {
            return new AclAjaxRepository(apiClient).findByUsername(username);
        },
        [username]);
    return {acls: resolved, error, loading, reload};
}

function SummariseAcls({acls, aaa}: { acls: AclEntryDto[], aaa: boolean }) {
    const {sessionData} = useServicesContext();
    if (aaa) {
        return <Typography variant="body1">'all service access' enabled (see 'access groups')</Typography>;
    } else if (!acls || !acls.length) {
        return <Typography variant="body1">no service access configured</Typography>;
    }
    const services = acls.filter(acl => acl.clazz.endsWith("ServiceAclId"));
    const projects = acls.filter(acl => acl.clazz.endsWith("ProjectAclId"));
    const projectsLabel = sessionData.getDto().messages["projects"];

    return <dl>
            <dt>services</dt>
            <dd>
                {services.length == 0 ? "-" : services.map(acl => sessionData.getService(acl.secureObjectId)?.name).join(", ") || ('reset cache')}
            </dd>
            <dt>{projectsLabel}</dt>
            <dd>
                {projects.length == 0 ? "-" : projects.map(
                    acl => acl.secureObjectId == -1 ? `can access all ${projectsLabel} within selected services`
                : sessionData.getProject(acl.secureObjectId)?.name).join(", ") || ('reset cache')}
            </dd>
        </dl>;
}

export function UserHeader(props: {user: UserDto, summary?: true | undefined}) {
    const {individual, username} = props.user;
    return <CardHeader
        title={<>
            {individual.firstName || ""} {individual.lastName || ""} ({username})
        </>}
        subheader={props.summary !== undefined ? undefined : individual.email}
    />;
}

export const UserView: FC<Props> = props => {
    const {user, loading, reload} = useUser(props.username);
    const [editing, setEditing] = useState(false);
    const [changePwd, setChangePwd] = useState(false);

    const {acls, reload: reloadAcls} = useAcls(props.username);
    const aclOn = user && getRelation(user, "acl");
    const aclPreConfigAllowed = user && getRelation(user, "acl-config");

    useAppBarOptions(`user: ${props.username}`, [props.username, editing]);
    const {apiClient, sessionData, getCommandRepository} = useServicesContext();

    if (loading) return <LoadingSpinner/>;

    // error can be undefined, so test user
    if (!user) return <></>;

    function showAclEntryControl(userId: number, data?: AclEntryDto[] | undefined) {
        const aclEntryControl = new AclEntryControl(
            userId,
            !sessionData.hasRoleAAA(),
                () => {
                    showNotification("info", "permissions updated");
                    reloadAcls();
                },
            data);
        showModalWithSubmitCancel("permissions", "done", "cancel", aclEntryControl.element(), () => aclEntryControl.submitForm());
    }

    const mfaStatus = user.mfaRequired ? user.mfaValidated ? "active" : "pending activation"
            : user.mfaValidated ? "activated but temporarily disabled" : "not required";

    const resetMfaSecret = () => {
        const cmd = new UserMfaResetCommand(user.userId)
        getCommandRepository()
                .sendCommand(cmd)
                .then(() => {
                    showNotification("info", "mfa registration reset");
                    reload();
                })
                .catch(e => {
                    showNotification("error", e.reason.message);
                });
    };

    const dtdd = (title: string, child: ReactNode) => <>
                <dt style={{marginTop: "20px"}}>{title}</dt>
                <dd>{child}</dd>
            </>

    // NB could do with allowing config via env, similar to acl-config
    const isLoginProvidersOnly = applicationProperties.isLoginProvidersOnly;

    // Separate component because TabsBuilder has hooks and we've got a conditional return above
    const Content = () => <>
        <Box p={2}>
            {new TabsBuilder()
                .addTab("user details",
                    <Grid container spacing={1} direction="column">
                        <Grid item>
                            {/*<ContactAvatar contact={user && user.individual}/>*/}
                            <Card variant="outlined">
                                <UserHeader user={user}/>
                                <CardContent>
                                    <dl>
                                        {dtdd('account status', <span>{user.enabled ? "active" : "disabled"}</span>)}
                                        {dtdd('mfa status (with username/password)', <span>{mfaStatus}</span>)}
                                        {dtdd('access groups', <span>{user.groups.join(", ")}</span>)}
                                    </dl>
                                </CardContent>
                                <CardActions>
                                    <Grid container direction="row">
                                        {sessionData.hasRoleSwitchUser() && <Grid item>
                                            <Button onClick={
                                                () => {
                                                    sessionStorage.setItem(RECENT_USER_CHANGE, user.username)
                                                    return apiClient.post(`${applicationRootPath}nav/secure/admin/switchUser?username=${user.username}`, null)
                                                        .then(() => window.location.href = `${applicationRootPath}nav/r/welcome/`);
                                                }
                                            }>
                                                impersonate user
                                            </Button>
                                        </Grid>}
                                        <Grid item>
                                            {!isLoginProvidersOnly &&
                                                <Button onClick={() => setChangePwd(true)}>change password</Button>
                                            }
                                            {changePwd && <ChangePasswordForm onChange={(pwd) => {
                                                if (!pwd) {
                                                    setChangePwd(false);
                                                    return;
                                                }
                                                new UserAjaxRepository(apiClient).updatePassword(props.username, pwd)
                                                    .then(() => {
                                                        showNotification("info", "password updated");
                                                        setChangePwd(false);
                                                    })
                                                    .catch((errorResult: WebApiError) => {
                                                        var msg = sessionData.getMessages()[errorResult.reason.message] || errorResult.reason.message;
                                                        showNotification("error", msg);
                                                    });
                                            }}/>}
                                        </Grid>

                                        {!isLoginProvidersOnly && <Grid item>
                                            <Button onClick={resetMfaSecret}>reset mfa reg.</Button>
                                        </Grid>}

                                        <Grid item xs /*give flex-grow: 1 for spacing*//>
                                        <Grid item>
                                            <Button onClick={() => setEditing(true)}>edit</Button>
                                        </Grid>
                                    </Grid>
                                </CardActions>
                            </Card>
                        </Grid>
                        {/* ideally we'd hide this when 'syncUser' is on - see EccoEnhancingOidcUserService */}
                        {(acls && acls.length || aclPreConfigAllowed || aclOn) && <Grid item>
                            <Card variant="outlined">
                                <CardHeader
                                    title="Service Access"
                                    subheader="access to specific services and locations"
                                />
                                <CardContent>
                                    <SummariseAcls acls={acls} aaa={user.groups.some(g => g == "all service access")}/>
                                    {/* aclPreConfig setting can be left on... so once inside this section, just show when 'not on' */}
                                    {!aclOn && <Typography variant="caption">
                                        <Alert severity="warning">access shown is not currently enforced until the feature is switched on.</Alert>
                                    </Typography>}
                                </CardContent>
                                <CardActions>
                                    <Grid container direction="row">
                                        <Grid item xs /*give flex-grow: 1 for spacing*//>
                                        <Grid item>
                                            {/* preConfig or 'on or off', if we have acls we should be able to edit them */}
                                            {((acls && acls.length) || aclPreConfigAllowed || aclOn) && <Grid item xs={2}>
                                              <Button onClick={() => showAclEntryControl(user.userId, acls)}>edit</Button>
                                            </Grid>}
                                        </Grid>
                                    </Grid>
                                </CardActions>
                            </Card>
                        </Grid>}
                    </Grid>,
                    undefined, "fa-user")
                // .addTab( "authenticated devices", <UserDevicesList/> // TODO: HATEOAS style list showing userdevices table, last login and allowing them to be revoked
                .addTab( "audit history",
                    <AuditHistory resource={user} sessionData={sessionData}/>,
                    undefined, "fa-history")
                .build()
            }
            <UserForm show={editing} setShow={setEditing} username={props.username} afterSave={reload}/>
        </Box>
    </>;
    return <Content/>;
};
