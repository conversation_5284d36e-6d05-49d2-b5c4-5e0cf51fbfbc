import $ = require("jquery");

import BaseAsyncCommandForm = require("../../cmd-queue/BaseAsyncCommandForm");
import commands = require("../../feature-config/commands");
import FeatureVote = domain.FeatureVote;
import VoteEntry = domain.VoteEntry;
import {apiClient} from "ecco-components";
import * as domain from "ecco-dto";
import {SessionDataAjaxRepository, SessionDataRepository} from "ecco-dto";
import {
    ColumnRepresentation,
    RowContext, Table,
    TableRepresentationBase,
    textColumn
} from "ecco-reports";
import {
    TableControl
} from "../../controls/tables";

var featureConfigRepository: SessionDataRepository = new SessionDataAjaxRepository(apiClient.withCachePeriod(0));

function textColumnWithClasses<TRow>(representCol: ColumnRepresentation<TRow>, deriveCssClasses: (row: TRow) => string): ColumnRepresentation<TRow> {
    return {
        getHeading: () => representCol.getHeading(),
        getType: () => representCol.getType(),
        getCssClasses: (row) => representCol.getCssClasses(row) + " " + deriveCssClasses(row),
        represent: (ctx: RowContext, row, substitutePath) => representCol.represent(ctx, row, substitutePath)
    };
}

class FeatureConfigForm extends BaseAsyncCommandForm<domain.SessionData> {

    private tableCtl = new TableControl(null);
    private featureVotes = new Array<VoteEntry>();
    private representation: TableRepresentationBase<VoteEntry>;
    private footer: $.JQuery;

    constructor() {
        super("Feature configuration");
        this.setOnFinished( () => {} );
    }

    protected fetchViewData(): Promise<domain.SessionData> {
        return featureConfigRepository.getSessionData();
    }

    protected render(data: domain.SessionData): void {
        this.element().empty();

        let $content = $("<div>").addClass("ecco-rounded");
        $content.append(this.tableCtl.element().addClass("table-control"));

        this.footer = this.getFooter();
        this.footer.show();
        this.element().append(this.footer);

        this.element().append($content);

        let columns: ColumnRepresentation<VoteEntry>[] = [
            textColumn<VoteEntry>("name", (row) => row.name),
            textColumn<VoteEntry>("description", (row) => row.description),
            textColumnWithClasses<VoteEntry>(
                textColumn<VoteEntry>("vote", (row) => FeatureVote[row.vote]),
                (row) => (row.vote == FeatureVote.ENABLED_BY_DEFAULT ? "success" : ""))
        ];
        this.representation = new TableRepresentationBase<VoteEntry>(columns);

        this.featureVotes = data.getGlobalFeatureSet().getVotes();
        this.setData();
    }

    private setData() {
        let tableData = new Table<VoteEntry>(this.featureVotes, this.representation);
        tableData.addClickEventHandler((event) => this.onRowClick(event.getDatums()[0]));
        this.tableCtl.setData(tableData, true);
    }

    private onRowClick(entry: VoteEntry) {
        var newVote = (entry.vote == FeatureVote.ENABLED_BY_DEFAULT)
            ? FeatureVote.DISABLED_BY_DEFAULT
            : FeatureVote.ENABLED_BY_DEFAULT;
        this.enableSubmit();
        this.voteChange(entry, newVote);
        //$tr.find("td:nth-child(2)").removeClass().addClass("warning").text(FeatureVote[newVote]);

        let newVoteEntry: VoteEntry = {name: entry.name, description: entry.description, vote: newVote};
        this.featureVotes.filter((vote) => vote.name == entry.name)[0] = newVoteEntry;
        this.setData();
    }

    private voteChange(entry: VoteEntry, newVote: FeatureVote) {
        var cmd = new commands.FeatureVoteChangeCommand(entry.name, entry.vote, newVote);
        this.commandQueue.addCommand(cmd);
        entry.vote = newVote;
    }

}

export = FeatureConfigForm;
