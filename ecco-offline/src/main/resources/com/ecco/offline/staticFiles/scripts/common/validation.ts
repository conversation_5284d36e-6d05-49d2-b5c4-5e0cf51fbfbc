import {array, SparseArray, StringToObjectMap} from "@eccosolutions/ecco-common";


/**
 * The types of checks that can be performed on fields.
 */
export enum ValidationCheck {
  Required = 1,
  NotFuture = 2,
  NotDuplicate = 3,
  UTF8 = 4,
  Recent = 5,
  NotPast = 6,
  NearFuture = 7,
  Money = 8,
  Number = 9,
  Sms = 10
}

export var ValidationCheckName: SparseArray<string> = {};
ValidationCheckName[ValidationCheck.Required] = "is required";
ValidationCheckName[ValidationCheck.NotFuture] = "cannot be future dated";
ValidationCheckName[ValidationCheck.NotPast] = "cannot be in the past";
ValidationCheckName[ValidationCheck.NotDuplicate] = "this is a duplicate";
ValidationCheckName[ValidationCheck.UTF8] = "cannot contain emojis";
ValidationCheckName[ValidationCheck.Recent] = "must be in last 28 days"; // see DateInput.ts
ValidationCheckName[ValidationCheck.NearFuture] = "must be no more than 28 days in future";
ValidationCheckName[ValidationCheck.Money] = "not a monetary item (numbers only with dp)";
ValidationCheckName[ValidationCheck.Number] = "not a number";
ValidationCheckName[ValidationCheck.Sms] = "not a mobile number";

/**
 * From https://github.com/cambridgeweblab/common-ui
 * isValidEmailAddress - returns if email passes regex validation
 */
export function isValidEmailAddress(emailAddress: string): boolean {
    // removed unrequired escapes as according to http://eslint.org/docs/rules/no-useless-escape
    const regex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return regex.test(emailAddress);
}

// see ClientDetailForm in ecco-components currently
// export function isValidNi(ni: string): boolean {
// }

/**
 * Placeholder for the error handling of multiple controls
 */
export class ValidationErrors {

    private errors: StringToObjectMap<ValidationFailureBuilder> = {};

    constructor(private nestedPath: string) {
    }

    // common validation check
    public requireLength(field: string, value: string) {
        if (!(value && value.length > 0)) {
            this.addError(field, ValidationCheck.Required);
        }
        this.requireNoEmojis(field, value);
    }

    // common validation check
    public requireNotNull(field: string, value: any) {
        if (value === null) {
            this.addError(field, ValidationCheck.Required);
        }
    }

    // common validation check
    public requireNotEmpty(field: string, value: any[]) {
        if (!value || value.length == 0) {
            this.addError(field, ValidationCheck.Required);
        }
    }

    // common validation check
    public requireTrue(field: string, value: boolean) {
        if (!value) {
            this.addError(field, ValidationCheck.Required);
        }
    }

    public requireNoEmojis(field: string, value: any) {
        let badChar = /[\ud800-\udbff][\udc00-\udfff]/.exec(value);
        if (badChar) {
            this.addError(field, ValidationCheck.UTF8);
        }
    }

    public requireMoney(field: string, value: string) {
        const regex = /^\s*-?\d+(\.\d{1,2})?\s*$/; // from https://stackoverflow.com/questions/660682/regex-that-matches-numeric-with-up-to-2-decimal-places
        if (value && !regex.test(value)) {
            this.addError(field , ValidationCheck.Money);
        }
    }

    public requireNumber(field: string, value: string) {
        const regex = /^\s*-?\d+\s*$/;
        if (value && !regex.test(value)) {
            this.addError(field , ValidationCheck.Number);
        }
    }

    public requireSms(field: string, value: string) {
        const regex = /^(07[\d]{8,12}|\+447[\d]{7,11})$/;
        if (value && !regex.test(value)) {
            this.addError(field , ValidationCheck.Sms);
        }
    }

    // common validation check
    public requireNotDuplicate<T>(field: string, value: T, values: T[],
            compare?: ((v1: T, v2: T) => boolean) | undefined) {
        if (values) {
            var matches = values.filter((v) => {
                return compare
                    ? compare(value, v)
                    : value === v;
            });
            if (matches.length > 0) {
                this.addError(field, ValidationCheck.NotDuplicate);
            }
        }
    }

    public clearErrors(field: string) {
        if (field in this.errors) {
            this.errors[field] = new ValidationFailureBuilder();
        }
    }

    public addError(field: string, failedCheck: ValidationCheck) {
        if (!(field in this.errors)) {
            this.errors[field] = new ValidationFailureBuilder();
        }
        this.errors[field].addFailure(failedCheck);
    }

    public isValid() {
        var valid = true;
        for (var field in this.errors) {
            if (this.errors[field].hasFailures()) {
                valid = false;
            }
        }
        return valid;
    }

    public isFieldValid(field: string, check: ValidationCheck) {
        if (field in this.errors) {
            return !this.errors[field].hasFailed(check);
        }
        return true;
    }

    public getFirstFailureMessage(field: string) {
        if (field in this.errors) {
            var failures = this.errors[field].getFailures();
            if (failures.length > 0) {
                return ValidationCheckName[failures[0]];
            }
       }
       return null;
    }

    public getGlobalMessage() {
        var parts: string[] = [];

        var failedFields = this.getFailureFields();
        parts.push(failedFields.length > 0
            ? "save not allowed -"
            : "");

        failedFields.forEach((field) => {
            var failures = this.errors[field].getFailures();
            parts.push(" (" + field + " ");
            failures.forEach((failedCheck, idx) => {
                parts.push(ValidationCheckName[failedCheck]);
                if (idx+1 < failures.length) {
                    parts.push(" and ");
                }
            });
            parts.push(")");
        });

        var msg = parts.reduce((prev, curr) => {
                return prev.concat(curr);
            });
        return msg;
    }

    private getFailureFields() {
        var fields: string[] = [];
        for (var field in this.errors) {
            if (this.errors[field].hasFailures()) {
                fields.push(field);
            }
        }
        return fields;
    }

}


/** Build validation failures for a field */
export class ValidationFailureBuilder {

    private failures: ValidationCheck[] = [];

    public addFailure(check: ValidationCheck) {
        if (!array.contains(this.failures, check)) {
            this.failures.unshift(check);
        }
    }

    public hasFailures() {
        return this.failures.length > 0;
    }

    public hasFailed(check: ValidationCheck) {
        return array.contains(this.failures, check);
    }

    public getFailures(): ValidationCheck[] {
        return array.copy(this.failures);
    }

}

/** Build validation checks for a field */
export class ValidationChecksBuilder {

    private checks = new Set<ValidationCheck>();

    public addCheck(check: ValidationCheck) {
        this.checks.add(check);
        return this;
    }

    public static REQUIRED = new ValidationChecksBuilder().addCheck(ValidationCheck.Required);
    public static VALID_TEXT = new ValidationChecksBuilder().addCheck(ValidationCheck.Required)
        .addCheck(ValidationCheck.UTF8);
    public static MONEY = new ValidationChecksBuilder().addCheck(ValidationCheck.Money);
    public static NUMBER = new ValidationChecksBuilder().addCheck(ValidationCheck.Number);

    public hasChecks() {
        return this.checks.size > 0;
    }

    public isChecked(check: ValidationCheck) {
        return this.checks.has(check);
    }

    public append(checksIn: Set<ValidationCheck>) {
        if (checksIn == null) {
            return;
        }
        checksIn.forEach(check => this.checks.add(check));
    }
}
