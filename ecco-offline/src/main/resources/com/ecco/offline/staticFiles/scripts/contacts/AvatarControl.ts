import $ = require("jquery");
import BaseControl = require("../controls/BaseControl");
import {ImageResizer} from "ecco-components";
import {applicationRootPath} from "application-properties";
import {getGlobalApiClient} from "ecco-dto";
import {WebApiError} from "@eccosolutions/ecco-common";

const apiClient = getGlobalApiClient();

class AvatarControl extends BaseControl {

    private contactId: number;

    private $imageOverlay: $.JQuery;
    private $imageThumbnail: $.JQuery;
    private $prompt: $.JQuery;
    private $imageDrop: $.JQuery;
    private $fileInput: $.JQuery;

    private imageResizer: ImageResizer;

    private avatarControllerUri: string;

    /** @param imageId The current image ID, or null if there is no image
     *                 associated with the contact. */
    constructor(contactId: number, imageId: number) {
        if (contactId == null) {
            throw new TypeError("ContactId must not be null.");
        }

        var $imageOverlay = $("<div>")
                .addClass("image-overlay hidden")
                .css("position", "absolute")
                .css("overflow", "hidden")
                .append($("<div>")
                        .addClass("placeholder")
                        .css("cursor", "pointer")
                        .append($("<h2>").addClass("fa fa-times-circle"))
                        .append($("<p>")
                                .addClass("prompt")
                                .text("Remove photo")));

        var $imageThumbnail = $("<img>")
                .addClass("image-thumbnail hidden")
                .attr("alt", "photo");

        var $prompt = $("<p>")
                .addClass("prompt")
                .attr("title", "Click or drop an image")
                .text("Add a photo");

        var $fileInput = $("<input>")
                .attr("type", "file")
                .attr("accept", "image/jpeg")
                .css("position", "absolute")
                .css("top", "0")
                .css("right", "0")
                .css("margin", "0")
                .css("border", "10000px solid transparent")
                .css("opacity", "0")
                .css("cursor", "pointer");

        var $placeholder = $("<div>")
                .addClass("placeholder")
                .append($("<h2>").addClass("fa fa-camera"))
                .append($prompt);

        var $imageDrop = $("<div>")
                .addClass("image-drop")
                .css("position", "relative")
                .css("overflow", "hidden")
                .append($placeholder)
                .append($fileInput);

        if (imageId != null) {
            $imageDrop.addClass("hidden");

            $imageThumbnail
                    .removeClass("hidden")
                    .attr("src", `${applicationRootPath}api/secure/images/${imageId}`);
        }

        super($("<div>")
                .addClass("avatar thumbnail")
                .css("position", "relative")
                .append($imageOverlay)
                .append($imageThumbnail)
                .append($imageDrop));

        this.contactId = contactId;

        this.$imageOverlay = $imageOverlay;
        this.$imageThumbnail = $imageThumbnail;
        this.$prompt = $prompt;
        this.$imageDrop = $imageDrop;
        this.$fileInput = $fileInput;

        this.imageResizer = new ImageResizer({ maxWidth: 300, maxHeight: 400 });

        this.avatarControllerUri = `contact/${this.contactId}/avatar/`;

        if (this.imageResizer.supported() && !!FormData) {
            $imageThumbnail.mouseenter(() => this.onMouseEnterImage());

            $imageOverlay
                .mouseleave(() => this.onMouseLeaveOverlay())
                .click((e: $.JQueryMouseEventObject) => this.onClickOverlay(e));

            $fileInput
                .click(() => this.onFileInputClick())
                .change(() => this.onFileChange());
        } else {
            $fileInput.remove();

            $placeholder
                    .empty()
                    .append($("<i>")
                            .addClass("fa fa-5x fa-user")
                            .css("color", "white"));
        }
    }

    private onMouseEnterImage(): void {
        this.$imageOverlay.removeClass("hidden");
    }

    private onMouseLeaveOverlay(): void {
        this.$imageOverlay.addClass("hidden");
    }

    private onClickOverlay(e: $.JQueryMouseEventObject): boolean {
        e.preventDefault();
        e.stopPropagation();

        if (window.confirm("Are you sure you wish to remove this photo? " +
                "The photo will be deleted from the system: this operation " +
                "cannot be undone.")) {
            this.removeClientAvatar();
        }

        return false;
    }

    private onFileInputClick(): void {
        // Ensure that onFileChange is always called after the user selects a
        // file, even if they select the same file twice in a row.
        this.$fileInput.val("");
    }

    private onFileChange(): void {
        var files = <FileList> this.$fileInput.prop("files");

        if (!files) {
            throw new Error("Illegal state.");
        } else if (files.length == 0) {
            this.removeClientAvatar();
        } else if (files.length > 1) {
            throw new Error("Illegal state.");
        } else {
            var file = files[0];

            var resizedFile = this.imageResizer.resizeFile(file);

            var formData = resizedFile
                    .then((resizedFile) => {
                        var formData = new FormData();
                        formData.append("file", resizedFile, file.name);
                        return formData;
                    });

            // avatar is a global upload
            formData
                    .then(formData => {
                        apiClient.post("secure/uploadHiddenJs.html?source=global", formData,
                                undefined)
                                .catch( (reason: WebApiError) =>
                                        this.onUploadError(String(reason.message || reason.statusCode)))
                                .then((data: UploadedFileOrErrorResultDto) => {
                                    if (data.error == null) {
                                        this.setClientAvatar(data);
                                    } else {
                                        this.onUploadError(data.error);
                                    }
                                })
                    })
                    .catch((reason) => {
                        this.onResizeError(reason);
                    });
        }
    }

    private setClientAvatar(file: UploadedFileDto): void {
        apiClient.post(this.avatarControllerUri, null, undefined,
                {query: {bytesId: file.bytesId.toString()}})
            .catch((reason: WebApiError) =>
                    this.onSetAvatarError(reason.statusCode.toString(), reason.message))
            .then((): void => {
                this.$imageDrop.addClass("hidden");
                this.$imageThumbnail
                        .attr("src", `${applicationRootPath}api/secure/images/${file.bytesId}`)
                        .removeClass("hidden");
                this.$imageOverlay.addClass("hidden");
                this.$prompt.text("Add a photo")
                        .attr("title", "Click or drop an image");
            });
    }

    private removeClientAvatar(): void {
        apiClient.del(this.avatarControllerUri, null)
            .catch((reason: WebApiError) => {
                this.$prompt.text("Add failed - try again")
                        .attr("title", reason.message || reason.statusCode.toString());
            })
            .then((): void => {
            this.$imageOverlay.addClass("hidden");
            this.$imageThumbnail.addClass("hidden")
                 .attr("src", "");
            this.$imageDrop.removeClass("hidden");
        })
    }

    private onUploadError(error: string): void {
        this.$prompt
                .text("Add errored - try again")
                .attr("title", error);
    }

    private onSetAvatarError(textStatus: string, errorThrown: any): void {
        this.$prompt.text("Add failed - try again")
                .attr("title", errorThrown || textStatus);
    }

    private onResizeError(reason: any): void {
        this.$prompt.text("Resize failed - try again")
                .attr("title", reason);
    }
}

/** Matches UploadedFileResource.
 *
 * All fields are marked optional as the Web API may return
 * UploadErrorResultDto instead. */
interface UploadedFileDto {
    links?: LinkDto[];
    fileId?: number;
    filename?: string;
    size?: number;
    bytesId?: number;
    type?: string;
}

/** Matches org.springframework.hateoas.Link. */
interface LinkDto {
    href: string;
    rel: string;
}

/** Matches output of UploadErrorResult#toJson().
 *
 * All fields are marked optional as the Web API may return
 * UploadedFileDto instead. */
interface UploadErrorResultDto {
    filename?: string;
    type?: string;
    error?: string;
}

/** Either UploadedFileDto or UploadErrorResultDto. */
interface UploadedFileOrErrorResultDto extends UploadedFileDto, UploadErrorResultDto {
}

export default AvatarControl;