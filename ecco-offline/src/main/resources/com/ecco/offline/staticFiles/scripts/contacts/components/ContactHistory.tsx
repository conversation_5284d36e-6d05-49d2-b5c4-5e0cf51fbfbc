import $ = require("jquery");
import * as ReactDom from "react-dom";
import * as React from "react"
import {FC} from "react"
import ContactHistoryListControl from "../ContactHistoryListControl";
import {LoadingOrError, useServiceRecipientWithEntities} from "ecco-components";
import {onParentTabActivated} from "../../common/tabEvents";


interface Props {
    contactId?: number | undefined
}


class ContactHistory extends React.Component<Props> {

    private control: ContactHistoryListControl;

    constructor(props) {
        super(props);
        this.control = new ContactHistoryListControl(props.contactId)
    }

    public override componentDidMount() {
        ($(ReactDom.findDOMNode(this)) as any).append(this.control.element());
        this.control.load();
        onParentTabActivated(this.control.element(), (event) => {
            this.control.load();
        })
    }

    override render() {

        return <div/>;
    }
}

export default ContactHistory;

export const ServiceRecipientContactHistory: FC<{srId: number}> = ({srId}) => {
    const {context, error} = useServiceRecipientWithEntities(srId)
    if (!context) return <LoadingOrError error={error}/>;
    return <ContactHistory contactId={context.client.contactId}/>
}
