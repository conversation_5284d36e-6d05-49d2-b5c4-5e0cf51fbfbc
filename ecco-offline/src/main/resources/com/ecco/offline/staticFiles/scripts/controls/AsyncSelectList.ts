import $ = require("jquery");

import BaseAsyncDataControl = require("../controls/BaseAsyncDataControl");


/**
 * A list control for working with async data that can be reloaded such as when other values on the form
 * change.
 * NOTE: this is deliberately working on having numeric keys, which should generally be the case.
 */
abstract class AsyncSelectList<ENTITY extends {id: number, name: string}> extends BaseAsyncDataControl<ENTITY[]> {

    private selectedId: number;

    private $select: $.JQuery;


    constructor(private onSelected?: (val: number) => void, private emptyOptionLabel = "") {
        super();

        this.$select = $("<select>");
        this.$select.change( (event) => this.onChanged(event) );
    }


    protected render(items: ENTITY[]) {
        this.$select.empty()
            .append( $("<option>").attr("value", "").text(this.emptyOptionLabel) );

        if (items) {
            items.forEach( (item) => {
                this.$select.append( $("<option>").attr("value", item.id).text(item.name) );
            });
        }

        this.$select.val(this.selectedId);
        this.element().empty().append(this.$select);
        //this.$select.change();
    }

    private onChanged(event: $.BaseJQueryEventObject) {
        if (this.onSelected) {
            this.onSelected(this.$select.val());
        }
    }

    public getSelectedId(): number {
        return this.selectedId = this.$select.val() && parseInt(this.$select.val()) || null;
    }

    public setSelectedId(selectedId: number) {
        this.$select.val(this.selectedId = selectedId);
    }

    public selectElement(): $.JQuery {
        return this.$select;
    }
}
export = AsyncSelectList;
