
import $ = require("jquery"); // note: typeahead is supported via a shim configured in require-boot.js
import InputControl = require("./InputControl");
import bloodhound = require("bloodhound");
import typeahead = require("typeahead");
import TTJQ = typeahead.JQuery;

class AutoCompleteInput<DATUM> implements InputControl {

    private $input: $.JQuery = $("<input>").addClass("form-control").attr("type","text");
    private $container: $.JQuery = $("<span>").append(this.$input);
    private selectedItem: DATUM;

    private selectedHandler: (ev: $.JQueryEventObject, selectedObject: DATUM, datasetName: string) => void = (ev, obj, dataset) => {
        console.log("Selected an item from the [" + dataset + "] dataset:");
        console.log(obj);
    };

    /**
     * @param name
     *  the name of the input field (and also the key passed to typeahead to identify the source for caching)
     */
    constructor(private name: string, private display: (item:any) => string) {
        this.$input.attr("name", this.name);
    }

    /**
     * Set callback when something is selected
     */
    public selected(handler: (ev: $.JQueryEventObject, selectedObject: DATUM, datasetName: string) => void) {
        this.selectedHandler = handler;
    }

    /**
     * Initialise typeahead on this field. This will often be called asynchronously after the form has been
     * rendered.
     *
     * @param source
     *  currently only Bloodhound supported, but could be an interface for Typeahead, not specifically Bloodhound
     */
    public typeahead(source: bloodhound.Source, templates?: typeahead.Templates) {
        (<TTJQ>this.$input).typeahead({
            minLength: 1,
            highlight: true
        },
        {
            name: this.name,
            displayKey: this.display,
            source: source,
            templates: templates
        });
        this.$container.on("typeahead:selected", null, this.selectedHandler);
        this.$container.on("typeahead:selected", null, (ev, obj: DATUM, dataset) => { this.selectedItem = obj;});
        return this;
    }

    public change( onChange: (value: string) => void ): never {
        throw new Error("Not implemented");
    }

    public addClass(classNames: string) {
        this.$input.addClass(classNames);
    }

    public element(): $.JQuery {
        return this.$container;
    }

    public val() {
        return this.selectedItem;
    }
}
export = AutoCompleteInput;
