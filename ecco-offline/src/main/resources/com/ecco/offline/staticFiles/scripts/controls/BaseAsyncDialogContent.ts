import $ = require("jquery")

import ActionButton = require("../controls/ActionButton");
import BaseAsyncDataControl = require("../controls/BaseAsyncDataControl");
import DialogContent from "../controls/DialogContent";
import {ActionsChangedCallback} from "@eccosolutions/ecco-common";


/** For use with the following from the derived class
 *       var form = new ManageServiceActivitiesControl(serviceId, serviceTypeId);
 *      Modal.showInModal(form);
 *      form.load();
 */
abstract class BaseAsyncDialogContent<T> extends BaseAsyncDataControl<T> implements DialogContent {


    private onFinished: () => void;

    private $title = $("<span>");

    protected submitButton: ActionButton;

    constructor(formTitle: string) {
        super();
        this.$title.text(formTitle);
    }

    // DialogContent
    registerActionsChangeListener(updateActions: ActionsChangedCallback) {
        // NO-OP
    }

    public getTitle(): $.JQuery {
        return this.$title;
    }

    public getFooter(): $.JQuery {
        return null; // this.submitButton.element();
    }

    public setOnFinished( onFinished: () => void) {
        this.onFinished = onFinished;
    }
}

export = BaseAsyncDialogContent;