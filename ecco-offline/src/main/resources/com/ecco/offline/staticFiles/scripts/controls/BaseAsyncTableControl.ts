import $ = require("jquery");

import BaseAsyncDataControl = require("./BaseAsyncDataControl");
import BaseTableRowControl = require("./BaseTableRowControl");
import { EccoDate } from "@eccosolutions/ecco-common";


type Header = string | EccoDate | { text: string, help: string };

abstract class BaseAsyncTableControl<ENTITY> extends BaseAsyncDataControl<ENTITY[]> {

    private $tbody: $.JQuery;

    /** headers can be indexed by string or an object like EccoDate for example */
    constructor() {
        super();
    }

    protected abstract createRowControl(item: ENTITY): BaseTableRowControl<ENTITY>;

    protected abstract getHeaders(): Header[];

    protected getHeaderLabel(header: Header): string {
        return typeof header != "object"
            ? header
            : header instanceof EccoDate
                ? header.formatShort()
                : header.text;
    }

    private getHeaderHelp(header: Header): string | null {
        return header ? (typeof header != "object" || header instanceof EccoDate ? null : header.help) : null;
    }

    protected render(items: ENTITY[]) {
        var $headerRow = $('<tr>');

        // clear the table before re-rendering
        this.$tbody = $('<tbody>');

        var $table = $('<table class="table dataTable no-footer">')
            .append($('<thead>').append($headerRow))
            .append(this.$tbody);

        this.getHeaders().forEach(header => {
            const th = $("<th>")
                .append(this.getHeaderLabel(header));

            if (this.getHeaderHelp(header)) {
                const help = $('<i>')
                    .css('color', '#337ab7')
                    .attr({
                        class: 'fa fa-question-circle',
                        'data-toggle': 'tooltip',
                        title: this.getHeaderHelp(header)
                    });
                help.tooltip();
                th.append(" ").append(help);
            }
            $headerRow.append(th);
        });

        items.forEach( item => this.addRow(item));

        this.element().empty().append($table);
    }

    protected addRow(item: ENTITY) {
        const rowControl = this.createRowControl(item);
        rowControl.render(this.getHeaders().map(h => this.getHeaderLabel(h)));
        this.$tbody.append(rowControl.element()); // If this blows up you've called addRow before render!
        if (rowControl.secondRow()) {
            this.$tbody.append(rowControl.secondRow().addClass("second-row"));
        }
    }
}

export = BaseAsyncTableControl;