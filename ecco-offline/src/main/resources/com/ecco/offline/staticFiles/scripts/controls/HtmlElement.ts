import $ = require("jquery");
import Element = require("./Element");

/**
 * Basic HTML element with common functions - an wrapper to avoid us being too dep on <PERSON><PERSON>uer<PERSON>, but also provide
 * us with basic conveniences.
 */
class HtmlElement implements Element {


    public constructor( private $container: $.JQuery, id?: string ) {
        if (id) {
            this.$container.attr("id", id);
        }
    }

    public addClass(classes: string): this {
        this.$container.addClass(classes);
        return this;
    }

    /** Append either an Element, or some content <PERSON><PERSON><PERSON><PERSON> is happy to append() */
    public append(element: $.JQuery|string|Element): this {
        var $child: $.JQuery;
        if (typeof (element as Element).element === "function") {
            $child = (element as Element).element();
        } else {
            $child = $(element);
        }
        if ($child) {
            this.$container.append($child);
        }
        return this;
    }

    /**
     * element = Element or something <PERSON><PERSON><PERSON><PERSON> is happy to .append()
     */
    public setContent(element: $.JQuery|Element): void {
        this.element().empty();
        this.append(element);
    }

    public element(): $.JQuery {
        return this.$container;
    }
}

export = HtmlElement;
