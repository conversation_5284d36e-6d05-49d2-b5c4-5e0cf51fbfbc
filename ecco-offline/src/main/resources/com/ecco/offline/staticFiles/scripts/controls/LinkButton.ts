import $ = require("jquery");
import Element = require("./Element");

class LinkButton implements Element {

    private $container: $.JQuery = $("<a>");
    private _autoDisable = true;
    private _iconClasses: string = null;

    public constructor(private text: string) {
        this.$container.text(text);
    }

    /** e.g. glyphicon glyphicon-plus-sign */
    public iconClasses(iconClasses: string): LinkButton {
        this.$container.empty();

        if (iconClasses) {
            this.$container.append($("<span>").addClass(iconClasses))
                .append($("<span>").text(" " + this.text));
        } else {
            this.$container.text(this.text);
        }

        return this;
    }

    public href( href: string ) {
        this.$container.attr("href", href);
        return this;
    }
    public addClass(classes: string) {
        this.$container.addClass(classes);
        return this;
    }

    public element(): $.JQuery {
        return this.$container;
    }
}

export = LinkButton;
