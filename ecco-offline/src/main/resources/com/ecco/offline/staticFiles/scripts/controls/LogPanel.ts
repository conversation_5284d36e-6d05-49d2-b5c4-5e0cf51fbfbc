import $ = require("jquery");
import CollapsiblePanel = require("./CollapsiblePanel");

class LogPanel extends CollapsiblePanel {

    constructor(title: string) {
        super();
        this.titleElement().text(title);
    }

    public append( element: $.JQuery ) {
        this.element().show();
        this.bodyElement().append(element); // may want to wrap this and add a timestamp etc later
    }
}
export = LogPanel;
