import $ = require("jquery-bundle");
import BaseControl = require("./BaseControl");
import environment = require("../environment");
import {EccoTime} from "@eccosolutions/ecco-common";
import {ValidationCheck, ValidationChecksBuilder, ValidationErrors} from "../common/validation";
import {bus, EventBus, EventHandler} from "@eccosolutions/ecco-common";

// Test if the browser provides a native HTML 5 date picker.
var nativeTimePicker = $("<input>")
    .attr("type", "time")
    .prop("type") == "time";

class TimeInput extends BaseControl {
    private static CSS_CLASS = "datetime-input";

    private $input: $.JQuery;
    private useNative: boolean;
    private changeEventBus: EventBus<EccoTime>;

    /**
     * A time control - either text, or native type 'time'
     *
     * @param bootstrap whether the ecco date picker uses bootstrap styling
     * @param allowNative allow us to use the ecco datepicker regardless of whether native could be used
     *
     * NB bootstrap applies to non-native ONLY currently - see DateInput.ts
     */
    constructor(time?: EccoTime, id?: string, bootstrap?: boolean, allowNative:boolean = true) {
        var $input: $.JQuery;
        if (allowNative && nativeTimePicker) {
            $input = $("<input>")
                .attr("id", id)
                .attr("name", id)
                .attr("type", "time")
                .addClass(TimeInput.CSS_CLASS + " form-control");

            if (time) {
                $input.val(time.formatHoursMinutes());
            }

            super($input);
        }
        else {
            $input = $("<input>")
                .attr("type", "text")
                .attr("id", id)
                .attr("size", "7")
                .attr("maxLength", "7")
                .attr("autocomplete", "off")
                .addClass("form-control");

            if (time) {
                $input.val(time.formatHoursMinutes());
            }

            // this was imported from TimePicker, but we know we are type 'text'
            // if (!$input.is("input[type=text], input[type=hidden], input:not([type])")) {
            //     // input:not([type]) is semantically equivalent to input[type=text].
            //     throw new Error("DatePicker must be attached to input[type=text] or input[type=hidden]");
            // }

            let $container = $("<span>")
                .addClass(TimeInput.CSS_CLASS)
                .append($input);
            super($container);

            // also see dateBox.js
            (<any>$input).timepicker({
                buttonImage: environment.imagesBase + "datepicker.png",
                buttonImageOnly: true,
                hourGrid: 4,
                minuteGrid: 10,
                showOn: "both",

                onClose: (timeText: string, instance: any, dt_inst: any) => {
                    var trimmed = $.trim(timeText);
                    this.$input.val(trimmed);
                    this.onChange();
                },
                //onClose: validate

                // Stupid hack to make datepicker work in conjunction with Bootstrap-modal.
                // See: https://github.com/jschr/bootstrap-modal/issues/239
                beforeShow: () => {
                    // JQuery UI sets the z-index of the datepicker immediately
                    // after this function returns, so we arbitrarily delay for one
                    // more turn of the event loop using setTimeout.
                    setTimeout(() => $(".ui-datepicker").css("z-index", "10000"), 0)
                }
            });
        }

        $input.change(() => this.onChange());
        this.$input = $input;
        this.changeEventBus = bus<EccoTime>();
        this.useNative = allowNative && nativeTimePicker;
    }

    public placeholder(placeholderText: string) {
        this.$input.attr("placeholder", placeholderText);
        return this;
    }

    public getTime(): EccoTime | null {
        let val = this.$input.val();
        let timeParts = val.match(/^(\d*):(\d*)$/);
        return timeParts == null ? null : new EccoTime(timeParts[1], timeParts[2], 0, 0);
    }

    public setTime(time: EccoTime): void {
        if (time) {
            this.$input.val(time.formatHoursMinutes());
        } else {
            this.$input.val("");
        }
        this.onChange();
    }

    public isValid() {
        return this.getTime() !== null;
    }

    public validate(field: string, checks: ValidationChecksBuilder, errors: ValidationErrors) {

        if (checks.isChecked(ValidationCheck.Required)) {
            errors.requireNotNull(field, this.getTime());
        }

    }

    public change(handler: EventHandler<EccoTime>): TimeInput {
        this.addChangeEventHandler(handler);
        return this;
    }

    public addChangeEventHandler(handler: EventHandler<EccoTime>): void {
        this.changeEventBus.addHandler(handler);
    }

    public removeChangeEventHandler(handler: EventHandler<EccoTime>): void {
        this.changeEventBus.removeHandler(handler);
    }

    private onChange() {
        this.changeEventBus.fire(this.getTime());
    }
}

export = TimeInput;
