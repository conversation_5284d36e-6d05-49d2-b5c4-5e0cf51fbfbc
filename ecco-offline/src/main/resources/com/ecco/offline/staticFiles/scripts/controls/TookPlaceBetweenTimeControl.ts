import $ = require("jquery");
import DateTimePicker = require("../controls/DateTimePicker");
import TimePicker = require("../controls/TimePicker");
import {EccoDate, EccoDateTime, EccoTime} from "@eccosolutions/ecco-common";

/**
 * Used on evidence pages where start dateTime and and end time is required.
 * This calculates the difference to be placed in minutesSpent
 */
class TookPlaceBetweenTimeControl {

    public static attachByIds(startDateTimeId: string, endTimeId: string, minsBetweenId: string, durationTextId: string) {
        return new TookPlaceBetweenTimeControl( $('#' + startDateTimeId), $('#' + endTimeId), $('#' + minsBetweenId),
                durationTextId );
    }

    private lastDateTime: Date;
    private lastTime: EccoTime;

    constructor(private $startDateTime: $.JQuery, private $endTime: $.JQuery, private $minsBetween: $.JQuery,
            private durationTextId: string) {

        this.attach();
    }

    private attach() {

        // from
        const fromStr = this.$startDateTime.text();
        let initFromDate = null;
        if (fromStr) {
            initFromDate = EccoDate.parseIso8601(fromStr).toLocalJsDate();
        }
        const fromCtl = new DateTimePicker(initFromDate, (source, datetime) => {
            this.lastDateTime = datetime;
            this.updateBetweenTime();
        });
        fromCtl.attach(this.$startDateTime);


        // to time
        // const toStr = this.$endTime.text();
        const toCtl = new TimePicker(null, (source, time) => {
            this.lastTime = time;
            this.updateBetweenTime();
        });
        toCtl.attachTime(this.$endTime);
    }

    private updateBetweenTime() {
        if (!this.lastDateTime || !this.lastTime) {
            this.$minsBetween.val("0");
            return;
        }
        const s = EccoDateTime.fromLocalJsDate(this.lastDateTime);
        const st = new EccoTime(s.getHours(), s.getMinutes(), 0, 0);
        const duration = this.lastTime.subtract(st);
        this.$minsBetween.val(duration.inMinutes());

        const d = "(" + duration.getHours() + " hours, " + duration.getMinutes() + " mins)";
        $('#'+this.durationTextId).text(d);
    }

}

export = TookPlaceBetweenTimeControl;
