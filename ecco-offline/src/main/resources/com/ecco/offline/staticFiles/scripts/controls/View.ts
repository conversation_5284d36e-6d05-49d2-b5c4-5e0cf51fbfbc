import $ = require("jquery");
import Element = require("../controls/Element");


/** A View component is a component that has a main body (element()) and other elements which relate to it.
 * Generally an input form will be a view */
interface View extends Element {

    /** Title to be used as the label for an embedded view or the title for a dialog */
    title(): string | $.JQuery;

    /**Element containing actions to put in the footer of the component that embeds this form. */
    getFooter(): $.JQuery;

    /** If defined, this should be called after the view is shown - e.g. for JQUI components */
    afterShow?(): void;
}
export = View;