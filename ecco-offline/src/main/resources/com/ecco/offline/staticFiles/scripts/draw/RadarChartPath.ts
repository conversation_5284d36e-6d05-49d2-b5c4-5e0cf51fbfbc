
import raphael = require("raphael");

class RadarChartPath {

    private angleRads = 0;
    private angleIncr: number;
    private svgPath: string;

    constructor( private centreX: number, private centreY: number, private colour: string, 
            private strokeWidth: string, private numSpokes: number ) {

        this.angleIncr = Math.PI * 2 / numSpokes;
    }

    /** Add path to the next spoke at the given radius */
    public addSpoke( radialDistance: number ) {
        var toX = this.centreX + radialDistance * Math.sin(this.angleRads);
        var toY = this.centreY - radialDistance * Math.cos(this.angleRads);

        if (!this.svgPath) {
            this.svgPath = "M " + toX.toFixed(1) + " " + toY.toFixed(1);
        }
        else {
            this.svgPath += "L " + toX.toFixed(1) + " " + toY.toFixed(1);
        }
        this.angleRads += this.angleIncr;
    }

    public render(paper: raphael.RaphaelPaper) {
        var path = paper.path(this.svgPath + " z");
        path.attr("stroke", this.colour);
        path.attr("stroke-width", this.strokeWidth);
        path.attr("stroke-opacity", 1);
    }
}
export = RadarChartPath;