import $ = require("jquery");
import EditableComponent = require("./EditableComponent");


class EditableTextControl extends EditableComponent {

    private $input: $.JQuery;

    constructor(private initialValue: string, private callback?: (from: string, to: string) => void) {
        super();
        this.setButtonClass("pull-right");
        this.setEditableSpanClass("form-control");
        this.enhanceField( $("<span>").text(initialValue) );
    }

    override makeEditable() {
        this.getTextSpan().detach();
        this.$input = $("<input>").addClass("form-control").attr("type","text").val(this.getTextSpan().text());
        var $form = $("<form>").addClass("editable")
            .append(this.$input);
        this.addSaveCancel($form);
        this.getContainer().append($form);
        this.$input.focus();
    }

    override submitEdit(event: $.JQueryEventObject) {
        super.submitEdit(event);
        this.callback && this.callback( this.initialValue, this.$input.val() );
        this.editCommitted(); // TODO: perhaps allow callback to fail?
    }

    override editCommitted() {
        this.getTextSpan().text(this.$input.val());
        this.replaceWithTextSpan();
    }
}

export = EditableTextControl;
