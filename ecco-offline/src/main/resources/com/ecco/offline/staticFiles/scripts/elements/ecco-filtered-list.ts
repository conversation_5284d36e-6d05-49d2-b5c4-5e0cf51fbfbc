import $ = require("jquery");
import URI = require("URI");
import BaseAsyncDataControl = require("../controls/BaseAsyncDataControl");
import EccoFilter = require("./ecco-filter");
import EccoList = require("./ecco-list");
import {ApiClient} from "ecco-dto";
import "document-register-element";
import {apiClient} from "ecco-components";
import {implementInterface, JsonSchemaDto, JsonSchemaAdapter} from "ecco-dto";
import "./ecco-filter"; // avoids elision
import "./ecco-list"; // avoids elision

class EccoFilteredList extends HTMLElement {
    private _delegate: FilteredListDelegate;

    get schema(): URI.URI {
        return this.getAttribute('schema') && URI(this.getAttribute('schema')) || null;
    }

    set schema(schema: URI.URI) {
        this.setAttribute('schema', schema.href());
    }

    get type(): string {
        return this.getAttribute('type');
    }

    set type(type: string) {
        this.setAttribute('type', type);
    }

    createdCallback(): void {
        this._delegate = new FilteredListDelegate(apiClient);
        this._delegate.element().appendTo(this);

        if (this.schema) {
            this.render();
        }
    }

    attributeChangedCallback(attrName: string, oldVal: string, newVal: string): void {
        if (attrName === 'schema') {
            this.render();
        }
    }

    render(): void {
        this._delegate.schema = this.schema;
        this._delegate.type = this.type;
        this._delegate.load();
    }
}

// Responsible for fetching the schema and rendering an ecco-filter and an ecco-list with default filter applied.
// We use a delegate to reuse async load handling from existing non-element custom controls
class FilteredListDelegate extends BaseAsyncDataControl<JsonSchemaDto> {
    public schema: URI.URI;
    public type: string;

    constructor (private client: ApiClient) {
        super();
    }

    protected fetchViewData():Promise<JsonSchemaDto> {
        return this.client.get<JsonSchemaDto>(this.schema).then(schemaData => implementInterface(schemaData));
    }

    protected render(schemaData: JsonSchemaAdapter):void {
        // TODO: Remove this next line when jackson-jsonSchema handles custom formats.
        this.deriveFormatsFromTitles(schemaData.asObjectSchema().properties);
        let instancesLink = schemaData.asObjectSchema().links.filter(l => l.rel === 'instances')[0];
        let filterSchema = instancesLink.schema;
        let $container = this.element().empty();
        if (filterSchema.type !== 'object') {
            console.warn('Schema at ' + this.schema.href() + ' returned filter schema of type ' + filterSchema.type);
        } else {
            let filterEl = <EccoFilter>document.createElement('ecco-filter');
            let listEl = <EccoList>document.createElement('ecco-list');
            filterEl.schemaData = filterSchema.asObjectSchema();
            listEl.schemaData = schemaData.asObjectSchema();
            listEl.type = this.type;
            $container.append(filterEl).append(listEl);

            $(filterEl).on('change', (e: $.JQueryEventObject) => {
                e.stopPropagation();
                let filter = filterEl.value;
                console.log('Filter changed: ' + JSON.stringify(filter));
                // Turn the filter object into URL parameters
                if (instancesLink.method && instancesLink.method !== 'GET') {
                    console.warn('Instances link from ' + this.schema.href() + ' uses method ' + instancesLink.method);
                }
                let filterParameters = Object.keys(filter)
                    .map(propertyName => encodeURIComponent(propertyName) + '=' + encodeURIComponent(filter[propertyName]))
                    .join('&');
                $(listEl).attr('src', filterParameters? (instancesLink.href + '?' + filterParameters) : instancesLink.href);
            });
            /*
             * TODO: Create an ecco-list, build a URL from the default filter (as returned by ecco-filter) and set it on the list as the data source.
             * Also specify the schema directly on the list to avoid a separate repeat fetch. Another attribute should specify the parameter for page number.
             * ecco-list should use the presence of 'first', 'last', 'next' and 'prev' links to determine whether to show paging or not.
             * If it determines based on the current fetch that there are other pages, it should display a page navigation (another web component).
             * Also 'pageSize' should be read only so it does not appear in the filter.
             * Add a change event listener on ecco-filter to keep updatng the list URL accordingly.
             */
        }
    }

    /**
     * Any properties which have a ':format' suffix have their format replaced with the suffix and it is then stripped off the title.
     * This allows us to specify custom formats whilst jackson-jsonSchema is lacking support to do it (as JsonValueFormat is an enum).
     * @param properties the properties, with titles.
     */
    private deriveFormatsFromTitles(properties: {[name: string]: JsonSchemaAdapter}): void {
        let propertyNames = Object.keys(properties);
        for (let i = 0, l = propertyNames.length; i < l; i++) {
            let property = properties[propertyNames[i]].asValueTypeSchema();
            let index;
            if (property && property.title && (index = property.title.indexOf(':')) >= 0) {
                property.format = property.title.substring(index + 1) as any;
                property.title = property.title.substring(0, index);
                console.debug('Updated format of %o to %o', propertyNames[i], property.format);
            }
        }
    }
}

document.registerElement('ecco-filtered-list', EccoFilteredList);

export = EccoFilteredList;
