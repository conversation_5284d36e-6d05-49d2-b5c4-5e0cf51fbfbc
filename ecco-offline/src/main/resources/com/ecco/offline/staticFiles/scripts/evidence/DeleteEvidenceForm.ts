import $ = require("jquery");

import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import TextAreaInput = require("../controls/TextAreaInput");
import Form = require("../controls/Form");
import InputGroup = require("../controls/InputGroup");
import {DeleteEvidenceCommand, DeleteEvidenceRequestCommand, EvidenceDiscriminator} from "ecco-commands";
import {DeleteType, EvidenceGroup} from "ecco-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import {Command} from "ecco-commands";

/**
 * Provides the pop up form from which to do deletions/requests.
 */
export class DeleteEvidenceForm extends BaseAsyncCommandForm<void> {

    public static showInModal(discriminator: EvidenceDiscriminator, deleteType: DeleteType, serviceRecipientId: number,
                              workUuid: Uuid,
                              evidenceGroup: EvidenceGroup, taskName: string, viewModel: string,
                              onSubmitted: (cmd: Command) => void, requestDeleteUuid?: string, requestReason?: string) {
        let form = new DeleteEvidenceForm(discriminator, deleteType, serviceRecipientId, workUuid, evidenceGroup,
            taskName, viewModel, onSubmitted, requestDeleteUuid, requestReason);
        form.load();
        // Modal.showInModal(form);
        showFormInModalDom(form);
    }

    private form = new Form();
    private reason = new TextAreaInput("reason");

    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private discriminator: EvidenceDiscriminator, private deleteType: DeleteType, private serviceRecipientId: number,
                private workUuid: Uuid, private evidenceGroup: EvidenceGroup, private taskName: string,
                private viewModel: string, private onSubmitted: (cmd: Command) => void,
                private requestDeleteUuid?: string, private requestReason?: string) {
        super(deleteType == DeleteType.REVOKE ? "undo deletion request" :
            requestDeleteUuid ? "delete this entry" : "request deletion for this entry", undefined, null);
        // NB this did use 'this.setOnFinished' but this does the same job but passes the command sent
        this.commandQueue.addCommandSubmittedEventHandler(this.onSubmitted);

        const label = deleteType == DeleteType.REVOKE ? "undo deletion request reason" :
            requestDeleteUuid ? "comment on this deletion" : "request deletion reason";
        if (requestDeleteUuid) {
            this.form.append( $("<div>").text("request deletion reason: ".concat(this.requestReason)) );
            this.form.append($("<br>"));
        }
        this.form.append( new InputGroup(label, this.reason) );
    }

    protected fetchViewData(): Promise<void> {
        return Promise.resolve(null);
    }

    protected render() {
        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }

    protected override submitForm(): Promise<void> {
        let cmd;
        switch (this.deleteType) {
            case DeleteType.REQUEST:
                cmd = new DeleteEvidenceRequestCommand(this.discriminator, this.serviceRecipientId, this.workUuid, this.evidenceGroup,
                    this.taskName, this.reason.val(), false, this.viewModel);
                break;
            case DeleteType.REVOKE:
                cmd = new DeleteEvidenceRequestCommand(this.discriminator, this.serviceRecipientId, this.workUuid, this.evidenceGroup,
                    this.taskName, this.reason.val(), true, this.viewModel);
                break;
            case DeleteType.DELETE:
                cmd = DeleteEvidenceCommand.create(this.serviceRecipientId, this.workUuid, this.evidenceGroup,
                    this.taskName, this.reason.val(), this.requestDeleteUuid, this.viewModel);
                break;
        }
        this.commandQueue.addCommand(cmd);
        return super.submitForm();
    }

}
