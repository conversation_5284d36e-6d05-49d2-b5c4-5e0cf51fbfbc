import $ = require("jquery");
import ActionButton = require("../controls/ActionButton");
import WizardForm = require("../controls/WizardForm");
import BaseEvidenceForm = require("../evidence/BaseEvidenceForm");
import QuestionnaireEvidenceForm = require("../evidence/questionnaire/QuestionnaireEvidenceForm");
import QuestionnaireHistoryListControl = require("../evidence/questionnaire/QuestionnaireHistoryListControl");
import QuestionnaireEvidenceStarForm = require("../evidence/star/QuestionnaireEvidenceStarForm");
import SupportHistoryListControl = require("../evidence/support/SupportHistoryListControl");
import CommandHistoryListControl = require("../service-recipients/CommandHistoryListControl");

import services = require("ecco-offline-data");
import EvidenceEmbeddedSwitcherControl = require("./EvidenceEmbeddedSwitcherControl");
import RiskHistoryListControl = require("./risk/RiskHistoryListControl");
import {SparseArray} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {applicationRootPath} from "application-properties";
import {CommandQueue, WorkUuidResolver} from "ecco-commands";
import {
    getGlobalEccoAPI,
    getGuidanceUrlCallback} from "ecco-components";
import {possiblyModalFormDom,
    showInModalDom,
    showNotification} from "ecco-components-core";
import * as referralDto from "ecco-dto";
import {
    ConfigResolver,
    EvidencePageType,
    isOffline,
    ReferralDto,
    ReferralSummaryWithEntities,
    ReviewChoices,
    ServiceRecipientWithEntities,
    SessionData,
    SupportWorkAjaxRepository
} from "ecco-dto";
import {SupportWork} from "ecco-dto/evidence-dto";
import {ActivityType, VisualStyle} from "ecco-dto/service-config-dto";
import {PrefixType} from "ecco-dto/service-recipient-dto";
import {EvidenceDef} from "ecco-evidence";
import * as ReactDom from "react-dom";
import {LoneWorkerControlArchived, TimerControl} from "../loneworker/LoneWorkerControlArchived";
// ** ONLINE ONLY currently
import {GroupSupportActivityTypeAjaxRepository} from "ecco-dto";
import {getCommandQueueRepository, getReferralRepository} from "ecco-offline-data";
import HactNotificationControl from "./hact/HactNotificationControl";
import SmartStepEvidenceForm from "./tabular/SmartStepEvidenceForm";
import {MutableRefObject, ReactNode} from 'react';
import {ifNeededSetDefaultGlobalEccoAPI} from "../ifNeededSetDefaultGlobalEccoAPI";
import {FlagReloadEvent} from "./risk/RiskStatusAreaControl";

//const lazySmartStepEvidenceForm = () => import("./tabular/SmartStepEvidenceForm");
const lazySmartStepEvidenceVisualForm = () => import("./graph/SmartStepEvidenceForm");
const lazyChecklistEvidenceForm = () => import("../evidence/checklist/ChecklistEvidenceForm");

/**
 * Pass to difference evidence handlers - ie smart steps, reviews, questionnaires
 */
export class EvidenceDelegatingForm {

    public static enhanceHistoryButtons() {
        services.getFeatureConfigRepository().getSessionData().then(sessionData => {
            let $elements = $(".open-history-link");
            $elements.each((index, element) => {
                const srId = parseInt($(element).attr("data-service-recipient-id"));
                const taskName = $(element).attr("data-task-name");
                const type = EvidenceDef.taskEvidenceType(sessionData, taskName);
                if (EvidenceDef.isQuestionnaire(type)) {
                    $(element).click(() => QuestionnaireHistoryListControl.showInModal(srId, taskName));
                } else if (EvidenceDef.isRisk(type)) {
                    $(element).clickSynchronous(() => RiskHistoryListControl.showInModal(srId, taskName));
                } else {
                    $(element).click(() => SupportHistoryListControl.showInModal(srId, taskName));
                }
            });
        });
    }

    // NB this doesn't take into account the newer react pages tabular-new
    // - we should generally move towards a TasksControl.lookupEvidenceTasks
    public static showInModal(r: ReferralDto, title: string, taskName: string) {
        EvidenceDelegatingForm.showInModalByIds(r.serviceRecipientId, title, taskName);
    }

    public static showInModalByIds(serviceRecipientId: number, title: string,
                                   taskName: string, eventId?: string | undefined, onCompleted?: (() => void) | undefined) {

        // TODO we need form for the title/header/footer, even though footer used
        //const form: BaseEvidenceForm = new SmartStepEvidenceForm(referral, [], evidenceDef, title);
        const $container = $("<div>");
        const cancelRef = {} as MutableRefObject<() => void>;
        showInModalDom(title ,$container[0], undefined, undefined, undefined, cancelRef);
        const onCompletedAndClose = () => {
            showNotification("info", "change saved");
            cancelRef.current();
            onCompleted && onCompleted();
        };
        EvidenceDelegatingForm.layoutLoadAndAttachEvidenceQ($container, serviceRecipientId, taskName, onCompletedAndClose, eventId);
    }

    // called by TasksControl
    public static showInReactModalByIds(serviceRecipientId: number, title: string,
                                        taskName: string, eventId?: string | undefined) {
        // TODO we need form for the title/header/footer, even though footer used
        //const form: BaseEvidenceForm = new SmartStepEvidenceForm(referral, [], evidenceDef, title);
        let $modalEl = $("#modal-for-form");
        if ($modalEl.length == 0) {
            $modalEl = $("<div>").appendTo($("body"));
        }
        const unmount = () => { ReactDom.unmountComponentAtNode($modalEl[0])};
        const $container = $("<div>");
        const p = EvidenceDelegatingForm.layoutLoadAndAttachEvidenceInnerQ($container, serviceRecipientId, taskName, unmount, eventId);
        p.then(serviceRecipient => {

            // attempt to resolve any guidance
            const guidanceFormDefinitionUuid = serviceRecipient.configResolver.getServiceType().getTaskDefinitionSetting(taskName, "guidanceFormDefinition");
            const guidanceCallback = guidanceFormDefinitionUuid ? getGuidanceUrlCallback(guidanceFormDefinitionUuid) : undefined;

            // get size of modal
            const modalFullScreen = serviceRecipient.configResolver.getServiceType().getTaskDefinitionSetting(taskName, "modalFullScreen") == "y"
                                                || undefined;

            // Now readOnly hides the 'close' button so we don't end up with a 'save' next to close.
            ReactDom.render(possiblyModalFormDom(title, true, true,
                            unmount, unmount,
                            false, true, $container[0], // readOnly = true for now as we've not hooked into modal save
                            undefined, guidanceCallback,
                            undefined, undefined, undefined, modalFullScreen
                    ),
                    $modalEl[0]);
        });
    }

    public static enhance($container: $.JQuery, serviceRecipientId: number, taskName: string, onCompleted: () => void) {
        // NB reviews aren't called as embedded elements
        EvidenceDelegatingForm.layoutLoadAndAttachEvidenceQ($container, serviceRecipientId, taskName, onCompleted);
    }

    private static layoutLoadAndAttachEvidenceQ($container: $.JQuery, srId: number, taskName: string, onCompleted: () => void, eventId?: string | undefined, helpContainerCallback?: Promise<ReactNode> | undefined) {
        this.layoutLoadAndAttachEvidenceInnerQ($container, srId,  taskName, onCompleted, eventId)
                .then();
    }
    private static layoutLoadAndAttachEvidenceInnerQ($container: $.JQuery, srId: number, taskName: string, onCompleted: () => void, eventId?: string | undefined): Promise<ServiceRecipientWithEntities> {
        // NB the sessionData etc could come from the caller, but should be cached anyway
        return ReferralDataWrapper.instance.getServiceRecipientWithEntitiesQ(false, srId).then(serviceRecipient => {

            // switch to loading a referral if we need the clientId due to hact
            const svcId = serviceRecipient.features.getServiceCategorisation(serviceRecipient.serviceAllocationId).serviceId;
            if (serviceRecipient.features.hasHactForService(svcId) && serviceRecipient.prefix == 'r') {
                ReferralDataWrapper.instance.getReferralSummaryWithEntitiesQ(false, srId).then(referral => {
                    EvidenceDelegatingForm.layoutLoadAndAttachEvidence(serviceRecipient.prefix, $container, referral.features, referral.configResolver,
                                                                       srId, referral.clientId, taskName, onCompleted, eventId);
                });
            } else {
                EvidenceDelegatingForm.layoutLoadAndAttachEvidence(serviceRecipient.prefix, $container, serviceRecipient.features, serviceRecipient.configResolver,
                                                                   srId, null, taskName, onCompleted, eventId);
            }

            return serviceRecipient;
        });
    }

    /**
     * Provide the layout for the $container to then populate the controls
     * Replicate supportEvidenceForm.jsp and genericTypeSupport.jsp, but without the jsp.
     * Our focus here is on including all functionality such as hact/history/switcher components
     * and existing menu items.
     */
    private static layoutLoadAndAttachEvidence(prefix: PrefixType, $container: $.JQuery, sessionData: SessionData, configResolver: ConfigResolver,
                                               srId: number, clientId: number, taskName: string, onCompleted: () => void, eventId?: string | undefined) {

        const hactId = "hactNotification";
        const loneWorkerId = "loneWorker";
        const timerId = "timer";
        const componentUuid = Uuid.randomV4();
        const embeddedComponentsSwitcherId = "embeddedComponentsSwitcher-".concat(componentUuid.toString());
        const supportHistoryListWrapperId = "supportHistoryListWrapper-".concat(componentUuid.toString());
        const evidenceAreaId = "evidenceArea-".concat(componentUuid.toString());

        const embeddedComponentsSwitcherEnabled = configResolver.getServiceType()
            .taskDefinitionSettingHasFlag(taskName,  "embeddedComponentsSwitcher", "y");
        const historyEnabled = configResolver.getServiceType()
            .taskDefinitionSettingHasFlag(taskName,  "embeddedComponents", "history");
        const loneWorkerModuleEnabled = prefix == 'r' && sessionData.isModuleEnabled("loneWorker");
        const loneWorkerEnabled = loneWorkerModuleEnabled &&
            configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(taskName,  "showCommentComponents", "loneWorker");
        const timerEnabled = configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(taskName,  "tookPlaceOn", "timer");

        const title = configResolver.getServiceType().lookupTaskName(taskName)
        $("#printable-title").text(title)

        let $wrapper = $("<div>").addClass("row");

        let $hact = $("<div>")
            .attr("id", hactId)
            .data("serviceRecipientId", srId)
            .data("clientId", clientId)
        ;
        $wrapper.append($("<div>").addClass("text-center").append($hact));

        if (loneWorkerEnabled) {
            let $loneWorker = $("<div>")
                .attr("id", loneWorkerId)
                .data("serviceRecipientId", srId)
                .data("userContactId", sessionData.getDto().individualUserSummary.individualId);
            $wrapper.append($("<div>").addClass("text-center").append($loneWorker));
        }

        if (timerEnabled && !loneWorkerEnabled) {
            let $timer = $("<div>")
                .attr("id", timerId);
            $wrapper.append($("<div>").addClass("text-center").append($timer));
        }

        if (embeddedComponentsSwitcherEnabled) {
            let $embeddedComponentsSwitcher = $("<div>")
                .attr("id", embeddedComponentsSwitcherId)
                .attr("data-history-text", "show history")
                .attr("data-history-id", supportHistoryListWrapperId)
                .attr("data-plan-text", "support plan")
                .attr("data-plan-id", evidenceAreaId)
            ;
            $wrapper.append($embeddedComponentsSwitcher);
        }

        //$wrapper.addClass("row rounded");
        let $evidenceControl = $("<div>").addClass("col-xs-12 evidence-control top-gap-15 bottom-gap-15");
        let $footer = $("<p>").addClass("form-footer col-xs-12 text-right");
        let $menu = $("<p>").addClass("col-xs-12 text-center"); // was 'embedded-menu' id during transitional 'tasks' list
        $wrapper.append($menu);
        $wrapper.append($footer);
        $wrapper.append($evidenceControl);
        $container.append($wrapper);

        // TODO make agnostic to the evidence task we are in
        if (historyEnabled) {
            let $history = $("<div>").addClass("row rounded")
                .attr("id", supportHistoryListWrapperId)
            ;
            $history.toggle(!embeddedComponentsSwitcherEnabled); // hide if switched enabled
            let $historyReplace = $("<div>");
            $history.append($historyReplace);
            $container.append($history);

            // NB if ROLE_STAFF, but really if we are here, we are staff?
            let historyCtl = new SupportHistoryListControl(srId, taskName);
            historyCtl.attach($historyReplace);
        }

        // supportEvidenceForm.jsp still refers to evidenceGroupName from PageAndEvidenceGroupMappings.fromTaskNameToEvidenceGroupName(aspectName)
        // but is based on EvidenceDef.fromTaskName() and apparently the server side is only used for attachments
        // and its true that on the client side it doesn't pass the evidenceGroupName to SmartStepEvidenceForm.loadAndAttach
        // TODO check attachments
        const srQ = getReferralRepository().findOneServiceRecipientWithEntities(srId).then( svcRec => {
            EvidenceDelegatingForm.loadAndAttach(prefix, $evidenceControl, $footer, evidenceAreaId, hactId, loneWorkerId, timerId, embeddedComponentsSwitcherId,
                svcRec, taskName, onCompleted, onCompleted, undefined, eventId);

            // DEV-1235: See if we have a React footer nearby and move stuff there instead
            // We've also removed the 'close' button next to the 'save' via possiblyModalForm above
            const $muiFooter = $footer.parents(".MuiDialogContent-root").siblings(".modal-footer")
            if ($muiFooter.length > 0) {
                $footer.prependTo($muiFooter)
            }

        });

        // TODO make agnostic to the taskName
        // TODO add more menus
        const menuHistoryEnabled = configResolver.getServiceType()
            .taskDefinitionSettingHasFlag(taskName,  "showMenus", "overview");
        if (menuHistoryEnabled) {
            let $menuHistory = new ActionButton("history", undefined, true);
            $menu.append($("<div>").css({"text-align": "center"}).append($menuHistory.element()));

            // probably better as some delegator, as now has a few of these if statements
            const type = EvidenceDef.taskEvidenceType(sessionData, taskName);
            if (EvidenceDef.isQuestionnaire(type)) {
                $menuHistory.clickSynchronous(() => QuestionnaireHistoryListControl.showInModal(srId, taskName));
            } else if (EvidenceDef.isChecklist(type)) {
                // see changes-page.ts
                //const grpText = configResolver.getServiceType().getTaskDefinitionSetting(taskName,  "evidenceGroupId");
                const evidenceDef = EvidenceDef.fromTaskName(sessionData, configResolver.getServiceType(), taskName);
                // NB this is only for this checklist, so show the detail
                $menuHistory.clickSynchronous(() => {
                    const history = CommandHistoryListControl.createWithIds(srId, evidenceDef.getEvidenceGroup(), taskName, true);
                    showInModalDom("history", history.domElement());
                    history.load();
                });
            } else if (EvidenceDef.isRisk(type)) {
                $menuHistory.clickSynchronous(() => RiskHistoryListControl.showInModal(srId, taskName));
            } else {
                $menuHistory.clickSynchronous(() => SupportHistoryListControl.showInModal(srId, taskName));
            }

        }

        const tapToEdit = sessionData.isEnabled("support.evidence.tapToEditTextAreas");
        // this avoids us doing something in the dom later to hide the 'printable' link because we end up with two - from enhanceMenuElements.ts
        const isModalHack = !(window.location.href.indexOf(taskName) > -1);

        // is there something to print!? are we just a comment-based new entry page
        const evidenceDef = EvidenceDef.fromTaskName(sessionData, configResolver.getServiceType(), taskName);
        const isCommentOnly = evidenceDef.getEvidencePageType() == EvidencePageType.commentsOnly
                || configResolver.getServiceType().taskDefinitionSettingHasFlag(taskName,  "showOutcomes", "none");

        if (tapToEdit && isModalHack && !isCommentOnly) {
            const $menuPrintable = $("<a>");
            $menu.append($("<div>").css({"text-align": "center"}).append($menuPrintable));
            const printableUrl = `${applicationRootPath}nav/service-recipient/${srId}/task/${taskName}/printable`;
            $menuPrintable.addClass("printable-link-open-tab")
                .attr("target","_blank")
                .attr('href', printableUrl)
                .text("printable");
        }

    }

    /**
     * Whether popup/embed, attach controls to a container with elements-in-waiting.
     * This handles stayOnSave/hact/embedded-history.
     */
    public static loadAndAttach(prefix: PrefixType, $container: $.JQuery, $footer: $.JQuery, planAllocatedId: string, hactAllocatedId: string,
                                $loneWorkerId: string, $timerId: string, switcherAllocatedId: string,
                                serviceRecipient: ServiceRecipientWithEntities,
                                taskName: string, onDirty: () => void, onCompleted: () => void,
                                previousCommandQueue?: CommandQueue | undefined,
                                eventId?: string | undefined) {

        let commandQueue = new CommandQueue(getCommandQueueRepository());

        let stayOnSaveReload = () => {
            $container.empty();
            $footer.empty();
            // show a confirmation msg of the save on reload - so that we don't
            // lose any work on the page after 2 seconds
            showNotification("info", "change saved");
            EvidenceDelegatingForm.loadAndAttach(prefix, $container, $footer, planAllocatedId, hactAllocatedId, $loneWorkerId, $timerId,
                switcherAllocatedId, serviceRecipient, taskName, onDirty, onCompleted, commandQueue);
        };

        const svcId = serviceRecipient.features.getServiceCategorisation(serviceRecipient.serviceAllocationId).serviceId;
        const hactEnabled = HactNotificationControl.hactEnabled(prefix, svcId, serviceRecipient.features);
        const hactShowAllOutstanding = serviceRecipient.configResolver.getServiceType().taskDefinitionSettingHasFlag(taskName,  "hactConfig", "showAllOutstanding");
        const hactControlPromise: Promise<HactNotificationControl> = isOffline() || !hactEnabled
            ? Promise.resolve(null)
            : EvidenceDelegatingForm.loadAndAttachHact(hactAllocatedId, hactShowAllOutstanding);

        let workUuidQResolver = new WorkUuidResolver(); // be safe with a new reference per 'load and attach'
        EvidenceDelegatingForm.loadAndAttachLoneWorker(workUuidQResolver.getWorkUuid(), serviceRecipient, taskName, $loneWorkerId);
        EvidenceDelegatingForm.loadAndAttachTimer($timerId);

        let embeddedSwitcherControl = EvidenceDelegatingForm.loadAndAttachEmbeddedSwitcher(switcherAllocatedId);

        hactControlPromise.then(hactControl => {
            const type = EvidenceDef.taskEvidenceType(serviceRecipient.features, taskName);
            if (EvidenceDef.isQuestionnaire(type)) {
                QuestionnaireEvidenceDelegatingForm.loadAndAttach(prefix, workUuidQResolver, $container, $footer, planAllocatedId, serviceRecipient, taskName, onDirty, stayOnSaveReload, hactControl, embeddedSwitcherControl, onCompleted, commandQueue);
            } else if (EvidenceDef.isChecklist(type)) {
                ChecklistEvidenceDelegatingForm.loadAndAttach(prefix, workUuidQResolver, $container, $footer, planAllocatedId, serviceRecipient, taskName, onDirty, stayOnSaveReload, hactControl, embeddedSwitcherControl, onCompleted);
            } else {
                // for a review, we assume the review setup form has been completed and we are rendering the smart step page
                SmartStepEvidenceDelegatingForm.loadAndAttach(prefix, workUuidQResolver, $container, $footer, planAllocatedId,
                    serviceRecipient, taskName, onDirty, stayOnSaveReload, hactControl, embeddedSwitcherControl, onCompleted,
                    commandQueue, previousCommandQueue, eventId);
            }
        });
    }

    private static loadAndAttachHact(hactAllocatedId: string, showAllOutstanding: boolean): Promise<HactNotificationControl> {
        let $hactEl: $.JQuery = $('#'.concat(hactAllocatedId));

        if ($hactEl.length) {
            const serviceRecipientId = parseInt($hactEl.data("service-recipient-id"));
            const clientId = parseInt($hactEl.data("client-id"));

            return import("./hact/HactNotificationControl").then(({HactNotificationControl}) => {
                const control = new HactNotificationControl(clientId, serviceRecipientId, showAllOutstanding);
                const $innerEl = $("<div>");
                $hactEl.empty().append($innerEl);
                control.attach($innerEl);
                control.load();
                return control;
            });
        }
        return Promise.resolve(null);
    }

    private static loadAndAttachLoneWorker(
        workUuid: Uuid,
        serviceRecipient: ServiceRecipientWithEntities,
        taskName: string, $elementId: string): LoneWorkerControlArchived {

        let $loneWorkerEl: $.JQuery = $('#'.concat($elementId));

        if ($loneWorkerEl.length) {
            const userContactId = parseInt($loneWorkerEl.data("user-contact-id"));
            const control = new LoneWorkerControlArchived(serviceRecipient.features, workUuid, serviceRecipient.serviceRecipientId,
                userContactId, taskName, serviceRecipient.configResolver.getServiceType());

            const $innerEl = $("<div>");
            $loneWorkerEl.empty().append($innerEl);
            control.attach($innerEl);
            control.render();
            return control;
        }
        return null;
    }

    private static loadAndAttachTimer($elementId): TimerControl {
        let $timerEl: $.JQuery = $('#'.concat($elementId));

        if ($timerEl.length) {
            const control = new TimerControl();
            const $innerEl = $("<div>");
            $timerEl.empty().append($innerEl);
            control.attach($innerEl);
            control.render();
            return control;
        }
        return null;
    }

    private static loadAndAttachEmbeddedSwitcher(switcherAllocatedId: string): EvidenceEmbeddedSwitcherControl {
        let $switcher: $.JQuery = $('#'.concat(switcherAllocatedId));

        if ($switcher.length) {
            return new EvidenceEmbeddedSwitcherControl($switcher);
        }
        return null;
    }

}

class ReferralDataWrapper {
    static instance = new ReferralDataWrapper();

    private cacheServiceRecipientWithEntities: SparseArray<Promise<ServiceRecipientWithEntities>> = {};
    private cacheReferralSummaryWithEntities: SparseArray<Promise<ReferralSummaryWithEntities>> = {};

    constructor() {}

    public getServiceRecipientWithEntitiesQ(clearCache: boolean, serviceRecipientId: number): Promise<ServiceRecipientWithEntities> {

        // cache the query so we can re-use for the duration of this enhanceTaskList
        if (clearCache || !this.cacheServiceRecipientWithEntities[serviceRecipientId]) {
            let srQ: Promise<ServiceRecipientWithEntities> =
                services.getReferralRepository().findOneServiceRecipientWithEntities(serviceRecipientId);
            this.cacheServiceRecipientWithEntities[serviceRecipientId] = srQ;
        }

        return this.cacheServiceRecipientWithEntities[serviceRecipientId];
    }

    public getReferralSummaryWithEntitiesQ(clearCache: boolean, serviceRecipientId: number): Promise<ReferralSummaryWithEntities> {

        // cache the query so we can re-use for the duration of this enhanceTaskList
        if (clearCache || !this.cacheReferralSummaryWithEntities[serviceRecipientId]) {
            let srQ: Promise<ReferralSummaryWithEntities> =
                services.getReferralRepository().findOneReferralSummaryWithEntitiesUsingDto(serviceRecipientId);
            this.cacheReferralSummaryWithEntities[serviceRecipientId] = srQ;
        }

        return this.cacheReferralSummaryWithEntities[serviceRecipientId];
    }

}

class BaseEvidenceDelegatingForm {

    public static getOnCompleted(isStayOnSave: boolean,
                                isReloadFlags: boolean,
                             hactControl: HactNotificationControl,
                             onCompletedStayOnSaveReload: () => void,
                             onCompletedFinal: () => void): () => void {

        // work out where to go after completion of submission of commands
        let onCompleted: () => void;
        const isHact = hactControl != null;

        if (isHact && isStayOnSave) {
            onCompleted = () =>
                // stayOnSave never calls onCompletedFinal
                hactControl.checkPromptNeeded(onCompletedStayOnSaveReload);
        } else if (isHact) {
            onCompleted = () =>
                hactControl.checkPromptNeeded(onCompletedFinal);
        } else if (isStayOnSave) {
            onCompleted = onCompletedStayOnSaveReload;
        } else {
            onCompleted = onCompletedFinal;
        }

        if (isReloadFlags) {
            return () => {
                onCompleted();
                FlagReloadEvent.bus.fire();
            };
        }

        return onCompleted;
    }

}

class SmartStepEvidenceDelegatingForm {

    public static loadAndAttach(prefix: PrefixType, workUuidQResolver: WorkUuidResolver, $container: $.JQuery, $footer: $.JQuery, planAllocatedId: string,
                                serviceRecipientWithEntities: ServiceRecipientWithEntities, taskName: string, onDirty: () => void,
                                onCompletedStayOnSaveReload: () => void, hactControl: HactNotificationControl,
                                embeddedSwitcherControl: EvidenceEmbeddedSwitcherControl,
                                onCompletedFinal: () => void,
                                commandQueue: CommandQueue,
                                previousCommandQueue: CommandQueue,
                                eventId?: string | undefined) {
        ifNeededSetDefaultGlobalEccoAPI(serviceRecipientWithEntities.features);

        const supportWorkAjaxRepository = new SupportWorkAjaxRepository(getGlobalEccoAPI().apiClient);
        const activityTypeRepository = new GroupSupportActivityTypeAjaxRepository(getGlobalEccoAPI().apiClient);

        const serviceRecipientId = serviceRecipientWithEntities.serviceRecipientId;

        const risksOutstandingPromise = services.getSupportWorkRepository()
            .findSupportWorkByServiceRecipientIdAndRiskManagementOutstanding(serviceRecipientId);

        // ** ONLINE ONLY currently
        let isReview = taskName.indexOf("Review") >= 0;
        const reviewChoicesPromise: Promise<ReviewChoices | null> = isReview
            ? supportWorkAjaxRepository.findReviewChoicesByServiceRecipientId(serviceRecipientId)
            : Promise.resolve(null);
        const activityTypeInterest = prefix == 'r' && !isOffline()
            ? activityTypeRepository.findActivityInterestsByServiceRecipientId(serviceRecipientId)
            : Promise.resolve([]);
        // ** ONLINE ONLY currently

        Promise.all([reviewChoicesPromise, risksOutstandingPromise, activityTypeInterest])
            .then(([
             reviewChoices,
             risksOutstanding,
             activityTypeInterest]: [ReviewChoices,SupportWork[],ActivityType[]]) => {

                const type = EvidenceDef.taskEvidenceType(serviceRecipientWithEntities.features, taskName);
                const evidenceDef = EvidenceDef.fromTaskName(serviceRecipientWithEntities.features,
                        serviceRecipientWithEntities.configResolver.getServiceType(), taskName, reviewChoices);

                // work out where to go after completion of submission of commands
                const isReview = EvidenceDef.isReview(taskName);
                const isStayOnSave = serviceRecipientWithEntities.features.isEnabled("support.evidence.stayOnSave");
                        // POSSIBLY... || isReview;
                const hasFlags = serviceRecipientWithEntities.configResolver.getServiceType().taskDefinitionSettingHasFlag(taskName, "showFlagsOn", "statusArea");
                const hasRiskFlags = EvidenceDef.isRisk(type);
                const isReloadFlags = hasFlags || hasRiskFlags;

                const onCompleted = BaseEvidenceDelegatingForm.getOnCompleted(isStayOnSave, isReloadFlags, hactControl, onCompletedStayOnSaveReload, onCompletedFinal);
                const form = SmartStepEvidenceDelegatingForm.getFormControl(serviceRecipientWithEntities, taskName,
                    workUuidQResolver, planAllocatedId, embeddedSwitcherControl, activityTypeInterest, risksOutstanding,
                    evidenceDef, onCompleted, commandQueue, previousCommandQueue, eventId);

                form.then(({form, isVisual}) => {
                    if (!isVisual && isReview) {
                        const tabular = form as SmartStepEvidenceForm;
                        const wizardForm = new WizardForm(serviceRecipientWithEntities.configResolver.getServiceType().getOutcomes().length, tabular);
                        tabular.setOnLoad(() => {wizardForm.loadWizardFirstPage();});

                        let $status = $("<div>")
                            .addClass("review-status")
                            .append($("<span>").text("review progress: "))
                            .append(wizardForm.status());
                        $container
                            .append($status)
                            .append(wizardForm.element());
                        $footer.append(wizardForm.getFooter());
                    } else {
                        $container.append(form.element());
                        $footer.append(form.getFooter());
                    }
                });
            });
    }

    private static getFormControl(serviceRecipientWithEntities: ServiceRecipientWithEntities, taskName: string,
                                    workUuidQResolver: WorkUuidResolver, planAllocatedId: string, embeddedSwitcherControl: EvidenceEmbeddedSwitcherControl,
                                  activityTypeInterest: ActivityType[], risksOutstanding: SupportWork[], evidenceDef, onCompleted: () => void,
                                  commandQueue: CommandQueue, previousCommandQueue: CommandQueue, eventId?: string | undefined)
    : Promise<{form: BaseEvidenceForm, isVisual: boolean}> {
        if (SmartStepEvidenceDelegatingForm.isNormalSmartSteps(serviceRecipientWithEntities, taskName)) {
            const form = new SmartStepEvidenceForm(workUuidQResolver, planAllocatedId, embeddedSwitcherControl,
                serviceRecipientWithEntities, activityTypeInterest, risksOutstanding, evidenceDef,
                "title - not used", onCompleted, commandQueue, previousCommandQueue, eventId);
            return Promise.resolve({form, isVisual: false});
        } else {
            return lazySmartStepEvidenceVisualForm().then(({SmartStepEvidenceForm}) => {
                const form = new SmartStepEvidenceForm(workUuidQResolver, serviceRecipientWithEntities, [],
                    taskName, "title");
                return {form, isVisual: true};
                }
            );
        }
    }

    private static isNormalSmartSteps = (rwe: referralDto.ServiceRecipientWithEntities, taskName: string) => {
        const style = rwe.configResolver.getServiceType().getTaskDefinitionSetting(taskName,  "showVisualStyleAs") as VisualStyle;
        const taskEnabled = style ? style == 'graphical' : false;

        return !(taskEnabled);
    };
}

class QuestionnaireEvidenceDelegatingForm {

    // taken from tabular/SmartStepEvidenceForm
    // TODO extract common functionality with SmartStepEvidenceDelegatingForm
    public static loadAndAttach(prefix: PrefixType, workUuidQResolver: WorkUuidResolver,
                                $container: $.JQuery, $footer: $.JQuery, planAllocatedId: string,
                                serviceRecipientWithEntities: ServiceRecipientWithEntities,  taskName: string, onDirty: () => void,
                                onCompletedStayOnSaveReload: () => void, hactControl: HactNotificationControl,
                                embeddedSwitcherControl: EvidenceEmbeddedSwitcherControl,
                                onCompletedFinal: () => void,
                                commandQueue: CommandQueue) {

            // currently, we don't get the group from the config
            //const grpText = serviceRecipientWithEntities.configResolver.getServiceType().getTaskDefinitionSetting(taskName,  "evidenceGroupId");
            const evidenceDef = EvidenceDef.fromTaskName(serviceRecipientWithEntities.features,
                    serviceRecipientWithEntities.configResolver.getServiceType(), taskName);
            // NB support flags only - we're assuming a questionnaire has no risk flags
            const hasFlags = serviceRecipientWithEntities.configResolver.getServiceType().taskDefinitionSettingHasFlag(taskName, "showFlagsOn", "statusArea");

            // work out where to go after completion of submission of commands
            let onCompleted = BaseEvidenceDelegatingForm.getOnCompleted(
                serviceRecipientWithEntities.features.isEnabled("support.evidence.stayOnSave"),
                hasFlags,
                hactControl, onCompletedStayOnSaveReload, onCompletedFinal);

            const isNonStar = QuestionnaireEvidenceDelegatingForm.isNormalQuestionnaire(serviceRecipientWithEntities, evidenceDef.getTaskName());
            const form: BaseEvidenceForm = isNonStar
                ? new QuestionnaireEvidenceForm(workUuidQResolver, planAllocatedId,
                    embeddedSwitcherControl, serviceRecipientWithEntities, evidenceDef,
                    "title - not used", onCompleted, commandQueue)
                : new QuestionnaireEvidenceStarForm(workUuidQResolver, planAllocatedId,
                    embeddedSwitcherControl, serviceRecipientWithEntities, evidenceDef,
                    "title - not used", onCompleted, commandQueue);

            $container.append(form.element());
            $footer.append(form.getFooter());
    }

    private static isNormalQuestionnaire = (rwe: referralDto.ServiceRecipientWithEntities, taskName: string) => {
        const style = rwe.configResolver.getServiceType().getTaskDefinitionSetting(taskName,  "showEvidenceStyleAs");
        return style ? style == 'default' : true; // either default or empty is good
    };
}

class ChecklistEvidenceDelegatingForm {

    // taken from tabular/SmartStepEvidenceForm
    // TODO extract common functionality with SmartStepEvidenceDelegatingForm
    public static loadAndAttach(prefix: PrefixType, workUuidQResolver: WorkUuidResolver, $container: $.JQuery, $footer: $.JQuery, planAllocatedId: string,
                                serviceRecipientWithEntities: ServiceRecipientWithEntities, taskName: string, onDirty: () => void,
                                onCompletedStayOnSaveReload: () => void, hactControl: HactNotificationControl,
                                embeddedSwitcherControl: EvidenceEmbeddedSwitcherControl,
                                onCompletedFinal: () => void) {

        const type = EvidenceDef.taskEvidenceType(serviceRecipientWithEntities.features, taskName);
        const evidenceDef = EvidenceDef.fromTaskName(serviceRecipientWithEntities.features,
                serviceRecipientWithEntities.configResolver.getServiceType(), taskName);
        // NB support flags only - we're assuming a checklist has no risk flags
        const hasFlags = serviceRecipientWithEntities.configResolver.getServiceType().taskDefinitionSettingHasFlag(taskName, "showFlagsOn", "statusArea");

        // work out where to go after completion of submission of commands
        let onCompleted = BaseEvidenceDelegatingForm.getOnCompleted(
            serviceRecipientWithEntities.features.isEnabled("support.evidence.stayOnSave"),
            hasFlags,
            hactControl, onCompletedStayOnSaveReload, onCompletedFinal);

        services.getSupportSmartStepsSnapshotRepository().findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroup(
            serviceRecipientWithEntities.serviceRecipientId, evidenceDef.getEvidenceGroup().name)
            .then(snapshot => {
                // NB it doesn't obey embeddedSwitcherControl, nor share the commandQueue (with lone worker?) but we use onCompleted and set the id below
                lazyChecklistEvidenceForm().then(({ChecklistEvidenceForm}) => {
                    const form = new ChecklistEvidenceForm(workUuidQResolver, serviceRecipientWithEntities, evidenceDef, snapshot)
                        .onSubmit((commandQueue) => {
                            commandQueue.flushCommands()
                                .then(() => onCompleted());
                        });

                    if (planAllocatedId) {
                        form.element().attr("id", planAllocatedId);
                    }

                    $container.append(form.element());
                    $footer.append(form.getFooter());

                });
            });
    }

}

export default EvidenceDelegatingForm;
