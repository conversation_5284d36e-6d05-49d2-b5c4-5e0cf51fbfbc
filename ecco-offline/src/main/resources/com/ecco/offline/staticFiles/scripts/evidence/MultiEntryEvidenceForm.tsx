import View = require("../controls/View");
import $ = require("jquery");
import _ = require("lodash");
import {Uuid} from "@eccosolutions/ecco-crypto";
import {
    Button,
    ExpansionPanel,
    ExpansionPanelDetails,
    ExpansionPanelSummary,
    Icon,
    Typography
} from "@eccosolutions/ecco-mui";
import {ExpansionPanelProps} from "@material-ui/core/ExpansionPanel";
import {CommandQueue, SupportCommentCommand, WorkUuidResolver} from "ecco-commands";
import {update, UpdateSpec} from "ecco-components";
import {dropdownList, numberInput, textArea} from "ecco-components-core";
import {ConfigResolver, listDefToIdName, ServiceRecipientWithEntities, SessionData} from "ecco-dto";
import {EvidenceDef} from "ecco-evidence";
import * as React from "react";
import {Component} from "react";
import * as ReactDom from "react-dom";


/*function relatedSrc(): string {
    return "link";
}*/


export class MultiEntryEvidenceForm implements View {

    private multiForm: MultiEntryEvidenceComponent;
    private $elMulti = $("<div>");

    constructor(workUuidResolver: WorkUuidResolver, private sre: ServiceRecipientWithEntities, private evidenceDef: EvidenceDef) {
        ReactDom.render(<MultiEntryEvidenceComponent ref={frm => this.multiForm = frm} sre={sre} evidenceDef={evidenceDef}><span/></MultiEntryEvidenceComponent>, this.$elMulti[0]);
    }

    // VIEW
    element(): $.JQuery {
        return this.$elMulti;
    }
    title(): string | $.JQuery {
        return null;
    }
    getFooter(): $.JQuery {
        return null;
    }
    // VIEW

    /** True if required fields are set */
    public isValid(): boolean {
        // TODO validate each entry
        return this.multiForm.isValid();
    }

    public emitTo(cmdQueue: CommandQueue, workDate: string) {
        this.multiForm.getEntries().map(entry => this.emitMultiEntryTo(entry, cmdQueue, workDate));
    }

    private emitMultiEntryTo(entry: SingleEntry, cmdQueue: CommandQueue, workDate: string) {
        const workUuid = Uuid.randomV4();
        const cmd = SupportCommentCommand.create(false, workUuid, this.sre.serviceRecipientId, this.evidenceDef.getEvidenceGroup(), this.evidenceDef.getTaskName());
        cmd.changeComment(null, entry.comment)
            .changeWorkDate(null, workDate)
            .changeCommentTypeId(null, entry.commentTypeId)
            //.changeClientStatusId(null, entry.clientStatusId)
            //.changeMeetingStatusId(null, entry.meetingStatusId)
            .changeLocationId(null, entry.locationId)
            .changeMinsSpent(null, entry.minsSpent);
            //.changeMinsTravel(null, entry.minsTravel)
            //.changeMileageTo(this.work && this.work.mileageTo, this.getMileageTo())
            //.changeMileageDuring(this.work && this.work.mileageDuring, this.getMileageDuring())
            // TODO .withAttachments(this.getAttachmentIds());

        // TODO
        // if (this.threatFlagCheckboxes) {
        //     cmd.changeAddedThreatFlags(null, this.getAddedThreatFlags());
        //     cmd.changeRemovedThreatFlags(null, this.getRemovedThreatFlags());
        // }

        cmdQueue.addCommand(cmd.build());
    }

}

interface SingleEntryProps {
    sessionData: SessionData,
    configResolver: ConfigResolver,
    evidenceDef: EvidenceDef,
    updateEntry: (entry: SingleEntry) => void;
    removeEntry: (entry: SingleEntry) => void;
    entry: SingleEntry;
}

interface SingleEntryState {
    showCommentBox: boolean;
}

class SingleEntryComponent extends Component<SingleEntryProps, SingleEntryState> {

    constructor(props) {
        super(props);
        this.state = {
            showCommentBox: props.entry && props.entry.comment!!
        }
    }

    override render() {
        const locationListName = this.props.configResolver.getServiceType().getTaskDefinitionSetting(this.props.evidenceDef.getTaskName(),  "locationListName");

        const stateSetter: (entry: SingleEntry) => void = state => {
            this.props.updateEntry(state);
        };
        //const { classes } = this.props;

        let commentTypes = this.props.configResolver.getServiceType().getCommentTypesById(this.props.sessionData, this.props.evidenceDef.getTaskName())
                .map(ld => {return {
                    id: ld.getId(),
                    name: ld.getDisplayName(),
                    disabled: ld.getDisabled()
                }});

        return (
            <ExpansionPanel>
                <ExpansionPanelSummary expandIcon={<Icon className={"fa fa-chevron-down"}/>}>
                    <Typography>{this.props.entry.commentTypeId
                                ? this.props.sessionData.getListDefinitionEntryById(this.props.entry.commentTypeId).getDisplayName()
                                    .concat(": ").concat((this.props.entry.minsSpent ? this.props.entry.minsSpent.toString().concat(" mins") : ""))
                                : (this.props.entry.minsSpent ? this.props.entry.minsSpent.toString().concat(" mins") : "-")}</Typography>
                </ExpansionPanelSummary>
                <ExpansionPanelDetails>
                    <form className='form-horizontal' style={{width: "100%"}}>
                        {numberInput("minsSpent", "mins spent", stateSetter, this.props.entry)}
                        {dropdownList("type", stateSetter, this.props.entry, "commentTypeId",
                            commentTypes)}
                        {dropdownList("location", stateSetter, this.props.entry, "locationId",
                            this.props.sessionData.getListDefinitionEntriesByListName(locationListName).map(ld => listDefToIdName(ld)))}

                        {this.state.showCommentBox
                            ? textArea("comment", "comment", stateSetter, this.props.entry)
                            : <Button onClick={() => {this.setState({showCommentBox: true})}}>
                                <Icon className={"fa fa-comments-o"}/>
                            </Button>
                        }
                        <Typography align={"right"}>
                            <Button onClick={() => {this.props.removeEntry(this.props.entry)}}>
                                <Icon className={"fa fa-minus"}/>
                            </Button>
                        </Typography>
                    </form>
                </ExpansionPanelDetails>
            </ExpansionPanel>
        );
    }
}


interface Props extends ExpansionPanelProps {
    sre: ServiceRecipientWithEntities;
    evidenceDef: EvidenceDef;
}

interface SingleEntry {
    uuid: Uuid;
    comment: string;
    commentTypeId: number;
    minsSpent: number;
    locationId: number;
}

interface State {
    entries: SingleEntry[];
}

/*const styles = theme => ({
    root: {
        width: '100%',
    },
    heading: {
        fontSize: theme.typography.pxToRem(15),
        fontWeight: theme.typography.fontWeightRegular,
    },
});*/

class MultiEntryEvidenceComponent extends Component<Props, State> {

    constructor(props) {
        super(props);

        this.state = {
            entries: []
        };
    }

    override render() {
        //const { classes } = this.props;

        return(
            <div>
                {this.state.entries.map(entry =>
                    <SingleEntryComponent sessionData={this.props.sre.features}
                                          configResolver={this.props.sre.configResolver}
                                          evidenceDef={this.props.evidenceDef}
                                          entry={entry}
                                          updateEntry={entry => this.updateSingleEntry(entry)}
                                          removeEntry={entry => this.removeSingleEntry(entry)}
                                          key={entry.uuid.toString()}
                    />
                )}
                <ExpansionPanel>
                    <ExpansionPanelSummary onClick={() => this.newSingleEntry()}>
                        <Typography><Icon className={"fa fa-plus"}/></Typography>
                    </ExpansionPanelSummary>
                </ExpansionPanel>
            </div>
        );
    }

    public isValid() {
        // TODO isValid
        return true;
    }

    public getEntries(): SingleEntry[] {
        return this.state.entries;
    }

    private updateSingleEntry(entry: SingleEntry) {
        const i = _.findIndex(this.state.entries, item => item.uuid == entry.uuid);
        const updatedEntry = update(this.state.entries[i], {
            comment: {$set: entry.comment},
            commentTypeId: {$set: entry.commentTypeId},
            minsSpent: {$set: entry.minsSpent},
            locationId: {$set: entry.locationId}
        });
        const newEntries = update(this.state.entries, {
            $splice: [[i, 1, updatedEntry]]
        });
        this.setState({entries: newEntries});
        // const updated = update(this.state, {entries: {$push: [newData]}} as UpdateSpec<State>);
        // this.setState(updated);
    }

    private removeSingleEntry(entry: SingleEntry) {
        const copyArray = [...this.state.entries];
        const i = _.findIndex(this.state.entries, item => item.uuid == entry.uuid);
        copyArray.splice(i, 1);
        this.setState({entries: copyArray});
    }

    private newSingleEntry() {
        const newData: SingleEntry = {
            uuid: Uuid.randomV4(),
            comment: null,
            locationId: null,
            commentTypeId: null,
            minsSpent: null
        };
        const updated = update(this.state, {entries: {$push: [newData]}} as UpdateSpec<State>);
        this.setState(updated);
    }

}

//export default withStyles(styles)(MultiEntryEvidenceForm);
export default MultiEntryEvidenceForm;