import * as React from "react";
import {Component} from "react";
import ReactDOM = require('react-dom');
import $ = require("jquery");

import {EvidenceDelegatingForm} from "../EvidenceDelegatingForm";


interface Props {
    serviceRecipientId: number;
    title: string;
    taskName: string;
    onCompleted: () => void;
}

export class EvidenceDelegatingEditor extends Component<Props> {

    override componentDidMount(): void {
        const div = $(ReactDOM.findDOMNode(this));
        EvidenceDelegatingForm.enhance(div, this.props.serviceRecipientId, this.props.taskName, this.props.onCompleted);
    }

    override render() {
        return (
            <div>
            </div>
        );
    }
}
export default EvidenceDelegatingEditor;
