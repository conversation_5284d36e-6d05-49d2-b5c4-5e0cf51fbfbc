import {BackingData, BaseHistoryListControl} from "../BaseHistoryListControl";
import {ServiceRecipientWithEntities} from "ecco-dto";
import {EvidenceDef} from "ecco-evidence";

import {CollectionSubscription, FormEvidenceWork} from "ecco-commands";
import {showInModalDom} from "ecco-components-core";
import CustomFormHistoryItemControl from "./CustomFormHistoryItemControl";
import services = require("ecco-offline-data");

class CustomFormHistoryListControl extends BaseHistoryListControl<FormEvidenceWork, CustomFormHistoryItemControl> {

    public static showInModal(serviceRecipientId: number, taskName: string) {
        const form = new CustomFormHistoryListControl(serviceRecipientId, taskName);
        // we didn't have a header or footer, so just the form
        showInModalDom("history", form.element()[0]);
        form.load();
    }

    constructor(serviceRecipientId: number, taskName: string,
                private attachmentsOnly?: boolean, private workUuid?: string) {
        super(serviceRecipientId, taskName, null);
    }

    protected createItemControl(serviceRecipient: ServiceRecipientWithEntities, work: FormEvidenceWork) {
        return new CustomFormHistoryItemControl(serviceRecipient, work, undefined, this.attachmentsOnly);
    }

    protected type(): string {
        return "form";
    }

    protected fetchViewData(): Promise<BackingData<FormEvidenceWork>> {
        const historyQ = CustomFormHistoryListControl.customFormHistoryQ(this.serviceRecipientId, this.taskName,
            this.pageNumber, this.workUuid, this.attachmentsOnly);
        return historyQ;
    }

    public static customFormHistoryQ(serviceRecipientId: number, taskName: string, pageNumber: number,
                                  uuidOnly?: string, attachmentsOnly?: boolean): Promise<BackingData<FormEvidenceWork>> {
        const serviceRecipientQ = services.getReferralRepository().findOneServiceRecipientWithEntities(serviceRecipientId);
        return serviceRecipientQ
            .then(serviceRecipientWithEntities => {
                const evidenceDefGroup = taskName ? EvidenceDef.fromTaskName(serviceRecipientWithEntities.features,
                        serviceRecipientWithEntities.configResolver.getServiceType(), taskName).getEvidenceGroup() : null;
                let customFormWorkObs: CollectionSubscription<FormEvidenceWork>;
                if (uuidOnly) {
                    customFormWorkObs =
                        services.getEvidenceEffectsRepository().findOneFormEvidenceWorkByWorkUuid(
                            serviceRecipientId,
                            uuidOnly);
                } else {
                    customFormWorkObs =
                        services.getEvidenceEffectsRepository().findFormEvidenceWorkByServiceRecipientId(
                            serviceRecipientId,
                            evidenceDefGroup);
                } // request page 0 if only want first page
                const backingData = new BackingData(serviceRecipientWithEntities, customFormWorkObs);

                return new Promise<BackingData<FormEvidenceWork>>((resolve, reject) => {
                    customFormWorkObs.getSnapshots().then(
                        () => resolve(backingData),
                        exception => reject(exception)
                    );
                });
            });
    }

}
export = CustomFormHistoryListControl;
