
import ActionNode = require("./ActionNode");
import ActionGroupNode = require("./ActionGroupNode");
import * as domain from "ecco-dto";
import dynamicTree = require("../../draw/dynamic-tree");
import * as evidenceDto from "ecco-dto/evidence-dto";
import SupportAction = evidenceDto.SupportAction;
import GraphContext = require("./GraphContext");
import AnnotationNode = dynamicTree.AnnotationNode;
import Node = dynamicTree.DynamicTreeNode;
import NodeProxy = require("./NodeProxy");
import {StringUtils} from "@eccosolutions/ecco-common";

interface SupportActionTransient extends SupportAction {
    transientActionInstanceId: string;
}

class OutcomeNode implements NodeProxy {

    private static skipActionGroups: boolean = true;
    private node: Node;
    private childNodesById = new Map<string, NodeProxy>();
    private scoreNode: AnnotationNode;

    constructor(private outcomeDef: domain.Outcome, private context: GraphContext) {
        this.node = new Node(StringUtils.wrapString(outcomeDef.getName(), 15));
        this.node.addClickEventHandler( (event) => { this.outcomeClicked();} );

        /* NOT YET USED at this level, but probably a score
        this.scoreNode = new AnnotationNode("+");
        this.scoreNode.addClickEventHandler( (event) => { this.clickedScoreElement();} );
        this.node.addAnnotation(this.scoreNode); */
    }

    private outcomeClicked() {
        this.context.treeControl.setContextNode(this.node);
    }

    /** Called when the annotation node for the score is clicked */
    private clickedScoreElement() {
        // use showInModalDom("<p>Adding a new goal could be done here</p>");
    }

    public addAction(action: domain.Action, supportAction: SupportAction): void {
        if (OutcomeNode.skipActionGroups) {
            this.getActionNode(action, supportAction);
        }
        else {
            this.getActionGroupNode(action.actionGroup, supportAction)
                .addAction(action, supportAction);
        }

        this.node.setCaption(StringUtils.wrapString(this.outcomeDef.getName(), 15)
            + "\n(" + this.childNodesById.size.toString() + " goals)");
    }

    /** Get ActionGroupNode for supplied def.  Create it if it didn't exist.
     */
    public getActionNode(action: domain.Action, supportAction: SupportAction) {
        let id = supportAction.actionInstanceUuid;
        var node = this.childNodesById.get(id);
        if (!node) {
            node = new ActionNode(action, supportAction, this.context);
            this.node.addChild(node.getNode());
            this.childNodesById.set(id, node);
        }
        return node;
    }

    /** Get ActionGroupNode for supplied def.  Create it if it didn't exist.
     */
    public getActionGroupNode(actionGroup: domain.ActionGroup, supportAction: SupportAction) {
        let id = supportAction.actionInstanceUuid;
        var node = this.childNodesById.get(id);
        if (!node) {
            node = new ActionGroupNode(actionGroup, supportAction, this.context);
            this.node.addChild(node.getNode());
            this.childNodesById.set(id, node);
        }
        return node;
    }

    public withAllLeafNodes( callback: (leaf: ActionNode) => void): void {
        for (const node of this.childNodesById.values()) {
            node.withAllLeafNodes( callback );
        }
    }

    public getNode(): Node {
        return this.node;
    }
}
export = OutcomeNode;