import ActionButton = require("../../controls/ActionButton");
import CommandEmittingForm = require("../../cmd-queue/CommandEmittingForm");
import Form = require("../../controls/Form");
import InputGroup = require("../../controls/InputGroup");
import TextAreaInput = require("../../controls/TextAreaInput");
import * as commands from "ecco-commands";
import {CommandQueue} from "ecco-commands";
import {SupportAction} from "ecco-dto/evidence-dto";
import {getCommandQueueRepository} from "ecco-offline-data";
import {EvidenceDef} from "ecco-evidence";
import {Uuid} from "@eccosolutions/ecco-crypto";


/** Provides ability to add or edit a 'my plan' goal */
class EditGoalForm implements CommandEmittingForm {

    private submitButton: ActionButton;
    private commentInput = new TextAreaInput("comments");
    private form = new Form();
    private commandQueue = new CommandQueue(getCommandQueueRepository());
    private submitCallback: (commandQueue: CommandQueue) => void;


    /** We provide the form with the serviceRecipientId and and ServiceType, and we get back a CommandQueue
     *  containing the changes which have happened.
     * If we provide actionInstanceUuid, then we are editing a previous instance.
     */
    constructor(private serviceRecipientId: number, private evidenceDef: EvidenceDef,
            private actionId: number, private actionInstanceUuid: string, private supportAction: SupportAction) {
        this.submitButton = new ActionButton("save", "saving...")
                .addClass("btn btn-primary")
                .clickSynchronous( () => this.submitForm() )
                .enable();
        this.form.append(new InputGroup(
            "description",
            this.commentInput));
        if (actionInstanceUuid) {
            this.commentInput.setVal(supportAction.goalName);
        }
    }

    public onSubmit(submitCallback: (commandQueue: CommandQueue) => void) {
        this.submitCallback = submitCallback;
        return this;
    }

    private submitForm() {
        const addOrUpdate = this.actionInstanceUuid ? "update" : "add";
        const workUuid = Uuid.randomV4();
        const uuid = Uuid.randomV4();
        const actionInstanceUuid = this.actionInstanceUuid ? Uuid.parse(this.actionInstanceUuid) : Uuid.randomV4();

        const addGoalCmd = new commands.GoalUpdateCommand(addOrUpdate,
                    uuid, workUuid, this.serviceRecipientId, this.evidenceDef.getTaskName(), this.actionId,
                    this.evidenceDef.getEvidenceGroup(), actionInstanceUuid, null)
                .changeGoalName(this.supportAction.goalName, this.commentInput.val());
        this.commandQueue.addCommand(addGoalCmd);
        this.submitCallback && this.submitCallback( this.commandQueue );
    }

    public element() {
        return this.form.element();
    }
    public getFooter() {
        return this.submitButton.element();
    }
    public title() { return this.actionInstanceUuid ? "edit entry" : "add new entry"; }
}

export = EditGoalForm;
