import $ = require("jquery");

import BaseEvidenceControl = require("../BaseEvidenceControl");
import QuestionGroupControl = require("./QuestionGroupControl");
import services = require("ecco-offline-data");
import TabbedContainer = require("../../controls/TabbedContainer");
import {CommandQueue, CommandSource, GoalUpdateCommand} from "ecco-commands";
import {EvidenceContext, EvidenceDef} from "ecco-evidence";
import {QuestionAnswerSnapshotDto, ServiceRecipientWithEntities} from "ecco-dto";
import {EvidenceControl} from "../evidenceControls";
import {QuestionGroup} from "ecco-dto/service-config-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {ResizeEvent} from "@eccosolutions/ecco-common";


/**
 * Visual representation of a container with questionnaires (multiple QuestionGroup's). Mimics OutcomesControl.
 */
class QuestionGroupsControl extends BaseEvidenceControl implements EvidenceControl, CommandSource {

    private tabControl: TabbedContainer = new TabbedContainer();
    protected $container = $("<div>");

    protected context: EvidenceContext;

    private questionGroupControls: QuestionGroupControl[] = [];
    private questionGroupControlsById: { [questionGroupId: number]: QuestionGroupControl } = {};
    private questionGroupsToProcess: QuestionGroup[];
    private latestAnswersToProcess: QuestionAnswerSnapshotDto[] = [];

    /**
     * construct the control to display question groups - which requires information on what to display and save
     */
    public constructor(protected serviceRecipient: ServiceRecipientWithEntities,
                       private getWorkUuid: () => Uuid,
                       protected evidenceDef: EvidenceDef) {
        super();

        this.subscribeToEvents();

        this.populateContainer();

        this.initAfterSnapshot();
    }

    private initAfterSnapshot() {
        this.questionGroupsToProcess = this.serviceRecipient.configResolver.getQuestionGroupsFilteredForTask(this.evidenceDef.getTaskName());
        const questionnaireAsBlank = this.serviceRecipient.configResolver.getServiceType().taskDefinitionSettingHasFlag(this.evidenceDef.getTaskName(),  "questionnaireAsBlank", "y");

        const snapshotsQ = questionnaireAsBlank
            ? Promise.resolve([])
            : services.getQuestionnaireSnapshotRepository().findLatestAnswersByServiceRecipientIdAndEvidenceGroupKey(
                this.serviceRecipient.serviceRecipientId,
                this.evidenceDef.getEvidenceGroup())
                .then(snapshot => snapshot.answers);

        snapshotsQ.then(answers => {
            this.latestAnswersToProcess = answers;
            this.context = new EvidenceContext(this.serviceRecipient.serviceRecipientId, this.serviceRecipient,
                null, this.getWorkUuid, this.evidenceDef, null,
                this.serviceRecipient.features, this.serviceRecipient.configResolver);

            this.initQuestionGroups();
        });
    }

    private initQuestionGroups() {
        this.createAllControls();
        this.renderAll();
    }

    protected createAllControls() {
        const totalQuestionGroups = this.questionGroupsToProcess.length;
        for (let i = 0; i < totalQuestionGroups; i++) {
            this.createQuestionGroupControl(i);
        }
        if (totalQuestionGroups == 0) {
            this.$container.prepend($("<div>").addClass('errortext').css("text-align", "center").text("there are no questions configured for this questionnaire"));
        }
    }
    private createQuestionGroupControl(i: number): QuestionGroupControl {
        const questionGroupDef = this.questionGroupsToProcess[i];
        const control = new QuestionGroupControl(this.context, this.questionGroupsToProcess[i]);
        control.applySnapshots(this.latestAnswersToProcess);
        this.questionGroupControls.push(control);
        this.questionGroupControlsById[questionGroupDef.id] = control;
        return control;
    }
    protected renderAll(): void {
        this.questionGroupControls.forEach(control => {
            this.ensureControlExistsInTab(control);
            control.render();
        });

        ResizeEvent.bus.fire(new ResizeEvent());
    }
    private ensureControlExistsInTab(control: QuestionGroupControl) {
        const questionGroup = control.getQuestionGroup();
        const questionGroupId = questionGroup.id.toString();
        // Make sure the tab has been added for it
        if (!this.tabControl.hasTab(questionGroupId)) {
            this.tabControl.append(questionGroupId, questionGroup.name, control.element());
        }
    }

    // dynamic
    public updateAction(goalUpdateCommand: GoalUpdateCommand) {}

    public updateQuestionAnswer(questionDefId: number, answer: string) {
    }

    /** True if required fields are set */
    public isValid(): boolean {
        // return false early through every
        return this.questionGroupControls.every((qg) => {
            return qg.isValid();
        });
    }

    public emitChangesTo(queue: CommandQueue) {
        this.questionGroupControls.forEach( (control) => control.emitChangesTo(queue) );
    }

    protected populateContainer() {
        this.$container
            .append(this.tabControl.element());
    }

    public element(): $.JQuery {
        return this.$container;
    }

}
export = QuestionGroupsControl;
