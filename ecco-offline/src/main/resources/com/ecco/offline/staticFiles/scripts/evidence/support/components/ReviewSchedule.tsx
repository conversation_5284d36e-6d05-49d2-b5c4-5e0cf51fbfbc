import {EccoDate} from "@eccosolutions/ecco-common";
import {Grid, Typography} from "@eccosolutions/ecco-mui";
import {DatePickerEccoDate} from "@eccosolutions/ecco-mui-controls";
import {CommandQueue, CommandSource, ReferralTaskScheduleReviewsCommand} from "ecco-commands";
import {
    apiClient,
    CommandSubform,
    Loading,
    LoadingSpinner,
    useServiceRecipientWithEntities,
    withCommandForm
} from "ecco-components";
import {button,
    possiblyModalForm} from "ecco-components-core";
import {ReviewChoices, ServiceRecipientWithEntities, SupportWorkAjaxRepository, TaskNames} from "ecco-dto";
import * as React from "react";
import {FormGroup, HelpBlock} from "react-bootstrap";

const supportWorkAjaxRepository = new SupportWorkAjaxRepository(apiClient);


export const ReviewScheduleDialog = (props: {serviceRecipientId: number, taskHandle: string}) => {
    const {context, reload} = useServiceRecipientWithEntities(props.serviceRecipientId)
    return withCommandForm(commandForm =>
            possiblyModalForm(
                    "schedule reviews",
                    true, true,
                    () => commandForm.cancelForm(),
                    () => commandForm.submitForm().then(reload),
                    false,
                    false,
                    <Loading loaded={context.serviceRecipient != null}>
                                <ScheduleReviewsSubform
                                        serviceRecipient={context.serviceRecipient}
                                        taskHandle={props.taskHandle}
                                        commandForm={commandForm}
                                />
                            </Loading>
            )
    );
};

interface Props {
    serviceRecipient: ServiceRecipientWithEntities;
    taskHandle: string;
}

interface State {
    reviewChoices: ReviewChoices;
    nextReviewDate?: EccoDate | undefined;
}

export class ScheduleReviewsSubform extends CommandSubform<Props, State> implements CommandSource {

    private schedule: string = null;

    private initialReviewDate: EccoDate | null;

    private action: "schedule" | "custom" | null = null;
    override state: State = {
        reviewChoices: null
    };

    override componentDidMount() {
        super.componentDidMount();
        this.schedule = this.props.serviceRecipient.configResolver.getServiceType()
            .getTaskDefinitionSetting(TaskNames.scheduleReviews, "reviewSchedule");


        supportWorkAjaxRepository.findReviewChoicesByServiceRecipientId(this.props.serviceRecipient.serviceRecipientId)
            .then(reviewChoices => {
                this.initialReviewDate = reviewChoices.dto ? EccoDate.parseIso8601(reviewChoices.dto.nextReviewDate) : null;
                return this.setState({
                    reviewChoices,
                    nextReviewDate: this.initialReviewDate
                });
            })
            .catch( e => {
                alert("Failed to load review choices information");
                throw e;
            });
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const cmd = new ReferralTaskScheduleReviewsCommand(this.props.serviceRecipient.serviceRecipientId, this.props.taskHandle);

        if (this.action == "schedule") {
            cmd.changeScheduleDates(this.schedule);
        }
        else {
            cmd.changeCustomDate(this.initialReviewDate, this.state.nextReviewDate);
        }

        if(cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    private setScheduleReviewDates() {
        this.action = "schedule";
        this.props.commandForm.submitForm();
    }

    private layoutSetReviewDates() {
        if (!this.schedule) return null;

        const setDatesBtn = button("set review dates", () => this.setScheduleReviewDates(), "primary");

        return <>
            <Grid item xs={12}>
                <Typography>Either:</Typography>
            </Grid>
            <Grid container item xs={12} alignContent="center">
                <Grid sm={2} md={3}/>
                <Grid sm={5} md={4}>
                    <Typography style={{padding: "4px 23px"}}>Service review schedule: {this.schedule}</Typography>
                </Grid>
                <Grid sm={3} md={3} style={{textAlign: "right"}}>{setDatesBtn}</Grid>
            </Grid>
            <Grid item xs={12}>
                Or:
            </Grid>
        </>;
    }

    render() {
        if (!this.state.reviewChoices) {
            return <LoadingSpinner/>;
        }

        const nextReviewDate = <DatePickerEccoDate
            name="custom review date"
            label="custom review date"
            value={this.state.nextReviewDate}
            onChange={nextReviewDate => this.setState({nextReviewDate})}
        />

        const lastReviewDate = EccoDate.parseIso8601(this.state.reviewChoices.dto.lastReviewDate);

        const dateNotValid = this.state.nextReviewDate == null
            || lastReviewDate != null &&
                (lastReviewDate.earlierThanOrEqual(this.state.nextReviewDate)
                    || lastReviewDate.earlierThanOrEqual(EccoDate.todayLocalTime()) );

        let nextReviewDateGroup = <>
            <Grid sm={2} md={3}/>
            <Grid item sm={8} md={7}>
                <FormGroup validationState={dateNotValid ? "error" : "success"}>
                    {nextReviewDate}
                    {dateNotValid
                        ? <HelpBlock>date of next review must be after last review date and after today</HelpBlock>
                        : undefined}
                </FormGroup>
            </Grid>
        </>;

        return (
            <Grid container>
                {this.layoutSetReviewDates()}
                {nextReviewDateGroup}
            </Grid>
        );
    }

}
export default ReviewScheduleDialog;
