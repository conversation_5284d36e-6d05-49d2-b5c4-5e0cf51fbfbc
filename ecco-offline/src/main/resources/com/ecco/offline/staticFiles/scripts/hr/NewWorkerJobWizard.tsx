import * as React from "react"
import {ResizeEvent} from "@eccosolutions/ecco-common";
import {applicationRootPath} from "application-properties";

import {
    apiClient,
    AsyncSessionData,
    CommandForm,
    update,
    UpdateSpec,
    withSessionData
} from "ecco-components";
import {showReactInModal} from "ecco-components-core";
import {
    ProjectDto,
    ServiceDto,
    StaffJobDto,
    WorkersAjaxRepository
} from "ecco-dto";
import {Button, ButtonToolbar, Nav, NavItem} from "react-bootstrap";
import {unmountComponentAtNode} from "react-dom";
import {SessionDataService} from "../feature-config/SessionDataService";
import {
    BaseServiceRecipientWizard,
    FileTypeName,
    SRProps,
    SRState as BaseState,
    Step
} from "../referral/components/BaseServiceRecipientWizard";
import {CommandDtoSentEvent, CommandQueue, CreateWorkerJobCommand} from "ecco-commands";
import {showErrorAsAlert} from "ecco-offline-data";
import {ServiceRecipient} from "ecco-dto/service-recipient-dto";
import {ChooseService} from "../components/ChooseService";

const STATUS_LOADING_FILE = "LOADING_FILE";


interface StaffJobDtoTransient extends StaffJobDto {
    serviceIdTransient: number;
    projectIdTransient: number;
}


interface Props extends SRProps {
    /** Callback when staff is selected */
    onSelected?: ((job: StaffJobDtoTransient) => void) | undefined;
    workerId: number;
    /** Specify this if you want to pre-select which service they are being added to */
    serviceId?: number | undefined;
}

const stepsNonDisplay: Step[] = ["autoStart"];

const compareNames = (a: {name: string}, b: {name: string}) => a.name.localeCompare(b.name);

interface State extends BaseState {
    projects: ProjectDto[];
    job: Partial<StaffJobDtoTransient>;
    /** True if user has permission to open staffs on the service that has been selected */
    staffOpenPermission: boolean;
}


export class NewWorkerJobWizard extends BaseServiceRecipientWizard<Props, State> {

    // see https://stackoverflow.com/questions/37282159/default-property-value-in-react-component-using-typescript
    public static defaultProps: Partial<Props> = {
        subFormsAsModal: true,
        allowInboundServices: false
    };

    public static popup(workerId: number,
                        onSelected: (job: StaffJobDtoTransient) => void,
                        allowInboundServices = false,
                        fileTypeName: FileTypeName = "job",
                        serviceId?: number | undefined
    ) {

        const modalElm = document.createElement("div");
        const onCompleted = () => {unmountComponentAtNode(modalElm)};

        const reactElem = <AsyncSessionData promiseFn={SessionDataService.getFeatures}>
            {withSessionData(sessionData =>
                <NewWorkerJobWizard
                    sessionData={sessionData}
                    subFormsAsModal={true}
                    allowInboundServices={allowInboundServices}
                    workerId={workerId}
                    serviceId={serviceId}
                    fileTypeName={fileTypeName}
                    onSelected={job => {
                        console.debug("job selected: %o", job);
                        onCompleted();
                        window.onbeforeunload = null;
                        onSelected(job);
                    }
                    }/>
            )}
        </AsyncSessionData>;

        showReactInModal(`new ${fileTypeName}`, reactElem,
            {
                onAction: onCompleted,
                action: "cancel",
                mountPoint: modalElm
            });
    }

    private repository = new WorkersAjaxRepository(apiClient);

    constructor(props: Props) {
        super(props);

        const job: Partial<StaffJobDtoTransient> = {};
        job.workerId = props.workerId
        job.serviceRecipient = {} as ServiceRecipient;
        job.serviceIdTransient = props.serviceId;

        // if props.serviceId is set, then we need to do the equiv of handleServiceChange but without call to setState
        const serviceDto = props.serviceId && props.sessionData.getService(props.serviceId);
        if (serviceDto.projects?.length == 0) {
            const svcCat = this.props.sessionData.getServiceCategorisationByIds(props.serviceId, null);
            job.serviceRecipient.serviceAllocationId = svcCat.id;
        }

        const steps = this.buildSteps(serviceDto);
        this.state = {
            projects: (serviceDto && this.props.sessionData.getServiceCategorisationProjects(serviceDto.id, true)) || [],
            job,
            staffOpenPermission: true,
            errors: this.validate(job),
            steps,
            stepSelected: props.serviceId ? this.nextStep(steps, 'service') : 'service'
        };
    }

    public override componentDidMount() {
        window.onbeforeunload = function() {
            return "Your data will be lost";
        };
        // see DEV-747
        ResizeEvent.bus.fire();
    }

    public override componentWillUnmount() {
        window.onbeforeunload = null;
    }

    /**
     * When 'done' - flush work and decide action
     */
    private handleDoneClick = () => {
        // without a serviceRecipientId we haven't saved the referral before, so save
        if (!this.state.job.id) {
            this.saveJobAndLeave();
            // if we have saved before then flush the pending commands
            // details of referral which (at the top level) required a save first so its there for its context loading
        } else {
            const pendingQueue = new CommandQueue();
            //pendingQueue.addQueue(this.workerJobDetailsCommandQueue);
            //pendingQueue.addQueue(this.dataProtectionCommandQueue);
            //pendingQueue.addQueue(this.consentCommandQueue);
            pendingQueue.flushCommands()
                    .catch(showErrorAsAlert)
                    .then(() => this.handleOnSelectedAction(this.state.job as StaffJobDtoTransient));
        }
    };

    private handleOnSelectedAction(job: StaffJobDtoTransient) {
        if (this.props.onSelected) {
            this.props.onSelected(job);
        } else {
            if (!this.state.staffOpenPermission) {
                this.setState({
                    stepSelected: 'close'
                });
            } else {
                // redirect
                window.onbeforeunload = null;
                window.location.href = `${applicationRootPath}nav/r/main/staff/${job.serviceRecipient.serviceRecipientId}/`;
            }
        }
    }

    /**
     * Provide a 'next' button if the page doesn't have a way to progress
     * NB NextButtonElement = null for pages such as agreements which have an 'accept' so don't need 'next'.
     */
    private handleNextClick = () => {

        // first check we are showing referralDetailsComponent, and if so, save its work to the queue before it goes and gets set to null
        /*if (this.referralDetailsComponent) {
            this.referralDetailsCommandQueue.clear();
            this.referralDetailsComponent.emitChangesTo(this.referralDetailsCommandQueue);
        }*/

        this.saveJobMaybe().then(j => {
            this.setState({
                stepSelected: this.nextStep(),
                job: j}
            );
        });

    };

    private handleJobEntityChange<T>(updater: (entity: T) => UpdateSpec<StaffJobDtoTransient>, entity: T) {
        const job = update(this.state.job, updater(entity));
        const errors = this.validate(job);

        this.saveJobMaybe(job).then(j => {
            this.setState({
                job: j,
                errors
            });
        });
    }

    private handleJobEntityNextChange<T>(updater: (entity: T) => UpdateSpec<StaffJobDtoTransient>, entity: T) {
        const job = update(this.state.job, updater(entity));
        const errors = this.validate(job);

        let stepSelected = this.isStepValid(errors) && this.nextStep() || this.state.stepSelected;

        this.saveJobMaybe(job).then(j => {
            this.setState({
                job: j,
                errors,
                stepSelected
            });
        });
    }

    private handleServiceChange = (service: ServiceDto, hasAccessPermission: boolean) => {
        if (service) {
            const projects = this.props.sessionData.getServiceCategorisationProjects(service.id, true) || [];

            let job: Partial<StaffJobDtoTransient>;
            // we can set the svcCat if there are no projects
            if (projects.length == 0) {
                const svcCat = this.props.sessionData.getServiceCategorisationByIds(service.id, null);
                job = update(this.state.job, {serviceIdTransient: {$set: service.id}, projectIdTransient: {$set: null}, serviceRecipient: {serviceAllocationId: {$set: svcCat.id}}});
            } else {
                job = update(this.state.job, {serviceIdTransient: {$set: service.id}, projectIdTransient: {$set: null}, serviceRecipient: {serviceAllocationId: {$set: null}}});
            }
            const steps = this.buildSteps(service);
            const errors = this.validate(job);
            const stepSelected = this.isStepValid(errors) && this.nextStep(steps) || this.state.stepSelected;

            this.setState({
                projects,
                job,
                staffOpenPermission: hasAccessPermission,
                errors,
                steps,
                stepSelected
            });
        }
    };

    /*
    private handleDataProtectionChange = (agreement: SignedAgreement) => {
        const date = agreement.getAccepted() ? moment().format('YYYY-MM-DD') : null;
        const sig = agreement.getAccepted() && agreement.getSignatureData();
        const job = update(this.state.job, {
            // FIXME: dataProtectionAgreementDate: {$set: date},
            // dataProtectionSignatureSvgXml: {$set: sig}
        });
        const errors = this.validate(job);
        const stepSelected = this.isStepValid(errors) && this.nextStep() || this.state.stepSelected;

        this.setState({
            job,
            errors,
            stepSelected
        });

        if (!this.hasNext()) {
            this.saveStaff(job);
        }
    };
    */

    private saveJobMaybe = (jobIn?: Partial<StaffJobDtoTransient> | undefined) => {

        // get next step
        // but if we're in the process of changing service, re-get the steps
        let nextStep = this.nextStep();
        if (!this.state.job.serviceRecipient.serviceAllocationId) {
            const svcId = this.props.sessionData.getServiceCategorisation(jobIn.serviceRecipient.serviceAllocationId).serviceId;
            const serviceDto = this.props.sessionData.getService(svcId); //get serviceId just chosen
            const steps = this.buildSteps(serviceDto);
            nextStep = this.nextStep(steps);
        }

        // if we are something requiring id's to save, then save the referral so far, and update the state to render the form
        const ref = jobIn ? jobIn : this.state.job;

        return (nextStep == 'referralDetails' ||
                nextStep == 'protection' || nextStep == 'consent')
                ? this.saveJobAndStay(ref as StaffJobDto)
                : Promise.resolve(ref);
    }

    private saveJobAndLeave(job?: Partial<StaffJobDtoTransient> | undefined) {
        job = job || this.state.job;

        return this.saveJobToId(job as StaffJobDtoTransient).then((jobId) => {
            this.setState({status: STATUS_LOADING_FILE});
            return this.repository.findOneWorkerJob(jobId).then((job) => {
                this.handleOnSelectedAction({...job,
                        serviceIdTransient: this.props.sessionData.getServiceCategorisation(job.serviceRecipient.serviceAllocationId).serviceId,
                        projectIdTransient: this.props.sessionData.getServiceCategorisation(job.serviceRecipient.serviceAllocationId).projectId
                });
            });
        });
    }

    private saveJobToId(job: StaffJobDto): Promise<number> {

        // Return promise that resolves with the referralId when
        // the command has been sent
        return new Promise<number>((resolve, reject) => {

            const handler = (event: CommandDtoSentEvent) => {
                if (event.command.toCommandDto().commandName == CreateWorkerJobCommand.discriminator) {
                    const jobId = WorkersAjaxRepository.createWorkerJobIdExtractor(event.returnDto);
                    resolve(jobId);
                }
            };

            const q = new CommandQueue();
            q.addCommandDtoSentEventHandler(handler);
            const cmd = new CreateWorkerJobCommand(job);
            q.addCommand(cmd);
            q.flushCommands().catch(reject); // Effectively this promise is resolved via handler calling resolve
        });
    }

    private saveJobAndStay(jobIn: StaffJobDto): Promise<Partial<StaffJobDto>> {
        return jobIn.serviceRecipient
                ? Promise.resolve(jobIn)
                : this.saveJobToId(jobIn).then((jobId) => {
                    return this.repository.findOneWorkerJob(jobId).then((job) => {
                        const setIdUpdater = (jobId: number, serviceRecipientId: number) => ({id: {$set: jobId}, serviceRecipientId: {$set: serviceRecipientId}});
                        return update(jobIn, setIdUpdater(jobId, job.serviceRecipient.serviceRecipientId));
                    })
                });
    }

    /*
    private saveStaff(staff?: Partial<StaffDto>): Promise<void> {
        staff = staff || this.state.staff;

        return this.repository.saveStaff(staff as StaffDto).then(result => {
            this.setState({status: STATUS_LOADING_FILE});
            return this.repository.findOneWorker(parseInt(result.id)).then( staff => {
                this.handleOnSelectedAction(staff);
            });
        });
    }
    */

    // noinspection JSUnusedLocalSymbols,JSMethodCanBeStatic
    private validate(staff: Partial<StaffJobDtoTransient>) {
        const errors: any = {};
        // if (!staff.serviceId) errors['serviceId'] = 'required'; // Currently always -200
        // if (!staff.clientId) errors['clientId'] = 'required';
        // if (sourceType == 'professional') {
        //     if (!staff.referrerAgencyId) errors['referrerAgencyId'] = 'required';
        //     if (!staff.referrerIndividualId) errors['referrerIndividualId'] = 'required';
        // } else if (sourceType == 'individual') {
        //     if (!staff.referrerIndividualId) errors['referrerIndividualId'] = 'required';
        // }
        return errors;
    }

    override render() {
        let StepElement: React.ReactElement;
        let StepHeaderElement: React.ReactElement;
        let NextButtonElement: React.ReactElement;

        // show 'next' or 'done'
        if (this.hasNext()) {
            NextButtonElement = (
                <Button
                    onClick={this.handleNextClick}
                    disabled={!this.isStepValid()}
                    bsStyle="primary">
                    next
                </Button>
            );
        } else {
            NextButtonElement = (
                <Button
                    onClick={this.handleDoneClick}
                    disabled={!this.isStepValid() || this.state.status == STATUS_LOADING_FILE}
                    bsStyle="primary">
                    {this.state.status == STATUS_LOADING_FILE && `opening ${this.props.fileTypeName} file...` || 'save'}
                </Button>
            );
        }

        const dummyCommandForm = new CommandForm({onCancel: () => {}, onFinished: () => {}});
        const serviceType = this.state.job?.serviceIdTransient && this.props.sessionData.getServiceTypeByServiceIdHack(this.state.job?.serviceIdTransient);

        switch (this.state.stepSelected) {
            // case 'autoStart':
            //     this.handleStaffEntityNextChange((autoStart: boolean) => ({callAcceptOnService: {$set: autoStart}}), true);
            //     break;
            case 'service':
                StepHeaderElement = (
                    <h3>service</h3>
                );
                StepElement = (
                    <ChooseService
                        allowInboundServices={this.props.allowInboundServices}
                        onChange={this.handleServiceChange} />
                );
                NextButtonElement = null;
                break;

            /*case 'protection':
                StepHeaderElement = (
                    <h3>data protection</h3>
                );
                const splitAgreementDP = serviceType && serviceType.taskDefinitionSettingHasFlag(TaskNames.dataProtection,  "splitAgreement", "y");
                // NB we can't use signed agreements with custom forms as getAgreement expects an srId which we don't have to save against yet
                StepElement = (
                    <SignedAgreement
                        options={new DataProtectionOptions(this.state.job.serviceRecipient, "AUTO", splitAgreementDP)}
                        onChange={this.handleDataProtectionChange}
                        commandForm={dummyCommandForm}
                        readOnly={false}
                    />
                );
                NextButtonElement = null;
                break;*/

            case 'complete':
                StepHeaderElement = (
                    <h3>data capture completed</h3>
                );
                StepElement = (
                    <div className='row'>
                        <div className='col-xs-12'>
                            <div className='page-header'>
                                <h3>new {this.props.fileTypeName} is ready to be saved</h3>
                            </div>
                        </div>
                    </div>
                );
                break;

            case 'close':
                StepHeaderElement = (
                    <h3>thank you</h3>
                );
                StepElement = (
                    <div className='row'>
                        <div className='col-xs-12'>

                            <div className='page-header'>
                                <p>your request has been saved</p>
                            </div>
                        </div>
                    </div>
                );
                break;
        }

        return (
            <div>
                <div className='row ecco-rounded'>
                    <div className='col-xs-12 text-center'>
                        <Nav
                            className='nav-steps'
                            activeKey={this.state.stepSelected}
                            onSelect={this.handleStepClick}>
                            {this.state.steps.filter(s => stepsNonDisplay.indexOf(s) == -1)
                                .map( (step, index) => (
                                <NavItem
                                    key={index}
                                    eventKey={step}
                                    disabled={!this.isEnabledStep(step)}>
                                    {step}
                                </NavItem>
                            ))}
                        </Nav>
                    </div>
                </div>
                {StepElement}
                <div className='pull-right'>
                    <ButtonToolbar>
                        {NextButtonElement}
                    </ButtonToolbar>
                </div>
            </div>
        );
    }

}
