import $ = require("jquery");
import services = require("ecco-offline-data");
import {applicationRootPath} from "application-properties";
import {
    AuthenticationException,
    getUserSessionManager,
    OfflineSyncStatus,
    OfflineSyncStatusEvent, stringifyPossibleError
} from "ecco-offline-data";
import {FlashScope, FORBIDDEN, LogEvent} from "@eccosolutions/ecco-common";

import {Notifications} from "ecco-components-core";


/** Gather all data and callback when done */
class SyncPageManager {

    private static instance = new SyncPageManager();

    private offlineEnabled = false;
    private pendingCount: number;
    private referralCount: number;
    private userName: string;


    public static getInstance(): SyncPageManager {
        return SyncPageManager.instance;
    }

    constructor() {
        OfflineSyncStatusEvent.bus.addHandler( event => {
            if (event.getStatus() == OfflineSyncStatus.LOGIN_REQUIRED) {
                const successUrl =`${applicationRootPath}offline/`;
                FlashScope.put("success-url", successUrl);
                const logInUri = `${applicationRootPath}nav/secure/login.html`;
                Notifications.add("auth", "Authentication needed", "please login", () => {
                    window.location.href = logInUri.toString();
                });
            }
            else if (event.getProgress() == null) { // leave progress events to progress bar
                Notifications.add("sync-status", event.getMessage());
            }
        });
    }

    public refresh(): Promise<SyncPageManager> {
        if (services.getOfflineRepository() /*&& AppCacheInstaller.installed()*/) {
            this.offlineEnabled = true;
            return this.configureOffline();
        }
        else {
            return Promise.resolve(this);
        }
    }

    public doFullSync(onSynced: () => void) {
        return services.getOfflineRepository().update()
            .then(() => onSynced())
            .catch(reason => {
                // TODO Repository should encapsulate errors
                if (reason.statusCode === FORBIDDEN
                    || reason instanceof AuthenticationException) {
                    OfflineSyncStatusEvent.bus.fire(new OfflineSyncStatusEvent(
                        OfflineSyncStatus.LOGIN_REQUIRED,
                        null,
                        "Login required - please login."));
                    throw reason;
                }

                if (reason) {
                    LogEvent.bus.fire(new LogEvent($("<pre>").text(stringifyPossibleError(reason))));
                }
                throw reason;
            });
    }

    public isOfflineEnabled() { return this.offlineEnabled; }
    public getPendingCount() { return this.pendingCount; }
    public getReferralCount() { return this.referralCount; }
    public getUserName() { return this.userName; }

    private configureOffline(): Promise<SyncPageManager> {
        return services.getOfflineRepository().countReferrals().then( (count) => {
            this.referralCount = count;
            return services.getOfflineRepository().countMessageQueue();
        })
        .then((pendingCount) => {
            this.pendingCount = pendingCount;
            return getUserSessionManager().findCurrentUserSession();
        })
        .then((userSession) => {
            this.userName = userSession.getUserDevice().getUsername();
            return this;
        });
    }
}
export = SyncPageManager;
