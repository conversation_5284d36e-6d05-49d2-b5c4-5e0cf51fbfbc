import {ConfigurablePageComponentRegistry} from "ecco-components";
import {
    AccessWrapper,
    AppointmentsWrapper, AttachmentsWrapper,
    AuditsWrapper,
    CalendarWrapper,
    CommunicationWrapper,
    ContactsWrapper,
    SrFileLandingPageChooser,
    ForwardPlanWrapper,
    ReferralOfflineOverviewLoader,
    RelationshipsWrapper,
    ReportFileWrapper,
    RiskHistoryWrapper,
    ServicesWrapper,
    SupportHistoryWrapper,
    VisitHistoryWrapper, VisitWrapper,
    WorkerJobWrapper,
    EmergencyDetailsWrapper,
    ForwardRiskPlanWrapper,
    UnifiedTimelineWrapper,
    AuditsSearchWrapper,
    UnitWrapper,
    StaffWrapper,
    ResidentWrapper,
    ChecksDueWrapper,
    ChecksHistoryWrapper,
    IncidentsWrapper,
    RepairsWrapper,
    ManagedVoidsWrapper,
    OccupancyWrapper
} from "./router";
import {HousingManagementPage} from "../housing/HousingManagementPage";
import {Workflow} from "../workflow/WorkflowPanels";
import {TaskTimelineWrapper} from "../tasks/components/TaskTimeline";
import {FinancePage} from "../housing/FinancePage";

export const defaultPageComponentRegistry = new ConfigurablePageComponentRegistry({
    "default": SrFileLandingPageChooser,
    "info": ReferralOfflineOverviewLoader,
    "hms": HousingManagementPage,
    "finance": FinancePage,
    "tasks": Workflow,
    "calendar": CalendarWrapper,
    "contacts": ContactsWrapper,
    "relationships": RelationshipsWrapper,
    "attachments": AttachmentsWrapper,
    "visit": VisitWrapper,

    "timeline": UnifiedTimelineWrapper,
    "audits": AuditsWrapper,
    "search": AuditsSearchWrapper,
    "visit-history": VisitHistoryWrapper,
    "support-history": SupportHistoryWrapper,
    "risk-history": RiskHistoryWrapper,
    "forward-plan": ForwardPlanWrapper,
    "risk-forward-plan": ForwardRiskPlanWrapper,
    "appointments": AppointmentsWrapper,

    "services": ServicesWrapper,
    "incidents": IncidentsWrapper,
    "workerJobs": WorkerJobWrapper,
    "reports": ReportFileWrapper,
    "communication": CommunicationWrapper,
    "taskTimeline": TaskTimelineWrapper,
    "emergencyDetails": EmergencyDetailsWrapper,

    // hr
    "access": AccessWrapper,

    // buildings
    "units": UnitWrapper,
    "staff": StaffWrapper,
    "residents": ResidentWrapper,
    "repairs": RepairsWrapper,
    "occupancy": OccupancyWrapper,
    "managedvoids": ManagedVoidsWrapper,
    "checksDue": ChecksDueWrapper,
    "checksHistory": ChecksHistoryWrapper,
});