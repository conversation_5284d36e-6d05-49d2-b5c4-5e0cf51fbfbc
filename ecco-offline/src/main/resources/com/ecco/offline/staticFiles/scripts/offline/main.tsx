import * as React from "react";
import * as ReactDOM from "react-dom";
import {RouteManagedPage} from "../components/RouteManagedPage";
import {App} from "./router";

/* To use for nav/r/main/ to mount this */
/* see scripts/offline/router.tsx for App, which also explains wiring */

// opens the page where EvidencePageLayout is shown, using the theme in RouteManagedPage
ReactDOM.render(
    <RouteManagedPage subPath="/nav/r/main">
        <App />
    </RouteManagedPage>,
    document.getElementById("appbar"));
