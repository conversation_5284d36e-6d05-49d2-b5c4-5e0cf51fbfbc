import $ = require("jquery");
import {WebApiError} from "@eccosolutions/ecco-common";
import {TaskCommandAjaxRepository} from "ecco-dto"
import {apiClient} from "ecco-components";

const repository = new TaskCommandAjaxRepository(apiClient);

/**
 * Renders a form to do task admin for the provided task
 */
class TaskAdminControl {


    private $container: $.JQuery;

    constructor(private taskHandle: string, private taskState: string, private refreshUrl,
        private onSuccess: () => void) {
    }

    public attach($container: $.JQuery) {
        this.$container = $container;

        $container
            .append($("<div>").text("Task state is: " + this.taskState))
            .append($("<button>").attr("type","submit").attr("title","mark completed")
                .append("mark task as completed&nbsp;")
                .append($("<i>").attr("class", "fa fa-check"))
                .click( (event) => { this.completeTask() }));
    }


    public completeTask(): void {
        var commandSentPromise: Promise<void>;

        commandSentPromise = repository.postMarkCompleted(this.taskHandle);

        commandSentPromise
            .then( () => {
                this.onSuccess();
                this.triggerAjaxRefresh();
            })
            .catch( (error: WebApiError) => {
                alert(error.reason.message);
            });
    }

    private triggerAjaxRefresh() {
        if (this.refreshUrl){
            $.post(this.refreshUrl);
        }
    }

}

export = TaskAdminControl
