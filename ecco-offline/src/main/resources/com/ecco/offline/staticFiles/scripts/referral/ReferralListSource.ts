import {Observable} from "rxjs";
import {CardSource} from "ecco-components";
import {ReferralCard, ReferralCardGroup} from "./cards";
import {flatMap, map} from "rxjs/operators";
import services = require("ecco-offline-data");


class ReferralListSource implements CardSource {

    getCards() {
        const referralsQ = services.getReferralRepository().findAllReferralsForOffline();

        const todaysEvents = Observable.fromPromise(referralsQ).pipe(

            flatMap(a => a),
            map(dto => new ReferralCard(dto)));

        return Observable.from([new ReferralCardGroup("referrals", todaysEvents)]);
    }
}
export = ReferralListSource;
