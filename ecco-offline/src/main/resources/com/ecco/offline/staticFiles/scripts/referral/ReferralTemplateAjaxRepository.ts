import $ = require("jquery");
import URI = require("URI");

import * as applicationProperties from "application-properties";
import {ReferralTemplateRepository} from "./ReferralTemplateRepository"


export class ReferralTemplateAjaxRepository implements ReferralTemplateRepository {

    public findOneTemplate(name: string): Promise<string> {
        var uri = URI(applicationProperties.applicationRootPath)
            .segmentCoded('online')
            .segmentCoded('templates')
            .segmentCoded(name)
            .segmentCoded('rendered')
            .segmentCoded('');

        return <Promise<string>><any> Promise.resolve($.ajax({
            url: uri.toString(),
            dataType: 'html'
        }));
    }
}
