import * as React from "react"
import {EccoDate, IdNameDisabled} from "@eccosolutions/ecco-common";
import {Grid} from "@eccosolutions/ecco-mui";
import {CommandQueue, CommandSource, ReferralTaskDeliveredByCommand} from "ecco-commands";
import {
    apiClient,
    CommandSubform,
    LoadingOrError,
    useCurrentServiceRecipientWithEntities,
    useReferralBySrId,
    withCommandForm
} from "ecco-components";
import {
    datePickerInput,
    dropdownList,
    possiblyModalForm
} from "ecco-components-core";
import {ContactsAjaxRepository, ReferralDto} from "ecco-dto";
import {Agency} from "ecco-dto/contact-dto";

const repository = new ContactsAjaxRepository(apiClient);

const DeliveredByDialog = ({serviceRecipientId, task}) => {
    const {referral, error, reload: reloadReferral} = useReferralBySrId(serviceRecipientId);
    const {reload} = useCurrentServiceRecipientWithEntities()
    if (!referral) return <LoadingOrError error={error}/>;
    return withCommandForm(commandForm =>
            possiblyModalForm(
                    "delivered by",
                    true, true,
                    () => commandForm.cancelForm(),
                    () => commandForm.submitForm().then(reloadReferral).then(reload),
                    // TODO: Doesn't work dynamically as it needs to be a proper component - so in fact its pointless, and even the subform isn't registered yet to trigger getErrors
                    commandForm.getErrors().length > 0, // TODO could emitChangesTo and see if there are any commands
                    false,
                    <DeliveredBySubform
                            referral={referral}
                            taskHandle={task.taskHandle}
                            commandForm={commandForm}
                    />
            )
    );
}

interface Props {
    referral: ReferralDto;
    taskHandle: string;
}

interface State {
    agencies?: IdNameDisabled[] | undefined,
    deliveredByContactId: number,
    deliveredByDate: EccoDate
}

class DeliveredBySubform extends CommandSubform<Props, State> implements CommandSource {


    constructor(props) {
        super(props);

        let r = this.props.referral;

        this.state = {
            agencies: null,
            deliveredByContactId: r.deliveredById,
            deliveredByDate: EccoDate.parseIso8601(r.deliveredByStartDate)
        };
    }

    override componentDidMount(): void {
        repository.findAllAgencies(this.props.referral.serviceRecipientId).then((agencies:Agency[]) => {
            const idNames = agencies.map(agency => ({
                id: agency.contactId,
                name: agency.companyName,
                disabled: false
            }));
            this.setState({agencies: idNames});
        });
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const cmd = new ReferralTaskDeliveredByCommand(this.props.referral.serviceRecipientId, this.props.taskHandle)
            .changeDeliveredByDate(EccoDate.parseIso8601(this.props.referral.deliveredByStartDate), this.state.deliveredByDate)
            .changeDeliveredByContactId(this.props.referral.deliveredById, this.state.deliveredByContactId);

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }


    render() {
        const setter = state => this.setState(state);
        return <Grid container className="text-center">
            <Grid item xs={12}>
                {dropdownList("delivered by", setter, this.state, "deliveredByContactId", this.state.agencies)}
            </Grid>
            <Grid item xs={12}>
                {datePickerInput("deliveredByDate", "delivery start date", setter, this.state)}
            </Grid>
        </Grid>;
    }
}
export default DeliveredByDialog;
