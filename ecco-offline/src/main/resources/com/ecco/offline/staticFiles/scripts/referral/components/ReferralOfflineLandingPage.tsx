import * as React from "react";
import {FC, ReactNode} from "react";
import {Accordion, AccordionDetails, AccordionSummary, Box, Icon, Tab, Tabs} from "@eccosolutions/ecco-mui";
import {ExpandMore} from "@eccosolutions/ecco-mui-controls"
import {
    Building, Contact, isAgency, isIndividual,
    isOffline,
    ReferralDto,
    SessionData, StaffDto, StaffJobDto
} from "ecco-dto";
import {Client} from "ecco-dto/client-dto";
import {
    handleLazy,
    LoadingOrError,
    ServiceRecipientContext,
    useCurrentServiceRecipientWithEntities
} from "ecco-components";
import {WithWidth} from '@material-ui/core/withWidth';
import {SupportHistory} from "../../evidence/support/components/SupportHistory";

const ServiceRecipientTasks = React.lazy(() => import("../../service-recipients/ServiceRecipientTasks"));
const TasksControl = React.lazy(() => import("../../workflow/tasklist/TasksControl"));
const AuditHistory = React.lazy(() => import("../../service-recipients/components/AuditHistory").then(i => ({default: i.AuditHistory})));


// NOTE: Params are always string so need parsing to number where needed
interface Props extends Partial<WithWidth> {
    srId: number;
    supportTabTaskName?: string | undefined;
}

interface OfflineServiceRecipientWithEntitiesContext extends ServiceRecipientContext {
    referral?: ReferralDto | undefined; // DIFFERENT TO ONLINE, ReferralDto offers more data that we use offline
    features?: SessionData | undefined;
    client?: Client | null | undefined;
    building?: Building | null | undefined;
    workerJob?: StaffJobDto | null | undefined;
    worker?: StaffDto | null | undefined;
}

interface InnerProps extends Props {
    srContext?: OfflineServiceRecipientWithEntitiesContext | undefined
}

interface State {
    loaded: boolean;
    tab: string;
    tabOverviewExpandedSection: string | null;
}

// TODO Extract to a util package
function dlEntry(term: string, def: string | JSX.Element) {
    return (<dl className="dl-horizontal"><dt>{term}</dt><dd>{def}</dd></dl>);
}
function dlEntryOrOmit(term: string, def: string) {
    return def && dlEntry(term, def);
}

function dlNumEntry(term: string, def: number) {
    return dlEntryOrOmit(term, def ? def.toString() : "-");
}
function dlNumEntryOrOmit(term: string, def: number) {
    return def && dlNumEntry(term, def);
}
// This is answer to http://stackoverflow.com/questions/38850661/outputting-plain-text-with-br-tags-in-jsx
// using span allows dd to work with dl-horizontal in Bootstrap - https://developer.mozilla.org/en/docs/Web/CSS/white-space
function dlEntryWithLines(term: string, def?: string | undefined) {
    return (<dl className="dl-horizontal"><dt>{term}</dt><dd><span style={{whiteSpace:"pre"}}>{def}</span></dd></dl>);
}

const TAB_STATE_KEY = 'referral.tab';

/**
 * The "offline referral" with overview, tasks, support, history & audit history
 * NB This file was not part of tab renames in DEV-2266
 */
class _ReferralOfflineLandingPage extends React.Component<InnerProps, State> {

    public static defaultProps: Partial<Props> = {
        supportTabTaskName: "needsReduction"
    };

    constructor(props) {
        super(props);
        this.state = {
            loaded: false,
            tab: localStorage[TAB_STATE_KEY] || "overview",
            tabOverviewExpandedSection: null
        };
    }

    private tabChange(value: string) {
        localStorage[TAB_STATE_KEY] = value;
        this.setState({tab: value});
    };

    override render() {
        const {tab} = this.state;
        const showText = this.props.width != 'xs' && this.props.width != 'sm';

        const NotAvailableOfflineMessage = <span>not currently available offline</span>;
        const {srContext: context} = this.props

        // in combination with Sr2AppBar <Box m={2}>
        return (<Box mx={-1}>
            <Tabs value={tab}
                  onChange={(event, value) => this.tabChange(value)}>
                <Tab label={showText ? "overview" : undefined}
                     value="overview"
                     icon={showText ? undefined : <Icon className="fa fa-user"/>}
                     onClick={() => this.tabChange("overview")}
                />
                <Tab label={showText && "tasks"}
                     value="tasks"
                     icon={showText ? undefined : <Icon className="fa fa-check-square-o"/>}
                     onClick={() => this.tabChange("tasks")}
                />
                <Tab label={showText && "support"}
                     value="support"
                     icon={showText ? undefined : <Icon className="fa fa-edit"/>}
                     onClick={() => this.tabChange("support")}
                />
                <Tab label={showText && "support history"}
                     value="support_history"
                     icon={showText ? undefined : <Icon className="fa fa-history"/>}
                     onClick={() => this.tabChange("support_history")}
                />
                {context.serviceRecipient.features.hasRoleAdmin() && <Tab label={showText && "audit history"}
                                                                  value="audit_history"
                                                                  icon={showText ? undefined :
                                                                          <Icon className="fa fa-history"/>}
                                                                  onClick={() => this.tabChange("audit_history")}
                />}
            </Tabs>
            {tab == 'overview' && <ReferralOfflineOverview referral={context.referral} client={context.client}
                                                           sessionData={context.features}
                                                           expanded={this.state.tabOverviewExpandedSection}
                                                           expand={(section) => this.expand(section)}
                                                           renderContacts={true}/>}
            {tab == 'tasks' && handleLazy(<TasksControl srId={context.referral.serviceRecipientId}/>)}
            {tab == 'support' && handleLazy(<Box mx={-2}>
                <ServiceRecipientTasks srId={context.referral.serviceRecipientId} taskName={this.props.supportTabTaskName}/>
            </Box>)}
            {tab == 'support_history' && handleLazy(<Box>
                <SupportHistory srId={context.referral.serviceRecipientId} taskName={context.serviceType.getFirstSupportTaskName(context.features)}/>
            </Box>)}
            {tab == 'audit_history' &&
                    (isOffline() ? NotAvailableOfflineMessage
                            : handleLazy(<Box mx={-2}>
                                <AuditHistory serviceRecipientId={context.serviceRecipient.serviceRecipientId} sessionData={context.features}/>
                            </Box>))}
        </Box>)
    }

    private expand(section: string) {
        this.setState({tabOverviewExpandedSection: this.state.tabOverviewExpandedSection != section ? section : null})
    }

    private static renderContact(contact: Contact) {
        return <dl className="dl-horizontal top-gap-15">
            {isAgency(contact) && <>
                {dlEntry("title", contact.companyName)}
                {dlEntry("phone", contact.phoneNumber)}
            </>
            }
            {isIndividual(contact) && <>
                {dlEntry("title", contact.title)}
                {dlEntry("first name", contact.firstName)}
                {dlEntry("last name", contact.lastName)}
                {dlEntry("job title", contact.jobTitle)}
                {dlEntry("phone", contact.phoneNumber)}
                {dlEntry("mobile", contact.mobileNumber)}
                </>
            }
        </dl>
    }

    public static renderContacts(r: ReferralDto, expanded: string, expand: (string) => void) {
        const CONTACTS = "Contacts";
        return <ExpandedSection title={CONTACTS} content={
            (!r.contacts || r.contacts.length === 0)
                    ? <p className='text-center'>no contacts</p>
                    : r.contacts.map(contact => this.renderContact(contact))
            } expanded={expanded} expand={expand}/>
    }

}

/** The "offline referral" with overview, tasks, support, history & audit history */
export const ReferralOfflineLandingPage: FC<Props> = props => {
    // NB we've wrapped the offline client file in LoadSRWithEntitiesContext because TaskSummary (and TaskControlNG)
    // calls useServiceRecipientWithEntities, as most methods do now - or useCurrentServiceRecipientWithEntities.
    // LoadSRWithEntitiesContext calls loadSrWithEntities, which calls 'findOneServiceRecipientWithEntities'
    // and 'findOneReferralSummaryWithEntitiesUsingDto' and 'findOneClient':
    //      findOneServiceRecipientWithEntities - offline returns as a ReferralDto with service/features/configResolver
    //      findOneReferralSummaryWithEntitiesUsingDto - offline returns the same as above
    //      findOneClient - offline return the client
    // NB reload wasn't used here, and is already handled by using the right components/hooks
    const {resolved: context} = useCurrentServiceRecipientWithEntities();
    if (!context) return <LoadingOrError/>

    // CAST the context for offline - because it uses ReferralDto not ReferralSummaryDto for .contacts
    // the cast is showing an error of missing interviewer1WorkerDisplayName and calendarEvents
    const overrideContext = {...context, referral: context.referral as any as ReferralDto, features: context.referral.features};
    return  <_ReferralOfflineLandingPage {...props} srContext={overrideContext}/>
}
// as any as React.FunctionComponent;


const ExpandedSection = (props: {title: string, content: ReactNode, expanded: string, expand: (title: string) => void}) => {
    const {title, content, expanded, expand} = props;
    return <Accordion expanded={expanded == title}>
        <AccordionSummary onClick={() => expand(title)} expandIcon={<ExpandMore/>}>
            {title}
        </AccordionSummary>
        <AccordionDetails>
            <div>
                {/*<div> is to avoid flex of AccordionDetails*/}
                {content}
            </div>
        </AccordionDetails>
    </Accordion>;
}

export const ReferralOfflineOverview = (props: {referral: ReferralDto, client: Client, sessionData: SessionData, expanded: string, expand: (title: string) => void,  renderContacts: boolean}) => {
    const {expanded, expand} = props;
    const c: Client = props.client;
    const r = props.referral;
    const sd: SessionData = props.sessionData;
    if (!r || !c) {
        return null;
    }

    const CLIENT_DTLS = "Client Details";
    const EMERG_DTLS = "Emergency Details";
    const RFRL_DTLS = "Referral Details";
    return <>
        <ExpandedSection title={CLIENT_DTLS} content={<>
                    {dlEntryOrOmit("contact", c.preferredContactInfo)}
                    {dlEntryOrOmit("gender", c.genderId ? sd.getListDefinitionEntryById(c.genderId).getDisplayName() : null)}
                    {/*{birthDate && <DatePicker floatingLabelText="birth date" defaultDate={birthDate} disabled={true} underlineShow={false} className="mtrl-ui-disabled"/>}*/}
                    {dlEntryOrOmit("date of birth", c.birthDate)}
                    {dlNumEntryOrOmit("age", c.age)}
                    {dlEntryOrOmit("residing at", c.residenceName)}
                    {dlEntry("first language", c.firstLanguageId ? sd.getListDefinitionEntryById(c.firstLanguageId).getDisplayName() : null)}
                    {dlEntryOrOmit("disability", c.disabilityId ? sd.getListDefinitionEntryById(c.disabilityId).getDisplayName() : null)}
                    {dlEntry("ethnic origin", c.ethnicOriginId ? sd.getListDefinitionEntryById(c.ethnicOriginId).getDisplayName() : null)}
                    {dlEntry("religion", c.religionId ? sd.getListDefinitionEntryById(c.religionId).getDisplayName() : null)}
                    {dlEntryOrOmit("sexual orientation", c.sexualOrientationId ? sd.getListDefinitionEntryById(c.sexualOrientationId).getDisplayName() : null)}
                    {dlNumEntry("client id", c.clientId)}
                </>} expanded={expanded} expand={expand}/>
        <ExpandedSection title={EMERG_DTLS} content={<>
                    {dlEntryWithLines("description", c.description)}
                    {dlEntryWithLines("communication needs", c.communicationNeeds)}
                    {dlEntry("key word", c.emergencyKeyWord)}
                    {dlEntryWithLines("emergency details", c.emergencyDetails)}
                    {dlEntryWithLines("medication details", c.medicationDetails)}
                    {dlEntryWithLines("doctor", c.doctorDetails)}
                    {dlEntryWithLines("dentist", c.dentistDetails)}
                </>} expanded={expanded} expand={expand} />
        <ExpandedSection title={RFRL_DTLS} content={<>
                    {dlEntry("referral source", r.source)}
                    {dlEntry("referral received", r.receivedDate)}
                    {dlEntry("start date", r.receivingServiceDate)}
                    {dlNumEntry("referral id", r.referralId)}
                </>}
                expanded={expanded} expand={expand}/>
        {props.renderContacts && _ReferralOfflineLandingPage.renderContacts(r, expanded, expand)}
    </>;
}
