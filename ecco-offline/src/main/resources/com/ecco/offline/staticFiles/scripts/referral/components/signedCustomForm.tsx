import * as React from "react"
import {ReactElement} from "react"
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {CommandQueue, FormEvidenceWork, Signature, SignWorkCommand, WorkUuidResolver} from "ecco-commands";
import {
    AsyncServiceRecipientWithEntities,
    CommandForm,
    CommandSubform,
    EditTaskSubFormEmbedded,
    ServiceRecipientWithEntitiesContext,
    SignatureCapture,
    withCommandForm,
    withSessionData
} from "ecco-components";
import {DomElementContainer,
    possiblyModalForm} from "ecco-components-core";
import {EvidenceGroup, ServiceRecipientWithEntities, SessionData} from "ecco-dto";
import {FormEvidence} from "ecco-dto/evidence-dto";
import {showInCommandForm} from "../../components/CommandForm";

import ViewSignatureControl from "../../controls/ViewSignatureControl";
import CustomFormHistoryItemControl from "../../evidence/customform/CustomFormHistoryItemControl";
import {CustomSubform} from "./CustomForm";
import services = require("ecco-offline-data");

export class CustomFormWithSignatureForm {

    /* // NB Doesn't load the right context - need WithEntities
    public static showInModal(serviceRecipientId: number, taskName: string, taskHandle: string, onCompleted?: () => void) {
        showInCommandForm(
                {withSessionData(sessionData =>
                    <ServiceRecipientLoadAndRender srId={serviceRecipientId}>{workflow =>
                        getCustomFormWithSignatureForm(sessionData, serviceRecipientId, workflow.serviceRecipient.serviceId, taskName, taskHandle, () => {})
                    }</ServiceRecipientLoadAndRender>
                )
            , onCompleted);
    }
    */

    /**
     * Enhance the control on the page.
     */
    public static enhanceForPrinting($element: $.JQuery, onCompleted: () => void, srId: number, taskName: string, taskNameGroup: string, taskHandle: string,
                          snapshotWorkUuid: string, fromLastSignedSnapshot: boolean, forceReadonly = false) {
        const readOnly = forceReadonly || !!snapshotWorkUuid;
        showInCommandForm(
            withSessionData(sessionData =>
                <AsyncServiceRecipientWithEntities srId={srId}>
                    <AsyncServiceRecipientWithEntities.Resolved>
                        {(context: ServiceRecipientWithEntitiesContext) =>
                            getCustomFormWithSignatureForm(sessionData, context.serviceRecipient, taskName, taskNameGroup, taskHandle,
                                snapshotWorkUuid, fromLastSignedSnapshot, false, readOnly, readOnly, !readOnly, !readOnly)
                        }
                    </AsyncServiceRecipientWithEntities.Resolved>
                </AsyncServiceRecipientWithEntities>
            )
            , onCompleted
        , $element[0]);
    }

}

export function getCustomFormWithSignatureForm(sessionData: SessionData, serviceRecipient: ServiceRecipientWithEntities,
                                               taskName: string,
                                               taskNameGroup: string,
                                               taskHandle: string | undefined,
                                               snapshotWorkUuid: string | undefined,
                                               fromLastSignedSnapshot: boolean,
                                               modal = true, disableSave = false, readOnly = false,
                                               historyLink = true,
                                               printableLink = true,
                                               helpCallback?: (() => void) | undefined) {
    return withCommandForm(commandForm => {
        const cancel = modal ? () => commandForm.cancelForm() : null!; // FIXME: :null shouldn't be used for cancel
        const save = modal ? () => commandForm.submitForm() : null!; // FIXME: Ensure there is no save button!
        return possiblyModalForm(
            "",
            modal, true,
            cancel,
            save,
            disableSave,
            readOnly,
            <React.Fragment>
                <CustomFormWithSignatureSubform
                    serviceRecipientWithEntities={serviceRecipient}
                    serviceRecipientId={serviceRecipient.serviceRecipientId}
                    readOnly={readOnly}
                    historyLink={historyLink}
                    printableLink={printableLink}
                    taskName={taskName}
                    taskNameGroup={taskNameGroup}
                    taskHandle={taskHandle}
                    snapshotWorkUuid={snapshotWorkUuid}
                    fromLastSignedSnapshot={fromLastSignedSnapshot}
                    commandForm={commandForm}
                    onChange={() => commandForm.submitForm()}
                >
                    {/*<EditTaskSubFormEmbedded
                        taskHandle={taskHandle}
                    />*/}
                </CustomFormWithSignatureSubform>
            </React.Fragment>,
            undefined,
            helpCallback
        );
    });
}

interface Props extends React.ClassAttributes<CustomFormWithSignatureSubform> {
    serviceRecipientId: number;
    serviceRecipientWithEntities: ServiceRecipientWithEntities;
    readOnly: boolean;
    taskName: string;
    taskNameGroup: string;
    taskHandle: string | undefined;
    snapshotWorkUuid: string | undefined;
    fromLastSignedSnapshot: boolean;
    historyLink: boolean;
    printableLink: boolean;
    commandForm: CommandForm,
    onChange?: ((form: CustomFormWithSignatureSubform) => void) | undefined;
}

interface State {
    workUuidResolver: WorkUuidResolver;
    currentSignatureData: string | null;
    previousSignatureId: string | null;
    evidenceGroup: EvidenceGroup;
    previousWorkDate: EccoDateTime | null;
    previousWorkUuid?: string | undefined;
    form?: FormEvidence<any> | undefined;
    notes?: FormEvidence<any>[] | undefined;
    clear?: boolean | undefined;
}

class CustomFormWithSignatureSubform extends CommandSubform<Props, State> {

    private formRef = React.createRef<CustomSubform>();

    constructor(props) {
        super(props);

        this.state = {
            workUuidResolver: new WorkUuidResolver(),
            previousSignatureId: null,
            previousWorkDate: null,
            currentSignatureData: null,
            // custom forms group is the taskName
            evidenceGroup: EvidenceGroup.fromSameTaskName(this.props.taskName),
        };
    }

    override componentDidMount() {
        super.componentDidMount();

        // this call is repeated in CustomForm, but not worth consolidating for the simplicity of the code
        const workQ = this.props.snapshotWorkUuid
            ? services.getFormEvidenceRepository().findOneFormEvidenceWorkByServiceRecipientId(this.props.serviceRecipientId,
                this.state.evidenceGroup, this.props.snapshotWorkUuid, this.props.fromLastSignedSnapshot)
            : services.getFormEvidenceRepository().findLatestFormEvidenceSnapshotByServiceRecipientId(this.props.serviceRecipientId,
                this.state.evidenceGroup);

        workQ.then( formData => {

            if (formData) {
                // load notes between the snapshot and the last signed entry
                const notes = (this.props.fromLastSignedSnapshot && (this.props.snapshotWorkUuid != formData.id))
                    ? this.getNotesBetweenWork(formData, this.props.snapshotWorkUuid)
                    : Promise.resolve([] as FormEvidence<any>[]);

                notes.then(n => {
                    this.setState({
                        previousSignatureId: formData.signatureId,
                        previousWorkDate: formData.workDate && EccoDateTime.parseIso8601(formData.workDate),
                        previousWorkUuid: formData.id,
                        form: formData,
                        notes: n
                    });
                });
            }
        });
    }

    private getNotesBetweenWork(fromSignedWork: FormEvidence<any>, toSnapshotWorkUuid: string): Promise<FormEvidence<any>[]> {

        // get the created of snapshotWorkUuid
        return services.getFormEvidenceRepository().findOneFormEvidenceWorkByServiceRecipientId(this.props.serviceRecipientId,
                this.state.evidenceGroup, toSnapshotWorkUuid)
            .then( toSnapshotForm => {
                const fromCreatedDateSigned = EccoDateTime.parseIso8601(fromSignedWork.createdDate);
                const toCreatedDateSelected = EccoDateTime.parseIso8601(toSnapshotForm.createdDate);
                return services.getFormEvidenceRepository().findAllBetweenFormEvidenceByServiceRecipientId(this.props.serviceRecipientId,
                    this.state.evidenceGroup, fromCreatedDateSigned, toCreatedDateSelected)
                    .then(notes => {
                        // return everything but the displayed work item - which is the same as the one we are showing in 'printable'
                        // we could exclude on the server side by sorting dates, but easier and more guaranteed here
                        // since dates could be date+time or just date, so difficult to know how much to take off
                        return notes.filter(n => n.id != fromSignedWork.id).reverse();
                    })
            });
    }

    /**
     * Make the user aware that we shouldn't be signing a form twice without a change
     * because this outcome is unknown.
     * Inside getError means we can't call a promise, which means we can't inspect the commandQueue.
     * So, we go directly to the form to check for any changes.
     * Alternatives are to create a wrapper component in charge of all state.
     */
    getErrors(): string[] {
        const hasChanges = this.hasFormChanges();
        // if there is a previous sig, and a new one, but we have no form changes (inc. note added)
        if (this.state.previousSignatureId && this.state.currentSignatureData && !hasChanges) {
            return ["signing requires a change in the form"]
        }
        return [];
    }

    hasFormChanges() {
        return this.formRef.current?.hasFormChanges() || false;
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const sigData = this.state.currentSignatureData;
        const newSignature = sigData != null;

        // we can sign the form (without other changes) if it hasn't been signed already for what is being shown
        const workUuid = this.hasFormChanges()
                ? this.state.workUuidResolver.getWorkUuid().toString()
                : this.state.previousWorkUuid;

        if (newSignature) {
            const cmd = SignWorkCommand.create(this.props.serviceRecipientId,
                this.state.evidenceGroup, this.props.taskName, [Uuid.parse(workUuid)],
                Signature.create(sigData, EccoDateTime.nowLocalTime()));
            commandQueue.addCommand(cmd);
        }
    }

    updateSignatureData(sigData: string) {
        this.setState({
            currentSignatureData: sigData
        })
    }

    render() {
        const CustomForm = <CustomSubform
                        serviceRecipientId={this.props.serviceRecipientId}
                        workUuidResolver={this.state.workUuidResolver}
                        snapshotWorkUuid={this.props.snapshotWorkUuid} /*this.state.form && this.state.form.id}*/
                        readOnly={this.props.readOnly}
                        historyLink={this.props.historyLink}
                        printableLink={this.props.printableLink}
                        taskName={this.props.taskName}
                        taskNameGroup={this.props.taskNameGroup}
                        taskHandle={this.props.taskHandle}
                        commandForm={this.props.commandForm}
                        clearCallback={() => this.setState({clear: true})}
                        ref={this.formRef}
        />;

        // work items between the two dates, the signed form and the selected snapshot from history
        const mountPoint = document.createElement("ul");
        mountPoint.setAttribute("class", "entry-list list-unstyled");
        this.state.notes && this.state.notes.reduce((elm, curr) => {
            const ctl = new CustomFormHistoryItemControl(this.props.serviceRecipientWithEntities, FormEvidenceWork.fromDto(curr));
            return elm.appendChild(ctl.domElement());
        }, mountPoint);

        const FormNotes = this.state.notes && <DomElementContainer content={mountPoint}/>;

        const SignatureAsCapture = this.props.readOnly ? null :
            <div style={{paddingTop: "25px", paddingBottom: "75px"}} className="text-center">
                <SignatureCapture callback={(sigData) => this.updateSignatureData(sigData)}/>
            </div>;

        let SignatureAsView: ReactElement = null;
        if (!this.state.clear && this.state.previousSignatureId) {
            const ctl = ViewSignatureControl.showSignatureInDomElement(this.state.previousSignatureId);
            SignatureAsView = <DomElementContainer content={ctl.domElement()}/>;
        }

        const WorkDate = (this.props.readOnly || this.state.previousSignatureId) && <div>
            work date: {this.state.previousWorkDate ? this.state.previousWorkDate.formatDateTimePretty(true) : ""}
        </div>;

        return (<div className='row'>
                <div className='col-xs-10 col-xs-offset-1'>
                    {WorkDate}
                    {CustomForm}
                    {SignatureAsCapture}
                    {SignatureAsView}
                    {FormNotes}
                </div>
            </div>
        );
    }
}
