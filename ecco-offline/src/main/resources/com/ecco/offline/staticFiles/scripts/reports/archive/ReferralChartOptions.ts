import $ = require("jquery")

import NameValue = require("../../common/NameValue");
import SelectList = require("../../controls/SelectList");

/**
 * Holds the report criteria for the referal graph
 */
class ReferralChartOptions {

    private byCriteria: SelectList;

    constructor(private onChange: (source: ReferralChartOptions) => void) {
        this.createByCriteria();
    }

    public getByCriteria(): string {
        return this.byCriteria.selected(false);
    }

    public attachTo($inputElement: $.JQuery): void {
        $inputElement.append(this.byCriteria.element());
    }

    /* Creates the drop down lists for 'by x' crtieria */
    private createByCriteria(): void {
        this.byCriteria = new SelectList("displayCriteriaReferral");
        this.byCriteria.change((id) => this.onChange(this));

        var lst: NameValue<string>[] = [];
        lst.push(new NameValue<string>("byService", "by service"));
        lst.push(new NameValue<string>("byProject", "by project"));
        lst.push(new NameValue<string>("bySource", "by source"));
        lst.push(new NameValue<string>("byStatus", "by status"));
        lst.push(new NameValue<string>("byWorker", "by worker"));

        this.byCriteria.populateFromList(lst, (item) => {
                return {key: item.name(), value: item.value()};
            });
    }

}

export = ReferralChartOptions;
