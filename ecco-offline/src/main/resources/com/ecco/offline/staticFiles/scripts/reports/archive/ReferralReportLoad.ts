import {ReportRepository} from "ecco-reports"
import ReportLoad = require("./ReportLoad");
import ReportCriteriaDtoImpl = require("./../ReportCriteriaDtoImpl");
import {ReferralDto as Referral} from "ecco-dto";

/**
 * A report, around referrals.
 */
class ReferralReportLoad implements ReportLoad {

    private referrals: Referral[];
    private getCriteria: () => ReportCriteriaDtoImpl;

    constructor(private repository: ReportRepository) {
    }

    public setupCriteria(getCriteria: () => ReportCriteriaDtoImpl) {
        this.getCriteria = getCriteria;
    }

    public load(callback: () => void) {
        var onSuccess = (referrals: Referral[]) => {
            // transfer the query result to this object
            this.referrals = referrals;
            callback();
        };
        var query = this.repository.findAllReferrals(this.getCriteria());
        query.then(onSuccess);
    }

    public data(): any[] {
        return this.referrals;
    }

}

export = ReferralReportLoad;
