import * as React from "react";
import {ClassAttributes, MutableRefObject} from "react";
import {asDateChange, ListDefinitionEntry, listDefToIdName, SessionData, UserDto} from "ecco-dto";
import {useState, FC} from "react";
import {EccoDate, IdNameDisabled, SelectListOption} from "@eccosolutions/ecco-common";
import {
    AsyncSessionData,
    withSessionData,
    UsersSelectList,
    WeekMonthYearButtons,
    useServicesContext,
    usePromise
} from "ecco-components";
import {
    checkBox,
    datePickerInput,
    dropdownList,
    showReactInModal,
    SelectList,
    link,
    EccoV3Modal
} from "ecco-components-core";
import {ChartDefinition, SelectionCriteria, ReportCriteriaAllowableBuilder} from "ecco-reports";
import {SessionDataService} from "../../feature-config/SessionDataService";
import {asAtText, ReferralStatusHandler, statuses, statusesById, statusIsAtEnd} from "ecco-reports";
import {Grid} from '@eccosolutions/ecco-mui';
import ReferralServiceProject = require("../../referral/components/ReferralServiceProject");
import {ServicesContextProvider} from "../../offline/ServicesContextProvider";

interface FormProps extends ClassAttributes<ChartCriteriaForm> {
    sessionData: SessionData,
    current: ChartDefinition
}
interface FormState {
    fromDate: EccoDate;
    toDate: EccoDate;
    serviceId: number;
    projectId: number;
    entityStatusKey?: string | undefined;
    referralStatusKey?: string | undefined;
    referralStatusLimit?: boolean | undefined;
    geographicAreaId?: number | undefined;
    geoWithinArea: boolean;
    entityStatusOptions: Array<SelectListOption>;
    referralStatusOptions: Array<SelectListOption>;
    geographicAreaOptions: Array<SelectListOption>;
    user: UserDto;
    userId: number;
    // TODO: Other criteria
}

/** Get the 'null' value entry label to use for the given listDef list */
// function listDefaultName(listName: string) {
//     return withMessages(msgs => msgs[ "listDef.nullText." + listName] || "-");
// }

const UsersListSelected: FC<{username: string | null, user: UserDto | null}> = props => {
    const {contactsRepository, userRepository} = useServicesContext();

    const {resolved: contactFromUserRepo} = usePromise(
        () => (!props.user && props.username)
            ? userRepository.findOneUser(props.username).then(user =>
                contactsRepository.findOneIndividual(user.individual.contactId))
            : Promise.resolve(null),
        [props.username, props.user?.username]);

    const {resolved: contactFromRepo} = usePromise(
        () => (props.user && !props.username)
            ? contactsRepository.findOneIndividual(props.user.individual.contactId)
            : Promise.resolve(null),
        [props.username, props.user?.username]);

    const contact = contactFromRepo || contactFromUserRepo;
    return (!contact ? null :
        <span>{`${contact?.firstName} ${contact?.lastName}`}</span>
    );
}

const UsersSelectListPopup: FC<{username: string | null, user: UserDto | null, setSelected: (user: UserDto) => void}> = props => {
    const [show, setShow] = useState(false);
    return (
        <div>
            {`filter user: `}
            <UsersListSelected username={props.username} user={props.user}/>
            {link(<span className={"fa fa-edit"} style={{fontSize: "large"}}></span>,
                () => setShow(true))
            }
            <EccoV3Modal
                title={"select user"}
                action={"close"}
                show={show}
                saveEnabled={false}
                onCancel={() => setShow(false)}
            >
                <UsersSelectList selected={user => {setShow(false); props.setSelected(user);}} />
            </EccoV3Modal>
        </div>
    );
}

/** See CronScheduleStatus for a working example */
class ChartCriteriaForm extends React.Component<FormProps, FormState> {

    private currentAllowableBuilder: ReportCriteriaAllowableBuilder;
    private selectionCriteria: SelectionCriteria;

    public static showInModal(current: ChartDefinition, onUpdated: (newCriteria: SelectionCriteria[]) => void) {
        const formRef: MutableRefObject<ChartCriteriaForm | null> = {current: null}; // A nice pattern to use where we can't useRef()
        const form = <AsyncSessionData promiseFn={SessionDataService.getFeatures}>
                {withSessionData(sessionData => <ChartCriteriaForm
                    current={current}
                    ref={ref => formRef.current = ref}
                    sessionData={sessionData}
                />)}
        </AsyncSessionData>;
        showReactInModal("change criteria", form,
            {
                onAction: () => formRef.current && onUpdated(formRef.current.getCriteria()),
                action: "update"
            });
    }

    constructor(props: FormProps) {
        super(props);
        this.selectionCriteria = props.current.getSelectionCriteria(0);
        const reportCriteria = this.selectionCriteria.getReportCriteriaDto();
        this.currentAllowableBuilder = this.selectionCriteria.getReportCriteriaAllowableBuilder();
        this.state = {
            fromDate: reportCriteria && EccoDate.parseIso8601(reportCriteria.from),
            toDate: reportCriteria && EccoDate.parseIso8601(reportCriteria.to),
            serviceId: reportCriteria.serviceId,
            projectId: reportCriteria.projectId,
            entityStatusKey: this.selectionCriteria.getDto().entityStatus,
            referralStatusKey: this.selectionCriteria.getDto().referralStatus,
            referralStatusLimit: this.selectionCriteria.getDto().newReferralsOnly === undefined // Deliberately to avoid false
                ? ReportCriteriaAllowableBuilder.defaultNewReferralsOnly()
                : this.selectionCriteria.getDto().newReferralsOnly,
            geographicAreaId: this.selectionCriteria.getDto().geographicAreaIdSelected,
            geoWithinArea: true,
            geographicAreaOptions: this.getListOptions(this.props.sessionData, "country-list"),
            entityStatusOptions: this.currentAllowableBuilder.getEntityStatusOptions(),
            referralStatusOptions: this.getReferralStatusList(),
            user: null,
            userId: this.selectionCriteria.getDto().userId
        };
    }

    private setter = state => this.setState(state);

    protected getListEntryName(entry: ListDefinitionEntry): IdNameDisabled {
        const idName: IdNameDisabled = listDefToIdName(entry);
        idName.name = entry.getFullName();
        return idName;
    }

    override render() {
        const anyDateEnabled = this.currentAllowableBuilder.useFrom() || this.currentAllowableBuilder.useTo();
        // TODO we should use reportCapabilities.cleanCriteria() to not repeat logic here?
        //  the from date should be disabled only if its a referral-based report
        //  but we've not fixed this as there is a wider consideration - see commit
        //const fromDateDisabled = this.selectionCriteria.getReportCapability().isReferralBasedReport() && statusIsAtEnd(this.state.statusKey);
        //const fromDateEnabled = !fromDateDisabled;
        const fromDateEnabled = !(statusIsAtEnd(this.state.entityStatusKey) || statusIsAtEnd(this.state.referralStatusKey));
        const toDateLabel = fromDateEnabled ? "to date:" : "as at date:";

        const setter = state => this.setState(state);
        const AREA_LISTNAME = "country-list";
        const areaChange = (o: ListDefinitionEntry) => {
            setter({...this.state, geographicAreaId: (o as ListDefinitionEntry).getId()})
        }

        return <Grid container>
            {anyDateEnabled &&
                <Grid item xs={12}>
                    <WeekMonthYearButtons
                            rangeEnabled={fromDateEnabled}
                            start={this.state.fromDate && this.state.fromDate.formatIso8601CatchingInvalidAsNull()}
                            end={this.state.toDate && this.state.toDate.formatIso8601CatchingInvalidAsNull()}
                            setStart={start => this.setState({fromDate: EccoDate.parseIso8601(start)})}
                            setEnd={end => this.setState({toDate: EccoDate.parseIso8601(end)})}
                    />
                </Grid>
            }
            {this.currentAllowableBuilder.useFrom() && fromDateEnabled
                && <Grid item xs={6}>{datePickerInput("fromDate", "from date:", this.setter, this.state)}</Grid>}
            {this.currentAllowableBuilder.useTo()
                && <Grid item xs={6}>{datePickerInput("toDate", toDateLabel, this.setter, this.state)}</Grid>}
            {this.currentAllowableBuilder.useServiceProject() && <Grid item xs={12}>
                  <ReferralServiceProject
                    serviceId={this.state.serviceId}
                    projectId={this.state.projectId}
                    onServiceChange={service => this.setState({serviceId: service && service.id})}
                    onProjectChange={project => this.setState({projectId: project && project.id})}
                />
            </Grid>}
            {this.currentAllowableBuilder.useEntityStatus() && <Grid item xs={12}>
                {dropdownList("status", this.setter, this.state, "entityStatus",
                              this.state.entityStatusOptions)}
            </Grid>}
            {this.currentAllowableBuilder.useReferralStatus() && <Grid item xs={12}>
                {dropdownList("referral status group",
                              this.setter, this.state, "referralStatusKey", this.state.referralStatusOptions)}
            </Grid>}
            {this.currentAllowableBuilder.useReferralStatus() &&
            ReferralStatusHandler.referralStatusReceivedDuringPeriodAllowed(this.state.referralStatusKey) && <Grid item xs={12}>
                {checkBox("referralStatusLimit", "limit to received date (in the period) ", this.setter, this.state)}
            </Grid>}
            {this.currentAllowableBuilder.useGeographicalArea() && this.state.geographicAreaOptions.length > 0 &&
                [
                    <Grid item xs={12}>
                        <SelectList
                            placeholder={"geographical area"}
                            createNew={false}
                            getOptionLabel={l => l.getDisplayName()}
                            getOptionValue={l => l.getId().toString()}
                            value={this.props.sessionData.getEntries(AREA_LISTNAME).filter(l => l.getId() == this.state.geographicAreaId)}
                            options={this.props.sessionData.getEntries(AREA_LISTNAME)}
                            onChange={value => areaChange(value as ListDefinitionEntry)}
                        />
                    </Grid>,
                    <Grid item xs={12}>
                        {checkBox("geoWithinArea", " inc within ", this.setter, this.state)}
                    </Grid>
                ]}
            {this.currentAllowableBuilder.useUsername() && <Grid item xs={12}>
                <ServicesContextProvider>
                    <UsersSelectListPopup username={this.selectionCriteria.getDto().username} user={this.state.user} setSelected={user => this.setter({...this.state, user: user, userId: user ? user.userId : null})} />
                </ServicesContextProvider>
            </Grid>}

        </Grid>;
    }

    /** TODO: Extract <ListDefSelect listName="..." value={} sessionData={} /> with all this covered
     * useMetaValue is used when what is passed/stored is not the id of the list def, but a value for looking up the
     * listdef based on its metadata.value. The value is also returned on getSelected()
     */
    private getListOptions(sessionData: SessionData, listName: string): Array<SelectListOption> {
        const options: Array<SelectListOption> = [];
        const entries = sessionData.getListDefinitionEntriesByListName(listName)
            .map(entry => ({id: entry.getId().toString(), name: entry.getFullName()}));
        return options.concat(entries);
    }

    private getCriteria(): SelectionCriteria[] {
        const newCriteria = this.props.current.clone();
        const selectionCriteria = newCriteria.getSelectionCriteria(0);
        selectionCriteria.getDto().relativeStartIndex = newCriteria.getSelectionCriteria(0).getDto().relativeStartIndex;

        const fromIn = EccoDate.parseIso8601(this.selectionCriteria.getReportCriteriaDto().from);
        const toIn = EccoDate.parseIso8601(this.selectionCriteria.getReportCriteriaDto().to);
        const datesChanged = asDateChange(fromIn, this.state.fromDate) != undefined
                            || asDateChange(toIn, this.state.toDate) != undefined;
        if (datesChanged) {
            selectionCriteria.setDateRange(this.state.fromDate, this.state.toDate);
            // as per EditCriteriaForm.ts
            selectionCriteria.clearDateSelectorType(); // we shouldn't obey prev/next if we change dates
        }

        selectionCriteria.setServiceProject(this.state.serviceId, this.state.projectId);
        const isPropertyPathUsed = selectionCriteria.getReportCapability().allowablePropertyPaths(this.props.sessionData)
            .some( entry => entry.propertyName == this.state.entityStatusKey );
        if (isPropertyPathUsed) {
            selectionCriteria.setPropertyPath(this.state.entityStatusKey);
            selectionCriteria.setEntityStatus(undefined);
            selectionCriteria.setReferralStatus(undefined, false);
        } else {
            selectionCriteria.setPropertyPath(undefined);
            selectionCriteria.setEntityStatus(this.state.entityStatusKey);
            // if we have an 'as at' date, don't clear dates here in case the user changes their mind on the form
            // instead we check this on completing the form (as the form goes directly into reloading the chart)
            const referralStatusLimitAllowed = ReferralStatusHandler.referralStatusReceivedDuringPeriodAllowed(this.state.referralStatusKey);
            selectionCriteria.setReferralStatus(this.state.referralStatusKey, this.state.referralStatusLimit ? referralStatusLimitAllowed : false);
        }

        if (this.state.userId) {
            selectionCriteria.setUserId(this.state.userId);
        }

        const geoAreaId = this.state.geographicAreaId;
        if (!geoAreaId) {
            selectionCriteria.setGeoAreaIds(undefined, undefined);
        } else if (this.state.geoWithinArea && this.props.sessionData.getListDefinitionEntryById(geoAreaId).getIsParent()) {
            // within ticked and selected is a parent
            const matchIds = this.props.sessionData.getAllMatchingListDefIdsForParent(geoAreaId);
            selectionCriteria.setGeoAreaIds(geoAreaId, matchIds);
        } else {
            // one selected
            selectionCriteria.setGeoAreaIds(geoAreaId, undefined);
        }
        return newCriteria.getAllSelectionCriteria();
    }

    private getReferralStatusList() {
        const options: Array<SelectListOption> = [];
        const statusReceivingDuringPeriod = this.selectionCriteria.getDto().newReferralsOnly == undefined
            ? ReportCriteriaAllowableBuilder.defaultNewReferralsOnly()
            : this.selectionCriteria.getDto().newReferralsOnly;
        if (this.currentAllowableBuilder.useReferralStatus()) {
            statuses.forEach( (entry) => {

                // if we are not referral based - then we keep the from/to dates and change the status text to be 'at end date'
                let referralStatusText = statusesById[entry.id];
                if (!this.selectionCriteria.getReportCapability().isReferralBasedReport()) {
                    if (ReferralStatusHandler.referralStatusAtEndDate(entry.id)) {
                        referralStatusText = referralStatusText.replace(asAtText, "(at end date)");
                    }
                }

                options.push({name: referralStatusText, id: entry.id});
                // TODO: if (this.selectionCriteria.getDto().referralStatus == entry.id) option.attr("selected", "selected");
            });
        }
        return options;
    }
}

export default ChartCriteriaForm;