
This should be added to to cover GOTCHAs :)

Events
------

When we load a rota, we have ALL activities and ALL workers which are going to be used, until we change date (i.e. load a different rota) or refresh the page.

Events should therefore be registered for all possible states where an event is needed.

For Worker, we should register all events to be called relating to that worker - which is:
- activity allocated -> Update WorkerRowControl to add the activity
- activity deallocated -> Remove the activity from the worker row control

For Activity, we want:
- activity allocated -> Remove Activity from UnassignedActivitiesRowControl - perhaps via the GroupControl
- activity deallocated -> Add the activity to the appropriate row via insertActivity.

**Significant care** should be taken to match up if you are modifying event registrations, and naturally you will need to keep a reference to the registration.
 
