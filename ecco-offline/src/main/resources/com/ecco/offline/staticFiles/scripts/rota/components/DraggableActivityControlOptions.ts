import $ = require("jquery");
import {Activity} from "ecco-rota";


interface DraggableActivityControlOptions {
    readonly activity: Activity;
    readonly onClick: (activity: Activity) => void;
    readonly drag?: {
        readonly $containment: $.JQuery;
        readonly onDragStart: (activity: Activity, ctrlKey: boolean) => void;
        readonly onDragEnd: (activity: Activity) => void;
    };
}

export = DraggableActivityControlOptions;
