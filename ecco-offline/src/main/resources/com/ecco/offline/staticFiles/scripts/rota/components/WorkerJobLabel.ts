import $ = require("jquery");
import {DemandResource} from "ecco-rota";

class WorkerJobLabel {
    private static CSS_CLASS = "worker-label";

    private $container: $.JQuery = $("<span>");

    constructor(private workerJob: DemandResource, private onResourceClick: (resource: DemandResource) => void) {
    }

    public attach($replacedElement?: $.JQuery): $.JQuery {

        if ($replacedElement) {
            this.$container.attr("id", $replacedElement.attr("id"))
                .attr("class", $replacedElement.attr("class"));
            $replacedElement.replaceWith(this.$container);
        } else {
            this.$container.attr("id", "")
                .attr("class", "");
        }

        const mainLabel = this.workerJob.getName();
        const subLabel = this.workerJob.getContractedWeeklyHours() && this.workerJob.getContractedWeeklyHours().toString() + "hrs";
        const $label = subLabel
            ? $("<span>").text(mainLabel).append($("<br/>")).append($("<small>").text(subLabel))
            : $("<span>").text(mainLabel);
        this.$container
                .attr("title", subLabel ? mainLabel + " " + subLabel : mainLabel)
                .addClass(WorkerJobLabel.CSS_CLASS)
                .append($label);

        return this.$container;
    }

    /**
     * when a label is clicked, we want to show options for the associated component,
     * and while active, we want to show updates
     */
    public clickHandler(event: $.JQueryMouseEventObject) {
        event.preventDefault();
        this.onResourceClick(this.workerJob);
    }
}

export = WorkerJobLabel;
