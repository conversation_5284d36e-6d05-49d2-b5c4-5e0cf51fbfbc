import * as React from "react";
import {ReactElement, Fragment} from "react";
import {Activity, DemandResource} from "ecco-rota";
import {Avatar} from "@eccosolutions/ecco-mui";
import {ButtonMenu} from "ecco-components";
import {PersonIcon} from "@eccosolutions/ecco-mui-controls"

export interface Props {
    readonly activity: Activity;
    readonly onSelection: (worker: DemandResource) => void;
}

export function AllocateWorkerJobButton({activity, onSelection}: Props): ReactElement {
    const availableStaff = activity.getRota().getAvailableWorkers(activity);

    const hasRequired = activity.getRequiredAttributes()?.length > 0;

    const staffEntries = availableStaff
        .map(worker => ({worker, missingAttributes: activity.getMissingRequiredAttrs(worker)}))
        .sort((a, b) => a.missingAttributes.length - b.missingAttributes.length);

    const options = staffEntries
        .map(({worker, missingAttributes}, i) => <Fragment key={i}>
            <Avatar style={{marginRight: 8}}><PersonIcon/></Avatar>
            {worker.getName()}
            {hasRequired
                ? missingAttributes.length === 0 ? "(match)" : `(missing ${missingAttributes.join(", ")})`
                : ""
            }
        </Fragment>);

    return <ButtonMenu options={options}
                       selectedIndex={null}
                       onClick={() => {/* Not used. Button gets hidden when we select*/}}
                       onSelection={i => onSelection(staffEntries[i].worker)}>
        Allocate...
    </ButtonMenu>
}