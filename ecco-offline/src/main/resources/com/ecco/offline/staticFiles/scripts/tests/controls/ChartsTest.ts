import $ = require("jquery");
import QUnit = require("qunit");

import charts = require("../../controls/charts");


function setup($fixture: $.JQuery): void {
    setupBarChart($fixture);
    setupPieChart($fixture);
}

function setupBarChart ($fixture): void {
    var kvPairs1 = [
        {key: "c", value: 10},
        {key: "b", value: 11}
    ];
    var kvPairs2 = [
        // Note that keys don't all match. Jqplot collates matching
        // keys automatically :).
        {key: "a", value: 20},
        {key: "b", value: 9}
    ];
    var dataSeries1 = new charts.DataSeries<charts.KeyValuePair>("series 1", null, kvPairs1,
            new charts.BarChartDataRepresentation((e: charts.KeyValuePair) => e));
    var dataSeries2 = new charts.DataSeries<charts.KeyValuePair>("series 2", null, kvPairs2,
            new charts.BarChartDataRepresentation((e: charts.KeyValuePair) => e));
    var chartData = new charts.ChartData([dataSeries1, dataSeries2]);

    var control = new charts.ChartControl();
    $fixture.append(control.element());

    var $pre = $("<pre>").appendTo($fixture);

    function onClick(event: charts.ClickEvent<charts.KeyValuePair>): void {
        $pre.text(JSON.stringify(event.getDatums()));
    }

    dataSeries1.addClickEventHandler(onClick);
    dataSeries2.addClickEventHandler(onClick);

    control.setData(chartData, {canClickThrough: true});
}

function setupPieChart($fixture: $.JQuery): void {
    var data = [
        {key: "apples", value: 12},
        {key: "oranges", value: 7},
        {key: "bananas", value: 9}
    ];

    var dataSeries = new charts.DataSeries<charts.KeyValuePair>(null, null, data,
            new charts.PieChartDataRepresentation((e: charts.KeyValuePair) => e));

    var chartData = new charts.ChartData([dataSeries]);

    var control = new charts.ChartControl();
    $fixture.append(control.element());
    control.setData(chartData, {canClickThrough: true});

    var $pre = $("<pre>").appendTo($fixture);

    dataSeries.addClickEventHandler((e) => $pre.text(JSON.stringify(e.getDatums())));
}

QUnit.module( "Charts tests", {
    setup: () => setup($("#qunit-fixture"))
});

// After the tests, recreate the controls so we can play with them.
// (QUnit destroys the original controls automatically).
QUnit.config.done.push(() => setup($("#fixture")));

QUnit.test('dummy', () => {
    QUnit.equal(2, 2, "2 == 2 ");
});

QUnit.load();
QUnit.start();

