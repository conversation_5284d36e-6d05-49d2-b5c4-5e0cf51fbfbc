<!DOCTYPE html>
<html>
<head>
    <title>listdef-hierarchical-auto-test</title>
    <meta charset="UTF-8" />

    <link rel="stylesheet" href="../../../css/qunit/qunit.css"/>


    <script src="../../lib/require.min.js"></script>
    <script>
        var requirejs_baseUrl = "../../";
        var apiBaseUrl = "../../../../../api/";
        var requirejs_devMode = "true";
    </script>
    <script src="../../common/require-boot.js"></script>
    <script>require(["./tests/data-attr/listdef-hierarchical-auto-test"])</script>

</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>

    <b>different length hierarchies</b>
    <div id="test1_countries" class="listdef-hierarchical-select-list1"
        data-name-shared="sharedName1"
        data-initial-value-shared="25"
        list-name="my-countries2"></div>
    <br/>

</body>
</html>
