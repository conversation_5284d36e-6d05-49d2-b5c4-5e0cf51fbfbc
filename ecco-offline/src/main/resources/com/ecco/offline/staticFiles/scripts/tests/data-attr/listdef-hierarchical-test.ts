import qunit = require("qunit");

import $ = require("jquery")
import ListDefHierarchicalSelectList = require("../../data-attr/ListDefHierarchicalSelectList");
import featuresMock = require("./featuresMocks");

qunit.asyncTest("parent and child loads", () => {
    var myLists = new ListDefHierarchicalSelectList($(".listdef-hierarchical-select-list1"), featuresMock.featureRepository);

    qunit.expect(3);

    myLists.load().then( () => {
        var $parent = $("#test1");
        var $child = $("#test2");

        // check parent has 3 items ('-' + 2)
        var itemsFound = $parent.find("option").length;
        qunit.strictEqual(itemsFound, 3, "loaded parent data matched");

        // check child has 4 items ('-' + 3) since its parent has UK selecte
        var itemsFound = $child.find("option").length;
        qunit.strictEqual(itemsFound, 4, "loaded child data matched");

        // check parent initial value
        var parentInitialVal = $parent.find("select").val();
        qunit.strictEqual(parentInitialVal, "11", "loaded parent inital value data matched");

        qunit.start();
    });

});

qunit.asyncTest("parent select updated child list", () => {
    var myLists = new ListDefHierarchicalSelectList($(".listdef-hierarchical-select-list2"), featuresMock.featureRepository);

    qunit.expect(3);

    myLists.load().then( () => {
        var $parent = $("#test3");
        var $child = $("#test4");

        var firstItemValue = $child.find("option").eq(1).val();
        qunit.strictEqual(firstItemValue, "1", "loaded child data matched");

        // change parent selection from default 11 to Japan 12
        $parent.find("select option:selected").prop('selected', false);
        $parent.find("select option").filter('[value="12"]').prop('selected', true).trigger("change");

        // check 2 items ('-' + 1)
        var itemsFound = $child.find("option").length;
        qunit.strictEqual(itemsFound, 2, "loaded child data items matched");

        // check the first item (after dash) is 'english'
        var firstItemValue = $child.find("option").eq(1).val();
        qunit.strictEqual(firstItemValue, "4", "loaded child data matched");

        qunit.start();
    });

});

qunit.asyncTest("parent/child select updates child list", () => {
    var myLists = new ListDefHierarchicalSelectList($(".listdef-hierarchical-select-list3"), featuresMock.featureRepository);

    qunit.expect(4);

    myLists.load().then( () => {
        var $country = $("#test_country_uk");
        var $language = $("#test_language");
        var $dialect = $("#test_dialect");

        // check country has first item (after '-') of UK, 11
        var firstItemValue = $country.find("option").eq(1).val();
        qunit.strictEqual(firstItemValue, "11", "loaded country data matched");

        // check language has 4 items ('-' + 3) since its parent has UK selected
        var itemsFound = $language.find("option").length;
        qunit.strictEqual(itemsFound, 4, "loaded language data matched");

        // check dialect has 1 items ('-') because the langauge has nothing selected
        itemsFound = $dialect.find("option").length;
        qunit.strictEqual(itemsFound, 1, "loaded dialect data not present yet");

        // change language selection from '-' to english 1
        $language.find("select option:selected").prop('selected', false);
        $language.find("select option").filter('[value="1"]').prop('selected', true).trigger("change");

        // check the first item (after dash) is 'scouse' because we have english selected
        firstItemValue = $dialect.find("option").eq(1).val();
        qunit.strictEqual(firstItemValue, "5", "loaded dialect data matched");

        qunit.start();
    });

});

qunit.load();
qunit.start();
