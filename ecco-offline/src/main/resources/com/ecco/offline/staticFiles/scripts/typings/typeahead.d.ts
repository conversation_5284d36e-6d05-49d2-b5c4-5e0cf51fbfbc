// Type definitions for typeahead.js 0.10.4
// Project: http://twitter.github.io/typeahead.js/
// Definitions by: <PERSON><PERSON><PERSON><PERSON> <https://github.com/igo<PERSON><PERSON>/>
// Definitions: https://github.com/borisyankov/DefinitelyTyped

// Modified for use with AMD modules.

declare module "typeahead" {
    import $ = require("jquery");

    export interface JQuery extends $.JQuery {

        /**
         * Destroys previously initialized typeaheads. This entails reverting
         * DOM modifications and removing event handlers.
          *
          * @constructor
         * @param methodName Method 'destroy'
          */
        typeahead(methodName: 'destroy'): JQuery;

        /**
         * Opens the dropdown menu of typeahead. Note that being open does not mean that the menu is visible.
         * The menu is only visible when it is open and has content.
          *
          * @constructor
         * @param methodName Method 'open'
          */
        typeahead(methodName: 'open'): JQuery;

        /**
         * Closes the dropdown menu of typeahead.
         *
         * @constructor
         * @param methodName Method 'close'
         */
        typeahead(methodName: 'close'): JQ<PERSON>y;

        /**
         * Returns the current value of the typeahead.
         * The value is the text the user has entered into the input element.
          *
          * @constructor
         * @param methodName Method 'val'
          */
        typeahead(methodName: 'val'): string;

        /**
          * Sets the value of the typeahead. This should be used in place of jQuery#val.
          *
          * @constructor
          * @param methodName Method 'val'
          * @param query The value to be set
          */
        typeahead(methodName: 'val', val: string): JQuery;

        /**
          * Accommodates the val overload.
          *
          * @constructor
          * @param methodName Method name ('val')
          */
        typeahead(methodName: string): string;


        /**
          * Accommodates multiple overloads.
          *
          * @constructor
          * @param methodName Method name
          * @param query The query to be set in case method 'val' is used.
          */
        typeahead(methodName: string, query: string): JQuery;

        /**
          * Accomodates specifying options such as hint and highlight.
          * This is in correspondence to the examples mentioned in http://twitter.github.io/typeahead.js/examples/
          *
          * @constructor
          * @param options ('hint' or 'highlight' or 'minLength' all of which are optional)
          * @param datasets Array of datasets
          */
        typeahead(options: Options, datasets: Dataset[]): JQuery;

        /**
          * Accomodates specifying options such as hint and highlight.
          * This is in correspondence to the examples mentioned in http://twitter.github.io/typeahead.js/examples/
          *
          * @constructor
          * @param options ('hint' or 'highlight' or 'minLength' all of which are optional)
          * @param datasets One or more datasets passed in as arguments.
          */
        typeahead(options: Options, ... datasets: Dataset[]): JQuery;
    }

    /**
      * A dataset is an object that defines a set of data that hydrates
      * suggestions. Typeaheads can be backed by multiple datasets.
      * Given a query, a typeahead instance will inspect its backing
      * datasets and display relevant suggestions to the end-user.
      */
    interface Dataset {
        /**
         * The backing data source for suggestions.
         * Expected to be a function with the signature (query, cb).
         * It is expected that the function will compute the suggestion set (i.e. an array of JavaScript objects) for query and then invoke cb with said set.
         * cb can be invoked synchronously or asynchronously.
         *
          */
        source: (query: string, cb: (result: any) => void) => void;

        /**
          * The name of the dataset.
          * This will be appended to tt-dataset- to form the class name of the containing DOM element.
          * Must only consist of underscores, dashes, letters (a-z), and numbers.
          * Defaults to a random number.
          */
        name?: string;

        /**
         * For a given suggestion object, determines the string representation of it.
         * This will be used when setting the value of the input control after a suggestion is selected. Can be either a key string or a function that transforms a suggestion object into a string.
         * Defaults to value.
         */
        displayKey?: string | ((obj: any) => string);

        /**
         * A hash of templates to be used when rendering the dataset.
         * Note a precompiled template is a function that takes a JavaScript object as its first argument and returns a HTML string.
          */
        templates?: Templates;
    }


    interface Templates {

        /**
         * Rendered when 0 suggestions are available for the given query.
         * Can be either a HTML string or a precompiled template.
         * If it's a precompiled template, the passed in context will contain query
          */
        empty?: any;

        /**
         * Rendered at the bottom of the dataset.
         * Can be either a HTML string or a precompiled template.
         * If it's a precompiled template, the passed in context will contain query and isEmpty.
          */
        footer?: any;

        /**
         * Rendered at the top of the dataset.
         * Can be either a HTML string or a precompiled template.
         * If it's a precompiled template, the passed in context will contain query and isEmpty.
          */
        header?: any;

        /**
         * Used to render a single suggestion.
         * If set, this has to be a precompiled template.
         * The associated suggestion object will serve as the context.
         * Defaults to the value of displayKey wrapped in a p tag i.e. <p>{{value}}</p>.
          */
        suggestion?: (datum: any) => string;
    }


    /**
        * Prefetched data is fetched and processed on initialization.
        * If the browser supports localStorage, the processed data will be cached
        * there to prevent additional network requests on subsequent page loads.
        */
    interface PrefetchOptions {
        /**
            * A URL to a JSON file containing an array of datums. Required.
            */
        url: string;

        /**
            * The time (in milliseconds) the prefetched data should be cached
            * in localStorage. Defaults to 86400000 (1 day).
            */
        ttl?: number;

        /**
            * A function that transforms the response body into an array of datums.
            *
            * @param parsedResponse Response body
            */
        filter?: (parsedResponse: any) => Datum[];
    }

    /**
        * Remote data is only used when the data provided by local and prefetch
        * is insufficient. In order to prevent an obscene number of requests
        * being made to remote endpoint, typeahead.js rate-limits remote requests.
        */
    interface RemoteOptions {
        /**
            * A URL to make requests to when the data provided by local and
            * prefetch is insufficient. Required.
            */
        url: string;

        /**
            * The type of data you're expecting from the server. Defaults to json.
            * @see http://api.jquery.com/jQuery.ajax/ for more info.
            */
        dataType?: string;

        /**
            * Determines whether or not the browser will cache responses.
            * @see http://api.jquery.com/jQuery.ajax/ for more info.
            */
        cache?: boolean;

        /**
            * Sets a timeout for requests.
            * @see http://api.jquery.com/jQuery.ajax/ for more info.
            */
        timeout?: number;

        /**
            * The pattern in url that will be replaced with the user's query
            * when a request is made. Defaults to %QUERY.
            */
        wildcard?: string;

        /**
            * Overrides the request URL. If set, no wildcard substitution will
            * be performed on url.
            *
            * @param url Replacement URL
            * @param uriEncodedQuery Encoded query
            * @returns A valid URL
            */
        replace?: (url: string, uriEncodedQuery: string) => string;

        /**
            * The function used for rate-limiting network requests.
            * Can be either 'debounce' or 'throttle'. Defaults to 'debounce'.
            */
        rateLimitFn?: string;

        /**
            * The time interval in milliseconds that will be used by rateLimitFn.
            * Defaults to 300.
            */
        rateLimitWait?: number;

        /**
            * The max number of parallel requests typeahead.js can have pending.
            * Defaults to 6.
            */
        maxParallelRequests?: number;

        /**
            * A pre-request callback. Can be used to set custom headers.
            * @see http://api.jquery.com/jQuery.ajax/ for more info.
            */
        beforeSend?: (jqXhr: $.JQueryXHR, settings: $.JQueryAjaxSettings) => void;

        /**
            * Transforms the response body into an array of datums.
            *
            * @param parsedResponse Response body
            */
        filter?: (parsedResponse: any) => Datum[];
    }

    /**
        * The individual units that compose datasets are called datums.
        * The canonical form of a datum is an object with a value property and
        * a tokens property.
        *
        * For ease of use, datums can also be represented as a string.
        * Strings found in place of datum objects are implicitly converted
        * to a datum object.
        *
        * When datums are rendered as suggestions, the datum object is the
        * context passed to the template engine. This means if you include any
        * arbitrary properties in datum objects, those properties will be
        * available to the template used to render suggestions.
        */
    interface Datum {
        /**
            * The string that represents the underlying value of the datum
            */
        value: string;

        /**
            * A collection of single-word strings that aid typeahead.js in
            * matching datums with a given query.
            */
        tokens: string[];
    }

    /**
        * When initializing a typeahead, there are a number of options you can configure.
        */
    interface Options {
        /**
            * highlight:  If true, when suggestions are rendered,
            * pattern matches for the current query in text nodes will be wrapped in a strong element.
            * Defaults to false.
            */
        highlight?: boolean;

        /**
            * If false, the typeahead will not show a hint. Defaults to true.
            */
        hint?: boolean;

        /**
            * The minimum character length needed before suggestions start getting rendered. Defaults to 1.
            */
        minLength?: number;
    }
}