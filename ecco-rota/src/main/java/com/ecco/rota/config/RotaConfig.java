package com.ecco.rota.config;

import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.config.service.SettingsService;
import com.ecco.dom.contracts.RateCardCalculation;
import com.ecco.dom.contracts.RateCardCalculationBasedOnMultipleRateCardEntriesWithoutChildren;
import com.ecco.evidence.repositories.CalendarEventSnapshotRepository;
import com.ecco.rota.service.CalendarEventSnapshotService;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.cachebust.EntityCacheBustKeyRepository;
import com.ecco.security.repositories.ContactRepository;
import com.ecco.service.acls.CachedAclVisibilityService;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.webApi.calendar.ServiceRecipientRotaDecorator;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.*;
import com.ecco.dom.agreements.AppointmentTypeRepository;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.repositories.contracts.RateCardRepository;
import com.ecco.repositories.contracts.UnitOfMeasurementRepository;
import com.ecco.rota.service.*;
import com.ecco.rota.service.RotaServiceImpl.RecurrenceResource;
import com.ecco.service.EventService;
import com.ecco.servicerecipient.ServiceRecipientSummaryService;
import com.ecco.calendar.core.CalendarService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.persistence.EntityManager;
import java.util.List;

@Configuration(proxyBeanMethods = false)
//@EnableJpaRepositories(basePackageClasses=SomeRepository.class)
public class RotaConfig {

    @Bean
    public RotaDelegator rotaDelegator(List<RotaHandler> rotaHandlers) {
        return new RotaDelegator(rotaHandlers);
    }

    @Bean(name="recurrenceDecorator")
    public ServiceRecipientRotaDecorator<RecurrenceResource> recurrenceDecorator(ReferralRepository referralRepository,
                                                                                 FixedContainerRepository fixedContainerRepository,
                                                                                 ServiceRecipientSummaryService serviceRecipientSummaryService,
                                                                                 ServiceRecipientRepository serviceRecipientRepository,
                                                                                 DemandScheduleRepository demandScheduleRepository,
                                                                                 ApplicationProperties appConfig) {
        return new ServiceRecipientRotaDecorator<>(referralRepository, fixedContainerRepository, serviceRecipientSummaryService, serviceRecipientRepository, demandScheduleRepository, appConfig);
    }

    @Bean
    public RotaServiceImpl rotaService(DemandScheduleRepository demandScheduleRepository,
                                       DemandScheduleDirectTaskRepository demandScheduleTaskDirectRepository,
                                       CalendarEventSnapshotRepository calendarEventSnapshotRepository,
                                       ServiceRecipientRepository serviceRecipientRepository,
                                       CalendarService calendarService,
                                       EntityUriMapper entityUriMapper,
                                       CustomEventRepository nonRecurringRepository,
                                       ServiceAgreementRepository agreementRepository,
                                       AppointmentTypeRepository appointmentTypeRepository,
                                       RateCardRepository rateCardRepository,
                                       UnitOfMeasurementRepository unitOfMeasurementRepository,
                                       EvidenceSupportWorkRepository supportWorkRepository,
                                       EvidenceSupportActionRepository supportActionRepository,
                                       @Qualifier("recurrenceDecorator") ServiceRecipientRotaDecorator<RecurrenceResource> recurrenceDecorator) {
        return new RotaServiceImpl(demandScheduleRepository, demandScheduleTaskDirectRepository, calendarEventSnapshotRepository,
                serviceRecipientRepository, calendarService, entityUriMapper,
                nonRecurringRepository, agreementRepository,
                appointmentTypeRepository, rateCardRepository, unitOfMeasurementRepository,
                supportWorkRepository, supportActionRepository, recurrenceDecorator);
    }

    @Bean
    public BaseRotaHandler defaultRotaHandler(DemandScheduleRepository demandScheduleRepository, WorkerJobRepository workerJobRepository,
                                              ServiceAgreementRepository serviceAgreementRepository,
                                              EventService eventService,
                                              RotaService rotaService) {
        return new ReferralWorkerRotaHandler(demandScheduleRepository, workerJobRepository, serviceAgreementRepository,
                rotaService, eventService);
    }

    @Bean
    public BuildingWorkerRotaHandler buildingRotaHandler(WorkerJobRepository workerJobRepository,
                                                         DemandScheduleRepository demandScheduleRepository, FixedContainerRepository containerRepository,
                                                         ServiceAgreementRepository serviceAgreementRepository,
                                                         RotaService rotaService,
                                                         EventService eventService) {
        return new BuildingWorkerRotaHandler(demandScheduleRepository, workerJobRepository, containerRepository,
                serviceAgreementRepository, rotaService, eventService);
    }

    @Bean
    public ServiceCatWorkerRotaHandler serviceCatRotaHandler(WorkerJobRepository workerJobRepository,
                                                         DemandScheduleRepository demandScheduleRepository,
                                                         ServiceCategorisationRepository serviceCatRepository,
                                                         ServiceAgreementRepository serviceAgreementRepository,
                                                         FixedContainerRepository fixedContainerRepository,
                                                         RotaService rotaService,
                                                         @Qualifier("aclVisibilityService") CachedAclVisibilityService aclService,
                                                         EventService eventService) {
        return new ServiceCatWorkerRotaHandler(demandScheduleRepository, workerJobRepository, serviceCatRepository,
                serviceAgreementRepository, fixedContainerRepository, rotaService, aclService, eventService);
    }

    @Bean
    public ServiceCatCareRunRotaHandler serviceCatCareRunRotaHandler(DemandScheduleRepository demandScheduleRepository,
                                                             ServiceCategorisationRepository serviceCatRepository,
                                                             FixedContainerRepository fixedContainerRepository,
                                                             ServiceCatWorkerRotaHandler serviceCatWorkerRotaHandler,
                                                             BuildingWorkerRotaHandler buildingWorkerRotaHandler,
                                                             RotaService rotaService) {
        return new ServiceCatCareRunRotaHandler(demandScheduleRepository, serviceCatRepository, fixedContainerRepository,
                buildingWorkerRotaHandler, serviceCatWorkerRotaHandler, rotaService);
    }

    @Bean
    ResourcesRotaHandler resourcesRotaHandler(DemandScheduleRepository demandScheduleRepository,
                                              FixedContainerRepository bookableResourceRepository,
                                              ListDefinitionRepository resourceTypeRepository,
                                              ServiceAgreementRepository serviceAgreementRepository,
                                              RotaService rotaService,
                                              EventService eventService) {
        return new ResourcesRotaHandler(demandScheduleRepository, bookableResourceRepository,
                resourceTypeRepository, serviceAgreementRepository,
                rotaService, eventService);
    }

    @Bean
    public ShiftCareRunArchiveRotaHandler careRunShiftRotaHandler(FixedContainerRepository containerRepository,
                                                                  BuildingWorkerRotaHandler buildingWorkerRotaHandler,
                                                                  RotaService rotaService,
                                                                  DemandScheduleRepository demandScheduleRepository) {
        return new ShiftCareRunArchiveRotaHandler(containerRepository, buildingWorkerRotaHandler, rotaService, demandScheduleRepository);
    }

    @Bean
    public BuildingCareRunRotaHandler careRunBuildingRotaHandler(FixedContainerRepository containerRepository,
                                                                 BuildingWorkerRotaHandler buildingWorkerRotaHandler,
                                                                 DemandScheduleRepository demandScheduleRepository,
                                                                 RotaService rotaService) {
        return new BuildingCareRunRotaHandler(containerRepository, buildingWorkerRotaHandler, demandScheduleRepository,
                rotaService);
    }

    @Bean
    public RateCardCalculation defaultRateCardCalculation() {
        return new RateCardCalculationBasedOnMultipleRateCardEntriesWithoutChildren(true);
    }

    @Bean
    public CalendarEventSnapshotService calendarEventSnapshotService(EntityManager entityManager,
                                                                     CalendarEventSnapshotRepository calendarEventSnapshotRepository,
                                                                     ServiceRecipientRepository serviceRecipientRepository,
                                                                     ContactRepository contactRepository,
                                                                     SettingsService settingsService,
                                                                     RotaService rotaService,
                                                                     CalendarService calendarService,
                                                                     EntityUriMapper entityUriMapper
                                                                     ) {
        return new CalendarEventSnapshotServiceImpl(entityManager, calendarEventSnapshotRepository, serviceRecipientRepository,
                contactRepository, calendarService, rotaService, entityUriMapper, settingsService);
    }

    @Bean
    public RotaDemandPreChangeAgent rotaDemandPreChangeAgent(MessageBus<ApplicationEvent> messageBus,
                                                           CalendarEventSnapshotService calendarEventSnapshotService) {
        return new RotaDemandPreChangeAgent(messageBus, calendarEventSnapshotService);
    }

    @Bean
    public RotaDemandPostChangeAgent rotaDemandPostChangeAgent(MessageBus<ApplicationEvent> messageBus,
                                                           EntityCacheBustKeyRepository entityCacheBustKeyRepository,
                                                           CalendarEventSnapshotService calendarEventSnapshotService) {
        return new RotaDemandPostChangeAgent(messageBus, entityCacheBustKeyRepository, calendarEventSnapshotService);
    }

}
