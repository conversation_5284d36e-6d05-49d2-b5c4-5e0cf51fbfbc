package com.ecco.rota.service;

import com.ecco.calendar.core.RecurringEntry;

import org.jspecify.annotations.Nullable;
import java.time.Instant;

public interface CalendarEventSnapshotService {

    void clearRotaAppointmentsForHandle(@Nullable Instant start, RecurringEntry.@Nullable RecurringEntryHandle demandHandle);
    void recreateRotaAppointmentsForServiceRecipient(int serviceRecipientId, @Nullable Instant start, RecurringEntry.@Nullable RecurringEntryHandle demandHandle, boolean skipReset);

}
