package com.ecco.rota.service;

import com.ecco.calendar.core.*;
import com.ecco.evidence.repositories.CalendarEventSnapshotRepository;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.infrastructure.util.EccoTimeUtils;
import com.ecco.webApi.calendar.ServiceRecipientEventDecorator;
import com.ecco.webApi.calendar.ServiceRecipientRotaDecorator;
import com.ecco.dao.*;
import com.ecco.dom.*;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.dom.agreements.*;
import com.ecco.dom.contracts.UnitOfMeasurement;
import com.ecco.evidence.EvidenceTask;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.repositories.contracts.RateCardRepository;
import com.ecco.repositories.contracts.UnitOfMeasurementRepository;
import com.ecco.rota.service.DemandScheduleDirectTaskDefinitions.DemandScheduleDirectTaskDefinition;
import com.ecco.rota.webApi.dto.*;
import com.ecco.service.exceptions.RotaException;
import com.google.common.collect.Range;
import com.ecco.calendar.core.Recurrence.RecurrenceHandle;
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.val;
import org.joda.time.*;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.net.URI;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ecco.infrastructure.time.JodaToJDKAdapters.localDateTimeToJoda;
import static com.ecco.calendar.core.Recurrence.Status.CONFIRMED;

@Service("rotaService")
@WriteableTransaction
public class RotaServiceImpl implements RotaService {

    private final Logger log = LoggerFactory.getLogger(getClass());

    private final ServiceRecipientRepository serviceRecipientRepository;
    private final DemandScheduleRepository demandScheduleRepository;
    private final DemandScheduleDirectTaskRepository demandScheduleDirectTaskRepository;
    private final CalendarEventSnapshotRepository calendarEventSnapshotRepository;
    private final CalendarService calendarService;
    private final EntityUriMapper entityUriMapper;
    private final CustomEventRepository nonRecurringRepository;
    private final ServiceAgreementRepository agreementRepository;
    private final AppointmentTypeRepository appointmentTypeRepository;
    private final RateCardRepository rateCardRepository;
    private final UnitOfMeasurementRepository unitOfMeasurementRepository;
    private final EvidenceSupportWorkRepository supportWorkRepository; // TODO should be abstracted to 'canResetRecurrence'
    private final EvidenceSupportActionRepository supportActionRepository;

    private final ServiceRecipientRotaDecorator<RecurrenceResource> recurrenceDecorator;

    @PersistenceContext
    protected EntityManager entityManager;

    protected RotaServiceImpl() {
        this(null, null, null, null, null, null, null, null, null, null, null, null, null, null);
        // for CGLIB
    }

    public RotaServiceImpl(DemandScheduleRepository demandScheduleRepository,
                           DemandScheduleDirectTaskRepository demandScheduleDirectTaskRepository,
                           CalendarEventSnapshotRepository calendarEventSnapshotRepository,
                           ServiceRecipientRepository serviceRecipientRepository,
                           CalendarService calendarService,
                           EntityUriMapper entityUriMapper,
                           CustomEventRepository nonRecurringRepository,
                           ServiceAgreementRepository agreementRepository,
                           AppointmentTypeRepository appointmentTypeRepository,
                           RateCardRepository rateCardRepository,
                           UnitOfMeasurementRepository unitOfMeasurementRepository,
                           EvidenceSupportWorkRepository supportWorkRepository,
                           EvidenceSupportActionRepository supportActionRepository,
                           ServiceRecipientRotaDecorator<RecurrenceResource> recurrenceDecorator) {
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.demandScheduleRepository = demandScheduleRepository;
        this.demandScheduleDirectTaskRepository = demandScheduleDirectTaskRepository;
        this.calendarEventSnapshotRepository = calendarEventSnapshotRepository;
        this.calendarService = calendarService;
        this.entityUriMapper = entityUriMapper;
        this.nonRecurringRepository = nonRecurringRepository;
        this.agreementRepository = agreementRepository;
        this.appointmentTypeRepository = appointmentTypeRepository;
        this.rateCardRepository = rateCardRepository;
        this.unitOfMeasurementRepository = unitOfMeasurementRepository;
        this.supportWorkRepository = supportWorkRepository;
        this.supportActionRepository = supportActionRepository;
        this.recurrenceDecorator = recurrenceDecorator;
    }

    public static class RecurrenceResource extends RepresentationModel<RecurrenceResource> {
    }

    private void recurrenceDecorated(Recurrence r) {
        RecurrenceResource resource = new RecurrenceResource();
        var component = ServiceRecipientEventDecorator.getEntityComponents(entityUriMapper, r.getManagedBy());
        if (component != null && DemandSchedule.class.isAssignableFrom(component.getEntityClass())) {
            var scheduleId = component.getId();
            this.recurrenceDecorator.rotaEventLinks(resource, scheduleId,
                    r.getStart().toLocalDateTime(), r.getEnd().toLocalDateTime());
        }
        r.copyLinks(resource.getLinks().toList());
    }

    @Override
    public Stream<Recurrence> findRecurrences(RecurringEntryHandle handle, Range<Instant> interval, Recurrence.@Nullable Status status) {
        return calendarService.findRecurrences(handle, interval, status)
                .peek(this::recurrenceDecorated);
    }

    @Override
    public Stream<Recurrence> findRecurrencesFromCalendar(String calendarId, Interval interval) {
        return calendarService.findRecurrencesFromCalendar(calendarId, interval)
                .peek(this::recurrenceDecorated);
    }

    Stream<Recurrence> findRecurrenceExceptions(RecurringEntryHandle handle, Range<Instant> interval) {
        return calendarService.findRecurrenceExceptions(handle, interval);
    }

    Stream<Recurrence> findModifiedRecurrences(RecurringEntryHandle handle, LocalDate equalOrAfter) {
        return calendarService.findModifiedRecurrences(handle, equalOrAfter);
    }

    @Override
    public Availability findAvailability(String calendarId, Interval interval, boolean baselineOnly) {
        return calendarService.findAvailability(calendarId, interval, baselineOnly);
    }

    /**
     * @param serviceRecipientFilter optional handle to filter to a given calendar e.g. "building:1204"
     */
    @Override
    public Rota fetchRota(RotaHandler handler, LocalDate startDate, LocalDate endDate, String demandedResourceFilter, String serviceRecipientFilter,
                          boolean loadResource, boolean loadDemand) {
        Rota rota = new Rota(startDate, endDate, demandedResourceFilter, serviceRecipientFilter, loadResource, loadDemand);
        handler.populateRota(rota);
        return rota;
    }

    @Override
    public List<Integer> findAllAgreementSrIdsByDemandAndScheduleDate(RotaHandler handler, String demandedResourceFilter, String serviceRecipientFilter,
                                                                           LocalDate startDate, LocalDate endDate) {
        var params = new RotaParams(startDate, endDate, demandedResourceFilter, serviceRecipientFilter);
        return handler.findAllAgreementSrIdsByScheduleDate(params);
    }

    /**
     * Find agreements - typically for ad-hoc
     */
    @Override
    public List<ServiceAgreement> findAllAgreementsByDemandAndScheduleDate(RotaHandler handler, String demandedResourceFilter, String serviceRecipientFilter,
                                                                           LocalDate startDate, LocalDate endDate) {
        var params = new RotaParams(startDate, endDate, demandedResourceFilter, serviceRecipientFilter);
        return handler.findAllAgreementsByScheduleDate(params);
    }

    @Override
    public void splitActivity(RecurrenceHandle activityRecurrenceRecurrenceHandle, LocalTime splitPoint) {
        log.error("TODO: Split referral #" + activityRecurrenceRecurrenceHandle + " at period " + splitPoint); // TODO
    }

    /**
     * We no longer update a schedule - see comments in DemandSchedule#valid. Its safer to rely on the schedule as always
     * being accurate over all time, and consistent within calendaring rules etc - and its better for the user to be able
     * to see an 'applicable start' date instead of a start date that would only refer to the agreement date.
     * So we now end one schedule and clone it to create another, but to the user it appears as an edit.
     * This makes our schedule history more like our evidence history - any change generates a new schedule.
     */
    @Override
    public void splitAppointmentSchedule(@NonNull DemandSchedule origSchedule,
                                         @NonNull RecurringEntryChangeDefinition scheduleChange) {
        splitAppointmentSchedule(origSchedule, scheduleChange, null, null, null);
    }

    @Override
    public void splitAppointmentSchedule(@NonNull DemandSchedule origSchedule,
                                         @NonNull RecurringEntryChangeDefinition scheduleChange,
                                         int serviceRecipientId,
                                         @NonNull Individual author,
                                         @NonNull DemandScheduleDirectTaskDefinitions tasksChange) {
        var allTasksHandlesForPostSplitSchedule = handleDirectTasks(origSchedule, scheduleChange.getApplicableFromDate(), serviceRecipientId, author, tasksChange);
        splitAppointmentSchedule(origSchedule, scheduleChange, allTasksHandlesForPostSplitSchedule, null, null);
    }

    @NonNull
    private Set<DirectTaskHandle> handleDirectTasks(@NonNull DemandSchedule origSchedule, LocalDate applicableFromDate,
                                                    int serviceRecipientId, @NonNull Individual author,
                                                    @NonNull DemandScheduleDirectTaskDefinitions tasksChange) {
        var modifiedTaskHandles = getModifiedDirectTaskHandles(tasksChange);
        createModifiedDirectTasks(author, tasksChange, serviceRecipientId, applicableFromDate);
        var deletedTaskHandles = tasksChange.deletions;
        var existingTaskHandlesNotDeleted = demandScheduleDirectTaskRepository.findAllByScheduleId(origSchedule.getId())
                .stream()
                .filter(t -> !deletedTaskHandles.contains(DirectTaskHandle.fromUuid(t.getTaskInstanceId())))
                .map(t -> DirectTaskHandle.fromUuid(t.getTaskInstanceId())).toList();
        var allApplicableTasksHandles = new HashSet<>(modifiedTaskHandles);
        allApplicableTasksHandles.addAll(existingTaskHandlesNotDeleted);
        return allApplicableTasksHandles;
    }

    /*
    // TODO maintain care plan for pre-split smart steps now left behind
    private void truncateDirectTasks(Individual author, @Nonnull DemandScheduleDirectTaskDefinitions tasksChange, int serviceRecipientId, LocalDate applicableFromDate) {
        //      var tasksHandlesPreSplit = getDirectTaskHandlesPreSplit(tasksChange);
        //      - 'no-longer-relevant' deleted ones (at applicable date)
        //      - 'no-longer-relevant' modified ones (at applicable date)
        //      - maybe put applicablefrom date on DemandScheduleDirectTask for quicker access to data from care plan?
    }*/

    private void createModifiedDirectTasks(Individual author, @NonNull DemandScheduleDirectTaskDefinitions tasksChange,
                                           int serviceRecipientId, LocalDate applicableFromDate) {
        var modifiedTaskHandles = getModifiedDirectTaskHandles(tasksChange);
        if (modifiedTaskHandles.size() > 0) {

            // create work item for the new support instances - so the app picks them up and records work there
            EvidenceSupportWork work = createWork(author, serviceRecipientId, applicableFromDate);
            entityManager.persist(work);

            // create new tasks (actionInstanceUuids) if new or modified, at applicable date
            var addUpdated = new ArrayList<DemandScheduleDirectTaskDefinition>();
            addUpdated.addAll(tasksChange.additions);
            addUpdated.addAll(tasksChange.updates);
            addUpdated
                .forEach(t -> {
                    var taskSmartStep = EvidenceSupportAction.builder(serviceRecipientId, DirectTaskHandle.getAsUuid(t.getTaskInstanceId()),
                                    null, t.getTaskDefId().longValue())
                            .withStatus(EvidenceAction.isRelevant)
                            .withStatusChange(true)
                            .withGoalName(t.getTaskText())
                            .withGoalPlan(t.getTaskDescription())
                            .build();
                    taskSmartStep.setWork(work);
                    taskSmartStep.setWorkDate(work.getWorkDate());
                    supportActionRepository.save(taskSmartStep);
            });
        }
    }

    /**
     * Create a work item hardcoded to 'carePlan' and NEEDS.
     */
    private EvidenceSupportWork createWork(Individual author, int serviceRecipientId, LocalDate applicableFromDate) {
        // NOTE: This will be null for non-Referral evidence
        var sr = serviceRecipientRepository.findOne(serviceRecipientId);
        var contactId = Objects.requireNonNull(sr.getContact()).getId();
        EvidenceTask task = EvidenceTask.CARE_PLAN;
        EvidenceGroup grp = EvidenceGroup.NEEDS;//taskDefinitionService.findGroupFromGroupName(params.evidenceGroupKey);
        SupportEvidenceBuilder builder = new SupportEvidenceBuilder(serviceRecipientId);
        builder
                //.setChild(getChildAsRef(childServiceRecipientId))
                .fromSource(task, grp)
                .setId(UUID.randomUUID())
                .withWorkDate(applicableFromDate.toDateTimeAtStartOfDay())
                .withCreatedDate(DateTime.now().toDateTime(DateTimeZone.UTC))
                .withAuthor(author)
                .withContact(contactId);
        return builder.build();
    }

    @NonNull
    private Set<DirectTaskHandle> getModifiedDirectTaskHandles(@NonNull DemandScheduleDirectTaskDefinitions tasksChange) {
        var addedHandles = tasksChange.additions.stream()
                .map(DemandScheduleDirectTaskDefinition::getTaskInstanceId)
                .collect(Collectors.toList());
        var changedHandles = tasksChange.updates.stream()
                .map(DemandScheduleDirectTaskDefinition::getTaskInstanceId)
                .toList();
        addedHandles.addAll(changedHandles);
        return new HashSet<>(addedHandles);
    }

    /**
     * @param origSchedule   the schedule we are truncating to the split date
     * @param scheduleChange the additional changes to apply to the new schedule after the split date
     * @param applyParentId  (additionalStaff/child schedules only) set the correct parentId on the child
     * @param truncateOnly   (additionalStaff/child schedules only) determine whether we should truncateOnly or split
     */
    private void splitAppointmentSchedule(@NonNull DemandSchedule origSchedule,
                                          @NonNull RecurringEntryChangeDefinition scheduleChange,
                                          @Nullable Set<DirectTaskHandle> taskDirectInstanceIds,
                                          @Nullable Long applyParentId,
                                          @Nullable Boolean truncateOnly) {

        var splitDate = scheduleChange.getApplicableFromDate();

        // RESET recurrences after the splitDate for the original schedule (because its going to be truncated)
        var demandScheduleHandle = calendarService.getRecurringEntrySeriesHandle(origSchedule.getRecurringEntryHandle());
        resetRecurrencesFrom(demandScheduleHandle, splitDate);

        // DELETE original schedule if split date equals schedule start date
        var deleteOrig = splitDate.equals(origSchedule.getStart());
        // pass the previousScheduleId to the new schedule (don't FK to the one we are deleting)
        var previousScheduleId = deleteOrig ? origSchedule.getPreviousScheduleId() : origSchedule.getId();

        // CREATE new schedule from the split date using the original endDate
        var afterSplitShallowClone = Boolean.TRUE.equals(truncateOnly)
                ? null
                : addOneAppointmentScheduleFromSplit(origSchedule.instantiateCopy(), origSchedule.getEnd(), previousScheduleId, scheduleChange, taskDirectInstanceIds, applyParentId);
        var afterSplitParentId = afterSplitShallowClone == null ? null : afterSplitShallowClone.getId();

        // TRUNCATE recurrences after the 'CREATE new schedule' above (so we don't copy over the end date when cloning via the truncate method)
        var origEndDate = origSchedule.getEnd();

        if (!deleteOrig) {
            // Normal case: truncate the schedule to end the day before the split date
            // TODO be careful its not before the start date, which can happen if re-amending a schedule
            //  eg split long-running schedule tomorrow (eg time change) - truncates to applicableDate-1 (today) with new schedule starting tomorrow,
            //  then split again for tomorrow - splits short-running schedule to truncate tomorrow-1 (today) but it started tomorrow!
            origSchedule.truncate(splitDate.minusDays(1));
            // NB .save() returns the updated schedule
            demandScheduleRepository.save(origSchedule);
        }

        // CHILDREN - add/remove
        var childrenIds = demandScheduleRepository.findIdsByParentScheduleId(origSchedule.getId());
        var oldAdditionalStaff = childrenIds.size();
        var newAdditionalStaff = scheduleChange.getAdditionalStaff() == null ? oldAdditionalStaff : scheduleChange.getAdditionalStaff();

        // split existing children in the same way (NB we don't have children of children, its just one level)
        if (!childrenIds.isEmpty()) {
            int removeAdditionalStaff = oldAdditionalStaff - newAdditionalStaff;
            for (int i = 0; i < childrenIds.size(); i++) {
                var child = demandScheduleRepository.findOne(childrenIds.get(i));
                // apply all the same changes to the child - for now we assume they are clones (except for allocations)
                // (if we want specific differences, then we'd need to amend the child only BUT then it would get overwritten if the parent changed again)
                // NB we leave off duplicating tasksDirect on additional staff - we assume workers overlap exactly and don't split tasks
                splitAppointmentSchedule(child, scheduleChange, null, afterSplitParentId, removeAdditionalStaff > i);
            }
        }

        // if there are more additionalStaff to add (and we are dealing with the parent)
        // then create the additional staff using the original parent schedule, retaining the original end date before it was updated
        if (applyParentId == null) {
            var addAdditionalStaff = newAdditionalStaff - oldAdditionalStaff;
            while (addAdditionalStaff > 0) {
                // NB we leave off duplicating tasksDirect on additional staff - we assume workers overlap exactly and don't split tasks
                addOneAppointmentScheduleFromSplit(origSchedule.instantiateCopy(), origEndDate, previousScheduleId, scheduleChange, null, afterSplitParentId);
                addAdditionalStaff--;
            }
        }

        // When splitting at the schedule start date, just remove the original schedule entirely
        // instead of truncating it to an invalid end date (splitDate.minusDays(1) would be before start)
        if (deleteOrig) {
            // NB this deletes any ical_series entries, via .delete
            // NB we've removed any children first - which avoids a FK on parentScheduleId
            // NB we've handed over the previousScheduleId to the new schedule
            origSchedule.delete(); // NB this does a flush!
        }
    }

    private AppointmentSchedule addOneAppointmentScheduleFromSplit(@NonNull AppointmentSchedule shallowClone,
                                                                   @Nullable LocalDate origScheduleEnd,
                                                                   @Nullable Long previousScheduleId,
                                                                   @Nullable RecurringEntryChangeDefinition scheduleChange,
                                                                   @Nullable Set<DirectTaskHandle> taskDirectInstanceIds,
                                                                   @Nullable Long applyParentId) {
        // CREATE based on a copy of the existing schedule
        updateCommonProperties(scheduleChange, shallowClone);
        if (applyParentId != null) {
            shallowClone.setParentScheduleId(applyParentId);
        }

        // new schedule starts from applicableFromDate
        shallowClone.setStart(scheduleChange.getApplicableFromDate());

        // added (1e4f741c, Oct 2020) when wanting to update additional staff
        // but we should (as we do here) be overwriting the end date regardless, even if its null
        shallowClone.setEnd(origScheduleEnd);

        shallowClone.setPreviousScheduleId(previousScheduleId);

        // creates the TENTATIVE recurring entry for this entity through the base class createOrUpdateRecurringCalendarEvent
        AppointmentSchedule schedule = demandScheduleRepository.save(shallowClone);

        linkScheduleToDirectTask(taskDirectInstanceIds, schedule);

        return schedule;
    }

    private void resetRecurrencesFrom(@NonNull RecurringEntryHandle handle, LocalDate resetDate) {
        // CHECK
        // Find concrete recurrences - which represents user modifications. Concrete recurrences are modifications to the
        // recurring schedules. They may be CONFIRMED visits, and they may have evidence associated. We can reset them back
        // to the recurrence using CalendarService.resetRecurrence which removes the entry and any associations to calendars
        // whether CONFIRMED, with evidence or not.
        //      NB We could show the user a preview of these before resetting, perhaps by loading the calendarService.findConcreteRecurrenceItems
        //      to show all modifications (which will include CONFIRMED), and whether evidence are attached.
        //      The evidence can be found in supportWorkRepository.findUuidByEventId (or EventServiceImpl.getEntry(uid)).
        //      Also see EventService.getCalendars.
        findModifiedRecurrences(handle, resetDate)
            .forEach(recurrence -> {
                UUID workUuid = supportWorkRepository.findUuidByEventId(recurrence.getRecurrenceHandle().toString());
                if (workUuid != null) {
                    throw new IllegalStateException("Cannot split a schedule which has evidence attached: " + workUuid);
                }
                // remove the recurrence, but only if we are safe to do so - otherwise bubble up the FK issue
                // this check is also captured in 'resetRecurrence' but doesn't provide the opportunity to delete
                deleteEventStatus(recurrence.getRecurrenceHandle());
                calendarService.resetRecurrence(recurrence.getRecurrenceHandle());
            });
    }

    private void deleteEventStatus(RecurrenceHandle recurrence) {
        val existingStatus = calendarEventSnapshotRepository.findOneByEventUid(recurrence.toString());
        if (existingStatus.isPresent()) {
            if (existingStatus.get().canReset()) {
                calendarEventSnapshotRepository.delete(existingStatus.get());
            } else {
                throw new IllegalStateException("Cannot split a schedule which has snapshot data attached: " + recurrence);
            }
        }
    }

    /**
     * Create a schedule of appointments.
     * Additional schedules are created for 'additionalStaff' - see addAppointmentSchedule,
     * where the additional schedules will have a parentScheduleId.
     */
    public void addOneAppointmentSchedule(@NonNull RecurringEntryChangeDefinition scheduleChange,
                                          @Nullable DemandSchedule parent, int countRemaining,
                                          URI updatedBy,
                                          int serviceRecipientId,
                                          @NonNull Individual author,
                                          @NonNull DemandScheduleDirectTaskDefinitions tasksChange) {
        var applicableFrom = scheduleChange.getApplicableFromDate() != null
                ? scheduleChange.getApplicableFromDate() : scheduleChange.getStart();
        createModifiedDirectTasks(author, tasksChange, serviceRecipientId, applicableFrom);
        var tasksHandlesForPostSplitSchedule = getModifiedDirectTaskHandles(tasksChange);
        addOneAppointmentSchedule(scheduleChange, parent, countRemaining, updatedBy, tasksHandlesForPostSplitSchedule);
    }

    @Override
    public void addOneAppointmentSchedule(@NonNull RecurringEntryChangeDefinition scheduleChange,
                                          @Nullable DemandSchedule parent, int countRemaining,
                                          URI updatedBy) {
        addOneAppointmentSchedule(scheduleChange, parent, countRemaining, updatedBy, null);
    }

    private void addOneAppointmentSchedule(@NonNull RecurringEntryChangeDefinition scheduleChange,
                                           @Nullable DemandSchedule parent, int countRemaining,
                                           URI updatedBy, @Nullable Set<DirectTaskHandle> taskDirectInstanceIds) {

        AppointmentSchedule entity = new AppointmentSchedule();
        UnitOfMeasurement unitOfMeasurementMinutes = unitOfMeasurementRepository.findOne(UnitOfMeasurementRepository.BASEDATA_MINUTE);
        entity.setAgreedDurationUnitMeasurement(unitOfMeasurementMinutes);

        // just for clarity, set all days false in new entity
        if (entity.getId() == null) {
            entity.setDays(new DaysOfWeek()); // All false by default
        }

        updateCommonProperties(scheduleChange, entity);

        // TODO check adHoc on the server-side?
        if (Boolean.TRUE.equals(scheduleChange.isAdHoc())) {
            entity.setEnd(entity.getStart());
            entity.setIntervalType("WK");
            entity.setIntervalFrequency(1);
        }

        // assign the parent if there is one
        if (parent != null) {
            entity.setParentScheduleId(parent.getId());
        }

        // creates the TENTATIVE recurring entry for this entity through the base class createOrUpdateRecurringCalendarEvent
        entity = demandScheduleRepository.save(entity);
        // previously, entityService.setManagedEntity did a flush
        // which is needed because the entity (DemandSchedule) PrePersist calls the underlying cosmo contentDao.createContent which uses flush()
        entityManager.flush();

        linkScheduleToDirectTask(taskDirectInstanceIds, entity);

        // NB because ad-hoc was specifically removed in the previous commit and the 'range' modified
        // it means that the range is no longer the same (end wasn't minus(1) before) and therefore we
        // are required to fix things to make the tests pass

        // this handler works for any allocation - care runs, workers etc, but we only handle direct assignment of a
        // schedule via ad-hoc for workers currently, and for one schedule (parent only) for now in case our ui does something crazy
        // and because it should use the commented code below but that causes errors for ad-hoc - albeit they are resolved in the previous commit (but only tested on endtoend failure)
        if (scheduleChange.getResourceCalendarId() != null && parent == null) {

            // NB we separate logic of ad-hoc and not, because its simpler for now to copy what exists elsewhere
            //  BUT note that previous commits show some work towards combining the logic
            //  See "DEV-2507 Schedule to assign to carer"...
            if (Boolean.TRUE.equals(scheduleChange.isAdHoc())) {
                // set the worker as an attendee and both as CONFIRMED
                // ad-hoc resource/appointment demand allocation straight to a resource that fulfills it
                var demandScheduleHandle = calendarService.getRecurringEntrySeriesHandle(entity.getRecurringEntryHandle());
                calendarService.confirmRecurrencesInRange(demandScheduleHandle, entity.getRange(),
                        null, updatedBy, null, null, scheduleChange.getResourceCalendarId());
            } else {
                // set the worker as an attendee and both as CONFIRMED
                // ad-hoc resource/appointment demand allocation straight to a resource that fulfills it
                var demandScheduleHandle = calendarService.getRecurringEntrySeriesHandle(entity.getRecurringEntryHandle());

                // NB fails when range was entity.getRange() with no end date - which was okay for ad-hoc
                // however, we can use the same logic as ARecurringActionCHandler
                // NB ALSO SEE ARecurringActionCHandler
                val start = JodaToJDKAdapters.localDateToJDk(entity.getStart()).atStartOfDay(EccoTimeUtils.LONDON).toInstant();
                val minEnd = JodaToJDKAdapters.localDateToJDk(entity.getEnd());
                val range = minEnd == null
                        ? Range.atLeast(start)
                        : Range.closedOpen(start, minEnd.plusDays(1).atStartOfDay(EccoTimeUtils.LONDON).toInstant());
                calendarService.confirmRecurrencesInRange(demandScheduleHandle, range,
                        null, updatedBy, null, null, scheduleChange.getResourceCalendarId());
            }
        }
        // for ad-hoc and non-ad-hoc, this code should be used, but fails a test - see previous commit
        /*if (scheduleChange.getResourceCalendarId() != null && parent == null) {
            // set the worker as an attendee and both as CONFIRMED
            // ad-hoc resource/appointment demand allocation straight to a resource that fulfills it
            var demandScheduleHandle = calendarService.getRecurringEntrySeriesHandle(entity.getRecurringEntryHandle());

            // NB fails when range was entity.getRange() with no end date - which was okay for ad-hoc
            // however, we can use the same logic as ARecurringActionCHandler
            // NB ALSO SEE ARecurringActionCHandler
            val start = JodaToJDKAdapters.localDateToJDk(entity.getStart()).atStartOfDay(EccoTimeUtils.LONDON).toInstant();
            val minEnd = JodaToJDKAdapters.localDateToJDk(entity.getEnd());
            val range = minEnd == null
                    ? Range.atLeast(start)
                    : Range.closedOpen(start, minEnd.plusDays(1).atStartOfDay(EccoTimeUtils.LONDON).toInstant());
            calendarService.confirmRecurrencesInRange(demandScheduleHandle, range,
                    null, updatedBy, null, null, scheduleChange.getResourceCalendarId());
        }*/

        // if we are asking for more than one staff member to be on this visit then we duplicate the schedule
        if (countRemaining > 0) {
            // carry the same first parent through the duplicates - children will be siblings not grand-father->father->son.
            addOneAppointmentSchedule(scheduleChange, parent == null ? entity : parent, countRemaining - 1, updatedBy);
        }
    }

    private void linkScheduleToDirectTask(@Nullable Set<DirectTaskHandle> taskDirectInstanceIds, AppointmentSchedule entity) {
        if (taskDirectInstanceIds != null) {
            var tasks = taskDirectInstanceIds.stream().map(i -> {
                var t = new DemandScheduleDirectTask();
                t.setScheduleId(entity.getId());
                t.setTaskInstanceId(DirectTaskHandle.getAsUuid(i));
                return t;
            }).collect(Collectors.toList());
            demandScheduleDirectTaskRepository.saveAll(tasks);
        }
    }

    private void updateCommonProperties(RecurringEntryChangeDefinition scheduleChange,
                                        AppointmentSchedule entity) {

        if (scheduleChange.getAgreementId() != null) {
            ServiceAgreement serviceAgreement = agreementRepository.findById(scheduleChange.getAgreementId().longValue()).orElseThrow(NullPointerException::new);
            entity.setAgreement(serviceAgreement);
        }

        if (scheduleChange.getAppointmentTypeId() != null) {
            entity.setAppointmentTypeId(scheduleChange.getAppointmentTypeId().longValue());
        }

        if (scheduleChange.getDurationMins() != null) {
            entity.setAgreedDurationUnits(scheduleChange.getDurationMins());
        }

        if (scheduleChange.getTasks() != null) {
            entity.getParameters().put("tasks", scheduleChange.getTasks());
        }

        if (scheduleChange.getRateCardId() != null) {
            entity.setRateCard(scheduleChange.getRateCardId().value == null
                    ? null
                    : rateCardRepository.getReferenceById(scheduleChange.getRateCardId().value));
        }

        if (scheduleChange.getStart() != null) {
            entity.setStart(scheduleChange.getStart());
        }
        if (scheduleChange.getStartTime() != null) {
            entity.setTime(scheduleChange.getStartTime());
        }

        if (!Boolean.TRUE.equals(scheduleChange.isAdHoc())) {

            if (scheduleChange.getEnd() != null) {
                entity.setEnd(scheduleChange.getEnd());
            }

            entity.setIntervalType(scheduleChange.getIntervalType() != null ? scheduleChange.getIntervalType() : "WK");

            // specify 1 even for non-weekly, because it's safe and does imply a value eg 'every year' etc
            entity.setIntervalFrequency(scheduleChange.getIntervalFrequency() != null ? scheduleChange.getIntervalFrequency() : 1);

            if (scheduleChange.getIntervalType() == null || scheduleChange.getIntervalType().equals("WK")) {
                if (scheduleChange.getDaysAdded() != null && scheduleChange.getDaysAdded().size() > 0) {
                    for (int day : scheduleChange.getDaysAdded()) {
                        entity.getDays().setCalendarDay(day, true);
                    }
                }
                if (scheduleChange.getDaysRemoved() != null && scheduleChange.getDaysRemoved().size() > 0) {
                    for (int day : scheduleChange.getDaysRemoved()) {
                        entity.getDays().setCalendarDay(day, false);
                    }
                }
            }
        }
    }

    @Override
    public void rescheduleActivity(RecurrenceHandle recurrence, @Nullable String newTitle, @Nullable LocalDateTime newDateTime,
                                   @Nullable Integer newDurationMins, URI updatedBy) {
        var demandScheduleHandler = calendarService.getRecurringEntrySeriesHandle(
                calendarService.getEntryHandleFromRecurrenceHandle(recurrence));
        final DemandSchedule schedule = demandScheduleRepository.findOneByEntryHandleAsString(demandScheduleHandler.toString()).orElseThrow();
        Range<LocalDate> recurrenceBounds = schedule.getEnd() != null
            ? Range.closedOpen(schedule.getStart(), schedule.getEnd())
            : Range.atLeast(schedule.getStart());
        calendarService.rescheduleRecurrence(recurrence, newTitle, newDateTime, newDurationMins, updatedBy, recurrenceBounds);
    }

    @Override
    public void dropActivity(int serviceRecipientId, RecurrenceHandle recurrence, Integer reasonId) {
        try {
            // flush is called after, so we need to drop first
            deleteEventStatus(recurrence);
            calendarService.dropRecurrence(recurrence);
        } catch (CalendarException cal) {
            throw new RotaException("Could not cancel activity " + recurrence);
        }

        // create an entry to record the status rate (eg chargeable dropped reason)
        CustomEventImpl event = nonRecurringRepository.findOneByUid(recurrence.toString())
                .orElse(EventEntryAssembler.setUpNewCalendarEntry(serviceRecipientId, recurrence.toString()));
        event.setEventStatusRateId(reasonId);
        nonRecurringRepository.save(event); // Avoids sync to Cosmo as we don't update the calendar when doing planned work
        // flush is needed here
        entityManager.flush();
    }

    @Override
    public void reinstateActivity(RecurrenceHandle recurrence) {
        try {
            calendarService.reinstateRecurrence(recurrence);
        } catch (CalendarException cal) {
            throw new RotaException("Could not reinstate activity " + recurrence);
        }
        // remove any local information - such as the dropped reason
        nonRecurringRepository.deleteByUid(recurrence.toString());
    }

    // We keep this method for the purposes of knowing there is a flush required (or was before removing the aop code)
    // although now this has now been merged back into allocateActivitiesInRange
    @Override
    public void allocateAndMaybeRescheduleActivity(RotaHandler handler, @Nullable URI updatedBy, Integer allocateResourceId,
                                                   RecurrenceHandle recurrenceHandle, java.time.LocalDateTime adjustedStart) {
        if (adjustedStart != null) {
            var demandScheduleHandle = calendarService.getRecurringEntrySeriesHandle(
                    calendarService.getEntryHandleFromRecurrenceHandle(recurrenceHandle));
            final DemandSchedule schedule = demandScheduleRepository.findOneByEntryHandleAsString(demandScheduleHandle.toString()).orElseThrow();
            Range<LocalDate> scheduleBounds = schedule.getEnd() != null
                    ? Range.closedOpen(schedule.getStart(), schedule.getEnd())
                    : Range.atLeast(schedule.getStart());
            calendarService.rescheduleRecurrence(recurrenceHandle, null, localDateTimeToJoda(adjustedStart), null, updatedBy, scheduleBounds);
            entityManager.flush(); // Otherwise we end up allocating at the old start time
        }
        calendarService.confirmRecurrence(recurrenceHandle, updatedBy, handler.getResourceCalendarId(allocateResourceId));
    }

    // NB The equivalent rota wrapper for 'allocateActivities' isn't here, calendarService is called directly (eg AppointmentRecurringActionCommandHandler)
    //      what is here is the single allocation method, allocateAndMaybeRescheduleActivity
    @Override
    public int deallocateActivitiesFrom(RotaHandler handler, RecurringEntryHandle recurringEntryHandle, Range<Instant> range,
                                        com.ecco.calendar.core.@Nullable DaysOfWeek matchDays, @Nullable URI updatedBy,
                                        Integer deallocateResourceId) {
        return calendarService.unConfirmRecurrencesInRange(recurringEntryHandle, range, matchDays, updatedBy, handler.getResourceCalendarId(deallocateResourceId));
    }

    @Override
    public String findCalendarIdFromItemId(String itemId) {
        return calendarService.findCalendarIdFromItemId(itemId);
    }

    @Override
    public void addDemandWithStatuses(RotaParams params, AppointmentCollection rota, EnumSet<Recurrence.Status> recurrenceStatuses,
                                      BooleanExpression wherePredicate) {

        var cacheKey = wherePredicate.toString() + recurrenceStatuses.toString(); // But how do we flush!
//        log.info("FIXME: Cache this: getting demandSchedules for: " + wherePredicate); // 50ms on Ryzen 2600
        Iterable<DemandSchedule> demandSchedules = demandScheduleRepository.findAll(wherePredicate);

        // DEBUG
        //var demandSchedules = StreamSupport.stream(demandSchedulesAll.spliterator(), false)
        //        .filter(ds -> ds.getId().equals(107152L)).collect(Collectors.toList());

        for (DemandSchedule demandSchedule : demandSchedules) {
            log.debug("Processing recurrences for {}: {}",
                    demandSchedule.getAgreement().getServiceRecipient().displayName,
                    demandSchedule.getRecurringEntryHandle());

            var demandScheduleHandle = demandSchedule.getRecurringEntryHandle();

            if (recurrenceStatuses.contains(Recurrence.Status.TENTATIVE) || recurrenceStatuses.contains(CONFIRMED)) {
                // NB the location/address for a visit comes from:
                //      inside findRecurrences -> recurrenceDecorated for rotaEventLinks -> here it calls addDemandRe.. -> new view model with sr address
                //      the address is the latest, since updateContactAddress matches them up
                // These appointments were scheduled for today, so check the calendar to see if they are still valid
                findRecurrences(demandScheduleHandle, // FIXME: Take an array of handles from above??
                        params.getInterval(), null)
                        .filter( // HACK due to Cosmo expand StandardItemFilterProcessor.java:413 returning items that match end exclusive
                                item -> item.getStart().getMillis() != params.getEndInstant().toEpochMilli()
                        )

                        .forEach(recurrence -> {
                            // show on the rota if the event is TENTATIVE or CONFIRMED
                            // NB error if we are another status
                            addDemandRecurrenceIfAppropriate(rota, demandSchedule, recurrence, recurrenceStatuses);
                        });
            }

            // DROPPED appointments are just exceptions to the recurring entry
            if (recurrenceStatuses.contains(Recurrence.Status.DROPPED)) {
                // These appointments were scheduled for today, so check the calendar to see if they have any exceptions listed
                findRecurrenceExceptions(demandScheduleHandle, params.getInterval())
                        .forEach(recurrence -> {
                            if (recurrence != null) {
                                rota.addDemandAppointment(demandSchedule, recurrence);
                            }
                        });
            }
        }
    }

    // Allocated appointments are modifications of recurring events in CONFIRMED status, which correspond to having an
    // appointment schedule with a resource attendee. Confirmed occurrences are events with both calendarId's (resource and demand)
    // as attendees - see xHandler.allocateResource.
    public void addRelevantRecurrences(Recurrence recurrence, RotaResourceViewModel rotaEntry,
                                       final Class<? extends DemandSchedule> entityClass,
                                       @Nullable Integer serviceRecipientIdToUseAsAvailability) {

      // We need to get the demandSchedule from the recurrences to process. This can be done by determining the
      // recurringEntry parent for the recurrence (since that will be the demand schedule's handle).
      log.debug("Adding recurrences for srId: {}", serviceRecipientIdToUseAsAvailability);
      // TODO performance - this is not great
      if (recurrence.getStatus() == CONFIRMED || serviceRecipientIdToUseAsAvailability != null) {
//                WAS BooleanExpression predicate = demandSchedule.instanceOf(entityClass)
//                        .and(demandSchedule.entryHandleAsString.eq(recurrence.getRecurringEntryHandle().toString())); var ds = findOne(predicate)
        // FIXME: we could cache this

        var demandScheduleHandle = calendarService.getRecurringEntrySeriesHandle(recurrence.getRecurringEntryHandle());
        demandScheduleRepository.findOneByEntryHandleAsString(demandScheduleHandle.toString())
                .filter(entityClass::isInstance)
                .ifPresent(ds -> {
                    if (ds.getAgreement().getServiceRecipientId().equals(serviceRecipientIdToUseAsAvailability)) {
                        var allocatedResource = findAllocatedResource(recurrence);
                        rotaEntry.addAvailability(recurrence.getStart(), recurrence.getEnd(), allocatedResource);
                    } else if (recurrence.getStatus() == CONFIRMED) {
                        rotaEntry.addAppointment(new RotaAppointmentViewModel(ds, recurrence, false)); // requiredAttributes is emptySet() for Resource
                    } else {
                        log.debug("Skipped non-confirmed recurrence: {}", recurrence.getTitle());
                    }
                });
        }
    }

    /**
     * If recurrence is CONFIRMED, find the resource that's allocated via the attendees
     */
    @Nullable
    private String findAllocatedResource(Recurrence recurrence) {
        return recurrence.getStatus() == CONFIRMED
                ? recurrence.getAttendees().stream()
//                .filter(a -> !a.getName().equals(ds.getAgreement().getServiceRecipient().displayName))
                // TODO we need some better logic here
                .skip(1)
                .findFirst()
                .map(Attendee::getName).orElse(null)
                : null;
    }

    private void addDemandRecurrenceIfAppropriate(AppointmentCollection rota, DemandSchedule demandSchedule,
                                                  final Recurrence recurrence, EnumSet<Recurrence.Status> activityStates) {
        if (recurrence == null) {
            return;
        }

        switch (recurrence.getStatus()) {
            case TENTATIVE: // FALL-THRU
            case CONFIRMED:
                if (activityStates.contains(recurrence.getStatus())) {
                    rota.addDemandAppointment(demandSchedule, recurrence);
                }
                break;
            case CANCELLED: // FALL-THRU
            case DROPPED:   // FALL-THRU
            default:
                throw new IllegalStateException("Unexpected occurrence state: " + recurrence.getStatus().name());
        }
    }

    @Override
    public DemandSchedule getAppointmentSchedule(String eventRef) {
        RecurringEntryHandle ref = calendarService.getSeriesHandleFromAnyHandle(eventRef);
        return demandScheduleRepository.findOneByEntryHandleAsString(ref.toString()).orElse(null);
        // NB previously we had the below, but is unnecessary
        // we use eventService as the decorator 'decorateCalendarEntry' handles missing id regarding DEV-14
        // CombinedEntry entry = eventService.getEntry(eventRef.toString());
        // Long appointmentScheduleId = entityUriMapper.componentsForEntity(entry.getIcalEntry().getManagedByUri()).getId();
        // return demandScheduleRepository.findById(appointmentScheduleId).orElse(null);
    }

    public DemandSchedule getAppointmentSchedule(Entry entry) {
        // use eventService as the decorator handles missing id regarding DEV-14
        Long appointmentScheduleId = entityUriMapper.componentsForEntity(entry.getManagedByUri()).getId();
        return demandScheduleRepository.findById(appointmentScheduleId).orElse(null);
    }

    @Override
    public void updateAppointmentSchedule(DemandSchedule ds, RecurringEntryChangeDefinition scheduleChange,
                                          Integer serviceRecipientId, @NonNull Individual author,
                                          @NonNull DemandScheduleDirectTaskDefinitions tasksChange) {
        //assert scheduleChange.getStart() != null || scheduleChange.getEnd() != null;

        if (scheduleChange.startChanged() || scheduleChange.endChanged()) {
            updateAppointmentScheduleBoundary(ds, scheduleChange.getStart(), scheduleChange.getEnd(), true);
        }
        updateAppointmentTasks((AppointmentSchedule) ds, scheduleChange, serviceRecipientId, author, tasksChange);
    }

    private void updateAppointmentTasks(AppointmentSchedule ds, RecurringEntryChangeDefinition scheduleChange,
                                        Integer serviceRecipientId, @NonNull Individual author,
                                        @NonNull DemandScheduleDirectTaskDefinitions tasksChange) {
        // this means we can't update to null...but this is meant to be temporary anyway
        if (scheduleChange.getTasks() != null) {
            ds.getParameters().put("tasks", scheduleChange.getTasks());
        }

        // if no changes, then we don't need to un-associate and re-associate
        if (tasksChange.hasChanges()) {
            var applicableFrom = LocalDate.now(); // scheduleChange.getStart();
            // 'handle' - meaning create the 'actions' as required from the 'task' modifications
            // NB the return value is largely meaningless as we need to already have the additions and deletions, which is all we need
            handleDirectTasks(ds, applicableFrom, serviceRecipientId, author, tasksChange);

            if (tasksChange.deletions.size() > 0) {
                tasksChange.deletions.stream().map(DirectTaskHandle::getAsUuid)
                        .forEach(demandScheduleDirectTaskRepository::deleteByTaskInstanceId);
            }
            if (tasksChange.additions.size() > 0) {
                var addUuids = tasksChange.additions.stream().map(DemandScheduleDirectTaskDefinition::getTaskInstanceId).toList();
                linkScheduleToDirectTask(new HashSet<>(addUuids), ds);
            }
        }
    }

    private void updateAppointmentScheduleBoundary(DemandSchedule ds, org.joda.time.LocalDate newStart, org.joda.time.LocalDate newEnd,
                                                   boolean checkChildren) {

        // mimic the jsp which only allowed editing the cost and start/end
        if (newStart != null) {
            // TODO this doesn't update the schedule - it did, if the end date also changed
            ds.setStart(newStart);
            // NOT TIME - see DemandSchedule.verify
        }
        ds.setEnd(newEnd);

        // NB flush is needed to trigger the @PreUpdate on DemandSchedule#verify
        // but it also causes errors with "org.hibernate.HibernateException: Found shared references to a collection: com.ecco.dom.agreements.AppointmentType.requirements"
        // however, removing the 'requirements' property entirely gets the same exception on another collection - so some session issue.
        //em.flush();

        // manually trigger the verify, which is all we need in this scenario since the aop calendar code isn't required
        ds.verify();

        // CHILDREN - update schedules
        if (checkChildren) {
            var childrenIds = demandScheduleRepository.findIdsByParentScheduleId(ds.getId());

            // update existing children in the same way (NB we don't have children of children, its just one level)
            for (Long childrenId : childrenIds) {
                var child = demandScheduleRepository.findOne(childrenId);
                // apply all the same changes to the child - for now we assume they are clones (except for allocations)
                // (if we want specific differences, then we'd need to amend the child only BUT then it would get overwritten if the parent changed again)
                updateAppointmentScheduleBoundary(child, newStart, newEnd, false);
            }
        }
    }

}
