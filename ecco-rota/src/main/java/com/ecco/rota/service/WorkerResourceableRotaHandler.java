package com.ecco.rota.service;

import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dao.ServiceAgreementRepository;
import com.ecco.dom.agreements.AppointmentSchedule;
import com.ecco.dom.hr.WorkerJob;
import com.ecco.dom.hr.WorkerJobServiceRecipient;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.infrastructure.entity.AbstractIntKeyedEntity;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaResourceViewModel;
import com.ecco.service.EventService;
import com.ecco.calendar.core.Availability;
import com.ecco.calendar.core.AvailableInterval;
import org.joda.time.DateTime;
import org.joda.time.Interval;

import java.util.EnumSet;

import static com.ecco.rota.service.WorkerResourceableRotaHandler.WorkerOption.*;
import static java.util.stream.Collectors.toList;

/**
 * Base class for BuildingWorkerRotaHandler or ReferralWorkerRotaHandler
 */
public abstract class WorkerResourceableRotaHandler extends BaseRotaHandler {

    public enum WorkerOption {
        ALLOCATED_APPOINTMENTS,
        AVAILABILITY,
        EVENTS_WITH_CATEGORY
    }

    protected final WorkerJobRepository workerJobRepository;

    public WorkerResourceableRotaHandler(DemandScheduleRepository demandScheduleRepository,
                                         WorkerJobRepository workerJobRepository,
                                         ServiceAgreementRepository serviceAgreementRepository,
                                         RotaService rotaService,
                                         EventService eventService)
    {
        super(rotaService, demandScheduleRepository, serviceAgreementRepository, eventService);
        this.workerJobRepository = workerJobRepository;
    }

    /**
     * NB resourceId is a parameter received from the rota for allocate/deallocate (in the audits),
     * and as such needs to tie in with the resourceId we provide the rota.
     * This is now currently the workerJobId (it was the workerId).
     */
    @Override
    public String getResourceCalendarId(int resourceId) {
        WorkerJob workerJob = workerJobRepository.findById(resourceId).orElseThrow();
        return getCalendarId(workerJob);
    }

    protected String getCalendarId(WorkerJob workerJob) {
        return workerJob.getWorker().getContact().getCalendarId();
    }

    protected RotaResourceViewModel createOrFindRotaEntry(Rota rota, WorkerJob workerJob) {
        return rota.createOrFindResourceEntry(workerJob.getWorker().getContact().getDisplayName(), workerJob.getServiceRecipientId(), WorkerJobServiceRecipient.DISCRIMINATOR);
    }

    /**
     * Allocated appointments are modifications of recurring events in CONFIRMED status, which correspond to an
     * appointment schedule and have a worker attendee.
     *
     * @param rota the rota to which to add the allocated activities
     * @param workerJobs the workers' jobs that need their allocated activities populating
     */
    protected void addWorkersEntries(final Rota rota, final Iterable<WorkerJob> workerJobs,
                                     WorkerOption firstOption, WorkerOption... remainingOptions) {

        EnumSet<WorkerOption> optionsSet = EnumSet.of(firstOption, remainingOptions);
        DateTime start = rota.getStartDate().toDateTimeAtStartOfDay();
        DateTime end = rota.getEndDate().plusDays(1).toDateTimeAtStartOfDay();
        for (WorkerJob workerJob : workerJobs) {
            final String calendarId = getCalendarId(workerJob);
            if (calendarId != null) {
                RotaResourceViewModel rotaEntry = createOrFindRotaEntry(rota, workerJob);
                // the resource
                rotaEntry.setResourceId(workerJob.getId().longValue());
                rotaEntry.setCalendarId(calendarId);

                populateWorkerEntry(start, end, workerJob, rotaEntry, calendarId, optionsSet);
            }
        }
    }

    private void populateWorkerEntry(DateTime start, DateTime end, WorkerJob workerJob, RotaResourceViewModel rotaEntry,
                                     String calendarId, EnumSet<WorkerOption> options) {

        Interval interval = new Interval(start, end);

        if (workerJob.getTextMap() != null) {
            rotaEntry.setSkills(workerJob.getTextMap().get("skills"));
        }

        var provides = workerJob.getServiceRecipient().getProvidesAttributes();
        if (provides != null) {
            rotaEntry.setProvidesAttributes(provides.stream()
                    .map(AbstractIntKeyedEntity::getId)
                    .collect(toList()));
        }

        rotaEntry.setContractedWeeklyHours(workerJob.getContractedWeeklyHours());

        if (options.contains(ALLOCATED_APPOINTMENTS)) {
            rotaService.findRecurrencesFromCalendar(calendarId, interval).forEach(recurrence -> {
                rotaService.addRelevantRecurrences(recurrence, rotaEntry, AppointmentSchedule.class, null);
            });
        }

        if (options.contains(AVAILABILITY)) {
            Availability availability = rotaService.findAvailability(calendarId, interval, false);
            for (AvailableInterval ai : availability.getAvailableIntervals()) {
                rotaEntry.addAvailability(ai.getInterval().getStart(), ai.getInterval().getEnd());
            }
        }

        if (options.contains(EVENTS_WITH_CATEGORY)) {
            addEventsWithCategory(interval, rotaEntry, calendarId);
        }
    }
}
