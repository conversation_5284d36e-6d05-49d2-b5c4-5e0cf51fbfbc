package com.ecco.rota.webApi.dto

import com.ecco.dom.agreements.ServiceAgreement
import java.util.ArrayList
import java.util.HashSet

/**
 * TODO Merge with ServiceRecipientDemandViewModel
 */
class ServiceRecipientDemandDto(
    val serviceRecipientId: Int,
    /** Description of this the serviceRecipient  */
    val description: String,
) {
    val appointments = ArrayList<RotaAppointmentViewModel>()

    val agreements = HashSet<ServiceAgreement>()

    fun addAgreement(serviceAgreement: ServiceAgreement) {
        agreements.add(serviceAgreement)
    }

    fun addAppointment(appointment: RotaAppointmentViewModel) {
        appointments.add(appointment)
    }
}