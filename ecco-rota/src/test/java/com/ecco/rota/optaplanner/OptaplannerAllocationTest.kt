package com.ecco.rota.optaplanner

import com.ecco.rota.optaplanner.domain.*
import org.junit.Assert
import org.junit.Test
import org.optaplanner.core.api.score.buildin.hardsoft.HardSoftScore
import org.optaplanner.core.api.solver.SolverFactory
import java.time.LocalDateTime

class OptaplannerAllocationTest {
    val solverFactory = SolverFactory.createFromXmlResource<RotaProposal>("solverConfiguration.xml")

    @Test
    fun demandAllocatedToSupplyScoresZero() {
        val fixedAllocations = listOf<Allocation>()
        val availability = listOf(
            LocalDateTimeRange(
                LocalDateTime.of(2019, 11, 12, 10, 0),
                LocalDateTime.of(2019, 11, 12, 17, 0),
            ),
        )
        val supplies = listOf(Supply(0, null, availability, listOf()))
        val demand = Demand(
            null,
            null,
            LocalDateTimeRange(
                LocalDateTime.of(2019, 11, 12, 11, 30),
                LocalDateTime.of(2019, 11, 12, 11, 45),
            ),
            listOf(),
        )
        val variableAllocations = listOf(Allocation(demand, null))
        val unsolved = RotaProposal(fixedAllocations, variableAllocations, listOf(), HardSoftScore.ZERO, supplies)

        val solver = solverFactory.buildSolver()
        val solution = solver.solve(unsolved)

        Assert.assertEquals(1, solution.variableAllocations.size)
        Assert.assertEquals(demand, solution.variableAllocations[0].demand)
        Assert.assertEquals(supplies[0], solution.variableAllocations[0].allocatedSupply)
        Assert.assertEquals(0, solution.score.hardScore)
        Assert.assertEquals(0, solution.score.softScore)
    }

    @Test
    fun unallocatableDemandScoresNegative() {
        val fixedAllocations = listOf<Allocation>()
        val supplies = (0L..2L).asSequence()
            .map { id -> Supply(id, null, listOf(), listOf()) }
            .toList()
        val now = LocalDateTime.now()
        val variableAllocations = (0L..4L).asSequence()
            .map { hours -> now.plusHours(hours) }
            .map { start -> LocalDateTimeRange(start, start.plusMinutes(30)) }
            .map { time -> Allocation(Demand(null, null, time, listOf()), null) }
            .toList()
        val unsolved = RotaProposal(fixedAllocations, variableAllocations, listOf(), HardSoftScore.ZERO, supplies)

        val solver = solverFactory.buildSolver()
        val solution = solver.solve(unsolved)

        Assert.assertNotNull(solution.score)
        Assert.assertEquals(-5, solution.score.hardScore)
        Assert.assertEquals(0, solution.score.softScore)
    }

    @Test
    fun fixedAllocationsAreNotChanged() {
        val supply0 = Supply(0, null, listOf(), listOf())
        val supply1 = Supply(
            1,
            null,
            listOf(
                LocalDateTimeRange(
                    LocalDateTime.of(2019, 11, 13, 9, 0),
                    LocalDateTime.of(2019, 11, 13, 17, 0),
                ),
            ),
            listOf(),
        )
        val demand = Demand(
            null,
            null,
            LocalDateTimeRange(
                LocalDateTime.of(2019, 11, 13, 10, 0),
                LocalDateTime.of(2019, 11, 13, 10, 30),
            ),
            listOf(),
        )
        val fixedAllocations = listOf(Allocation(demand, supply0))
        val variableAllocations = listOf<Allocation>()
        val supplies = listOf(supply0, supply1)
        val unsolved = RotaProposal(fixedAllocations, variableAllocations, listOf(), HardSoftScore.ZERO, supplies)

        val solver = solverFactory.buildSolver()
        val solution = solver.solve(unsolved)

        // The demand is allocated to a supply that is not available at the appropriate time.
        // But, since the allocation is a fixed allocation, Optaplanner must not change it.
        Assert.assertEquals(1, solution.fixedAllocations.size)
        Assert.assertEquals(demand, solution.fixedAllocations[0].demand)
        Assert.assertEquals(supply0, solution.fixedAllocations[0].allocatedSupply)

        Assert.assertEquals(0, solution.variableAllocations.size)
        Assert.assertEquals(0, solution.score.softScore)
    }

    @Test
    fun conflictWithFixedAllocationScoresNegative() {
        val supply = Supply(
            0,
            null,
            listOf(
                LocalDateTimeRange(
                    LocalDateTime.of(2019, 11, 13, 9, 0),
                    LocalDateTime.of(2019, 11, 13, 17, 0),
                ),
            ),
            listOf(),
        )
        val fixedDemand = Demand(
            null,
            null,
            LocalDateTimeRange(
                LocalDateTime.of(2019, 11, 13, 10, 0),
                LocalDateTime.of(2019, 11, 13, 10, 30),
            ),
            listOf(),
        )
        val fixedAllocations = listOf(Allocation(fixedDemand, supply))
        val variableDemand = Demand(
            null,
            null,
            LocalDateTimeRange(
                LocalDateTime.of(2019, 11, 13, 10, 15),
                LocalDateTime.of(2019, 11, 13, 10, 45),
            ),
            listOf(),
        )
        val variableAllocations = listOf(Allocation(variableDemand, null))
        val supplies = listOf(supply)
        val unsolved = RotaProposal(fixedAllocations, variableAllocations, listOf(), HardSoftScore.ZERO, supplies)

        val solver = solverFactory.buildSolver()
        val solution = solver.solve(unsolved)

        Assert.assertEquals(-1, solution.score.hardScore)
        Assert.assertEquals(0, solution.score.softScore)
    }

    @Test
    fun conflictingAllocationsScoreNegative() {
        val fixedAllocations = listOf<Allocation>()
        val demand0 = Demand(
            null,
            null,
            LocalDateTimeRange(
                LocalDateTime.of(2019, 11, 13, 10, 0),
                LocalDateTime.of(2019, 11, 13, 10, 30),
            ),
            listOf(),
        )
        val demand1 = Demand(
            null,
            null,
            LocalDateTimeRange(
                LocalDateTime.of(2019, 11, 13, 10, 15),
                LocalDateTime.of(2019, 11, 13, 10, 45),
            ),
            listOf(),
        )
        val variableAllocations = listOf(Allocation(demand0, null), Allocation(demand1, null))
        val supplies = listOf(
            Supply(
                0,
                null,
                listOf(
                    LocalDateTimeRange(
                        LocalDateTime.of(2019, 11, 13, 9, 0),
                        LocalDateTime.of(2019, 11, 13, 17, 0),
                    ),
                ),
                listOf(),
            ),
        )
        val unsolved = RotaProposal(fixedAllocations, variableAllocations, listOf(), HardSoftScore.ZERO, supplies)

        val solver = solverFactory.buildSolver()
        val solution = solver.solve(unsolved)

        Assert.assertEquals(-1, solution.score.hardScore)
        Assert.assertEquals(0, solution.score.softScore)
    }

    @Test
    fun preferToMatchPriorAllocation() {
        val fixedAllocations = listOf<Allocation>()
        val demand = Demand(
            null,
            1,
            LocalDateTimeRange(
                LocalDateTime.of(2020, 4, 30, 10, 0),
                LocalDateTime.of(2020, 4, 30, 10, 30),
            ),
            listOf(),
        )
        val variableAllocations = listOf(Allocation(demand, null))
        val supplies = listOf(
            Supply(
                0,
                null,
                listOf(
                    LocalDateTimeRange(
                        LocalDateTime.of(2020, 4, 30, 9, 0),
                        LocalDateTime.of(2020, 4, 30, 17, 0),
                    ),
                ),
                listOf(),
            ),
            Supply(
                1,
                null,
                listOf(
                    LocalDateTimeRange(
                        LocalDateTime.of(2020, 4, 30, 7, 0),
                        LocalDateTime.of(2020, 4, 30, 15, 0),
                    ),
                ),
                listOf(),
            ),
        )
        val priorDemand = Demand(
            null,
            1,
            LocalDateTimeRange(
                LocalDateTime.of(2020, 4, 23, 10, 0),
                LocalDateTime.of(2020, 4, 23, 10, 30),
            ),
            listOf(),
        )
        val priorSupply = Supply(
            1,
            null,
            listOf(
                LocalDateTimeRange(
                    LocalDateTime.of(2020, 4, 23, 9, 0),
                    LocalDateTime.of(2020, 4, 23, 17, 0),
                ),
            ),
            listOf(),
        )
        val priorAllocations = listOf(Allocation(priorDemand, priorSupply))
        val unsolved = RotaProposal(fixedAllocations, variableAllocations, priorAllocations, HardSoftScore.ZERO, supplies)

        val solver = solverFactory.buildSolver()
        val solution = solver.solve(unsolved)

        Assert.assertEquals(0, solution.score.hardScore)
        Assert.assertEquals(1, solution.score.softScore)
        Assert.assertEquals(1, solution.variableAllocations.size)
        Assert.assertEquals(1L, solution.variableAllocations[0].allocatedSupply?.id)
    }

    @Test
    fun preferToMatchMajorityPriorAllocations() {
        val fixedAllocations = listOf<Allocation>()
        val demand = Demand(
            null,
            1,
            LocalDateTimeRange(
                LocalDateTime.of(2020, 4, 30, 10, 0),
                LocalDateTime.of(2020, 4, 30, 10, 30),
            ),
            listOf(),
        )
        val variableAllocations = listOf(Allocation(demand, null))
        val supplies = listOf(
            Supply(
                0,
                null,
                listOf(
                    LocalDateTimeRange(
                        LocalDateTime.of(2020, 4, 30, 9, 0),
                        LocalDateTime.of(2020, 4, 30, 17, 0),
                    ),
                ),
                listOf(),
            ),
            Supply(
                1,
                null,
                listOf(
                    LocalDateTimeRange(
                        LocalDateTime.of(2020, 4, 30, 7, 0),
                        LocalDateTime.of(2020, 4, 30, 15, 0),
                    ),
                ),
                listOf(),
            ),
        )
        val priorDemand0 = Demand(
            null,
            1,
            LocalDateTimeRange(
                LocalDateTime.of(2020, 4, 23, 10, 0),
                LocalDateTime.of(2020, 4, 23, 10, 30),
            ),
            listOf(),
        )
        val priorSupply0 = Supply(
            1,
            null,
            listOf(
                LocalDateTimeRange(
                    LocalDateTime.of(2020, 4, 23, 9, 0),
                    LocalDateTime.of(2020, 4, 23, 17, 0),
                ),
            ),
            listOf(),
        )
        val priorDemand1 = Demand(
            null,
            1,
            LocalDateTimeRange(
                LocalDateTime.of(2020, 4, 16, 10, 0),
                LocalDateTime.of(2020, 4, 16, 10, 30),
            ),
            listOf(),
        )
        val priorSupply1 = Supply(
            0,
            null,
            listOf(
                LocalDateTimeRange(
                    LocalDateTime.of(2020, 4, 16, 9, 0),
                    LocalDateTime.of(2020, 4, 16, 17, 0),
                ),
            ),
            listOf(),
        )
        val priorDemand2 = Demand(
            null,
            1,
            LocalDateTimeRange(
                LocalDateTime.of(2020, 4, 9, 10, 0),
                LocalDateTime.of(2020, 4, 9, 10, 30),
            ),
            listOf(),
        )
        val priorSupply2 = Supply(
            1,
            null,
            listOf(
                LocalDateTimeRange(
                    LocalDateTime.of(2020, 4, 9, 7, 0),
                    LocalDateTime.of(2020, 4, 9, 12, 0),
                ),
            ),
            listOf(),
        )
        val priorAllocations = listOf(
            Allocation(priorDemand0, priorSupply0),
            Allocation(priorDemand1, priorSupply1),
            Allocation(priorDemand2, priorSupply2),
        )
        val unsolved = RotaProposal(fixedAllocations, variableAllocations, priorAllocations, HardSoftScore.ZERO, supplies)

        val solver = solverFactory.buildSolver()
        val solution = solver.solve(unsolved)

        Assert.assertEquals(0, solution.score.hardScore)
        Assert.assertEquals(2, solution.score.softScore)
        Assert.assertEquals(1, solution.variableAllocations.size)
        Assert.assertEquals(1L, solution.variableAllocations[0].allocatedSupply?.id)
    }
}