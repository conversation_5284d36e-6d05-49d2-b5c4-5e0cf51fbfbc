/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

dependencies {
    implementation(project(":ecco-config"))
    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-security"))

    implementation("com.azure.spring:azure-spring-boot:3.6.1")
    implementation("org.quartz-scheduler:quartz:2.3.2")
    implementation("org.springframework.boot:spring-boot-autoconfigure")
    implementation("org.springframework:spring-context-support")
    implementation("org.springframework:spring-webmvc")
    implementation("org.springframework.security:spring-security-oauth2-client")
    implementation("org.springframework.security:spring-security-oauth2-core")
    implementation("org.springframework.security:spring-security-web")
    api("org.springframework.security:spring-security-acl")
    implementation("org.springframework.security:spring-security-config")
    implementation("org.springframework.security:spring-security-ldap")
    implementation("org.springframework.security.kerberos:spring-security-kerberos-core:1.0.1.RELEASE")
    implementation("org.springframework.security.kerberos:spring-security-kerberos-web:1.0.1.RELEASE")
    implementation("org.apache.commons:commons-lang3")
    testImplementation(project(":test-support"))
    compileOnly("org.hibernate:hibernate-jcache")
    compileOnly("org.ehcache:ehcache:3.9.10")
    implementation("com.google.guava:guava")
}

tasks.withType<Test> {
//    useJUnit() // Todo switch to platform and use Junit5 legacy engine
    systemProperty("ecco.authn.acl", "true")
}

description = "ecco-security-core"
