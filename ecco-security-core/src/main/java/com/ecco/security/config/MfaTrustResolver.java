/*
 * Copyright 2021 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ecco.security.config;

import com.ecco.security.service.MfaAuthentication;
import org.springframework.security.authentication.AuthenticationTrustResolver;
import org.springframework.security.authentication.AuthenticationTrustResolverImpl;
import org.springframework.security.core.Authentication;

/**
 * Allow an MfaAuthentication to be anonymous so that we don't get 'No AuthenticationProvider found for com.ecco.security.config.MfaAuthentication'
 */
public class MfaTrustResolver implements AuthenticationTrustResolver {

	private final AuthenticationTrustResolver delegate = new AuthenticationTrustResolverImpl();

	@Override
	public boolean isAnonymous(Authentication authentication) {
		return this.delegate.isAnonymous(authentication) || authentication instanceof MfaAuthentication;
	}

	@Override
	public boolean isRememberMe(Authentication authentication) {
		return this.delegate.isRememberMe(authentication);
	}

}
