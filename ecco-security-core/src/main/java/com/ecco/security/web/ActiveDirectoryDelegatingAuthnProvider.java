package com.ecco.security.web;

import com.ecco.config.service.SettingsService;
import com.ecco.security.dom.User;
import com.ecco.security.service.UserManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ldap.CommunicationException;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.ldap.authentication.ad.ActiveDirectoryLdapAuthenticationProvider;
import org.springframework.security.ldap.userdetails.UserDetailsContextMapper;

import javax.annotation.PostConstruct;

import static com.ecco.security.SecurityUtil.getUser;

/**
 * An Active Directory authentication provider wrapper
 * which delegates authentication to spring's {@see ActiveDirectoryLdapAuthenticationProvider}
 * This adds value for the scenario when AD authn is not switched on (hence not configured in ecco)
 * a {@code null} will be returned from {@link ActiveDirectoryDelegatingAuthnProvider#authenticate}
 * which means the next configured AuthenticationProvider will be used by Spring (if available)
 * See http://static.springsource.org/spring-security/site/docs/3.1.x/reference/springsecurity-single.html#core-services-authentication-manager
 */
@RequiredArgsConstructor
@Slf4j
public class ActiveDirectoryDelegatingAuthnProvider implements AuthenticationProvider {

    private ActiveDirectoryLdapAuthenticationProvider authnProvider;

    private final SettingsService settingsService;
    private final UserManagementService userManagementService;
    private final UserDetailsContextMapper userDetailsContextMapper;


    @PostConstruct
    public void configure() throws Exception {
        validateAndSetup();
        log.info("ActiveDirectory ENABLED - authentication will be attempted on active directory");
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        Authentication authn = null;
        if (authnProvider != null) {
            try {
                log.info("ActiveDirectory delegating authenticate() call to ActiveDirectoryLdapAuthenticationProvider");
                authn = authnProvider.authenticate(authentication);

                // if successfully authorised, then we check the password against what we have in ecco user
                // and if different update the ecco user
                boolean updatedPassword = updatePasswordIfChanged(authn);
                // and then update the Authentication with the latest User
                if (updatedPassword) {
                    authn = copyAuthenticationWithUpdatedUser(authn);
                }

            } catch (CommunicationException e) {
                log.error("ActiveDirectory: Could not communicate with ActiveDirectory", e);
            }
        } else {
            log.info("ActiveDirectory not enabled, returning null from authenticate()");
        }
        return authn;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }

    /**
     * Uses the incoming form-based password which has been authenticated against AD
     * to check if the encoded password matches the User we created/loaded from LdapUserDetailsContextMapper
     * and updates the user if so
     */
    private boolean updatePasswordIfChanged(Authentication authn) {
        log.debug("ecco password check to see if different from ActiveDirectory successful login");

        String rawPass = (String) authn.getCredentials();
        User user = getUser(authn);
        boolean passwordsMatch = userManagementService.isPasswordValid(user, rawPass);
        // given a password change, update the user account
        if (!passwordsMatch) {
            user.setNewPassword(rawPass);
            userManagementService.updateUser(user);
            log.info("ecco password updated since ActiveDirectory successful login was different");
            return true;
        }
        return false;
    }

    /**
     * Create a new authenticaion with the lastest User object - otherwise
     * subsequent code may rely on some outdated aspect (password, or version)
     */
    private Authentication copyAuthenticationWithUpdatedUser(Authentication authn) {
        User userIn = getUser(authn);
        User userUpdated = userManagementService.loadUserByUsername(userIn.getUsername());

        // see AbstractLdapAuthenticationProvider.createSuccessfulAuthentication
        // we use the original details of the authentication because there is a possibility the credentials come from ldap
        // and since the authorities haven't changed, we use the authn ones
        UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userUpdated,
                authn.getCredentials(), authn.getAuthorities());
        result.setDetails(authn.getDetails());
        return result;
    }

    private void validateAndSetup() throws Exception {
        if (log.isDebugEnabled()) {
            logSettings();
        }
        validateSettings();
        setupResources();
    }

    private void logSettings() {
        // check what we have is what we expect from the db
        log.debug("ActiveDirectory settings read - START:");
        log.debug("{} {}", ActiveDirectorySettings.DOMAIN_KEY, getSetting(ActiveDirectorySettings.DOMAIN_KEY));
        log.debug("{} {}", ActiveDirectorySettings.ROOTDOMAIN_KEY, getSetting(ActiveDirectorySettings.ROOTDOMAIN_KEY));
        log.debug("{} {}", ActiveDirectorySettings.LDAP_URL_KEY, getSetting(ActiveDirectorySettings.LDAP_URL_KEY));
        log.debug("{} {}", ActiveDirectorySettings.LDAP_SEARCH_FILTER_KEY, getSetting(ActiveDirectorySettings.LDAP_SEARCH_FILTER_KEY));
        log.debug("ActiveDirectory settings read - END:");
    }

    private void validateSettings() {
        StringBuilder errorMsgs = new StringBuilder();
        // ?domain not required?
        //validateSetting(ActiveDirectorySettings.DOMAIN_KEY, errorMsgs);
        //validateSetting(ActiveDirectorySettings.ROOTDOMAIN_KEY, errorMsgs);
        validateSetting(ActiveDirectorySettings.LDAP_URL_KEY, errorMsgs);
        // this is optional - the searchFilter detaults to ActiveDirectoryLdapAuthenticationProvider.searchFilter
        // which is currently "(&(objectClass=user)(userPrincipalName={0}))"
        //validateSetting(ActiveDirectorySettings.LDAP_SEARCH_FILTER_KEY, errorMsgs);
        if (errorMsgs.length() > 0) {
            throw new IllegalArgumentException("ActiveDirectory: Invalid settings for ActiveDirectory authentication: \n" + errorMsgs);
        }
    }

    private void setupResources() throws Exception {
        String domain = getSetting(ActiveDirectorySettings.DOMAIN_KEY);
        String rootDomain = getSetting(ActiveDirectorySettings.ROOTDOMAIN_KEY);
        String url = getSetting(ActiveDirectorySettings.LDAP_URL_KEY);
        String searchFilter = getSetting(ActiveDirectorySettings.LDAP_SEARCH_FILTER_KEY);
        authnProvider = new ActiveDirectoryLdapAuthenticationProvider(domain, url, rootDomain);
        if (org.springframework.util.StringUtils.hasText(searchFilter)) {
            authnProvider.setSearchFilter(searchFilter);
        }
        authnProvider.setUserDetailsContextMapper(userDetailsContextMapper);
    }

    private void validateSetting(String settingKey, StringBuilder errorMsgs) {
        if (settingNotValid(settingKey)) {
            errorMsgs.append("ActiveDirectory: Setting ").append(settingKey).append(" is missing/blank \n");
        }
    }

    private boolean settingNotValid(String settingKey) {
        String setting = getSetting(settingKey);
        return StringUtils.isBlank(setting);
    }

    private String getSetting(String key) {
        return settingsService.settingFor(SettingsService.Namespace.ACTIVEDIRECTORY_SETTINGS_NAMESPACE , key).getValue();
    }

    private boolean isSetting(String key) {
        return settingsService.settingFor(SettingsService.Namespace.ACTIVEDIRECTORY_SETTINGS_NAMESPACE , key).isTrue();
    }

}
