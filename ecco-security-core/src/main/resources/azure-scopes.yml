# registration-id, /login/oauth2/code/<azure>
azure:
  activedirectory:
    # For now we put fake values in for client-id/secret to wire up stuff we don't use due to .xml config oauth2-login
    client-id: disabled
    client-secret: disabled
    allow-telemetry: false # Default is true - sends MS lots of data and pollutes log locally
    #    tenant-id: ecco-office-365-spike
    #    client-id: 03a9da4f-5585-45bc-b1cc-014ac1bfe1dc
    #    client-secret: fvogRTJ31+^tspQSRW308$+
    # NB requesting user-groups does trigger a request in AADWebAppConfiguration allowedGroupsConfigured
    # but this requires listing groups in advance
    #user-group:
      #prefix: this is non-standard, eg GBL_SEC_ECCO
      #allowed-groups: ...
    authorization-clients:
      azure: # see https://www.drware.com/the-latest-on-azure-active-directory-spring-integrations/
        scopes:
          - https://graph.microsoft.com/User.Read
          - https://graph.microsoft.com/Calendars.Read
          - https://graph.microsoft.com/Calendars.ReadWrite
      graph: # see https://www.drware.com/the-latest-on-azure-active-directory-spring-integrations/
        scopes:
          - https://graph.microsoft.com/User.Read
          - https://graph.microsoft.com/Calendars.Read
          - https://graph.microsoft.com/Calendars.ReadWrite

      admin:
        scopes:
          - https://graph.microsoft.com/User.Read
#          - https://graph.microsoft.com/User.ReadWrite

