package com.ecco.security.ldap.service;

import com.ecco.exceptions.UnexpectedResultSizeException;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.security.dom.Group;
import com.ecco.security.dom.LdapGroupMapping;
import com.ecco.security.repositories.LdapGroupMappingRepository;
import com.ecco.security.service.UserManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("ldapGroupMappingService")
@WriteableTransaction
public class LdapGroupMappingServiceImpl implements LdapGroupMappingService {

    @Autowired
    private LdapGroupMappingRepository groupMappingRepository;

    @Resource(name = "userManagementService")
    private UserManagementService userManagementService;

    @Override
    public Set<LdapGroupAggregate> findAll() {
        // get all the mappings
        List<LdapGroupMapping> mappings = groupMappingRepository.findAll();

        // build flat rows into sets
        Map<String, LdapGroupAggregate> aggregateMap = new HashMap<>();
        for (LdapGroupMapping m : mappings) {
            if (aggregateMap.containsKey(m.getLdapGroup())) {
                LdapGroupAggregate la = aggregateMap.get(m.getLdapGroup());
                if (m.getLocalGroup() != null) {
					la.update(m.getLocalGroup().getName(), m.getLocalClass(), m.getLocalId());
				} else {
					la.update(null, m.getLocalClass(), m.getLocalId());
				}
            } else {
                LdapGroupAggregate la = new LdapGroupAggregate(m.getLdapGroup());
                la.update(m.getLocalGroup() != null ? m.getLocalGroup().getName() : null, m.getLocalClass(), m.getLocalId());
                aggregateMap.put(m.getLdapGroup(), la);
            }
        }
        return new HashSet<>(aggregateMap.values());
    }

    @Override
    public LdapGroupAggregate find(String ldapGroup) {
        List<LdapGroupMapping> mappings = groupMappingRepository.findAllByLdapGroupOrderByLocalGroupAscLocalClassAscLocalIdAsc(ldapGroup);
        if (mappings.isEmpty()) {
			throw new UnexpectedResultSizeException("No mappings found for LDAP group: " + ldapGroup);
		}
        LdapGroupAggregate agg = new LdapGroupAggregate(ldapGroup);
        for (LdapGroupMapping m : mappings) {
            if (m.getLocalGroup() != null) {
				agg.update(m.getLocalGroup().getName(), m.getLocalClass(), m.getLocalId());
			} else {
				agg.update(null, m.getLocalClass(), m.getLocalId());
			}
        }
        return agg;
    }

    /**
     * Replace the LDAP group entirely with a new aggregate
     */
    @Override
    public void save(LdapGroupAggregate aggregate) {
        delete(aggregate.getLdapGroup());
        for (String localGroupName : aggregate.getLocalGroups()) {
            Group localGroup = userManagementService.findGroupByName(localGroupName);
            groupMappingRepository.save(new LdapGroupMapping(aggregate.getLdapGroup(), localGroup));
        }
        // localAcls seem like they were never implemented for editing - see ldapGroupMapping.jsp
        if (aggregate.getLocalAcls() != null) {
            for (LdapGroupLocalAcl localAcl : aggregate.getLocalAcls()) {
                groupMappingRepository.save(new LdapGroupMapping(aggregate.getLdapGroup(), localAcl.getLocalClass(), localAcl.getLocalId()));
            }
        }
    }

    @Override
    public void delete(String ldapGroup) {
        List<LdapGroupMapping> mappings = groupMappingRepository.findAllByLdapGroupOrderByLocalGroupAscLocalClassAscLocalIdAsc(ldapGroup);
        groupMappingRepository.deleteAll(mappings);
    }
}
