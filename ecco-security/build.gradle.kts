/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id("org.eccosolutions.querydsl-kotlin-library")
}

dependencies {
    api(project(":ecco-infrastructure"))
    api(project(":ecco-contacts"))
    implementation(project(":ecco-config"))
    api(project(":ecco-calendar-core"))
    implementation(project(":ecco-upload-dom"))

    testImplementation(project(":test-support"))
    testImplementation(kotlin("test"))

    implementation("com.eatthepath:java-otp:0.3.1")
    implementation("commons-codec:commons-codec")
    implementation("joda-time:joda-time:2.10.8")
    implementation("org.springframework.security:spring-security-oauth2-core")
    implementation("org.springframework.security:spring-security-web")
    implementation("com.google.guava:guava")
}

description = "ecco-security"
