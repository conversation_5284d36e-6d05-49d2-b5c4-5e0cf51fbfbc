package com.ecco.dom;

import org.hibernate.envers.RevisionEntity;
import org.hibernate.envers.RevisionNumber;
import org.hibernate.envers.RevisionTimestamp;

import java.io.Serializable;
import java.text.DateFormat;
import java.util.Date;
import javax.persistence.*;

@Entity
@Table(name = "revision")
@RevisionEntity(EccoRevisionListener.class)
public class EccoRevision implements Serializable {

    private static final long serialVersionUID = 4207889317039559106L;

    @Id
    @GeneratedValue(generator="auditsTableGenerator", strategy = GenerationType.TABLE)
    @TableGenerator(
            name = "auditsTableGenerator", initialValue = 1, pkColumnValue = "audits",
            allocationSize = 1, table = "hibernate_sequences")
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    @RevisionNumber
    private Long id = null;

    @RevisionTimestamp
    private long timestamp;

    private String username;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Transient
    public Date getRevisionDate() {
        return new Date(timestamp);
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof EccoRevision)) return false;

        EccoRevision that = (EccoRevision) o;

        if (id != that.id) return false;
        if (timestamp != that.timestamp) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result;
        result = id.intValue();
        result = 31 * result + (int) (timestamp ^ (timestamp >>> 32));
        return result;
    }

    @Override
    public String toString() {
        return "EccoRevision(id = " + id + ", revisionDate = " + DateFormat.getDateTimeInstance().format(getRevisionDate()) + ", username = '" + username + "')";
    }
}
