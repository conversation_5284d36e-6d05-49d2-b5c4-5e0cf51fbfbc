package com.ecco.security;

import com.ecco.config.dom.Setting;
import com.ecco.config.service.SettingsService;
import com.ecco.config.service.SoftwareModuleService;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.config.root.EccoEnvironment;
import com.ecco.calendar.core.util.DateTimeUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.ui.ModelMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

@Slf4j
public class ReferenceDataImpl implements ReferenceDataSource {

    private final SettingsService settingsService;

    private final ApplicationProperties applicationProperties;

    private final SoftwareModuleService softwareModuleService;

    private final EccoEnvironment eccoEnvironment;

    private final String analyticsWebPropertyId;

    private final ObjectMapper objMapper = new ObjectMapper();

    @Autowired
    private MessageSourceAccessor messages;

    public ReferenceDataImpl(SettingsService settingsService, ApplicationProperties applicationProperties,
                             EccoEnvironment eccoEnvironment, SoftwareModuleService softwareModuleService,
                             @Value("${misc.analytics.webPropertyId}") String analyticsWebPropertyId
    ) {
        super();
        this.settingsService = settingsService;
        this.applicationProperties = applicationProperties;
        this.softwareModuleService = softwareModuleService;
        this.eccoEnvironment = eccoEnvironment;
        this.analyticsWebPropertyId = analyticsWebPropertyId;
    }

    @Override
    public void addReferenceDataToModel(ModelMap model, HttpServletRequest request) {
        model.put("devMode", eccoEnvironment.isDevMode());

        model.put("applicationProperties", applicationProperties);
        try {
            model.put("applicationPropertiesJson", objMapper.writeValueAsString(applicationProperties));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        model.put("enabledSoftwareModules", softwareModuleService.getEnabledModules());

        model.put("analyticsWebPropertyId", analyticsWebPropertyId);

        // determine if we should logout on redirect

        String loggedInUsername = null;
        Integer year = null;
        int sessionLength = 0;
        if (SecurityUtil.authenticatedUserExists()) {
            loggedInUsername = SecurityUtil.getAuthenticatedUsername();

            // this is used in date.tag to calculate the drop downs
            year = DateTimeUtils.getNow(SecurityUtil.getAuthenticatedUser().getTimeZone()).year().get();

            // a session is given if like it or not - but its only relevant when a user is logged in
            HttpSession session = request.getSession(false);
            sessionLength = session == null ? 60 : session.getMaxInactiveInterval() / 60;
        }
        model.put("loggedInUsername", loggedInUsername);
        model.put("year", year);
        model.put("sessionLength", sessionLength);

        // used to set js variable to know the path! - we could change this with the use of dwr
        String urlServletBase = request.getContextPath();
        model.put("urlServletBase", urlServletBase);


        model.put("organisationName", messages.getMessage("organisationName"));
        model.put("domainFriendlyName", messages.getMessage("domain.friendlyName"));

        // add the attributes...
        String generatedDateTime = null;
        if (SecurityUtil.authenticatedUserExists()) {
            // this has lived in each required controller - but makes sense here
            DateTime usersNow = new DateTime(DateTimeZone.UTC).withZone(SecurityUtil.getAuthenticatedUser().getTimeZone());
            generatedDateTime = DateTimeUtils.getFormattedDateTime(usersNow, SecurityUtil.getUserLocale());
        }
        model.put("generatedDateTime", generatedDateTime);

        try {
            Setting siteTitleSetting = settingsService.settingFor("com.ecco", "SITE_TITLE");
            model.put("siteTitle", siteTitleSetting == null ? "" : siteTitleSetting.getValue());
        } catch (Exception e) {
            log.error("Error reading SITE_TITLE", e);
            model.put("siteTitle", "error" + e.getMessage());
        }

        model.put("oldBrowserAdviceText", settingsService.settingFor("com.ecco.browser","OldBrowserAdviceText").getValue());
        model.put("newBrowserUrl", settingsService.settingFor("com.ecco.browser","NewBrowserUrl").getValue());
        model.put("autocompleteOFF", settingsService.settingFor("com.ecco.authn.form","LOGIN_AUTOCOMPLETE_OFF").isTrue());

        model.put("settings", new LazySettings(settingsService));

        String footerImageUrl = null;
        try {
            Setting logoFileIdSetting = settingsService.settingFor("com.ecco", "LOGO_FILE_ID");
            if (logoFileIdSetting != null && StringUtils.isNotBlank(logoFileIdSetting.getValue())) {
                footerImageUrl = urlServletBase + "/api/images/logo/" + logoFileIdSetting.getValue();
            }
            model.put("footerImageUrl", footerImageUrl);
        } catch (Exception e) {
            log.error("Error reading LOGO_FILE_ID", e);
            // Handle errors this way as otherwise
            model.put("footerImageUrl", "noop:error - " + e.getMessage());
        }
    }

    @SuppressWarnings("unused")
    private static class LazySettings {
        private final SettingsService settingsService;

        public LazySettings(SettingsService settingsService) {
            this.settingsService = settingsService;
        }

        public Setting getWelcomePageNotice() {
            return settingsService.settingFor("com.ecco.core","WelcomePageNotice");
        }
    }
}
