package com.ecco.security;

import com.ecco.dom.Individual;
import com.ecco.security.dom.User;
import com.ecco.security.dom.UserSource;
import com.ecco.security.service.UserManagementServiceImpl;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.util.Assert;

import org.jspecify.annotations.Nullable;
import java.util.Collection;
import java.util.Locale;

public class SecurityUtil {

    /**
     * Tests if there is an authenticated non-anonymous user associated with
     * the current request.
     *
     * @return true if there is an authenticated non-anonymous user associated
     *         with the current request, false otherwise.
     */
    public static boolean authenticatedUserExists() {
        Authentication authn = SecurityContextHolder.getContext().getAuthentication();
        return authn != null && authn.isAuthenticated() && !isAuthenticatedAnonymously(authn);
    }

    /**
     * Gets the authenticated {@link User} associated with the current request.
     *
     * @return The authenticated {@link User} if an authenticated user exists.
     * @throws IllegalStateException if there is no authenticated user.
     */
    public static User getAuthenticatedUser() {
        if (authenticatedUserExists()) {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            return getUser(authentication);
        } else {
            throw new IllegalStateException("No authenticated user exists, call authenticatedUserExists() to check for existence first");
        }
    }

    static public User getUser(Authentication auth) {
        var principal = auth.getPrincipal();
        if (principal instanceof UserSource) {
            return ((UserSource) principal).getEccoUser();
        }
        // HACK a user for now but with no permissions
        return new User(Individual.builder(auth.getName(), "").build(), auth.getPrincipal().toString());
    }


    public static void assertAuthority(String role) {
        Assert.isTrue(hasAuthority(role), "User must have this role");
    }

    public static boolean hasAuthority(String role) {
        Collection<? extends GrantedAuthority> authorities = SecurityContextHolder.getContext().getAuthentication().getAuthorities();
        return authorities.stream().anyMatch(new UserManagementServiceImpl.GrantedAuthorityMatcher(role));
    }

    private static boolean isAuthenticatedAnonymously(Authentication authn) {
        return (authn != null && authn.isAuthenticated() && authn instanceof AnonymousAuthenticationToken);
    }

    public static boolean isAuthenticatedOAuth2() {
        Authentication authn = SecurityContextHolder.getContext().getAuthentication();
        return (authn != null && authn.isAuthenticated() && authn.getPrincipal() instanceof OAuth2User);
    }

    public static String oAuth2Issuer() {
        assert isAuthenticatedOAuth2();
        Authentication authn = SecurityContextHolder.getContext().getAuthentication();
        OidcUser oAuthUser = (OidcUser) authn.getPrincipal();
        return oAuthUser.getClaims().get("iss").toString();
    }

    @Nullable
    public static String getAuthenticatedUsernameOrNull() {
        if (authenticatedUserExists()) {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();

            return auth.getPrincipal() instanceof UserSource ? getUser(auth).getUsername()
                    : auth.getName(); // which'll get UserDetails.getUserName if instanceof UserDetails
        } else {
            return null;
        }
    }

    @Nullable
    public static Long getAuthenticatedUserIdOrNull() {
        if (authenticatedUserExists()) {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();

            return auth.getPrincipal() instanceof UserSource ? getUser(auth).getId() : null;
        } else {
            return null;
        }
    }

    public static String getAuthenticatedUsername() {
        if (authenticatedUserExists()) {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();

            return auth.getPrincipal() instanceof UserSource ? getUser(auth).getUsername()
                    : auth.getName(); // which'll get UserDetails.getUserName if instanceof UserDetails
        } else {
            throw new IllegalStateException("No authenticated user exists, call authenticatedUserExists() to check for existence first");
        }
    }

    @Nullable
    public static String getAuthenticatedUserCalendarId() {
        if (authenticatedUserExists() && SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof UserSource) {
            return getAuthenticatedUser().getContact().getCalendarId();
        }
        return null;
    }

    @Nullable
    public static Long getAuthenticatedUserContactId() {
        if (authenticatedUserExists() && SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof UserSource) {
            return getAuthenticatedUser().getContact().getId();
        }
        return null;
    }

    @Nullable
    public static Locale getUserLocale() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        return auth instanceof UserSource ? getUser(auth).getLocale()
                : null; // which'll get UserDetails.getUserName if instanceof UserDetails

    }
}
