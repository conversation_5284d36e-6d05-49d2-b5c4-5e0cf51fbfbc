package com.ecco.security.dom;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.net.URI;

@Embeddable
public class CommandRequest {
    @NotNull
    @Column(length = 8, nullable = false)
    private String method;

    @NotNull
    @Column(length = 2000, nullable = false)
    private URI url;

    @NotNull
    @Column(nullable = false)
    private String contentType;

    @Column
    private String acceptType;

    @Lob
    @Column
    @Basic(fetch = FetchType.LAZY)
    private String body;

    protected CommandRequest() {  // for Hibernate
    }

    public CommandRequest(@NonNull String method, @NonNull URI url, @NonNull String contentType, @Nullable String acceptType, @Nullable String body) {
        this.method = method;
        this.url = url;
        this.contentType = contentType;
        this.acceptType = acceptType;
        this.body = body;
    }

    @NonNull
    public String getMethod() {
        return method;
    }

    @NonNull
    public URI getUrl() {
        return url;
    }

    @NonNull
    public String getContentType() {
        return contentType;
    }

    @Nullable
    public String getAcceptType() {
        return acceptType;
    }

    @Nullable
    public String getBody() {
        return body;
    }

    @Override
    public String toString() {
        return method + " " + url + "\n" +
                "Content-Type: " + contentType + "\n" +
                "Accept: " + acceptType + "\n\n" +
                body;
    }
}
