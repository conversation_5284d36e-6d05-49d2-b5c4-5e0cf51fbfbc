package com.ecco.security.dom;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;

@Entity
@Table(name = "passwordhistory")
public class PasswordHistory extends AbstractLongKeyedEntity {

    private static final long serialVersionUID = 1L;

    @NotNull
    private Long userId;
    private String password;

    protected PasswordHistory() {
    }

    /**
     * A password history entry. Since this is persisted, the password
     * must be supplied encoded in some way (e.g. strong hash with salt)
     */
    public PasswordHistory(Long userId, String encodedPassword) {
        this.userId = userId;
        this.password = encodedPassword;
    }

    public Long getUserId() {
        return userId;
    }

    public String getPassword() {
        return password;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((password == null) ? 0 : password.hashCode());
        result = prime * result + ((userId == null) ? 0 : userId.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (!super.equals(obj))
            return false;
        if (getClass() != obj.getClass())
            return false;
        PasswordHistory other = (PasswordHistory) obj;
        if (password == null) {
            if (other.password != null)
                return false;
        } else if (!password.equals(other.password))
            return false;
        if (userId == null) {
            if (other.userId != null)
                return false;
        } else if (!userId.equals(other.userId))
            return false;
        return true;
    }

}
