package com.ecco.security.dom;

import com.ecco.infrastructure.entity.ConfigurableLongKeyedEntity;
import com.ecco.security.KeyGenerator;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.jspecify.annotations.NonNull;

import java.net.InetAddress;
import java.util.UUID;

import javax.annotation.Resource;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Entity
@Table(name = "userdevices")
@Audited
public class UserDevice extends ConfigurableLongKeyedEntity {
    private static final int MAX_USER_AGENT_LENGTH = 500;

    @PersistenceContext
    @Transient
    private transient EntityManager em;

    @Resource(name = "userDeviceKeyGenerator")
    @Transient
    private transient KeyGenerator keyGenerator;

    @ManyToOne(optional = false)
    @JoinColumn(name = "users_id", updatable = false, nullable = false)
    @NotNull
    private User user;

    @Column(unique = true, nullable = false, columnDefinition = "CHAR(36)")
    @Type(type = "uuid-char")
    @NotNull
    private UUID guid;

    @Column(name = "randomkey", nullable = false, columnDefinition="BLOB")
//    @NotNull - removed because Hib jpamodelgen in 5.0.12 generates garbage import when moved platform-bom to Cairo-SR2
    @Lob
    byte[] key;

    @Column(nullable = false, length = 10)
    @NotNull
    String cipher;

    boolean valid;

    boolean autoDismiss;

    @Column(nullable = true, length = 50)
    String description;

    @Column(length = MAX_USER_AGENT_LENGTH)
    String userAgent;

    @Basic(fetch = FetchType.LAZY) // Lazy to avoid the type conversion if we don't need it.
    @Column(columnDefinition = "VARCHAR(39)") // Sufficient for IPv6
    InetAddress ipAddress;

    protected UserDevice() { // for Hibernate
    }

    public UserDevice(User user, String cipher) {
        this.user = user;
        this.guid = UUID.randomUUID();
        revalidate(cipher);
    }

    public @NonNull User getUser() {
        return user;
    }

    public @NonNull UUID getGuid() {
        return guid;
    }

    public @NonNull byte[] getKey() {
        return key;
    }

    public @NonNull String getCipher() {
        return cipher;
    }

    public boolean isValid() {
        return valid;
    }

    public boolean isAutoDismiss() {
        return autoDismiss;
    }

    public String getDescription() {
        return description;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public InetAddress getIpAddress() {
        return ipAddress;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setUserAgent(String userAgent) {
        // User agents can get VERY long - see http://stackoverflow.com/questions/654921/how-big-can-a-user-agent-string-get
        this.userAgent = StringUtils.abbreviate(userAgent, MAX_USER_AGENT_LENGTH);
    }

    public void setIpAddress(InetAddress ipAddress) {
        this.ipAddress = ipAddress;
    }

    public void invalidate(String description) {
        this.valid = false;
        if (StringUtils.isNotEmpty(description)) {
            this.description = description;
        }
    }

    public void revalidate(String cipher) {
        // Generate a new key if required
        if (!this.valid || !this.cipher.equals(cipher)) {
            this.key = keyGenerator.generateRandomKey(cipher);
            this.cipher = cipher;
            this.valid = true;
        }
    }
}
