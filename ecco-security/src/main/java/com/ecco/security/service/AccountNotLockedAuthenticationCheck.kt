package com.ecco.security.service

import com.ecco.infrastructure.time.Clock
import com.ecco.security.dom.User
import org.joda.time.Seconds
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.context.MessageSource
import org.springframework.context.support.MessageSourceAccessor
import org.springframework.security.authentication.LockedException
import org.springframework.security.core.Authentication
import org.springframework.security.core.AuthenticationException

class AccountNotLockedAuthenticationCheck
@JvmOverloads
constructor(
    messageSource: MessageSource,
    private val clock: Clock = Clock.DEFAULT,
) : AuthenticationCheck<User, Authentication> {
    private val log: Logger = LoggerFactory.getLogger(javaClass)
    private val messages = MessageSourceAccessor(messageSource)

    @Throws(AuthenticationException::class)
    override fun check(userDetails: User, authentication: Authentication) {
        val nextAllowedLoginDateTime = userDetails.nextPermittedLoginTime()
        val now = clock.now()
        if (nextAllowedLoginDateTime.isAfter(now)) {
            val requiredWait = Seconds.secondsBetween(now, nextAllowedLoginDateTime)
            log.debug("attempt to log while locked out. userId:" + userDetails.id)
            throw LockedException(
                messages.getMessage(
                    "AbstractUserDetailsAuthenticationProvider.accountLocked",
                    arrayOf(requiredWait.seconds.toString()),
                ),
            )
        }
    }
}