package com.ecco.security.service;

import com.ecco.config.service.SettingsService;
import com.ecco.security.dom.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static com.ecco.security.SecurityUtil.getUser;

/**
 * Special handler to check if a password change is required before a user can
 * proceed after login.
 *
 * Used in common med-security SecurityComponentConfig.
 */
@Slf4j
public class WebAuthentication<PERSON>uccess<PERSON>and<PERSON> extends SavedRequestAwareAuthenticationSuccessHandler {

    @Autowired
    SettingsService settingsService;

    // NB this does not need to be the same Bean object 'refererRequestCache', it just accesses the Session
    private RequestCache requestCache = new HttpSessionRequestCache(); // used to access requests stored in http session

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws ServletException, IOException {

        if (settingsService.settingFor(SettingsService.SecurityAuthnPasswd.NAMESPACE, "ENABLED").isTrue()) {
            User user = getUser(authentication);

            if (user.needsToChangePassword()) {
                authentication.setAuthenticated(false);
                // spring-boot is better throwing an exception and capturing client-side
                // see other BadCredentialsException's.
                //throw new BadCredentialsException("resetPwd");
                response.sendRedirect(request.getContextPath() + "/nav/secure/changePassword.html?passwordExpired=1&username="
                        + user.getUsername());
                return;
            }

        }
        this.removeInvalidSuccessRequest(request, response);
        super.onAuthenticationSuccess(request, response, authentication);
    }

    /** Remove request from request cache if it is not one that will succeed, thus allowing underlying handler
     * to do whatever it would otherwise have done. */
    private void removeInvalidSuccessRequest(HttpServletRequest request, HttpServletResponse response) {
        SavedRequest cachedRequest = requestCache.getRequest(request, response);
        if (cachedRequest == null) {
            return;
        }
        if (request.getRequestURI().contains(request.getContextPath() + "/api/")) {
            logger.warn("Removing invalid request from auth requestCache: " + request.getRequestURI());
            requestCache.removeRequest(request, response);
        }
    }
}
