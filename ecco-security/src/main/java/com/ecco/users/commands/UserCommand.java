package com.ecco.users.commands;

import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;

import org.jspecify.annotations.NonNull;

import javax.persistence.*;
import java.util.UUID;

@Entity
@Table(name = "usr_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
public abstract class UserCommand extends BaseUserCommand {

    public UserCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime, long userId, @NonNull String body,
                       Long userIdSubject) {
        super(uuid, remoteCreationTime, userId, body, userIdSubject);
    }

    /**
     * Required by JPA/Hibernate.
     */
    @Deprecated
    protected UserCommand() {
    }
}
