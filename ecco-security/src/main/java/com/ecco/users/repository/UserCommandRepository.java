package com.ecco.users.repository;

import com.ecco.infrastructure.spring.data.BaseCommandRepository;
import com.ecco.users.commands.UserCommand;
import org.springframework.data.jpa.repository.QueryHints;

import javax.persistence.QueryHint;

import java.util.List;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface UserCommandRepository extends BaseCommandRepository<UserCommand, Integer> {

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<UserCommand> findAllByUserIdSubject(long userIdSubject);

}
