package com.ecco.serviceConfig;

import java.util.List;

import com.ecco.dom.ProjectAclId;
import com.ecco.dom.ServiceAclId;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;

@ReadOnlyTransaction
public interface EntityRestrictionService {

    /**
     * Return the services the user is allowed access to - restricted to those which form part of the referral process
     * NB this only applies when -DenableAcls is on because this method is a configured for filtering through spring security
     * @return services the user is allowed access to
     */
    List<ServiceAclId> getRestrictedServiceIds();

    /**
     * Return the projects the user is allowed access to.
     * A project with id -1 is used to indicate all projects (for convenience) and no project for when a referral isn't yet assigned - see impl.
     *
     * NB this only applies when -DenableAcls is on because this method is a configured for filtering through spring security
     */
    List<ProjectAclId> getRestrictedProjectIds();

    ServicesProjectsDto getRestrictedServicesProjectsDto(
            RepositoryBasedServiceCategorisationService svcCatsService);

    void verifyAccess(int srId, long serviceId, Long projectId, RepositoryBasedServiceCategorisationService svcCatService);

    /**
     * Ensures that the classes we use (Service/Project) all have entries in the ACL tables
     * otherwise significant errors can occur for users
     */
    @WriteableTransaction
    void ensureAcls();

}
