package com.ecco.serviceConfig.dom;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.ecco.infrastructure.entity.IdName;
import org.hibernate.annotations.Type;
import org.jspecify.annotations.NonNull;

import javax.persistence.*;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * ActionGroup or "support area" of a desired {@link Outcome}. E.g. "temptation to steal" would be a risk to the outcome of "economic well-being".
 */
@Entity
@Table(name = "actiongroups")
@Getter
@Setter
public class ActionGroup extends IdName {

    private static final long serialVersionUID = 1L;

    /** uuid key to allow remote commands to reference what they created */
    @NonNull
    @Column(name = "uuid", nullable = false, columnDefinition = "CHAR(36)")
    @Type(type = "uuid-char")
    private UUID uuid;

    @ManyToOne(cascade = {}, fetch = FetchType.EAGER)
    @JoinColumn(name = "outcomeId", nullable = false)
    private Outcome outcome;

    @OneToMany(mappedBy = "actionGroup", cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
    @Fetch(FetchMode.SUBSELECT)
    @OrderBy("orderby,id")
    private final Set<Action> actions = new HashSet<Action>();

    @Column
    private int orderby;

    public ActionGroup() {
    }

    public ActionGroup(Long id, UUID uuid, String name) {
        super(id, name);
        this.uuid = uuid;
    }

    /**
     * Add an action to this risk.
     *
     * @param action to add.
     * @return true if this risk did not already contain the action.
     */
    public boolean addAction(Action action) {
        action.setActionGroup(this);
        return actions.add(action);
    }

    public void addActions(Set<Action> actions) {
        this.actions.clear();
        for(Action a : actions) {
            addAction(a);
        }
    }

    // risks can have questions, just like actions can - but for now we don't use these
    /*
    private List<Question> questions = new ArrayList<Question>();

    @ManyToMany(cascade={}, fetch=FetchType.EAGER)
    @JoinTable(name = "risks_questions", joinColumns = @JoinColumn(name = "riskId"), inverseJoinColumns = @JoinColumn(name = "questionId"))
    public List<Question> getQuestions() {
        return questions;
    }

    public void setQuestions(List<Question> questions) {
        this.questions = questions;
    }
    */

}
