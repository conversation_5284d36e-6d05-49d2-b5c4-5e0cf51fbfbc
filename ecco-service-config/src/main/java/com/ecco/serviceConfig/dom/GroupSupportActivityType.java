package com.ecco.serviceConfig.dom;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.*;

import com.ecco.infrastructure.entity.AbstractIntKeyedEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.BatchSize;

import com.ecco.dom.Service;


@Entity
@Table(name = "grp_activitytypes")
@Getter
@Setter
public class GroupSupportActivityType extends AbstractIntKeyedEntity {

    private static final long serialVersionUID = 1L;

    @Column(name="name")
    private String name;

    @Column
    private boolean disabled;

    @ManyToMany(cascade={}, fetch=FetchType.EAGER)
    @JoinTable(name = "gsat_linkedactions", joinColumns = @JoinColumn(name = "groupsupportactivitytypeId"),
            inverseJoinColumns = @JoinColumn(name = "actionId"))
    Set<Action> linkedActions;

    @OneToMany(mappedBy = "id.activityTypeId")
    Set<GroupSupportActivityTypeLinkedQuestionGroup> linkedQuestionGroups;

//    @ManyToMany(cascade={}, fetch=FetchType.EAGER)
//    @JoinTable(name = "referral_to_gsat", joinColumns = @JoinColumn(name = "groupsupportactivitytypeId"), inverseJoinColumns = @JoinColumn(name = ""))
//    Set<Action> referralsWithInterest;

    @Column(insertable=false, updatable=false) // we modify via the repository
    @ManyToMany(cascade={}, fetch=FetchType.LAZY)
    @JoinTable(
            name = "grp_activitytype_service",
            joinColumns = @JoinColumn(name = "activityTypeId", columnDefinition = "INT"),
            inverseJoinColumns = @JoinColumn(name = "serviceId"))
    @BatchSize(size = 20)
    private Set<Service> services = new HashSet<Service>();

    /** Don't try modifying this - use the repository instead */
    public Set<Service> getServices() {
        return java.util.Collections.unmodifiableSet(services);
    }

}
