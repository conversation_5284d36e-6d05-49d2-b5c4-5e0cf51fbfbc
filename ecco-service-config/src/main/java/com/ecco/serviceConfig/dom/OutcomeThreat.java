package com.ecco.serviceConfig.dom;


import lombok.Getter;
import lombok.Setter;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue("threat")
@Getter
@Setter
public class OutcomeThreat extends Outcome {

    private static final long serialVersionUID = 1L;

    int weighting;

    public OutcomeThreat() {
    }

    public OutcomeThreat(Long id, String name, UUID uuid) {
        super(id, name, uuid);
    }
}
