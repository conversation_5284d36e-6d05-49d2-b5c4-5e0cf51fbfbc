package com.ecco.serviceConfig.dom;

import com.ecco.infrastructure.dom.ConfigCommand;

import java.util.UUID;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

@Entity
@DiscriminatorValue("project")
public class ProjectCommand extends ConfigCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public ProjectCommand() {
        super();
    }

    public ProjectCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
            long userId, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

}
