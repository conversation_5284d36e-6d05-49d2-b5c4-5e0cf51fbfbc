package com.ecco.serviceConfig.dom;

import java.util.HashMap;
import java.util.Set;

import javax.persistence.*;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Cascade;

import com.ecco.infrastructure.entity.IdName;
import org.hibernate.annotations.Type;

@Entity
@Table(name="questions")
@Getter
@Setter
public class Question extends IdName {

    private static final long serialVersionUID = 1L;

    @Cache(usage=CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
    @ManyToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @Cascade(org.hibernate.annotations.CascadeType.ALL)
    @JoinTable(name = "questions_questionanswerfrees", joinColumns = @JoinColumn(name = "questionId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "questionanswerfreeId", referencedColumnName = "id"))
    // free answers - string or integer
    Set<QuestionAnswerFree> freeTypes;

    // use technique from http://stackoverflow.com/questions/5409560/manytomany-without-join-table-legacy-database
    // and http://stackoverflow.com/questions/1049809/hibernate-specifying-columns-in-a-one-to-many-relationship
    // but get "failed to lazily initialize a collection of role: com.ecco.dom.questionnaire.Question.answers, no session or session was closed"
    // can't call eager - https://hibernate.onjira.com/browse/HHH-2862
    // we could manage it if we loaded the answers in a specific load - but that defeats the point
    // so it proved difficult to map a OneToMany where the 'one' owns the relationship referring to a non-primary column so we used QE's approach of ManyToMany
    @Cache(usage=CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
    @ManyToMany(fetch=FetchType.EAGER, cascade = CascadeType.ALL)
    @Cascade(org.hibernate.annotations.CascadeType.ALL)
    @JoinTable(name = "questions_questionanswrchoices", joinColumns = @JoinColumn(name = "questionId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "questionanswerchoiceId", referencedColumnName = "id"))
    @OrderBy("value ASC")
//    @OrderBy // id asc assumed, should probably use an OrderColumn
    // restricted answers
    Set<QuestionAnswerChoice> choices;

    // Error: web error: 500 - ecco - unable to deep copy object; nested exception is org.hibernate.HibernateException: ecco - unable to deep copy object
    // Issue is the @Cacheable on this entity since it loads from second level cache to construct the entity
    // see QuestionGroup for the same issue!
    @Column
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private HashMap<String, Object> parameters = new HashMap<>();

    boolean answerRequired;

    boolean answersVertical; // default false is 'horizontal'

    // throw-back from the OutcomeThreat which may have questions on the outcomes - stored in blobs 'customObject/customString' properties
    String answerType;

    public String getType() {
        return freeTypes != null && freeTypes.size() > 0
                ? freeTypes.stream().map(ft -> ft.valueType).findFirst().get()
                : "choices";
    }

}
