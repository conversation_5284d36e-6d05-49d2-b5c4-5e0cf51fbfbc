package com.ecco.serviceConfig.dom;

import com.ecco.infrastructure.entity.IdName;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.*;

import javax.persistence.*;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "questiongroups")
@Inheritance(strategy=InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="discriminator_orm", discriminatorType=DiscriminatorType.STRING)
@DiscriminatorOptions(force=true)
public abstract class QuestionGroup extends IdName implements EvidenceGroup {

    private static final long serialVersionUID = 1L;

    @Column(name="headertext")
    private String headerText;

    @JoinColumn(name="evidenceguidanceId")
    @OneToOne(optional=true, cascade=CascadeType.ALL)
    private EvidenceGuidance evidenceGuidance;

    @OneToMany(mappedBy = "questionGroup")
    @OrderBy("orderby asc, question.id asc")
    @Cascade(org.hibernate.annotations.CascadeType.ALL)
    private List<QuestionGroupQuestion> questions = new ArrayList<>();

    @Column
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    // Error: web error: 500 - ecco - unable to deep copy object; nested exception is org.hibernate.HibernateException: ecco - unable to deep copy object
    // Issue is the @Cacheable on this entity since it loads from second level cache to construct the entity
    // Comparing welcome/settings/questiongroups/4/ (fails) vs editing a welcome/admin/services where 'parameters' (works) are both set
    // shows that 'CacheEntityLoaderHelper.loadFromSecondLevelCache' sets them on different paths, which ends up with
    // the questiongroups skipping the BaseJsonClob.getFromClob - see jackason-databind MapDesierializer.deserialize - case 5 (good) or case 6 (bad)
    private HashMap<String, Object> parameters = new HashMap<>();

    @Override
    public EvidenceGuidance getGuidance() {
        return evidenceGuidance;
    }

    @Override
    public void setGuidance(EvidenceGuidance guidance) {
        this.evidenceGuidance = guidance;
    }

    @Override
    public String getTitle() {
        return getName();
    }

    @Override
    public String getWebTemplatePath() {
        return "evidence/group/questions";
    }

    @Override
    public String getWebTemplatePathForTab() {
        return "evidence/group/evidenceGroupTab";
    }
}
