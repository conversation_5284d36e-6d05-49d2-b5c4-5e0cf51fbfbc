package com.ecco.serviceConfig.dom;

import com.ecco.infrastructure.dom.ConfigCommand;

import java.util.UUID;

import org.jspecify.annotations.NonNull;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;
import org.jspecify.annotations.Nullable;

@Entity
@DiscriminatorValue("service")
public class ServiceCommand extends ConfigCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public ServiceCommand() {
        super();
    }

    public ServiceCommand(@Nullable UUID uuid, @NonNull Instant remoteCreationTime,
                          long userId, @NonNull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

}
