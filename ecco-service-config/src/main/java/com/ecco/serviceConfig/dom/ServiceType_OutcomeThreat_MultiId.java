package com.ecco.serviceConfig.dom;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import java.io.Serializable;

@Embeddable
public class ServiceType_OutcomeThreat_MultiId implements Serializable {

    private static final long serialVersionUID = 1L;

    @OneToOne(fetch=FetchType.LAZY, optional=false)
    @JoinColumn(name="serviceTypeId")
    ServiceType serviceType;

    @OneToOne(fetch= FetchType.LAZY, optional=false)
    @JoinColumn(name="threatoutcomeId")
    OutcomeThreat outcome;

    protected ServiceType_OutcomeThreat_MultiId() {
    }

    public ServiceType_OutcomeThreat_MultiId(ServiceType serviceType, OutcomeThreat outcome) {
        this.serviceType = serviceType;
        this.outcome = outcome;
    }

    public ServiceType getServiceType() {
        return serviceType;
    }

    public void setServiceType(ServiceType serviceType) {
        this.serviceType = serviceType;
    }

    public OutcomeThreat getOutcome() {
        return outcome;
    }

    public void setOutcome(OutcomeThreat outcome) {
        this.outcome = outcome;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ServiceType_OutcomeThreat_MultiId that = (ServiceType_OutcomeThreat_MultiId) o;

        if (serviceType != null ? !serviceType.equals(that.serviceType) : that.serviceType != null) return false;
        return outcome != null ? outcome.equals(that.outcome) : that.outcome == null;

    }

    @Override
    public int hashCode() {
        int result = serviceType != null ? serviceType.hashCode() : 0;
        result = 31 * result + (outcome != null ? outcome.hashCode() : 0);
        return result;
    }
}
