package com.ecco.serviceConfig.dom;

import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.AssociationOverride;
import javax.persistence.AssociationOverrides;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EntityManager;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.ecco.infrastructure.util.FlagMap;
import com.ecco.infrastructure.util.HashMapWithNotFoundValue;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;

import static java.util.stream.Collectors.toList;

@Entity
@BatchSize(size=20)
@Access(AccessType.FIELD)
@Table(name="servicetypes_taskdefinitions")
@Getter
@Setter
@AssociationOverrides({
    @AssociationOverride(name="multiId.serviceType", joinColumns = @JoinColumn(name="servicetypeId")),
    @AssociationOverride(name="multiId.taskDefinition", joinColumns = @JoinColumn(name="taskDefinitionId"))
})
public class ServiceType_TaskDefinition implements Serializable { // ElementEntity


    public static String REFERRALVIEW = "referralView";

    private static final class IsWizardTask implements Predicate<ServiceType_TaskDefinition> {

        private boolean isActive = true;
        @Override
        public boolean test(ServiceType_TaskDefinition input) {

            // hidden breadcrumb marks the end of wizard
            if (input.getTaskDefinition().getName().equals(REFERRALVIEW)) {
                isActive = false;
                return true; // we want referralView and want to skip all after
            }

            return isActive;
        }
    }

    /** Stateful (<- note) filter to those which are to be shown as breadcrumbs */
    public static IsWizardTask isWizardTask() {
        return new IsWizardTask();
    }


    private static final long serialVersionUID = -2853261670260826811L;

    @EmbeddedId
    private ServiceType_TaskDefinition_MultiId multiId = new ServiceType_TaskDefinition_MultiId();

    @Column
    private int orderby; // avoid 'order' since its a keyword

    /** The due date for this task, from the point the task is created */
    @Column
    private String dueDateSchedule;

    @Column
    private boolean allowNext; // allow the next aspect to be active

    @Version
    @Column(name="version")
    private final Integer version = null;

    @BatchSize(size=20)
    @OneToMany(mappedBy = "serviceType_TaskDefinition", orphanRemoval = true, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    private final Set<ServiceType_TaskDefinitionSetting> settings = new HashSet<>();

    @Column
    private boolean showClientOnly;

    @Transient
    private ImmutableMap<String, ServiceType_TaskDefinitionSetting> settingsByName;

    @Transient
    private HashMapWithNotFoundValue<String, Map<String,Boolean>> settingFlagsByName;


    public ServiceType_TaskDefinition() {
        // default constructor needed for javassist
    }

    public static ServiceType_TaskDefinition create(EntityManager entityManager, long serviceTypeId, long taskDefinitionId) {
        ServiceType_TaskDefinition result = new ServiceType_TaskDefinition();
        ServiceType_TaskDefinition_MultiId multiId = ServiceType_TaskDefinition_MultiId.create(entityManager, serviceTypeId, taskDefinitionId);
        result.setMultiId(multiId);
        return result;
    }

    public void addSetting(ServiceType_TaskDefinitionSetting setting) {
        setting.setServiceType_TaskDefinition(this);
        this.settings.add(setting);
    }

    public String getSetting(String name) {
        NameValue setting = getSettingsByName().get(name);
        return setting == null ? null : setting.getValue();
    }

    /** Get the settings as something we can easily query in jsp */
    public Map<String,Boolean> getSettingAsFlag(String name) {
        return getSettingFlagsByName().get(name);
    }

    public Set<String> getSettingAsFlagsValues(String name) {
        return getSettingAsFlag(name).keySet();
    }

    private HashMapWithNotFoundValue<String, Map<String,Boolean>> getSettingFlagsByName() {
        if (this.settingFlagsByName == null) {
            this.settingFlagsByName = new HashMapWithNotFoundValue<>(new HashMap<>(), new FlagMap(""));
            for (NameValue nameValue : settings) {
                this.settingFlagsByName.put(nameValue.getName(), new FlagMap(nameValue.getValue()));
            }
        }
        return this.settingFlagsByName;
    }

    private ImmutableMap<String, ? extends NameValue> getSettingsByName() {
        if (this.settingsByName == null) {
            this.settingsByName = Maps.uniqueIndex(
                    settings.stream().filter(setting -> setting.getOutcomeId() == null).collect(toList()), // HACK: We ignore outcome overrides for old JSP code
                    NameValue.getName::apply);
        }
        return this.settingsByName;
    }

    @Override
    public String toString() {
        return  "servicetype_taskdefinition " + multiId.getServiceType() + ", " + multiId.getTaskDefinition();
    }

    /* would mimic, but using solution from http://boris.kirzner.info/blog/archives/2008/07/19/hibernate-annotations-the-many-to-many-association-with-composite-key/
    <composite-id name="multiId" class="Contact_Category$MultiId" unsaved-value="none">
        <key-property name="categoriesId" access="field" column="categoriesId"/>
        <key-property name="contactsId" access="field" column="contactsId"/>
    </composite-id>
    */
    //public ServiceType_TaskDefinition_MultiId getMultiId() {
        //return multiId;
    //}

    public TaskDefinition getTaskDefinition() {
        return getMultiId().getTaskDefinition();
    }

    /*
    // mimicing the contact orm - bidirectional with association table (for potential extra attributes)
    // <many-to-one name="contact" entity-name="Contact" cascade="none" insert="false" update="false" not-null="true" column="contactsId"/>
    // cascade: we don't deleting this class to delete a service
    // using annotations - see http://boris.kirzner.info/blog/archives/2008/07/19/hibernate-annotations-the-many-to-many-association-with-composite-key/
    */
    /*
    @ManyToOne(cascade={}, fetch=FetchType.LAZY)
    //@NotFound(action=NotFoundAction.EXCEPTION)
    // not part of insert since already mapped in MultiId
    // a joincolumn means we are the owner of this relationship
    @JoinColumn(name="serviceId", nullable=false, insertable=false, updatable=false)
    */

    public void setTaskDefinition(TaskDefinition taskDefinition) {
        getMultiId().setTaskDefinition(taskDefinition);
    }

    public void setServiceType(ServiceTypeMinimalView serviceType) {
        getMultiId().setServiceType(serviceType);
    }

    public void setServiceType(ServiceType serviceType) {
        getMultiId().setServiceType(serviceType.asMinimalView());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        ServiceType_TaskDefinition that = (ServiceType_TaskDefinition) o;

        return getMultiId() != null ? getMultiId().equals(that.getMultiId()) : that.getMultiId() == null;
    }

    @Override
    public int hashCode() {
        return (getMultiId() != null ? getMultiId().hashCode() : 0);
    }
}
