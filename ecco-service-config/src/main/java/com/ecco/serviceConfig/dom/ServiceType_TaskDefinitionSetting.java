package com.ecco.serviceConfig.dom;

import javax.persistence.*;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.BatchSize;
import org.jspecify.annotations.Nullable;

import java.util.Objects;

@Entity
@BatchSize(size=100)
@Table(name = "servicetypes_taskdefsettings")
@Getter
@Setter
public class ServiceType_TaskDefinitionSetting extends AbstractLongKeyedEntity implements NameValue {

    private static final long serialVersionUID = 1L;

    @ManyToOne(optional=false)
    @JoinColumns({
        @JoinColumn(name="servicetypeId", updatable=false),
        @JoinColumn(name="taskDefinitionId", updatable=false)
    })
    private ServiceType_TaskDefinition serviceType_TaskDefinition;

    private String name;

    private String value;

    @Nullable
    private Long outcomeId;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        ServiceType_TaskDefinitionSetting that = (ServiceType_TaskDefinitionSetting) o;
        return serviceType_TaskDefinition.equals(that.serviceType_TaskDefinition) &&
                name.equals(that.name) &&
                Objects.equals(outcomeId, that.outcomeId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), serviceType_TaskDefinition, name, outcomeId);
    }
}
