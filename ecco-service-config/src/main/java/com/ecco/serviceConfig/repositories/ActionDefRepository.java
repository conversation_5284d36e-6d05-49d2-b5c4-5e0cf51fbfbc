package com.ecco.serviceConfig.repositories;

import com.ecco.serviceConfig.dom.Action;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.Optional;
import java.util.UUID;

/**
 * Repository for action definitions
 */
public interface ActionDefRepository extends CrudRepository<Action, Long> {

    Action findOneByName(String name);

    Action findOneByUuid(UUID uuid);

    @Modifying
    @Query(nativeQuery = true,
            value = "INSERT INTO gsat_linkedactions (actionid, groupsupportactivitytypeId) VALUES (?1, ?2)")
    void linkActivity(long actionDefId, long activityTypeId);

    @Modifying
    @Query(nativeQuery = true,
            value = "DELETE FROM gsat_linkedactions"
            + " WHERE actionid = ?1 AND groupsupportactivitytypeId = ?2")
    void unlinkActivity(long actionDefId, long activityTypeId);

    @Modifying
    @Query(nativeQuery = true,
            value = "UPDATE actions"
            + " SET name = ?1"
            + " WHERE id = ?2")
    void changeName(String to, long actionDefId);

    Optional<Action> findById(long id);
}
