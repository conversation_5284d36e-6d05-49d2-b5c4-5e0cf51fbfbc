package com.ecco.serviceConfig.viewModel;

import java.util.function.Function;
import java.util.stream.Collectors;

import org.jspecify.annotations.Nullable;

import com.ecco.serviceConfig.dom.ActionGroup;
import com.ecco.serviceConfig.dom.Outcome;

public final class OutcomeToViewModel implements Function<Outcome, OutcomeViewModel> {

    private static final Function<ActionGroup, ActionGroupViewModel> actionGroupToViewModel = new ActionGroupToViewModel();

    @Nullable
    @Override
    public OutcomeViewModel apply(@Nullable Outcome input) {
        if (input == null) {
            throw new NullPointerException("input Outcome must not be null");
        }

        OutcomeViewModel vm = new OutcomeViewModel();
        vm.id = input.getId();
        vm.uuid = input.getUuid();
        vm.name = input.getName();
        vm.disabled = input.isDisabled();
        vm.actionGroups = input.getActionGroups().stream().map(actionGroupToViewModel::apply).collect(Collectors.toList());

        return vm;
    }
}