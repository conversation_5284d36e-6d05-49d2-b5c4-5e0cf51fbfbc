package com.ecco.serviceConfig.viewModel;

import com.ecco.serviceConfig.dom.QuestionAnswerChoiceView;
import java.util.function.Function;

import com.ecco.serviceConfig.dom.QuestionAnswerChoice;
import org.jspecify.annotations.Nullable;

public class QuestionChoiceToViewModel implements Function<QuestionAnswerChoice, QuestionAnswerChoiceView> {

    @Override
    @Nullable
    public QuestionAnswerChoiceView apply(@Nullable QuestionAnswerChoice input) {
        if (input == null) {
            throw new NullPointerException("input Question must not be null");
        }

        return new QuestionAnswerChoiceView(
                input.getId(),
                input.getDisplayValue(),
                input.isDisabled(),
                input.getDisplayImage(),
                input.getValue(),
                null
        );
    }

}
