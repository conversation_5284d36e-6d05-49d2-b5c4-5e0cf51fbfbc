# ui messages

# *********************
# domain backup
# *********************
# this is used during registration from the locale got from the domain
defaultCountry=GB
defaultTimeZoneId=Europe/London
defaultCurrency=GBP

# *********************
# main menu urls
# *********************
j_acegi_security_check.url=/nav/secure/j_acegi_security_check
j_spring_security_check.url=/nav/secure/j_spring_security_check

home.url=/nav/index.html
login.url=/nav/secure/login.html
welcome.url=/nav/secure/welcome.html
logout.url=/nav/secure/logout.html
logoutSuccess.url=/nav/secure/logoutSuccess.html
logoutExpiredSuccess.url=/nav/secure/logoutExpiredSuccess.html
addressProviderHidden.url=/nav/secure/addressProviderHidden.html
actionbarHidden.url=/nav/secure/actionbarHidden.html
noopHidden.url=/dynamic/noopHidden.html
jsRequired.url=/nav/secure/requirements.html#js

add.link=add {0}
edit.link=edit {0}
delete.link=delete {0}


# *********************
# main menu url text
# *********************
user=user
home=home
tasks=tasks

login=login
logins=logins

actionbar=action bar
myAccount=my details

welcome=welcome
logout=logout

tandcs=terms and conditions
aup=acceptable use policy
pp=privacy policy


# *********************
# form messages
# *********************
find.message=find {0}
view.message=the {0}
add.message=add a new {0}
edit.message=edit a {0}
delete.message=are you sure you want to delete this {0}?
search.message=find a {0}

feedback.message=to give feedback or suggestions, click {0}

enable=enable

deleted.message=the {0} has been successfully deleted
noEntities.message=you have no details saved here

question=?

page.message=page {0} of {1}

logout.message=Thank you for everything you do.
logout.canClose=You can now close your tab/window
logoutExpired.message=For security reasons you have been logged out due to inactivity.

jsRequired.message=to use this feature you need to enable javascript in your browser and try again

numbers=Numbers
addresses=Addresses


# *********************
# form text
# *********************
login.username.placeholder=e.g. fnightingale
username=username
password=password
passwordVerify=verify password

lookupAddress=address lookup
close=close

iagree=I accept this document

lastLoggedIn=last logged in

attachments=attachments
attach=attach

reminderEmail=email
reminderMobile=text/sms

advancedsearch=advanced find
clearsearch=clear filter
clearactionbar=clear action bar

byInitial=by initial on
byCategory=by group
#lastNameInitial=by last name initial
companyInitial=by company initial

all=all

date=date
time=time
at=at

%=%

address=address
town=town
county=
postCode=postcode
country=country

address.noResult=The postcode you entered is not recognised
address.demo=This service is disabled for the demo

# calendar event descriptions
calendarEvent=event
calendarWhoTo=reminder to
calendarWhat.message=event
calendarName=description
calendarAttendees=attendees
eventDate=date
repeatYears=yearly
calendarReminder=reminder
calendarMessage=alternative message
eventWhoTo.message=who the message goes to
daysAdvance=days advance
includeMe=me
remindCategory=add group
remindContact=add individual


# *********************
# tooltip
# *********************
showAddress.tooltip=This shows the name and address so it can be copied and pasted into a document and used for a letter/and or window envelope.
lookupAddress.tooltip=Enter a post code and click address lookup to automatically fill in the address

toggle.tooltip=Click to toggle for more options
actionbarIndividuals.tooltip=Drag contacts to the action bar and click some actions! You can also drag groups from the list above.
actionbarCompanies.tooltip=Drag contacts to the action bar and click some actions!
enableJs.tooltip=To use this feature, enable your browser's javascript and refresh the page.

calendarMessage.tooltip=content here gets sent instead of the event description


# *********************
# boxmessages
# *********************
entitySave.boxmessage = You need to save before continuing. Do you want to carry on without saving?
dontLogout.boxmessage = To keep your site secure, we log you out automatically after a period of inactivity. Do you want to remain logged in?
dontLogout = stay logged in


# *********************
# submit buttons/entity action text
# *********************
or=or
add=save
saved=saved
update=save
cancel=cancel
delete=delete
x=x
view=view
edit=edit
find=find
search=search
login=login
ok=ok
dontSave=don't save

copy=copy
appendDateTime=insert date &amp; time
save=save
backto=back to {0}
back=back

# odd wording, but more intuitive to user...
previous=back
next=next

list=list


# for displaying a save message
entityInPlaceSaved.boxmessage = saved successfully

