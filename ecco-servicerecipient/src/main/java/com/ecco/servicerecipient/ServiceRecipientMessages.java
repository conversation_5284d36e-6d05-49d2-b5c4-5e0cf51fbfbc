package com.ecco.servicerecipient;

import com.ecco.infrastructure.util.EccoMessageUtils;

public interface ServiceRecipientMessages {

    class Messages {

        public static String getIdentifierText(ServiceRecipientMessages input) {
            return EccoMessageUtils.getUiMessageSource().getMessage("sr.identifier." + input.getPrefix()) + ": " + input.getParentCode();
        }

    }

    String getPrefix();
    String getParentCode();
}
