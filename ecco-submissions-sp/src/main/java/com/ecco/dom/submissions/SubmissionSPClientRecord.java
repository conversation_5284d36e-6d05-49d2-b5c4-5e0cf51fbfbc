package com.ecco.dom.submissions;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

import com.ecco.dom.Referral;

@Entity
@DiscriminatorValue("spclientrecord")
public class SubmissionSPClientRecord extends Submission {

    @ManyToOne(optional=false, cascade={})
    @JoinColumn(name="referralId", insertable=true, updatable=false)
    Referral referral;

    public Referral getReferral() {
        return referral;
    }
    public void setReferral(Referral referral) {
        this.referral = referral;
    }

}
