package com.ecco.submissions.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.stereotype.Service;

import com.ecco.service.submissions.SubmissionServiceImpl;

@Configuration
//@Import(EntityServiceConfig.class)
@ImportResource("classpath:applicationContext-service-remote.xml")
@ComponentScan(basePackageClasses=SubmissionServiceImpl.class, includeFilters=@Filter(Service.class))
public class SubmissionConfig {

}
