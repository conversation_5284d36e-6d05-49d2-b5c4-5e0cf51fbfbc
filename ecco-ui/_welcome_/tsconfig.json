{
  "extends": "ecco-webpack-config/tsconfig/browser",
  "compilerOptions": {
    "types": [
      "application-properties",
      "ecco-spa-global",
      "ecco-webpack-config/types/browser",
      "../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"
    ],

    // FIXME Required for compatibility with legacy code, delete ASAP
    "skipLibCheck": true
  },
  "include": ["src"]
}
