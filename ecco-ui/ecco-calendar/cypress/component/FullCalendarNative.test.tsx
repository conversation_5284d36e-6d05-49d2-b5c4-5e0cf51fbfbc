import {mount} from "cypress/react";
import * as React from "react";
import FullCalendar from "@fullcalendar/react";
import timeGridPlugin from "@fullcalendar/timegrid";
import {calendarWithMsData} from "../../testUtils";

describe("render calendar", () => {
    it("layout", () => {
        mount(
            <FullCalendar
                events={calendarWithMsData}
                plugins={[timeGridPlugin]}
                initialView="timeGridDay"
            />
        );
    });
});
