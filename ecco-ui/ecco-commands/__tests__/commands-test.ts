import {CollectionMutator, New} from "../commands";
import {DomainObject} from "../DomainObject";
import {Uuid} from "@eccosolutions/ecco-crypto";

describe("New", () => {
    const uuid = Uuid.parse("7c75d9d8-cc93-414f-9d41-77452f847c1c");
    const domainObject = new DomainObject(uuid);
    const effect = new New(domainObject);

    const existingDomainObject = new DomainObject(uuid);
    const wrongDomainObject = new DomainObject(Uuid.parse("85fd1206-13eb-4004-b56a-31d307556c73"));

    it("gets Uuid of supplied DomainObject", () => {
        expect(effect.getObjectKey() == uuid.toString()).toBe(true);
    });

    it("gets supplied DomainObject", () => {
        expect(effect.getDomainObject()).toBe(domainObject);
    });

    it("inserts new DomainObject", () => {
        expect(effect.apply(null)).toBe(domainObject);
    });

    it("throws on existing DomainObject", () => {
        expect(() => effect.apply(existingDomainObject)).toThrow(Error);
    });

    it("throws on application to wrong DomainObject", () => {
        expect(() => effect.apply(wrongDomainObject)).toThrow(Error);
    });

    it("applies to collection", () => {
        const result = [1];

        const collectionMutator: CollectionMutator<typeof result, DomainObject> = {
            add: jest.fn(() => result),
            change: fail,
            remove: fail
        };

        expect(effect.applyToCollection(collectionMutator)).toBe(result);
        expect(collectionMutator.add).toHaveBeenCalledWith(uuid.toString(), domainObject);
        expect(collectionMutator.add).toHaveBeenCalledTimes(1);
    });
});
