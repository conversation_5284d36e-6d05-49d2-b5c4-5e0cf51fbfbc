window.alert = data => console.error("window.alert(" + data + ")");

// Suppress debug noise during test runs
console.debug = () => {}

window.applicationProperties = {applicationRootPath: "/jest-test/"} as ApplicationProperties;

/** See https://github.com/jestjs/jest/issues/11698#issuecomment-922351139 */
function fail(reason = "fail was called in a test.") {
    throw new Error(reason);
}
window.fail = fail;