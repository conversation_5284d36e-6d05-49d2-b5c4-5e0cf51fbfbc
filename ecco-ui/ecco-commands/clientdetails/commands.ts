import {
    BaseServiceRecipientCommandDto,
    ClientDetailAbstractSecretFields,
    NumberChangeOptional,
    StringChangeOptional,
    TaskNames
} from "ecco-dto";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {BaseUpdateCommand} from "../cmd-queue/commands";
import {BaseServiceRecipientTaskUpdateCommand} from "../referral/commands";

/* This module is for commands to do with updates to the client */

export class ClientAttributeUpdateCommand extends BaseUpdateCommand {
    protected change: StringChangeOptional;

    /** attributePath = e.g. residence (i.e. the property at the java end */
    constructor(protected clientId: number, protected attributePath: string) {
        super(`clients/${clientId}/commands/`);
        if (!attributePath) {
            throw new Error("Missing param 'attributePath'");
        }
    }

    /** this could potentially be a number, but until we hit that case, keeping tight as string.
     *  Note: for something like a building, the entity URI is required i.e. "building:123" */
    public changeAttribute(from: string, to: string) {
        this.change = this.asStringChange(from || null, to || null);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */
    public override hasChanges(): boolean {
        return this.attributePath != null || this.change != null;
    }

    public toDto(): ClientAttributeUpdateCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            clientId: this.clientId,
            attributePath: this.attributePath,
            change: this.change
        };
    }
}

export interface ClientAttributeUpdateCommandDto {
    uuid: string;
    commandUri: string;
    timestamp: string;
    clientId: number;
    attributePath: string;
    change: StringChangeOptional;
}

export interface ClientBaseCommandDto extends BaseServiceRecipientCommandDto {
    serviceRecipientId: number;
    taskName: string;
    taskHandle?: string;
}

export interface StaffDetailCommandDto extends ClientDetailAbstractCommandDto {}
export interface ClientWithContactUpdateCommandDto extends ClientDetailAbstractCommandDto {
    housingBenefitChange: StringChangeOptional;
    /** indicates whether this originates from an external system */
    externalSource?: boolean;
}
interface ClientDetailAbstractCommandDto extends ClientBaseCommandDto {
    codeChange: StringChangeOptional;
    firstNameChange: StringChangeOptional;
    lastNameChange: StringChangeOptional;
    knownAsChange: StringChangeOptional;
    pronounsChange: NumberChangeOptional;
    genderChange: NumberChangeOptional;
    genderAtBirthChange: NumberChangeOptional;
    birthDateChange: StringChangeOptional;
    firstLanguageChange: NumberChangeOptional;
    ethnicOriginChange: NumberChangeOptional;
    nationalityChange: NumberChangeOptional;
    maritalStatusChange: NumberChangeOptional;
    religionChange: NumberChangeOptional;
    disabilityChange: NumberChangeOptional;
    sexualOrientationChange: NumberChangeOptional;
    niChange: StringChangeOptional;
    nhsChange: StringChangeOptional;
    emailChange: StringChangeOptional;
    phoneNumberChange: StringChangeOptional;
    mobileNumberChange: StringChangeOptional;
    preferredContactMethodChange: StringChangeOptional;
    completeAtChange: StringChangeOptional;
}

class ClientDetailAbstractCommand extends BaseServiceRecipientTaskUpdateCommand {
    constructor(serviceRecipientId: number, taskName: string, taskHandle?: string) {
        super("update", serviceRecipientId, taskName, taskHandle);
    }

    private codeChange: StringChangeOptional;
    private firstNameChange: StringChangeOptional;
    private lastNameChange: StringChangeOptional;
    private knownAsChange: StringChangeOptional;
    private pronounsChange: NumberChangeOptional;
    private genderChange: NumberChangeOptional;
    private genderAtBirthChange: NumberChangeOptional;
    private birthDateChange: StringChangeOptional;
    private firstLanguageChange: NumberChangeOptional;
    private ethnicOriginChange: NumberChangeOptional;
    private nationalityChange: NumberChangeOptional;
    private maritalStatusChange: NumberChangeOptional;
    private religionChange: NumberChangeOptional;
    private disabilityChange: NumberChangeOptional;
    private sexualOrientationChange: NumberChangeOptional;
    private niChange: StringChangeOptional;
    private nhsChange: StringChangeOptional;
    // contact stuff (NB 'contacts' are currently not cmd-based)
    private emailChange: StringChangeOptional;
    private phoneNumberChange: StringChangeOptional;
    private mobileNumberChange: StringChangeOptional;
    private preferredContactMethodChange: StringChangeOptional;
    private completeAtChange: StringChangeOptional;

    public change(from: ClientDetailAbstractSecretFields, to: ClientDetailAbstractSecretFields) {
        this.changeCode(from.code, to.code);
        this.changeFirstName(from.firstName, to.firstName);
        this.changeLastName(from.lastName, to.lastName)
            .changeKnownAs(from.knownAs, to.knownAs)
            .changePronouns(from.pronounsId, to.pronounsId);
        this.changeGender(from.genderId, to.genderId);
        this.changeGenderAtBirth(from.genderAtBirthId, to.genderAtBirthId);
        this.changeBirthDate(from.birthDate, to.birthDate);
        this.changeFirstLanguage(from.firstLanguageId, to.firstLanguageId);
        this.changeEthnicOrigin(from.ethnicOriginId, to.ethnicOriginId);
        this.changeMaritalStatus(from.maritalStatusId, to.maritalStatusId);
        this.changeNationality(from.nationalityId, to.nationalityId);
        this.changeReligion(from.religionId, to.religionId);
        this.changeDisability(from.disabilityId, to.disabilityId);
        this.changeSexualOrientation(from.sexualOrientationId, to.sexualOrientationId);
        this.changeNi(from.ni, to.ni);
        this.changeNhs(from.nhs, to.nhs);
        this.changeEmail(from.email, to.email);
        this.changePhoneNumber(from.phoneNumber, to.phoneNumber);
        this.changeMobileNumber(from.mobileNumber, to.mobileNumber);
        this.changePreferredContactMethod(from.preferredContactMethod, to.preferredContactMethod);
        this.changeCompleteAt(
            EccoDateTime.parseIso8601(from.completeAt || null),
            EccoDateTime.parseIso8601IgnoringTimezone(to.completeAt || null)
        );
    }

    public changeCode(from: string | null | undefined, to: string | null | undefined) {
        this.codeChange = this.asStringChange(from || null, to || null);
        return this;
    }

    public changeFirstName(from: string | null | undefined, to: string | null | undefined) {
        this.firstNameChange = this.asStringChange(from || null, to || null);
        return this;
    }

    public changeLastName(from: string | null | undefined, to: string | null | undefined) {
        this.lastNameChange = this.asStringChange(from || null, to || null);
        return this;
    }

    public changeKnownAs(from: string | null | undefined, to: string | null | undefined) {
        this.knownAsChange = this.asStringChange(from || null, to || null);
        return this;
    }

    public changePronouns(from: number | null | undefined, to: number | null | undefined) {
        this.pronounsChange = this.asNumberChange(from || null, to || null);
        return this;
    }

    public changeGender(from: number | null | undefined, to: number | null | undefined) {
        this.genderChange = this.asNumberChange(from || null, to || null);
        return this;
    }

    public changeGenderAtBirth(from: number | null | undefined, to: number | null | undefined) {
        this.genderAtBirthChange = this.asNumberChange(from || null, to || null);
        return this;
    }

    public changeBirthDate(from?: EccoDate | string, to?: EccoDate | string) {
        this.birthDateChange = this.asDateChange(from || null, to || null);
        return this;
    }

    public changeFirstLanguage(from: number | null | undefined, to: number | null | undefined) {
        this.firstLanguageChange = this.asNumberChange(from || null, to || null);
        return this;
    }

    public changeEthnicOrigin(from: number | null | undefined, to: number | null | undefined) {
        this.ethnicOriginChange = this.asNumberChange(from || null, to || null);
        return this;
    }

    public changeNationality(from?: number, to?: number) {
        this.nationalityChange = this.asNumberChange(from || null, to || null);
        return this;
    }

    public changeMaritalStatus(from: number | null | undefined, to: number | null | undefined) {
        this.maritalStatusChange = this.asNumberChange(from || null, to || null);
        return this;
    }

    public changeReligion(from: number | null | undefined, to: number | null | undefined) {
        this.religionChange = this.asNumberChange(from || null, to || null);
        return this;
    }

    public changeDisability(from: number | null | undefined, to: number | null | undefined) {
        this.disabilityChange = this.asNumberChange(from || null, to || null);
        return this;
    }

    public changeSexualOrientation(from: number | null | undefined, to: number | null | undefined) {
        this.sexualOrientationChange = this.asNumberChange(from || null, to || null);
        return this;
    }

    public changeNi(from: string | null | undefined, to: string | null | undefined) {
        this.niChange = this.asStringChange(from || null, to || null);
        return this;
    }

    public changeNhs(from: string | null | undefined, to: string | null | undefined) {
        this.nhsChange = this.asStringChange(from || null, to || null);
        return this;
    }

    public changeEmail(from: string | null | undefined, to: string | null | undefined) {
        this.emailChange = this.asStringChange(from || null, to || null);
        return this;
    }

    public changePhoneNumber(from: string | null | undefined, to: string | null | undefined) {
        this.phoneNumberChange = this.asStringChange(from || null, to || null);
        return this;
    }

    public changeMobileNumber(from: string | null | undefined, to: string | null | undefined) {
        this.mobileNumberChange = this.asStringChange(from || null, to || null);
        return this;
    }

    public changePreferredContactMethod(
        from: string | null | undefined,
        to: string | null | undefined
    ) {
        this.preferredContactMethodChange = this.asStringChange(from || null, to || null);
        return this;
    }

    public changeCompleteAt(from: EccoDateTime | null, to: EccoDateTime | null) {
        this.completeAtChange = this.asLocalDateTimeChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.codeChange != null ||
            this.firstNameChange != null ||
            this.lastNameChange != null ||
            this.knownAsChange != null ||
            this.pronounsChange != null ||
            this.genderChange != null ||
            this.birthDateChange != null ||
            this.firstLanguageChange != null ||
            this.ethnicOriginChange != null ||
            this.nationalityChange != null ||
            this.maritalStatusChange != null ||
            this.religionChange != null ||
            this.disabilityChange != null ||
            this.sexualOrientationChange != null ||
            this.niChange != null ||
            this.nhsChange != null ||
            this.emailChange != null ||
            this.phoneNumberChange != null ||
            this.mobileNumberChange != null ||
            this.completeAtChange != null ||
            this.preferredContactMethodChange != null
        );
    }

    public toDto(): ClientDetailAbstractCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            codeChange: this.codeChange,
            firstNameChange: this.firstNameChange,
            lastNameChange: this.lastNameChange,
            knownAsChange: this.knownAsChange,
            pronounsChange: this.pronounsChange,
            birthDateChange: this.birthDateChange,
            genderChange: this.genderChange,
            genderAtBirthChange: this.genderAtBirthChange,
            firstLanguageChange: this.firstLanguageChange,
            ethnicOriginChange: this.ethnicOriginChange,
            nationalityChange: this.nationalityChange,
            maritalStatusChange: this.maritalStatusChange,
            religionChange: this.religionChange,
            disabilityChange: this.disabilityChange,
            sexualOrientationChange: this.sexualOrientationChange,
            niChange: this.niChange,
            nhsChange: this.nhsChange,
            emailChange: this.emailChange,
            phoneNumberChange: this.phoneNumberChange,
            mobileNumberChange: this.mobileNumberChange,
            preferredContactMethodChange: this.preferredContactMethodChange,
            completeAtChange: this.completeAtChange
        };
    }
}
/** We'll do this via the service recipient (which'll be fine for HR too).
 * This means that we will know via which referral a client's details got updated
 * Matches TaskClientDetailCommandViewModel.java
 */
export class ClientWithContactUpdateCommand extends ClientDetailAbstractCommand {
    constructor(serviceRecipientId: number, taskHandle?: string, private externalSource = false) {
        super(serviceRecipientId, TaskNames.clientWithContact, taskHandle);
    }

    private housingBenefitChange: StringChangeOptional;

    public changeHousingBenefit(from: string, to: string) {
        this.housingBenefitChange = this.asStringChange(from || null, to || null);
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.housingBenefitChange != null;
    }

    public override toDto(): ClientWithContactUpdateCommandDto {
        const dtoCmd: ClientWithContactUpdateCommandDto = {
            ...super.toDto(),
            housingBenefitChange: this.housingBenefitChange
        };
        if (this.externalSource) {
            dtoCmd.externalSource = true;
        }
        return dtoCmd;
    }
}

export class StaffDetailCommand extends ClientDetailAbstractCommand {
    constructor(serviceRecipientId: number) {
        super(serviceRecipientId, TaskNames.staffDetail);
    }

    public override toDto(): StaffDetailCommandDto {
        return {
            ...super.toDto()
        };
    }
}

/**
 * Change the residence of the client.
 * This refers to See ReferralTaskClientResidenceCommandDto.java.
 * However, the 'clientdetails.residenceId' also gets updated with AddressHistoryCommand.
 */
export interface ClientResidenceUpdateCommandDto extends BaseLocationUpdateCommandDto {
    residence: NumberChangeOptional;
}

interface BaseLocationUpdateCommandDto extends ClientBaseCommandDto {
    startDate: StringChangeOptional;
    endDate: StringChangeOptional;
}

class BaseLocationUpdateCommand extends BaseServiceRecipientTaskUpdateCommand {
    constructor(serviceRecipientId: number, taskName: string, taskHandle: string) {
        super("update", serviceRecipientId, taskName, taskHandle);
    }

    private startDate: StringChangeOptional;
    private endDate: StringChangeOptional;

    /** Set or clear a start date for this residence */
    public changeStartDate(from: EccoDate | string, to: EccoDate | string) {
        this.startDate = this.asDateChange(from || null, to || null);
        return this;
    }

    /** Set or clear an end date for this residence */
    public changeEndDate(from: EccoDate | string, to: EccoDate | string) {
        this.endDate = this.asDateChange(from || null, to || null);
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.startDate != null;
    }

    public toDto(): BaseLocationUpdateCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            startDate: this.startDate,
            endDate: this.endDate
        };
    }
}
export class ClientResidenceUpdateCommand extends BaseLocationUpdateCommand {
    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, TaskNames.accommodation, taskHandle);
    }
    private residence: NumberChangeOptional;

    public changeResidence(from: number, to: number) {
        this.residence = this.asNumberChange(from || null, to || null);
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.residence != null;
    }

    public override toDto(): ClientResidenceUpdateCommandDto {
        const superDto = super.toDto();
        return {
            ...superDto,
            residence: this.residence
        };
    }
}
export interface StaffLocationCommandDto extends BaseLocationUpdateCommandDto {
    location: NumberChangeOptional;
}

export class StaffPrimaryLocationCommand extends BaseLocationUpdateCommand {
    constructor(serviceRecipientId: number, taskHandle: string) {
        super(serviceRecipientId, TaskNames.staffLocation, taskHandle);
    }
    private location: NumberChangeOptional;

    public changeLocation(from: number, to: number) {
        this.location = this.asNumberChange(from || null, to || null);
        return this;
    }

    public override hasChanges() {
        return super.hasChanges() || this.location != null;
    }

    public override toDto(): StaffLocationCommandDto {
        const superDto = super.toDto();
        return {
            ...superDto,
            location: this.location
        };
    }
}
