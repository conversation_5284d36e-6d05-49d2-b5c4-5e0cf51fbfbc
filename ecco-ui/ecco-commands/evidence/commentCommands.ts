import {LocationDto, WorkEvidenceCommandDto} from "ecco-dto/evidence-dto";
import {Change, commandChangesToDto, CommandEffect, New} from "../commands";
import {
    asAddedRemoved,
    asArrayChange,
    asBooleanChange,
    asNumberChange,
    asStringChange,
    BaseOutcomeBasedWork,
    EvidenceGroup,
    RiskWorkEvidenceDto,
    SupportWork as SupportWorkDto
} from "ecco-dto";
import {RiskWork, SupportWork} from "./domain";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {EvidenceCommand} from "./EvidenceCommand";

// TODO: this could optionally take prevDto so that changes are calculated by the builder
export class CommentCommandBuilder {
    private dto = <WorkEvidenceCommandDto>{};

    constructor(
        private modifying: boolean,
        uuid: Uuid,
        private workUuid: Uuid,
        private serviceRecipientId: number,
        private evidenceGroup: EvidenceGroup,
        private taskName: string,
        private externalSource = false
    ) {
        this.dto.operation = modifying ? "update" : "add";
        this.dto.commandName = CommentCommand.discriminator;
        this.dto.uuid = uuid.toString();
        this.dto.timestamp = new Date().toISOString(); // the number of milliseconds elapsed since January 1, 1970 00:00:00 UTC
        this.dto.workUuid = workUuid.toString();
        this.dto.serviceRecipientId = serviceRecipientId;
        this.dto.evidenceGroup = evidenceGroup.name;
        this.dto.taskName = taskName;
        this.dto.externalSource = this.externalSource;
    }

    public changeWorkDate(from: string | null | undefined, to: string | null | undefined) {
        this.dto.workDate = asStringChange(from, to);
        return this;
    }

    /** If a scheduled visit, what was the scheduled start time and duration */
    public plannedStart(dateTimeIso8601: string | undefined) {
        this.dto.plannedWorkDateTime = dateTimeIso8601;
        return this;
    }

    public plannedMinsSpent(mins: number | undefined) {
        this.dto.plannedMinsSpent = mins;
        return this;
    }

    public changeComment(from: string | null | undefined, to: string | null | undefined) {
        this.dto.comment = asStringChange(from, to);
        return this;
    }

    // the 'from' here may not relate to list-defs, as they were an independent commenttypes table
    public changeCommentTypeId(from: number | null, to: number | null) {
        this.dto.commentTypeId = asNumberChange(from, to);
        return this;
    }

    public changeEventId(from: string | null, to: string) {
        this.dto.eventId = asStringChange(from, to);
        return this;
    }

    public changeClientStatusId(from: number | null, to: number) {
        this.dto.clientStatusId = asNumberChange(from, to);
        return this;
    }

    /**
     * NB false means no update is performed - see EvidenceCommentHandler.java
     */
    public changeClientStatusOkay(val: boolean) {
        if (val) {
            this.dto.clientStatusOkay = val;
        } else {
            delete this.dto.clientStatusOkay;
        }
        return this;
    }

    public changeMeetingStatusId(from: number | null, to: number) {
        this.dto.meetingStatusId = asNumberChange(from, to);
        return this;
    }
    /** As per meetingStatusId, it's the outcome of the event
     * but this is coming from the rota invoicing */
    public changeEventStatusId(from: number | null, to: number | null | undefined) {
        this.dto.eventStatusId = asNumberChange(from, to);
        return this;
    }

    /** This is used via invoicing (select from listDef named 'eventStatusRateId' */
    public changeEventStatusRateId(from: number | null, to: number | null) {
        this.dto.eventStatusRateId = asNumberChange(from, to);
        return this;
    }

    public changeLocationId(from: number | null, to: number | null) {
        this.dto.locationId = asNumberChange(from, to);
        return this;
    }

    public changeMinsSpent(from: number | null, to: number | null) {
        this.dto.minsSpent = asNumberChange(from, to);
        return this;
    }

    public changeMinsTravel(from: number | null, to: number) {
        this.dto.minsTravel = asNumberChange(from, to);
        return this;
    }

    public changeMileageTo(from: number | null, to: number) {
        this.dto.mileageTo = asNumberChange(from, to);
        return this;
    }

    public changeMileageDuring(from: number | null, to: number) {
        this.dto.mileageDuring = asNumberChange(from, to);
        return this;
    }

    public changeFlags(from: number[] | null, to: number[]) {
        this.dto.flagIds = asAddedRemoved(from || [], to);
        return this;
    }

    public changeRiskManagementRequired(from: boolean | null, to: boolean) {
        this.dto.riskManagementRequired = asBooleanChange(from, to);
        return this;
    }

    public changeRiskManagementHandled(from: string[] | null, to: string[]) {
        this.dto.riskManagementHandled = asArrayChange(from, to);
        return this;
    }

    public withAttachments(attachmentIdsToAdd: number[]) {
        this.dto.attachmentIdsToAdd = attachmentIdsToAdd;
    }

    public withReviewId(reviewId: number | undefined) {
        this.dto.reviewId = reviewId;
        return this;
    }

    public withLocation(location: GeolocationPosition | GeolocationPositionError) {
        this.dto.location = CommentCommandBuilder.fromLocation(location);
    }

    public static fromLocation(
        location: GeolocationPosition | GeolocationPositionError | undefined
    ) {
        let locationDto: LocationDto | undefined = undefined; // Don't include if not enabled
        if (location) {
            locationDto = {};
            const position = <GeolocationPosition>location;
            if (position.coords) {
                locationDto.lat = position.coords.latitude;
                locationDto.lon = position.coords.longitude;
                locationDto.time = position.timestamp;
            } else {
                locationDto.errorCode = (<GeolocationPositionError>location).code;
            }
        }
        return locationDto;
    }

    public build() {
        return CommentCommand.fromDto(this.dto);
    }
}

/** For building WorkEvidenceCommandDto which matches com.ecco.webApi.evidence.CommentCommandViewModel */
export class CommentCommand extends EvidenceCommand {
    public static discriminator = "comment";

    constructor(private dto: WorkEvidenceCommandDto, effects: CommandEffect<any>[]) {
        super(
            Uuid.parse(dto.uuid),
            effects,
            `service-recipients/${dto.serviceRecipientId}/evidence/${dto.evidenceGroup}/${dto.taskName}/comments/`,
            CommentCommand.discriminator
        );
    }

    public static create(
        modifying: boolean,
        workUuid: Uuid,
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        taskName: string,
        externalSource = false
    ) {
        return new CommentCommandBuilder(
            modifying,
            Uuid.randomV4(),
            workUuid,
            serviceRecipientId,
            evidenceGroup,
            taskName
        );
    }

    public static fromDto(dto: WorkEvidenceCommandDto): CommentCommand {
        switch (dto.evidenceGroup) {
            case EvidenceGroup.threat.name:
                return new RiskCommentCommand(dto);

            // TODO: Questionaire
            default:
                return new CommentCommands(dto);
        }
    }

    protected getCommandDto(): WorkEvidenceCommandDto {
        return this.dto;
    }
}

/** Create a work DTO from a command that modifies one.  This treats the command as being the item that
 * creates the work, and assumes that there will always be a CommentCommand
 */
function workCommentCommandToDto<T extends BaseOutcomeBasedWork>(
    commandDto: WorkEvidenceCommandDto
) {
    let dto = commandChangesToDto<WorkEvidenceCommandDto, T>(commandDto);
    dto.createdDate = dto.createdDate || commandDto.timestamp; // when editing we'll want to preserve created data
    dto.authorDisplayName = "me";
    dto.taskName = commandDto.taskName!!;
    dto.id = commandDto.workUuid;
    dto.serviceRecipientId = commandDto.serviceRecipientId;
    return dto;
}

export class SupportCommentCommand extends CommentCommand {
    constructor(commandDto: WorkEvidenceCommandDto) {
        let effects: CommandEffect<any>[] = [];
        const dto = workCommentCommandToDto<SupportWorkDto>(commandDto);

        let updateWorkItem = (work: SupportWork) => {
            let dtoOriginal = work.getDto();
            let dtoModified = commandChangesToDto(commandDto, dtoOriginal)!!;
            return SupportWork.fromDto(dtoModified);
        };

        // Create just the empty work, and then apply the change
        let work =
            "update" == commandDto.operation
                ? new Change(commandDto.workUuid, updateWorkItem)
                : new New(new SupportWork(Uuid.parse(commandDto.workUuid), dto));
        effects.push(work);

        super(commandDto, effects);
    }
}

export class RiskCommentCommand extends CommentCommand {
    constructor(commandDto: WorkEvidenceCommandDto) {
        let effects: CommandEffect<any>[] = [];
        const dto = workCommentCommandToDto<RiskWorkEvidenceDto>(commandDto);

        let updateWorkItem = (work: RiskWork) => {
            let dtoOriginal = work.getDto();
            let dtoModified = commandChangesToDto(commandDto, dtoOriginal)!!;
            return RiskWork.fromDto(dtoModified);
        };

        // Create just the empty work, and then apply the change
        // Create just the empty work, and then apply the change
        let work =
            "update" == commandDto.operation
                ? new Change(commandDto.workUuid, updateWorkItem)
                : new New(new RiskWork(Uuid.parse(commandDto.workUuid), dto));
        effects.push(work);
        super(commandDto, effects);
    }

    // supportCommentDealtWith
}

export class CommentCommands extends CommentCommand {
    constructor(commandDto: WorkEvidenceCommandDto) {
        let effects: CommandEffect<any>[] = [];
        const dto = workCommentCommandToDto<SupportWorkDto>(commandDto);

        let updateWorkItem = (work: SupportWork) => {
            let dtoOriginal = work.getDto();
            let dtoModified = commandChangesToDto(commandDto, dtoOriginal)!!;
            return SupportWork.fromDto(dtoModified);
        };

        // Create just the empty work, and then apply the change
        let work =
            "update" == commandDto.operation
                ? new Change(commandDto.workUuid, updateWorkItem)
                : new New(new SupportWork(Uuid.parse(commandDto.workUuid), dto));
        effects.push(work);

        super(commandDto, effects);
    }
}
