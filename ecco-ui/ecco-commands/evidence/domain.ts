import {Uuid} from "@eccosolutions/ecco-crypto";
import {BaseWork, QuestionnaireWorkDto, RiskWorkEvidenceDto, FormEvidence, SupportWork as SupportWorkDto} from "ecco-dto";
import {CustomFormFields} from "ecco-dto/evidence-dto";
import {New} from "../commands";
import {DomainObject} from "../DomainObject";

export class BaseEvidenceBasedWork<DTO extends BaseWork> extends DomainObject {
    constructor(uuid: Uuid, private dto: DTO, key?: string) {
        super(uuid, key);
    }

    getUuid(): Uuid {
        return this.uuid;
    }

    getDto() {
        return this.dto;
    }
}

export class RiskWork extends BaseEvidenceBasedWork<RiskWorkEvidenceDto> {
    constructor(uuid: Uuid, dto: RiskWorkEvidenceDto) {
        super(uuid, dto);
    }

    public create(dto: RiskWorkEvidenceDto): New<RiskWork> {
        return new New(new RiskWork(Uuid.randomV4(), dto));
    }

    public static fromDto(dto: RiskWorkEvidenceDto): RiskWork {
        return new RiskWork(Uuid.parse(dto.id), dto);
    }
}

export class SupportWork extends BaseEvidenceBasedWork<SupportWorkDto> {
    constructor(uuid: Uuid, dto: SupportWorkDto) {
        super(uuid, dto);
    }

    public create(dto: SupportWorkDto): New<SupportWork> {
        return new New(new SupportWork(Uuid.randomV4(), dto));
    }

    public static fromDto(dto: SupportWorkDto): SupportWork {
        return new SupportWork(Uuid.parse(dto.id), dto);
    }
}

export class QuestionnaireWork extends BaseEvidenceBasedWork<QuestionnaireWorkDto> {
    constructor(uuid: Uuid, dto: QuestionnaireWorkDto) {
        super(uuid, dto);
    }

    public create(dto: QuestionnaireWorkDto): New<QuestionnaireWork> {
        return new New(new QuestionnaireWork(Uuid.randomV4(), dto));
    }

    public static fromDto(dto: QuestionnaireWorkDto): QuestionnaireWork {
        return new QuestionnaireWork(Uuid.parse(dto.id), dto);
    }
}

export class FormEvidenceWork<
    T extends CustomFormFields = CustomFormFields
> extends BaseEvidenceBasedWork<FormEvidence<T>> {
    constructor(uuid: Uuid, dto: FormEvidence<T>, key: string) {
        super(uuid, dto, key);
    }

    public create(dto: FormEvidence<T>): New<FormEvidenceWork> {
        return new New(
            new FormEvidenceWork(Uuid.randomV4(), dto, `${dto.serviceRecipientId}-${dto.taskName}`)
        );
    }

    public static fromDto<T extends CustomFormFields>(dto: FormEvidence<T>): FormEvidenceWork {
        return new FormEvidenceWork(
            Uuid.parse(dto.id),
            dto,
            `${dto.serviceRecipientId}-${dto.taskName}`
        );
    }
}
