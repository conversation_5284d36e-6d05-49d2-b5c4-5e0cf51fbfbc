{"private": true, "name": "ecco-commands", "version": "0.0.0", "license": "UNLICENSED", "main": "./build-tsc/index.js", "typings": "./build-tsc/index.d.ts", "scripts": {"clean": "tsc --build --clean", "emit": "webpack --config-name=dev && eslint --ext .ts .", "build": "eslint --ext .ts . && webpack", "lint": "eslint --ext .ts .", "test": "yarn test-parallel-safe && yarn test-sequential", "test-parallel-safe": "jest", "test-sequential": "echo Nothing to do"}, "dependencies": {"@eccosolutions/ecco-common": "2.0.0", "@eccosolutions/ecco-crypto": "1.0.2", "ecco-dto": "^0.0.0", "jsonpatch": "3.1.0", "rxjs": "^5.1.1"}, "devDependencies": {"@babel/core": "^7.0.0", "@types/jest": "^29.5.2", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "application-properties": "0.0.0", "esbuild-loader": "^2.21.0", "eslint": "^7.14.0", "jest": "^29.5.0", "jest-cli": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "terser-webpack-plugin": "^4.2.3", "ts-jest": "^29.1.0", "typescript": "5.8.3", "webpack": "^5.101.0", "webpack-cli": "^6.0.1"}}