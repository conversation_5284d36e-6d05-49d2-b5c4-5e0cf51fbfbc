import {ServiceRecipientTaskBaseCommandDto} from "ecco-dto/evidence/evidence-command-dto";
import {
    ArrayChange,
    asArrayChange,
    BooleanChange,
    CommandDto,
    Mergeable,
    NumberChangeOptional,
    StringChangeOptional
} from "ecco-dto";
import {BaseUpdateCommandTransitioning} from "../cmd-queue/commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {EccoDate, EccoTime} from "@eccosolutions/ecco-common";


/** Matches interface ContractTaskContractDetailCommandViewModel.java */
export interface ContractTaskContractDetailCommandCommandViewModel extends ServiceRecipientTaskBaseCommandDto {

    operation: string;

    contractId: number | undefined;

    name: StringChangeOptional;

    startDateTime: StringChangeOptional;

    endDateTime: StringChangeOptional;

    PONumbers: StringChangeOptional;

    agreedCharge: NumberChangeOptional;

    contractTypeId: NumberChangeOptional;
}

/**
 * Command to add, remove or update
 */
export class ContractTaskContractDetailCommand extends BaseUpdateCommandTransitioning {
    public static discriminator = "contract";

    public static TASKNAME = "contractDetail";

    name: StringChangeOptional;

    startDateTime: StringChangeOptional;

    endDateTime: StringChangeOptional;

    PONumbers: StringChangeOptional;

    agreedCharge: NumberChangeOptional;

    contractTypeId: NumberChangeOptional;

    constructor(
        private operation: "add" | "update" | "remove",
        commandUuid: Uuid,
        private serviceRecipientId: number,
        private contractId?: number
    ) {
        super(
            `service-recipients/${serviceRecipientId.toString()}/tasks/` +
                ContractTaskContractDetailCommand.TASKNAME +
                "/",
            commandUuid,
            ContractTaskContractDetailCommand.discriminator
        );
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return this;
    }

    /** Add change data, but only if to != from */
    public changeStartDate(from: EccoDate | null, to: EccoDate) {
        this.startDateTime = this.asDateChange(from, to);
        return this;
    }

    public changeEndDate(from: EccoDate | null, to: EccoDate) {
        this.endDateTime = this.asDateChange(from, to);
        return this;
    }

    public changeName(from: string | null, to: string) {
        this.name = this.asStringChange(from, to);
        return this;
    }

    public changePONumbers(from: string | null, to: string) {
        this.PONumbers = this.asStringChange(from, to);
        return this;
    }

    public changeAgreedCharge(from: number | null, to: number) {
        this.agreedCharge = this.asNumberChange(from, to);
        return this;
    }

    public changeContractTypeId(from: number | null, to: number) {
        this.contractTypeId = this.asNumberChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */
    public override hasChanges(): boolean {
        return (
            this.startDateTime != null ||
            this.endDateTime != null ||
            this.name != null ||
            this.PONumbers != null ||
            this.agreedCharge != null ||
            this.contractTypeId != null ||
            this.operation == "add" ||
            this.operation == "remove"
        );
    }

    public getCommandDto(): ContractTaskContractDetailCommandCommandViewModel {
        return {
            uuid: this.uuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            contractId: this.contractId,
            //userComment: this.userComment,
            operation: this.operation,
            taskName: ContractTaskContractDetailCommand.TASKNAME,
            startDateTime: this.startDateTime,
            endDateTime: this.endDateTime,
            name: this.name,
            PONumbers: this.PONumbers,
            agreedCharge: this.agreedCharge,
            contractTypeId: this.contractTypeId
        };
    }
}


/** Matches interface RateCardCommandDto.java */
export interface RateCardCommandDto extends CommandDto {
    //BaseServiceRecipientCommandDto {

    operation: string;

    rateCardId?: number; // required for update

    /** ISO8601 format yyyy-MM-dd */
    startDateTime: StringChangeOptional;
    /** ISO8601 format yyyy-MM-dd */
    endDateTime: StringChangeOptional;

    name: StringChangeOptional;

    // advisoryTotalDuration: NumberChangeOptional;
    //
    // advisoryTotalCharge: NumberChangeOptional;

    chargeNameId: NumberChangeOptional;
    contractsChange: ArrayChange<number>;

    matchingPartsOfWeek: StringChangeOptional;
    matchingStartTime: StringChangeOptional;
    matchingEndTime: StringChangeOptional;
}

export class RateCardCommand extends BaseUpdateCommandTransitioning {
    public static discriminator = "rateCard";

    private startDateChange: StringChangeOptional;
    private endDateChange: StringChangeOptional;
    private nameChange: StringChangeOptional;
    private advisoryTotalDuration: NumberChangeOptional;
    private advisoryTotalCharge: NumberChangeOptional;
    private chargeNameId: NumberChangeOptional;
    private contractsChange: ArrayChange<number>;

    private matchingPartsOfWeek: StringChangeOptional;
    private matchingStartTime: StringChangeOptional;
    private matchingEndTime: StringChangeOptional;

    constructor(
        private operation: "add" | "update" | "remove",
        commandUuid: Uuid,
        private serviceRecipientId: number | undefined,
        private rateCardId?: number
    ) {
        super(`contracts/rateCards/`, commandUuid, RateCardCommand.discriminator);
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return this;
    }

    /** Add change data, but only if to != from */
    public changeStartDate(from: EccoDate | null, to: EccoDate) {
        this.startDateChange = this.asStringChange(
            from && from.toDateTimeMidnight().formatIso8601(),
            to && to.toDateTimeMidnight().formatIso8601()
        );
        return this;
    }

    public changeEndDate(from: EccoDate | null, to: EccoDate) {
        this.endDateChange = this.asStringChange(
            from && from.toDateTimeMidnight().formatIso8601(),
            to && to.toDateTimeMidnight().formatIso8601()
        );
        return this;
    }

    public changeName(from: string | null, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }
    public changeChargeNameId(from: number | null, to: number) {
        this.chargeNameId = this.asNumberChange(from, to);
        return this;
    }
    public changeContracts(from: number[] | null, to: number[] | null) {
        this.contractsChange = asArrayChange(from, to);
        return this;
    }
    public changeMatchingPartsOfWeek(from: string | null, to: string | null) {
        this.matchingPartsOfWeek = this.asStringChange(from, to);
        return this;
    }
    public changeMatchingStartTime(from: EccoTime | null, to: EccoTime | null) {
        this.matchingStartTime = this.asLocalTimeChange(from, to);
        return this;
    }
    public changeMatchingEndTime(from: EccoTime | null, to: EccoTime | null) {
        this.matchingEndTime = this.asLocalTimeChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */
    public override hasChanges(): boolean {
        return (
            this.startDateChange != null ||
            this.endDateChange != null ||
            this.nameChange != null ||
            this.chargeNameId != null ||
            this.contractsChange != null ||
            this.advisoryTotalCharge != null ||
            this.advisoryTotalDuration != null ||
            this.matchingPartsOfWeek != null ||
            this.matchingStartTime != null ||
            this.matchingEndTime != null ||
            this.operation == "add" ||
            this.operation == "remove"
        );
    }

    public getCommandDto(): RateCardCommandDto {
        return {
            uuid: this.uuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            //serviceRecipientId: this.serviceRecipientId,
            rateCardId: this.rateCardId,
            //userComment: this.userComment,
            operation: this.operation,
            startDateTime: this.startDateChange,
            endDateTime: this.endDateChange,
            name: this.nameChange,
            chargeNameId: this.chargeNameId,
            contractsChange: this.contractsChange,
            matchingPartsOfWeek: this.matchingPartsOfWeek,
            matchingStartTime: this.matchingStartTime,
            matchingEndTime: this.matchingEndTime
        };
    }
}


interface MatchingFactorsDto {
}

/** Matches interface RateCardEntryCommandDto.java */
export interface RateCardEntryCommandDto extends CommandDto {
    //BaseServiceRecipientCommandDto {

    operation: string;

    rateCardId: number;

    rateCardEntryId?: number; // required for update

    parentRateCardEntryId?: number;

    disabled: BooleanChange;

    defaultEntry: BooleanChange;

    matchingCategoryTypeId: NumberChangeOptional;

    matchingChargeCategoryId: NumberChangeOptional;

    addedToMatchingFactors: MatchingFactorsDto[] | undefined;

    removedFromMatchingFactors: string[];

    chargeTypeFixedTemporal: StringChangeOptional;

    fixedCharge: NumberChangeOptional;

    unitMeasurementId: NumberChangeOptional;
    units: NumberChangeOptional;

    unitCharge: NumberChangeOptional;

    unitsToRepeatFor: NumberChangeOptional;
}

export class RateCardEntryCommand extends BaseUpdateCommandTransitioning {
    public static discriminator = "rateCardEntry";

    private parentRateCardEntryId?: number;
    private disabled: BooleanChange;
    private defaultEntry: BooleanChange;
    private matchingCategoryTypeId: NumberChangeOptional;
    private matchingChargeCategoryId: NumberChangeOptional;
    private addedToMatchingFactors: MatchingFactorsDto[] = [];
    private removedFromMatchingFactors: string[] = [];
    private chargeTypeFixedTemporal: StringChangeOptional;
    private fixedCharge: NumberChangeOptional;
    private unitMeasurementId: NumberChangeOptional;
    private unitCharge: NumberChangeOptional;
    private unitsToRepeatFor: NumberChangeOptional;
    private units: NumberChangeOptional;

    constructor(
        private operation: "add" | "update" | "remove",
        commandUuid: Uuid,
        private serviceRecipientId: number | undefined,
        private rateCardId: number,
        private rateCardEntryId?: number
    ) {
        super(`contracts/rateCardEntries/`, commandUuid, RateCardEntryCommand.discriminator);
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return this;
    }

    // required for new appointment
    public changeChargeCategoryId(from: number | null, to: number) {
        this.matchingChargeCategoryId = this.asNumberChange(from, to);
        return this;
    }
    // required for new appointment
    public changeChargeTypeFixedTemporal(from: string | null, to: string) {
        this.chargeTypeFixedTemporal = this.asStringChange(from, to);
        return this;
    }
    // required for new appointment
    public changeUnitMeasurement(from: number | null, to: number) {
        this.unitMeasurementId = this.asNumberChange(from, to);
        return this;
    }
    public changeFixedCharge(from: number | null, to: number | null) {
        this.fixedCharge = this.asNumberChange(from, to);
        return this;
    }
    public changeUnitCharge(from: number | null, to: number | null) {
        this.unitCharge = this.asNumberChange(from, to);
        return this;
    }
    public changeDefaultEntry(from: boolean | null, to: boolean) {
        this.defaultEntry = this.asBooleanChange(from, to);
        return this;
    }
    public changeDisabledEntry(from: boolean | null, to: boolean) {
        this.disabled = this.asBooleanChange(from, to);
        return this;
    }
    public changeUnitsToRepeatFor(from: number | null, to: number | null) {
        this.unitsToRepeatFor = this.asNumberChange(from, to);
        return this;
    }
    public changeUnits(from: number | null, to: number | null) {
        this.units = this.asNumberChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */
    public override hasChanges(): boolean {
        return (
            this.disabled != null ||
            this.defaultEntry != null ||
            this.matchingCategoryTypeId != null ||
            this.matchingChargeCategoryId != null ||
            (this.addedToMatchingFactors != null && this.addedToMatchingFactors.length > 0) ||
            (this.removedFromMatchingFactors != null &&
                this.removedFromMatchingFactors.length > 0) ||
            this.chargeTypeFixedTemporal != null ||
            this.fixedCharge != null ||
            this.unitMeasurementId != null ||
            this.unitCharge != null ||
            this.unitsToRepeatFor != null ||
            this.units != null ||
            this.operation == "add" ||
            this.operation == "remove"
        );
    }

    public getCommandDto(): RateCardEntryCommandDto {
        return {
            uuid: this.uuid.toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            //serviceRecipientId: this.serviceRecipientId,
            rateCardId: this.rateCardId,
            rateCardEntryId: this.rateCardEntryId,
            //userComment: this.userComment,
            operation: this.operation,
            disabled: this.disabled,
            defaultEntry: this.defaultEntry,
            matchingCategoryTypeId: this.matchingCategoryTypeId,
            matchingChargeCategoryId: this.matchingChargeCategoryId,
            addedToMatchingFactors: undefined, // TODO
            removedFromMatchingFactors: this.removedFromMatchingFactors,
            chargeTypeFixedTemporal: this.chargeTypeFixedTemporal,
            fixedCharge: this.fixedCharge,
            unitMeasurementId: this.unitMeasurementId,
            units: this.units,
            unitCharge: this.unitCharge,
            unitsToRepeatFor: this.unitsToRepeatFor
        };
    }
}
