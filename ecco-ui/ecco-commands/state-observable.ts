import {DomainObject} from "./DomainObject";
import {CollectionSubscription, ObjectSubscription} from "./subscriptions";
import {Observable} from "rxjs";

export interface ObjectState<TDomainObject extends DomainObject> {
    /** The current state of the Domain Object, after pending effects
     * have been applied.
     *
     * `null` if the Domain Object has been deleted or does not exist. */
    readonly current: TDomainObject | null;

    // TODO: The `change` property can't be implemented yet.
    // See the definition of the `StateChange` type for why.
    // In the meantime you can compare `committed` to `current` instead.
    // readonly change: StateChange<TDomainObject>;

    /** The state of the Domain Object as committed to the database.
     *
     * `null` if the Domain Object does not (yet) exist in the database. */
    readonly committed: TDomainObject | null;
}

export type CollectionState<TDomainObject extends DomainObject> = ReadonlyMap<
    string,
    {
        /** The current state of the Domain Object, after pending effects
         * have been applied. */
        readonly current: TDomainObject;

        // TODO: The `change` property can't be implemented yet.
        // See the definition of the `StateChange` type for why.
        // In the meantime you can compare `committed` to `current` instead.
        // readonly change: StateChange<TDomainObject>;

        /** The state of the Domain Object as committed to the database. */
        readonly committed: TDomainObject;
    }
>;

// TODO: This type can't be used yet because DomainObject is a
// class with getter methods instead of a plain object. That means that we
// can't define a useful mapped type based on DomainObject.
// In the future we should change all DomainObjects to be plain objects with
// readonly fields, and no methods defined.
export type StateChange<T> = T extends symbol | string | number | boolean
    ? boolean
    : T extends ReadonlyArray<infer E>
    ? ReadonlyArray<StateChange<E>>
    : T extends Readonly<Record<infer K, infer V>>
    ? Readonly<Record<K, StateChange<V>>>
    : never;

export function objectStateObservableFromSubscription<TDomainObject extends DomainObject>(
    subscription: ObjectSubscription<TDomainObject>
): Observable<ObjectState<TDomainObject>> {
    const committed = Observable.fromPromise(subscription.getSnapshot());
    const effects = subscription.getEffects();
    return committed.switchMap(committed =>
        effects
            .scan((current, effect) => effect.apply(current), committed)
            .map(current => ({committed, current}))
    );
}

export function oldCollectionStateObservableFromSubscription<TDomainObject extends DomainObject>(
    subscription: CollectionSubscription<TDomainObject>
): Observable<CollectionState<TDomainObject>> {
    return (subscription.getSnapshots() as unknown as Observable<TDomainObject>) // WAS THIS
        .reduce(
            (map, object) =>
                map.set(object.getKey().toString(), {current: object, committed: object}),
            new Map<string, {readonly current: TDomainObject; readonly committed: TDomainObject}>()
        )
        .switchMap(initial =>
            subscription.getEffects().scan((state, effect) => {
                const key = effect.getObjectKey().toString();
                const objectState = state.get(key);
                if (objectState == null) {
                    return state;
                }
                const updated = effect.apply(objectState.current);
                const nextState = new Map(state);
                if (updated == null) {
                    nextState.delete(key);
                } else {
                    nextState.set(key, {current: updated, committed: objectState.committed});
                }
                return nextState;
            }, initial)
        );
}

/**
 * Given a subscription emit a new CollectionState for each effect
 */
export function collectionStateObservableFromSubscription<TDomainObject extends DomainObject>(
        subscription: CollectionSubscription<TDomainObject>
): Promise<Observable<CollectionState<TDomainObject>>> {
    return subscription
        .getSnapshots()
        .then( it => {
            const snapshots = it.reduce(
                    (map, [uuid, object]) => map.set(uuid, {current: object, committed: object}),
                    new Map<string, { readonly current: TDomainObject; readonly committed: TDomainObject }>()
            )
            const result = subscription.getEffects().scan((state, effect) => {
                    const key = effect.getObjectKey().toString();
                    const objectState = state.get(key);
                    if (objectState == null) {
                        return state;
                    }
                    const updated = effect.apply(objectState.current);
                    const nextState = new Map(state);
                    if (updated == null) {
                        nextState.delete(key);
                    } else {
                        nextState.set(key, {current: updated, committed: objectState.committed});
                    }
                    return nextState;
                },
                snapshots)
            return result;
        }
    );
}