import {
    createStyles,
    DialogTitle,
    Grid,
    Icon<PERSON>utton,
    makeStyles,
    Typography
} from "@eccosolutions/ecco-mui";
import ArrowBackRounded from "@material-ui/icons/ArrowBackRounded";
import Close from "@material-ui/icons/Close";
import Help from "@material-ui/icons/Help";
import Print from "@material-ui/icons/Print";
import * as React from "react";
import {FC} from "react";

const useStyles = makeStyles(theme =>
    createStyles({
        root: {
            margin: 0,
            padding: theme.spacing(2),
            flexGrow: 0
        },
        titleIndent: {
            marginLeft: theme.spacing(1)
        }
    })
);

export interface DialogTitleProps {
    id: string;
    children: React.ReactNode;
    /** Use back arrow instead of close x */
    back?: boolean | undefined;
    onClose: () => void;
    /** Search for helpTitle, helpContent for where this is used */
    onHelp?: (() => void) | undefined;
    onPrint?: (() => void) | undefined;
}

/** See https://material-ui.com/components/dialogs/#customized-dialogs */
export const DialogTitleWithClose: FC<DialogTitleProps> = props => {
    const {children, onClose, onHelp, onPrint, back, ...other} = props;
    const classes = useStyles();
    return (
        <DialogTitle disableTypography className={classes.root} {...other}>
            {/*&nbsp; fixes the 'bottom half of cross is not clickable' because
               the button is absolute, so when there is no title, then id="modal-title"
               is empty and the content of the dialog starts over the close icon */}
            <Grid container spacing={0}>
                {back ? (
                    <Grid item xs="auto">
                        <IconButton aria-label="back" onClick={onClose}>
                            <ArrowBackRounded />
                        </IconButton>
                    </Grid>
                ) : null}
                <Grid item xs>
                    <div className={back ? undefined : classes.titleIndent}>
                        <Typography variant="h6">{children || <>&nbsp;</>}</Typography>
                    </div>
                </Grid>
                {onHelp ? (
                    <Grid item xs="auto">
                        <IconButton aria-label="help" onClick={onHelp}>
                            <Help />
                        </IconButton>
                    </Grid>
                ) : null}
                {onPrint ? (
                    <Grid item xs="auto">
                        <IconButton aria-label="print" onClick={onPrint}>
                            <Print />
                        </IconButton>
                    </Grid>
                ) : null}
                {/* title always has a back or close icon */}
                {!back ? (
                    <>
                        {/* ensure there is a gap before any close icon */}
                        <Grid item xs={1}>
                            <div>&nbsp;</div>
                        </Grid>
                        <Grid item xs="auto">
                            <IconButton
                                aria-label="close"
                                title="Close/Cancel (Esc)"
                                onClick={onClose}
                            >
                                <Close />
                            </IconButton>
                        </Grid>
                    </>
                ) : null}
            </Grid>
        </DialogTitle>
    );
};
DialogTitleWithClose.displayName = "DialogTitleWithClose";
