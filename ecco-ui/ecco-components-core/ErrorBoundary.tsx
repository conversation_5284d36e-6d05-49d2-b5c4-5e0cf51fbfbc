import * as React from "react";
import {ErrorInfo} from "react";
import {EccoDateTime} from "@eccosolutions/ecco-common";

interface State {
    error: Error | null;
    errorInfo: ErrorInfo | null;
}

/* A direct copy of <PERSON>bra<PERSON>'s example at https://codepen.io/gaearon/pen/wqvxGa */
export class ErrorBoundary extends React.Component<{}, State> {
    constructor(props: {}) {
        super(props);
        this.state = {error: null, errorInfo: null};
    }

    override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        // Catch errors in any components below and re-render with error message
        this.setState({
            error: error,
            errorInfo: errorInfo
        });
        // You can also log error messages to an error reporting service here
    }

    override render() {
        if (this.state.errorInfo) {
            // Error path
            return (
                <div style={{padding: "0 16px"}}>
                    <h2>Something went wrong.</h2>
                    <p>Sometimes an error may occur if you have intermittent network connection.</p>
                    <p>
                        If you see this more than once, then please click on 'details' below and
                        send us a screenshot so that we can investigate. Once done, you may find it
                        helpful to refresh the page to get going again.
                    </p>
                    <details style={{whiteSpace: "pre-wrap"}}>
                        <h4>
                            time: {EccoDateTime.nowUtc().formatIso8601()} at {window.location.href}
                        </h4>
                        {this.state.error && this.state.error.toString()}
                        <br />
                        {this.state.errorInfo.componentStack}
                    </details>
                </div>
            );
        }
        // Normally, just render children
        return this.props.children;
    }
}
