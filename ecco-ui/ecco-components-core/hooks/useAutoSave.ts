import {useEffect, useState} from "react";
import {useDebounce} from "./useDebounce";
import {delay, EccoDate} from "@eccosolutions/ecco-common";
import {getJsonLocalStorage} from "./useXxStorage";

export const todayIso8601 = EccoDate.todayLocalTime().formatIso8601();
export const expiryIso8601 = EccoDate.todayLocalTime().addDays(4).formatIso8601();

const storage = getJsonLocalStorage();

/** Provide auto save function where data is saved to localstorage if autoSaveKey is provided
 *
 * @param value
 * @param onChange
 * @param autoSaveKey key to use for localStorage. if undefined, then auto save is not active.
 * @return text to show as current auto save status.
 */
export function useAutoSave<T>(
    value: T | null,
    onChange: (value: T | null) => void,
    autoSaveKey?: string
): string | undefined {
    if (!autoSaveKey) {
        return undefined;
    }

    const [status, setStatus] = useState<string | undefined>(undefined);
    let debouncedValue = useDebounce(value, 2000);

    if (!debouncedValue) {
        debouncedValue = storage.get(autoSaveKey);
    }

    // On mount, restore if value is null and we have an auto-saved value stored
    useEffect(() => {
        const stored = storage.get<T>(autoSaveKey);
        if (stored && !value) {
            onChange(stored);
            setStatus("draft restored");
            delay(2000).then(() => setStatus(undefined));
        }
    }, [autoSaveKey]);

    // If debounced value changes, then save it
    useEffect(() => {
        if (!storage.matches(autoSaveKey, debouncedValue) && debouncedValue != null) {
            storage.set(autoSaveKey, debouncedValue);
            setAutoSaveExpiry(autoSaveKey);
            setStatus("draft saved");
            delay(2000).then(() => setStatus(undefined));
        }
    }, [debouncedValue]);

    // If we've erased content
    useEffect(() => {
        if (debouncedValue && !value) {
            storage.set(autoSaveKey, undefined);
            removeDrafts(autoSaveKey);
            setStatus("draft erased");
            delay(2000).then(() => setStatus(undefined));
        }
    }, [value, debouncedValue]);

    return status;
}

export function removeDrafts(autoSaveKeyPrefix: string) {
    for (let key in localStorage) {
        if (key.indexOf(autoSaveKeyPrefix) == 0) {
            localStorage.removeItem(key);
            // console.log("draft: removed " + key);
            let expiryKey = `autosave-expiry-${key}`;
            localStorage.removeItem(expiryKey);
        }
    }
}

function setAutoSaveExpiry(key: string): void {
    localStorage[`autosave-expiry-${key}`] = expiryIso8601;
    //console.log("draft: added " + key);
}

function purgeAutoSave() {
    // if purged today, then ignore
    if (localStorage["autosave-last-purged"] == todayIso8601) {
        return;
    }
    let today = EccoDate.parseIso8601(todayIso8601);
    for (let key in localStorage) {
        const prefix = "autosave-expiry-";
        if (key.indexOf(prefix) == 0) {
            let expiresStr = localStorage[key] as string;
            let expires = EccoDate.parseIso8601(expiresStr);
            if (expires.compare(today) == -1) {
                removeDrafts(key.substr(prefix.length));
                localStorage.removeItem(key);
            }
        }
    }
    localStorage["autosave-last-purged"] = todayIso8601;
    //console.log("draft: set autosave-last-purged " + todayIso8601);
}

// Trigger once per page load
purgeAutoSave();
