import {EccoDate} from "@eccosolutions/ecco-common";
import update, {Spec} from "immutability-helper";

/**
 * Create an update for use with immutability helpers which sets the value at our given propertyPath.
 * TODO: Specifically we need to detect if the parents of the path don't exist, and create them.
 * So, instead of $set at a path that doesn't exist, we need to $set an object that is the path
 */
export function createPathUpdate<STATE>(state: STATE, propertyPath: string, value: any) {
    // TODO fix any
    return update(state, createDelta(propertyPath, value) as any); // 'as any' is to work around https://github.com/microsoft/TypeScript/issues/34933
}

function createDelta<TARGET>(
    propertyKey: string,
    value: string | boolean | number | EccoDate | Event
): Spec<TARGET> {
    const parts = propertyKey.split(".");
    return buildDelta(parts, 0, value) as Spec<TARGET>;
}

function buildDelta(
    paths: string[],
    pathIndex: number,
    value: string | boolean | number | EccoDate | Event
) {
    const toSet =
        pathIndex == paths.length - 1 ? {$set: value} : buildDelta(paths, pathIndex + 1, value);
    const delta = {} as {[key: string]: object};
    delta[paths[pathIndex]] = toSet;
    return delta;
}

export function resolvePath<T>(obj: any, path: string): T | null {
    const parts = path.split(".");
    let current = obj as {[key: string]: object};
    for (let i = 0; i < parts.length; i++) {
        if (typeof current[parts[i]] != "object" && i < parts.length - 1) {
            // Abort with null if non-object and we haven't finished
            return null;
        }
        current = (current as {[key: string]: any})[parts[i]];
    }
    return current as any as T | null;
}

/** @returns No validation state if not required, otherwise "success" or "error" */
export function calcValidationState(
    required: boolean,
    value: EccoDate | string | number | boolean | null
) {
    return required ? (value == null ? "error" : "success") : null;
}

/** @returns No validation state if not required, otherwise "success" or "error" */
export function calcPatternValidationState(
    pattern: RegExp
): (
    required: boolean | undefined,
    value: EccoDate | string | number | boolean | null
) => "error" | "success" | undefined | null {
    return (required: boolean | undefined, value: EccoDate | string | number | boolean | null) => {
        const valStr = value as string;
        return required
            ? // If required it must pass the pattern
              pattern.test(valStr)
                ? "success"
                : "error"
            : // If not required it only has to pass pattern if not null or empty
            value == null || valStr.length == 0 || pattern.test(valStr)
            ? null
            : "error";
    };
}

export function numberFromHtmlInput(
    eventTarget: EventTarget,
    decimalPlaces: number
): number | null {
    let value = (eventTarget as HTMLInputElement).value;
    if (!value) {
        return null;
    }

    // parse and take the number as-is to the required dp (at least for positive numbers - otherwise use .round)
    let valueNum = parseFloat(value);
    if (isNaN(valueNum)) {
        return value as unknown as number; // TODO: This is a hack to allow dropdownList to work with strings too
    }
    valueNum = Math.floor(valueNum * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces);
    return valueNum;
}

export function stringFromHtmlInput(eventTarget: EventTarget): string | null {
    let value = (eventTarget as HTMLInputElement).value;
    return value ? value.toString() : null;
}
