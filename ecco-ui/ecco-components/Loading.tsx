import * as React from "react";
import {Component, ReactNode, Suspense} from "react";
import {ErrorBoundary} from "ecco-components-core";
import {CircularProgress, Dialog, DialogContent, Typography} from "@eccosolutions/ecco-mui";
import * as ReactDOM from "react-dom";
import {ProgressState, stringifyPossibleError} from "ecco-dto";

const Progress = ({useProgress}: {useProgress: () => ProgressState}) => {
    const state = useProgress();
    // NOTE: We add a bit to the total so that a single initial request shows as no more that 25%
    const percent = state.total ? (state.count * 100) / Math.max(state.total, 4) : 25;
    return (
        <CircularProgress variant={state.total ? "determinate" : "indeterminate"} value={percent} />
    );
};

/** Note dependency on Font Awesome and styles for vertical-center and text-center */
export const LoadingSpinner = ({
    useProgress
}: {
    useProgress?: (() => ProgressState) | undefined;
}) => {
    return (
        <div className="vertical-center">
            <div className="container center">
                {useProgress ? (
                    <Progress useProgress={useProgress} />
                ) : (
                    <i className="fa fa-2x fa-spinner fa-spin" style={{color: "#3b80cb"}} />
                )}
            </div>
        </div>
    );
};

/**
 * Allows the following to simplify error handling flow
 * <pre>
 *   const {resolved, error} = usePromises([getPromise1, getPromise2])
 *   if (!resolved) return <LoadingOrError error={error}/>
 * </pre>
 */
export function LoadingOrError({error}: {error?: any}) {
    return error ? <pre>{stringifyPossibleError(error)}</pre> : <LoadingSpinner />;
}

export function LoadingSpinnerDialogWithMessage(props: {show: boolean; message: ReactNode}) {
    return (
        <Dialog maxWidth="sm" fullWidth={true} open={props.show}>
            <DialogContent style={{minHeight: 250}}>
                <Typography>{props.message}</Typography>
                <div style={{minHeight: "20vh", display: "flex", alignItems: "center"}}>
                    <i
                        className="center-block fa fa-2x fa-spin fa-spinner"
                        style={{color: "#3b80cb"}}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
}

export const SmallSpinner = () => (
    <i className="fa fa-spinner fa-spin" style={{color: "#3b80cb"}} />
);

export class Loading extends Component<{loaded: boolean}> {
    override render() {
        return this.props.loaded ? this.props.children : <LoadingSpinner />;
    }
}

/** Show LoadingSpinner instead of children when React.lazy was used to import the component and we're waiting */
export const handleLazy = (child: ReactNode) => (
    <ErrorBoundary>
        <Suspense fallback={<LoadingSpinner />}>{child}</Suspense>
    </ErrorBoundary>
);

const mountPoint = document.createElement("div");
const unmount = () => {
    ReactDOM.unmountComponentAtNode(mountPoint);
};

/** Show the loading spinner dialog until the promise either resolves or throws */
export function showLoadingDialogUntilResolved<T>(
    promise: Promise<T>,
    message?: ReactNode | undefined
): Promise<T> {
    ReactDOM.render(<LoadingSpinnerDialogWithMessage show={true} message={message} />, mountPoint);

    return promise.then(
        r => {
            unmount();
            return r;
        },
        reason => {
            unmount();
            throw reason;
        }
    );
}