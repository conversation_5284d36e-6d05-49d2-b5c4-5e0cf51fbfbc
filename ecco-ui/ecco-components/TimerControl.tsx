
import * as React from "react";
import {Dispatch, useEffect} from "react";
import {createContext} from "react";
import {useContext} from "react";
import {useReducer} from "react";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {bus} from "@eccosolutions/ecco-common";
import {Button} from "@eccosolutions/ecco-mui";
import {Typography} from "@eccosolutions/ecco-mui";

// NB the timer only runs for the TimerComponent class at the bottom of the file
export class TimerEvent {
    constructor(public running: boolean) {
    }
}
const timerEvents = bus<TimerEvent>();




interface TimerProps {
}

interface TimerState {
    timerStarted: EccoDateTime | undefined;
    timerStopped: EccoDateTime | undefined;
    timerMins: number;
}

const timerInitialState: TimerState = { timerStarted: undefined, timerStopped: undefined, timerMins: 0 };

// ACTIONS
type TimerActionType =
    {type: 'reset'} |
    {type: 'start'} |
    {type: 'tick'} |
    {type: 'stop'};

type TimerReducer = React.Reducer<TimerState, TimerActionType>;
const timerReducer: TimerReducer = (state: TimerState, action: TimerActionType) => {
    switch (action.type) {
        case 'reset':
            return timerInitialState;
        case 'start':
            return {
                ...state,
                timerStarted: EccoDateTime.nowLocalTime()
            };
        case 'tick':
            return {
                ...state,
                timerMins: state.timerMins + 1
            };
        case 'stop':
            return {
                ...state,
                timerStopped: EccoDateTime.nowLocalTime()
            };
        default:
            throw new Error();
    }
};

// CONTEXT
interface TimerContextProps {
    state: TimerState;
    dispatch: Dispatch<TimerActionType>;
}
const TimerContext = createContext<TimerContextProps>({} as TimerContextProps);

// CONTEXT CONSUMER
export const useTimerContext = (): TimerContextProps => {
    const ctx = useContext(TimerContext);
    if (!ctx) {
        throw new Error('You probably forgot to put Context.Provider somewhere');
    }
    return ctx;
};

// CONTEXT PROVIDER
interface TimerContextProviderProps {
    initState?: Partial<TimerState> | undefined;
    reducer: React.Reducer<TimerState, TimerActionType>;
}
export const TimerProvider: React.FC<TimerContextProviderProps> = ({ reducer, initState, children }) => {
    const [state, dispatch] = useReducer<TimerReducer>(reducer, {
        ...timerInitialState,
        ...initState,
    });
    const value: TimerContextProps = {state, dispatch};
    return (
        <TimerContext.Provider value={value}>
            {children}
        </TimerContext.Provider>
    );
};

// APP SETUP - choose the providers and reducers needed for the app
interface TimerSetupProps {
    timerInitState?: Partial<TimerState> | undefined;
}
export const TimerSetup: React.FC<TimerSetupProps> = ({timerInitState, children }) => {
    return (
        <TimerProvider reducer={timerReducer} initState={timerInitState}>
            {children}
        </TimerProvider>
    );
};

// APP INIT
export const Timer = (): React.ReactElement => {
    return (
        <TimerSetup timerInitState={{}}>
            <TimerComponentFC/>
        </TimerSetup>
    );
};

// TIMER COMPONENT
export const TimerComponentFC = () => {
    const {state, dispatch} = useTimerContext();
    const startTimer = () => {dispatch({type: 'start'})};
    const stopTimer = () => {dispatch({type: 'stop'})};

    useEffect(
        () => {
            const timer1 = setInterval(() => dispatch({type:'tick'}), 1000);
            // this will clear Timeout when component unmont like in willComponentUnmount
            return () => {
                clearInterval(timer1)
            }
        },
        [] // useEffect will run only one time (or when a vars in the array change)
    );

    const startBtn = <Button variant="contained" color="primary" onClick={startTimer}>start visit</Button>;

    const stopBtnAndTimer = (
        <span>
            <Button variant="contained" color="secondary" disabled={state.timerStopped !== undefined}
                    onClick={stopTimer}>stop visit</Button>
            <Typography>started: {state.timerStarted && state.timerStarted.toEccoTime().formatHoursMinutes()} duration: {state.timerMins}</Typography>
        </span>
    );

    return (
        <React.Fragment>
            {!state.timerStarted
                ? startBtn
                : stopBtnAndTimer
            }
        </React.Fragment>
    );
};

export class TimerComponent extends React.Component<TimerProps, TimerState> {
    private intervalCaller: any | null = null;

    constructor(props: TimerProps) {
        super(props);
        this.state = {
            timerStarted: undefined,
            timerStopped: undefined,
            timerMins: 0
        };
    }

    override componentDidMount() {
        this.intervalCaller = setInterval(() => this.tickMinute(), 1000);
    }

    override componentWillUnmount() {
        clearInterval(this.intervalCaller);
    }

    tickMinute() {
        this.setState(prevState => ({
            timerMins: prevState.timerMins + 1
        }));
    }

    start() {
        this.setState(() => ({
            timerStarted: EccoDateTime.nowLocalTime(),
            timerMins: 0
        }));

        timerEvents.fire(new TimerEvent(true));
    }

    stop() {
        clearInterval(this.intervalCaller);
        this.setState(() => ({
            timerStopped: EccoDateTime.nowLocalTime()
        }));

        timerEvents.fire(new TimerEvent(false));
    }

    override render() {
        const startBtn = (
            <Button variant="contained" color="primary" onClick={() => this.start()}>
                start visit
            </Button>
        );

        const stopBtnAndTimer = (
            <span>
                <Button
                    variant="contained"
                    color="primary"
                    disabled={this.state.timerStopped !== undefined}
                    onClick={() => this.stop()}
                >
                    stop visit
                </Button>
                {this.state.timerMins}
            </span>
        );

        return (
            <React.Fragment>{!this.state.timerStarted ? startBtn : stopBtnAndTimer}</React.Fragment>
        );
    }
}
