import {
    Box,
    FormControlLabel,
    Grid,
    Paper,
    Radio,
    RadioGroup,
    Typography
} from "@eccosolutions/ecco-mui";
import {DatePickerEccoDate, TimePickerEccoTime} from "@eccosolutions/ecco-mui-controls";
import * as React from "react";
import {useEffect, useState} from "react";

import {
    CalendarDayNames,
    calendarDaysToMap,
    DemandScheduleDto,
    RateCardDto,
    ShortDayName,
    IntervalType
} from "ecco-dto";
import {Agreement} from "ecco-rota";

import {useServicesContext} from "../ServicesContext";
import {createButtonGroup, dropdownList, numberInput, textInput} from "ecco-components-core";
import {EccoDate, EccoDateTime, EccoTime, IdNameDisabled, Nullable} from "@eccosolutions/ecco-common";

interface Props {
    agreement: Agreement;
    schedule: Nullable<DemandScheduleDto>;
    numStaff: number;
    onChangeSchedule: (schedule: Partial<Nullable<DemandScheduleDto>>) => void;
    onChangeStaff: (additionalStaff: number) => void;
    isSplit: boolean;
    editingScheduleRateCards?: RateCardDto[] | undefined;
}

/**
 * As per EntryConverter.java
 */
function adjustStartToDayOfWeek(date: EccoDate, days: number[] | null, endDate: EccoDate | null) {
    let start = date;

    if (days && days.length > 0) {
        const startDayOfWeekCalendar = date.getDayOfWeek() + 1;
        const daysSorted = days.sort((a,b) => a - b);
        const adjustOverStartDay = daysSorted.filter(d => d >= startDayOfWeekCalendar).shift();
        const adjustUnderStartDay = daysSorted.filter(d => d < startDayOfWeekCalendar).shift();

        // prioritise any past the current date
        if (adjustOverStartDay) {
            start = start.addDays(adjustOverStartDay - startDayOfWeekCalendar);
        } else {
            if (adjustUnderStartDay) {
                start = start.addDays((7 + adjustUnderStartDay) - startDayOfWeekCalendar);
            }
        }

        if (endDate) {
            // not after (so equal or before)
            if (start.laterThan(endDate)) {
                return null;
            }
        }
    }

    return start;
}

// FIXME: Simplify this by replacing schedule with data: FormData
// TODO: Rename. This is the fields, and it gets turned into a CommandForm by EditScheduleForm
export function AppointmentSchedule(props: Props) {
    const {schedule, numStaff, onChangeSchedule, onChangeStaff, agreement, isSplit} = props;
    //const [editingScheduleRateCards] = useState<RateCardDto[]>([]);

    const {sessionData} = useServicesContext();
    const [start, setStart] = useState<EccoDate | null>(null); // NOTE: This is the only exception from hoisted state

    const end = EccoDate.parseIso8601(schedule.end);
    const days = calendarDaysToMap(schedule.calendarDays || []);
    const time = EccoTime.parseIso8601(schedule.time);
    const tasks = schedule.parameters?.tasks!;
    const frequencyType = schedule.intervalType || null; // ensures RadioGroup is never uncontrolled - https://github.com/mui/material-ui/issues/35726#issuecomment-1370861406
    const isNewSchedule = !schedule.eventRef;

    const firstAppointmentFrom = isNewSchedule
        ? EccoDate.parseIso8601(schedule.start)
        : schedule.applicableFrom
        ? EccoDate.parseIso8601(schedule.applicableFrom)
        : undefined;
    const firstAppointmentActual =
        firstAppointmentFrom && frequencyType == "WK"
            ? schedule.calendarDays &&
              schedule.calendarDays.length > 0 &&
              adjustStartToDayOfWeek(firstAppointmentFrom, schedule.calendarDays, end)
            : firstAppointmentFrom;

    useEffect(() => {
        if (isNewSchedule) {
            setStart(agreement.start);
            updatePartial({
                start: agreement.start.formatIso8601(),
                intervalType: "WK"
            });
        } else {
            setStart(EccoDate.parseIso8601(schedule.start!));
            updatePartial({
                intervalType: schedule.intervalType || "WK"
            });
        }
    }, [props.schedule.eventRef]);

    const updatePartial = (update: Partial<Nullable<DemandScheduleDto>>) => {
        onChangeSchedule({...schedule, ...update});
    };

    const updateDay = (updateDay: ShortDayName, updateSelected: boolean) => {
        console.log(`${updateDay} ${updateSelected}`);
        const calendarDays = Object.entries(days)
            .map(([day, selected]) => {
                const javaDay = CalendarDayNames.indexOf(day as ShortDayName);
                // If not the day we want to change return according to it's selected status
                if (updateDay != day) {
                    return selected ? javaDay : -1;
                }
                // else return according to the new status
                return updateSelected ? javaDay : -1;
            })
            .filter(d => d >= 0);
        console.log(calendarDays);
        onChangeSchedule({...schedule, calendarDays});
        return calendarDays;
    };

    const updateTasks = (tasks: string | null) => {
        updatePartial({parameters: {tasks}});
    };

    const rateCardsAtTime = (_asOf: EccoDateTime | null): IdNameDisabled[] => {
        return props.editingScheduleRateCards
            ? props.editingScheduleRateCards.map(a => {
                  return {id: a.rateCardId, name: a.name, disabled: false};
              })
            : [];
    };

    return (
        <Box m={4}>
            {!isNewSchedule && (
                <Paper>
                    <Box p={2} mb={4}>
                        <Typography variant="h6" component="h2">
                            original start date: {start?.formatShort()}
                        </Typography>
                    </Box>
                </Paper>
            )}
            <Grid container direction="row" justify="flex-start" alignItems="flex-start">
                <Grid container>
                    <Grid item xs={12}>
                        {dropdownList(
                            "type",
                            onChangeSchedule,
                            schedule,
                            "categoryId",
                            sessionData.getDto().appointmentTypes.map(a => {
                                return {id: a.id, name: a.name, disabled: false};
                            }),
                            undefined,
                            undefined,
                            true
                        )}
                    </Grid>
                    <Grid item sm={6} xs={12}>
                        {isNewSchedule ? (
                            <DatePickerEccoDate
                                name="start date"
                                label="start date"
                                minDate={agreement.start}
                                maxDate={end ? end : agreement.end}
                                value={EccoDate.parseIso8601(schedule.start)}
                                onChange={start =>
                                    updatePartial({start: start?.formatIso8601() ?? null})
                                }
                            />
                        ) : (
                            <DatePickerEccoDate
                                name="applicable from"
                                label="applicable from"
                                disabled={!isSplit}
                                required={isSplit}
                                minDate={start}
                                maxDate={end}
                                value={EccoDate.parseIso8601(schedule.applicableFrom!)} // FIXME: Create an object for this
                                onChange={applicableFrom =>
                                    updatePartial({
                                        applicableFrom: applicableFrom?.formatIso8601() ?? null
                                    })
                                }
                            />
                        )}
                    </Grid>
                    <Grid item sm={6} xs={12}>
                        <DatePickerEccoDate
                            name="end date"
                            label="end date"
                            minDate={start}
                            maxDate={agreement.end}
                            value={end}
                            onChange={end => updatePartial({end: end?.formatIso8601() ?? null})}
                        />
                    </Grid>
                    <Grid item sm={6} xs={12}>
                        <TimePickerEccoTime
                            label="start time"
                            time={time}
                            onTimeChange={
                                time =>
                                    updatePartial({
                                        time: time?.formatHoursMinutes() ?? null
                                    }) /*FIXME: ENSURE ISO8601 */
                            }
                        />
                    </Grid>
                    <Grid item sm={6} xs={12}>
                        {numberInput(
                            "durationMins",
                            "duration (mins)",
                            onChangeSchedule,
                            schedule,
                            undefined,
                            undefined,
                            1
                        )}
                    </Grid>
                    <Grid item sm={6} xs={12}>
                        {numberInput(
                            "numStaff",
                            "staff",
                            o => onChangeStaff(o.numStaff),
                            {numStaff},
                            undefined,
                            undefined,
                            1,
                            4
                        )}
                    </Grid>
                    <Grid item xs={12}>
                        <RadioGroup
                            row={true}
                            name="frequencyType"
                            title={"frequency"}
                            value={frequencyType} // Must not be undefined as that would cause "uncontrolled -> controlled" issue
                            onChange={(_, value: string) =>
                                updatePartial({intervalType: value as IntervalType})
                            }
                        >
                            <FormControlLabel
                                key={"WK"}
                                value={"WK"}
                                control={<Radio />}
                                label={"week"}
                            />
                            <FormControlLabel
                                key={"MTH"}
                                value={"MTH"}
                                control={<Radio />}
                                label={"month"}
                            />
                            <FormControlLabel
                                key={"QTR"}
                                value={"QTR"}
                                control={<Radio />}
                                label={"quarter"}
                            />
                            <FormControlLabel
                                key={"BI"}
                                value={"BI"}
                                control={<Radio />}
                                label={"bi-annual"}
                            />
                            <FormControlLabel
                                key={"YR"}
                                value={"YR"}
                                control={<Radio />}
                                label={"year"}
                            />
                        </RadioGroup>
                    </Grid>
                    {frequencyType == "WK" && (
                        <>
                            <Grid item xs={12}>
                                {createButtonGroup("days", "days", days, updateDay)}
                            </Grid>
                            <Grid item sm={6} xs={12}>
                                {numberInput(
                                    "intervalFrequency",
                                    "every (weeks)",
                                    o => updatePartial({intervalFrequency: o.intervalFrequency}),
                                    {intervalFrequency: schedule.intervalFrequency},
                                    frequencyType != "WK",
                                    undefined,
                                    1,
                                    4
                                )}
                            </Grid>
                        </>
                    )}
                    {rateCardsAtTime(null).length > 0 && (
                        <Grid item xs={12}>
                            {dropdownList(
                                "rate",
                                onChangeSchedule,
                                schedule,
                                "rateCardId",
                                rateCardsAtTime(null)
                            )}
                        </Grid>
                    )}
                    <Grid item xs={12}>
                        {textInput(
                            "tasks",
                            "tasks summary (for rota and printed schedules)",
                            o => updateTasks(o.tasks),
                            {tasks}
                        )}
                    </Grid>
                </Grid>
            </Grid>
            {firstAppointmentActual && (
                <Box p={2} mb={4} style={{padding: "25px"}}>
                    <Typography align={"center"} style={{backgroundColor: "rgb(232, 244, 253)"}}>
                        first appointment:{" "}
                        {firstAppointmentActual ? firstAppointmentActual.formatShort() : "-"}
                    </Typography>
                </Box>
            )}
        </Box>
    );
}
