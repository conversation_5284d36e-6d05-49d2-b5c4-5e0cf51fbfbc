import {EccoDate} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {CommandQueue, ServiceRecipientAppointmentScheduleCommand} from "ecco-commands";
import {DemandResource} from "ecco-rota";
import * as React from "react";
import {FC} from "react";
import {CommandForm, CommandSubform, ModalCommandForm} from "../cmd-queue/CommandForm";
import {useServicesContext} from "../ServicesContext";
import {AppointmentFields, AppointmentForm, ScheduleEvent} from "./AppointmentForm";
import {EccoAPI} from "../EccoAPI";

type LocalProps = { services: EccoAPI, commandForm: CommandForm };

interface ModalProps {
    resource?: DemandResource | undefined;
    serviceId: number | null;
    date: EccoDate;
    demandFilter: string;
    resourceFilter: string;
    setShow: (show: boolean) => void;
}
interface Props extends ModalProps {
    readOnly: boolean;
}

interface State {
    editedData: AppointmentFields
}

export class CreateAppointmentCommandForm extends CommandSubform<Props & LocalProps, State> {
    constructor(props: Props & LocalProps) {
        super(props);

        // should really call removePostSubmitHandler - like useCommandFormPostSubmitHandler
        props.commandForm.addPostSubmitHandler(() => ScheduleEvent.fire());

        this.state = {
            editedData: {
                date: props.date
            } as AppointmentFields
        };
    }

    getErrors(): string[] {
        const errors = [];
        const editedData = this.state.editedData;
        if (!editedData.time) {
            errors.push("you must provide a time");
        }
        if (!editedData.durationMins || editedData.durationMins < 5) {
            errors.push("duration should be at least 5 minutes");
        }
        if (!editedData.categoryId) {
            errors.push("you must select an appointment type");
        }
        return errors;
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const editedData = this.state.editedData;
        const serviceRecipientId = editedData.referenceData.agreement!.getServiceRecipientId();

        let cmd = new ServiceRecipientAppointmentScheduleCommand(
            "add",
            Uuid.randomV4(),
            serviceRecipientId,
            null,
            editedData.agreementId
        )
            .asAdHoc()
            .changeStartDate(null, editedData.date)
            .changeTime(null, editedData.time!)
            .changeDurationMins(null, editedData.durationMins!)
            .changeAppointmentTypeId(null, editedData.categoryId!);
        if (this.props.resource) {
            cmd.withResourceSrId(this.props.resource.getServiceRecipientId());
        } else if (editedData.resourceSrId) {
            cmd.withResourceSrId(editedData.resourceSrId);
        }

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    override render() {
        return (
            <AppointmentForm
                {...this.props}
                data={this.state.editedData}
                onChange={editedData => this.setState({editedData})}
            />
        );
    }
}

/* @Exemplar - see usage */
export const CreateAppointmentModal: FC<ModalProps> = props => {
    const eccoAPI = useServicesContext();

    return (
        <ModalCommandForm
            show={true} // i.e. we're a form that's modal when shown
            setShow={props.setShow}
            title="new appointment"
            action="save"
            maxWidth="sm"
        >
            {form => <CreateAppointmentCommandForm
                {...props}
                readOnly={!eccoAPI.sessionData.hasRoleReferralEdit()}
                services={eccoAPI}
                commandForm={form}
            />}
        </ModalCommandForm>
    );
};
