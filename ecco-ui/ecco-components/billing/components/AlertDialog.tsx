import * as React from "react";
import {ReactChild} from "react";
import {Dialog} from "@eccosolutions/ecco-mui";
import {DialogTitle} from "@eccosolutions/ecco-mui";
import {DialogContent} from "@eccosolutions/ecco-mui";
import {DialogContentText} from "@eccosolutions/ecco-mui";
import {DialogActions} from "@eccosolutions/ecco-mui";
import {Button} from "@eccosolutions/ecco-mui";

/*
** This may need to change post demo to handle many more options but it is simple enough for demoware.
*/


interface Props {
    cancelContent: ReactChild
    confirmContent: ReactChild
    content: ReactChild
    title: string
    onConfirm: () => void
}

export default function AlertDialog(props: Props) {
    // open by default, which requires a check in the parent component.
    // this breaks with the usual material ui patterns
    const [open, setOpen] = React.useState(true);

    function handleClose() {
        setOpen(false);
    }

    function handleConfirm() {
        // post demo, worth considering if this should be async or not...
        // the power can be given to the parent component
        if (props.onConfirm) {
            props.onConfirm();
        }
        setOpen(false);
    }

    return (
        <div>
            <Dialog
                open={open}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title">{props.title}</DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        {props.content}
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    {props.cancelContent && (
                        <Button onClick={handleClose} color="primary">
                            {props.cancelContent}
                        </Button>
                    )}
                    {props.confirmContent && (
                        <Button
                            onClick={handleConfirm}
                            color="primary"
                            autoFocus={true}
                        >
                            {props.confirmContent}
                        </Button>
                    )}
                </DialogActions>
            </Dialog>
        </div>
    );
}
