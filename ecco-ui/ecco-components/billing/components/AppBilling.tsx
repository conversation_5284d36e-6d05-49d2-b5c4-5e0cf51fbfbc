import * as React from "react";
import {useState} from "react";
import InvoicesList from './InvoicesList';
import EvidenceView from './EvidenceView';
import PayrollView from "./PayrollView";
import {Box, createStyles, makeStyles, Tab, Tabs, Theme} from "@eccosolutions/ecco-mui";


function AppBilling() {
    const classes = useStyles();
    const [tab, setTab] = useState(0);

    function handleTabChange(_event: any, value: number) {
        setTab(value);
    }

    function renderRelevantView() {
        switch (tab) {
            case 0:
                return <EvidenceView />;
            case 1:
                return <InvoicesList />;
            case 2:
                return <PayrollView />;
            default:
                return <EvidenceView />;
        }
    }

    return (
        <Box className={classes.root}>
            <Tabs value={tab} onChange={handleTabChange}>
                <Tab label="Unverified" />
                <Tab label="Invoices" />
                <Tab label="Payroll" />
            </Tabs>
            {renderRelevantView()}
        </Box>
    );
}

const useStyles = makeStyles((_theme: Theme) =>
    createStyles({
        root: {
            width: "100%"
        }
    })
);
export default AppBilling;