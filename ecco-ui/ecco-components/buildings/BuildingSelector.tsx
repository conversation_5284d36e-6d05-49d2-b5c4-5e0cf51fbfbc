import * as React from "react";
import {ChangeEvent, FC} from "react";
import {useBuildings} from "../data/entityLoadHooks";
import {MenuItem, Select} from "@eccosolutions/ecco-mui";
import {SelectList} from "ecco-components-core";
import {Building} from "ecco-dto";
import { useMemo } from "react";

export function eventValueAsInt(e: ChangeEvent<{name?: string | undefined; value: unknown}>) {
    const str = e.target.value as string;
    return str == "null" ? null : parseInt(str);
}

export function SelectBuilding(props: {
    nullLabel?: string | undefined; // Omits null entry if no label provided - and provides "- select one -" option if buildingId is null
    buildingId: number | null;
    onChange: (buildingId: number | null) => void;
    buildings: Building[] | undefined | null;
}) {
    const sorted = useMemo(
        () => props.buildings && props.buildings.sort((a, b) => a.name!.localeCompare(b.name!)),
        [props.buildings]
    );

    return (
        <>
            {sorted && (
                <Select // using Select because we don't want the label etc wrapper here
                    value={props.buildingId == null ? "null" : props.buildingId}
                    onChange={e => props.onChange(eventValueAsInt(e))}
                >
                    {(props.nullLabel || props.buildingId == null) && (
                        <MenuItem key="null" value="null">
                            {props.nullLabel || "- select one -"}
                        </MenuItem>
                    )}
                    {sorted.map(b => (
                        <MenuItem key={b.buildingId} value={b.buildingId}>
                            {b.name}
                        </MenuItem>
                    ))}
                </Select>
            )}
        </>
    );
}

export function BuildingSelector(props: {
    nullLabel?: string | undefined; // Omits null entry if no label provided - and provides "- select one -" option if buildingId is null
    buildingId: number | null;
    onChange: (buildingId: number | null) => void;
    primaryOnly?: boolean | undefined;
}) {
    const {buildings} = useBuildings(props.primaryOnly);

    return <SelectBuilding buildings={buildings} {...props} />;
}

export const BuildingSearch: FC<{
    buildingId: number | null;
    onChange: (buildingId: number | null) => void;
    primaryOnly: boolean;
    filterOutParents?: boolean | undefined;
}> = props => {
    const {buildings} = useBuildings(props.primaryOnly);

    // for DODGY react-select
    // const newListItemToBldg = (inputValue: string, optionLabel: React.ReactNode) => {
    //     return {} as Building;
    // };

    if (buildings == null) {
        return null;
    }

    const bldgList = buildings
        .filter(b => (props.filterOutParents ? b.parentId : true))
        .map(b => {
            return {
                value: b.buildingId.toString(),
                // elsewhere, addresses are displayed with a '@' if it's a building (see BLDG_SYMBOL)
                // the building/unit name is only used for lists and file names
                // and the key for the system is 'line1, postcode'
                // therefore line1, postcode' need to be informative and useful
                //  - NB so line1 shouldn't be '81...', but 'Cosgarne Hall'
                // parent/units
                //  - the parent saves with the name and address as provided
                //  - the unit saves with the name and line1 altered as 'name, line1 of parent',  (see BuildingCommandHandler)
                //  - (which means we get 'Flat 1, Cosgarne Hall', '81...')
                // therefore... we can just show the address line1 here
                // and we should show the postcode, else it looks odd when line1 only shows with 'B' at the end
                label: `${b.name} ${b.address?.postcode}`
            };
        });
    const initial = bldgList.find(o => o.value == props.buildingId?.toString());

    function onChange(o: any) {
        const value = o && o.value && parseInt(o.value as string, 10);
        props.onChange(value);
    }

    return (
        <SelectList
            createNew={false}
            value={initial}
            options={bldgList}
            onChange={onChange}
            // TODO Sort DODGY react-select which has issues using standard options... prob okay with createNew=false
            // getOptionLabel={b => b.name}
            // getOptionValue={b => b.buildingId ? b.buildingId.toString() : ""}
            // value={props.buildingId}
            // options={buildings}
            // onChange={value => props.onChange(value as Building)}
        />
    );
};