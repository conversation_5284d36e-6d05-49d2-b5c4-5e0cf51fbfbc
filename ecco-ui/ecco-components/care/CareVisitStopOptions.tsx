import {StopOptionData, useCareVisitContext} from "./careVisitState";
import * as React from "react";
import {useState} from "react";
import {Grid} from "@eccosolutions/ecco-mui";
import {dropdownList, EccoTextInput, possiblyModalForm} from "ecco-components-core";
import {useServicesContext} from "../ServicesContext";
import {removeDrafts} from "ecco-components-core";

/**
 * When tasks have not been completed, we show a popup to provide a reason and force a comment.
 *
 * NB the reason is currently saved against each task not completed, not the whole visit - but could
 * be recorded on the 'events' table like eventStatusRateId (eg eventCategoryId). There is also a
 * possibility of using the 'cal_eventstatus' (lone worker) table.
 */
export function CareVisitStopOptionsForm(props: {
    onCancel: () => void
    onSave: (value: StopOptionData) => void
}) {
    const {sessionData} = useServicesContext();
    const [stopReasonId, setStopReasonId] = useState<number | null>(null);
    const {state, dispatch} = useCareVisitContext();

    const reasons =
        sessionData &&
        sessionData
            .getListDefinitionEntriesByListName("care-checks", false, true)
            .filter(l => !l.isIconClassSuccess())
            .map(l => l.getDto());

    // could depend on the config, as the evidence does... search locationListName
    const locations =
        sessionData &&
        sessionData
            .getListDefinitionEntriesByListName("care-locations", false, true)
            .map(l => l.getDto());

    const useLocations = locations?.length > 0;

    return (
        sessionData &&
        possiblyModalForm(
            "finish visit",
            true,
            true,
            props.onCancel,
            () => props.onSave({stopReasonId}),
            (useLocations && !state.locationId) || !stopReasonId,
            false,
            <Grid container={true} direction={"row"} justify={"center"}>
                <Grid item={true} lg={6} md={8} xs={12}>
                    <Grid container={true}>
                        <Grid item xs={12}>
                            {dropdownList(
                                "reason",
                                state => setStopReasonId(state.stopReasonId),
                                {stopReasonId},
                                "stopReasonId",
                                reasons,
                                undefined,
                                undefined,
                                true
                            )}
                        </Grid>
                        {useLocations && (
                            <Grid item xs={12}>
                                {dropdownList(
                                    "location",
                                    stateNew =>
                                        dispatch({
                                            type: "setState",
                                            setter: stateOld => ({
                                                ...stateNew
                                            })
                                        }),
                                    state,
                                    "locationId",
                                    locations,
                                    undefined,
                                    undefined,
                                    true
                                )}
                            </Grid>
                        )}
                        <Grid item xs={12}>
                            <EccoTextInput
                                data-test={"textarea-comment"}
                                propertyKey="comment"
                                label="comment"
                                value={state.commentForm.comment!}
                                onChange={comment => {
                                    removeDrafts(
                                        `visitComment-${state.serviceRecipientId}-${state.visitDateTime}`
                                    );
                                    dispatch({
                                        type: "changeVisitComment",
                                        commentForm: comment == null ? {} : {comment: comment}
                                    });
                                }}
                                type="textarea"
                                rows={8}
                                autoSaveKey={`visitComment-${state.serviceRecipientId}-${state.visitDateTime}`}
                            />
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>,
            "finish"
        )
    );
}

// DEMOWARE - the initial demo work, which was that we record a stop reason and setup a next date (for any undone tasks)
/*
export function CareVisitStopOptionsForm(props: {
    onCancel: () => void
    onSave: (value: StopOptionData) => void
    value: Partial<StopOptionData>
    autoSaveKey: string
}) {
    const {sessionData} = useServicesContext();
    const [stopReasonId, setStopReasonId] = useState<number | null>(props.value.stopReasonId!);
    const [nextAptDateTime, setNextAptDateTime] = useState<EccoDateTime | null>(props.value.nextAptDateTime!);

    return sessionData && possiblyModalForm("options", true, true,
            props.onCancel,
            () => props.onSave({stopReasonId, nextAptDateTime}),
            false, false,
            <Grid container>
                <Grid item xs={12}>
                    {dropdownList("reason", state => setStopReasonId(state.stopReasonId), {stopReasonId},
                            "stopReasonId", sessionData.getListDefinitionEntriesByListName("stopReasons").map(l => l.getDto()))}
                </Grid>
                <Grid item xs={12}>
                    {dateTimeIso8601Input("nextAptDateTime", "next appointment", state => setNextAptDateTime(state.nextAptDateTime), {nextAptDateTime})}
                </Grid>
            </Grid>
    );
}
*/
