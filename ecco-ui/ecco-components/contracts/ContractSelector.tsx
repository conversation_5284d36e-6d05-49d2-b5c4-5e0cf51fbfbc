import {ContractDto} from "ecco-dto";
import * as React from "react";
import {useEffect, useState} from "react";
import {SelectList} from "ecco-components-core";
import {useServicesContext} from "../ServicesContext";

// const useStyles = makeStyles((_theme: Theme) =>
//     createStyles({
//         formControl: {
//             margin: 20,
//             minWidth: 120
//         }
//     })
// );


export const ContractSelector = (props: {contract: ContractDto | null, onChange: (contract: ContractDto | null) => void}) => {
    const [contracts, setContracts] = useState<ContractDto[] | null>(null);
    const services = useServicesContext();

    useEffect(() => {
        services.contractRepository.findAllContracts()
            .then(setContracts);
    }, [services]);
    return <>
        {contracts && <SelectList
            isClearable={false}
            getOptionLabel={c => c.name}
            getOptionValue={c => c.contractId?.toString()}
            value={props.contract}
            options={contracts}
            onChange={value => props.onChange(value as ContractDto)}
        />}
    </>;
};

export const ContractSelect = (props: {
    contractId?: number | null | undefined;
    onChange: (contractId: number | null) => void;
    disabled: boolean;
}) => {
    //const classes = useStyles();

    const [contracts, setContracts] = useState<ContractDto[] | null>(null);
    const services = useServicesContext();

    useEffect(() => {
        services.contractRepository.findAllContracts().then(setContracts);
    }, [services]);
    return (
        <>
            {contracts && contracts.length > 0 && (
                <SelectList
                    placeholder={"contract"}
                    createNew={false}
                    isDisabled={props.disabled}
                    getOptionLabel={l => l.name}
                    getOptionValue={l => l.contractId.toString()}
                    value={contracts.find(l => l.contractId == props.contractId)}
                    options={contracts}
                    onChange={value => props.onChange((value as ContractDto).contractId)}
                />
            )}
        </>
    );
};
