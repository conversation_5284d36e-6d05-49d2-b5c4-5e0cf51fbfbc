import {Button} from "@eccosolutions/ecco-mui";
import {Card} from "@eccosolutions/ecco-mui";
import {CardActions} from "@eccosolutions/ecco-mui";
import {CardContent} from "@eccosolutions/ecco-mui";
import {Container} from "@eccosolutions/ecco-mui";
import {Grid} from "@eccosolutions/ecco-mui";
import {ContractDto} from "ecco-dto";
import * as React from "react";
import {FC} from "react";
import {useHistory} from "react-router";
import {useContracts} from "../data/entityLoadHooks";
import {LoadingSpinner} from "../Loading";
import { Typography } from "@eccosolutions/ecco-mui";
import {useAppBarOptions} from "../AppBarBase";

export const ContractsList: FC = () => {

    useAppBarOptions("contracts");
    const {contracts, loading} = useContracts();
    const history = useHistory();

    const renderRow = (contract: ContractDto) => {
        return <Grid item>
            <Card>
                <CardContent>
                    <Typography color="textSecondary" gutterBottom>
                        {contract.name}
                    </Typography>
                    id: {contract.contractId}
                </CardContent>
                <CardActions>
                    <Button
                        size="small" color="primary"
                        onClick={() => history.push("contracts/" + contract.contractId)}
                    >
                        manage
                    </Button>
                </CardActions>
            </Card>
        </Grid>;
    };

    return <Container maxWidth={"sm"}>
        {loading && <LoadingSpinner/>}
        <Grid container spacing={1} direction="column">
            {contracts && contracts.map(renderRow)}
        </Grid>
    </Container>;
};