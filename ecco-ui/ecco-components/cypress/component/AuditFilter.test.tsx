import {mount} from "cypress/react";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import * as React from "react";
import {
    AuditHistoryController,
    CommandViewHandler
} from "../../service-recipient/AuditHistoryPaged";
import {sessionData} from "../../__tests__/testUtils";
import {AuditHistoryItemControl, EccoAPI} from "../../EccoAPI";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {
    BaseServiceRecipientCommandDto,
    ReportCriteriaDto,
    ServiceRecipientAjaxRepository,
    ServiceRecipientRepository,
    SessionData
} from "ecco-dto";
import {taskComplete} from "./testData";

const serviceRecipientRepository = getFailAllMethodsMock<ServiceRecipientRepository>(
    ServiceRecipientAjaxRepository
);
serviceRecipientRepository.findServiceRecipientCommandsBySearch = (
    criteria: ReportCriteriaDto,
    pageNumber: number | null
): Promise<BaseServiceRecipientCommandDto[]> => {
    return Promise.resolve([taskComplete]);
};

const dummyAuditHistoryIntegrations = {
    handleCommand: (command: BaseServiceRecipientCommandDto, sessionData: SessionData) => {
        return {
            getCommand(): any {
                return command;
            }
        } as CommandViewHandler<any>;
    },
    componentFactory: (sessionData: SessionData) => {
        return {
            processHandler(handler: CommandViewHandler<any>) {},
            domElement(): HTMLElement {
                return document.createElement("div");
            }
        } as AuditHistoryItemControl;
    }
};
const overrides = {
    serviceRecipientRepository,
    auditHistoryIntegrations: dummyAuditHistoryIntegrations,
    sessionData: sessionData
} as EccoAPI;

describe("AuditFilter tests", () => {
    it("state operations", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <AuditHistoryController serviceRecipientId={99} />
            </TestServicesContextProvider>
        );
    });
});
