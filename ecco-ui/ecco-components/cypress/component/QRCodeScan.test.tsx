import {mount} from "cypress/react";
import * as React from "react";
import {QrCodeScan} from "../../inputs/barcode/QrCodeScan";

// https://medium.com/cypress-io-thailand/qr-code-barcode-testing-with-cypress-d738f496068b
// automate QR testing
//      https://www.linkedin.com/pulse/web-camera-testing-cypress-john-pourdanis/
//      https://github.com/cypress-io/cypress/issues/4929
//      https://www.linkedin.com/pulse/web-camera-testing-cypress-john-pourdanis/
//      https://stackoverflow.com/questions/52095416/how-can-i-correctly-provide-a-mock-webcam-video-to-chrome


describe("QR tests", () => {
    it("it mounts", () => {
        mount(<QrCodeScan />);
    });
});

// TEST with underlying library zxing
// QRCodeScanner uses the zxing library, which has a testing script at https://medium.com/cypress-io-thailand/qr-code-barcode-testing-with-cypress-d738f496068b
// but QRCodeScanner doesn't itself allow us to test it
/*
  it('can read qrcode', () => {
    cy.visit('./qrcode.html');
    cy.get('[data-testid="qr-code-img"]')
            .then($el => {
                const img = $el[0];
                const image = new Image();
                image.width = img.width;
                image.height = img.height;
                image.src = img.src;
                image.crossOrigin = 'Anonymous';
                return image;
            })
            .then(image => {
                const reader = new BrowserMultiFormatReader();
                return reader.decodeFromImageElement(image[0])
            })
            .then(result => {
                expect(result.getText()).to.equal('https://www.cypress.io');
            });
    });
*/
