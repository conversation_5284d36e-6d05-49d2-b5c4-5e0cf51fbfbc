import { base64 } from "@eccosolutions/ecco-common";

export module supported {
    /** True if the readAsDataUrl function is supported on the current
     * browser. */
    export var readAsDataUrl = !!Blob && !!FileReader;

    /** True if the readImage function is supported on the current browser. */
    export var readImage = readAsDataUrl;
}

/** Reads the specified file asynchronously and returns a Promise of a
 * data URL representing the file's data.
 *
 * If the read operation is aborted, the promise will be rejected with an
 * instance of Aborted.
 *
 * If the read operation fails with an error, the promise will be rejected
 * with an instance of Error. */
export function readAsDataUrl(file: Blob): Promise<string> {
    return new Promise<string>((resolve, reject) => {
        const fileReader = new FileReader();
        fileReader.onload = () => resolve(fileReader.result as string); // result will be string when use readAsDataURL()
        fileReader.onabort = () => reject(new Aborted());
        fileReader.onerror = () => reject(fileReader.error);

        fileReader.readAsDataURL(file);
    });
}

/** Reads the specified file asynchronously and returns a Promise of an
 * HTML img element.
 *
 * If the Promise resolves then the image has loaded and the img element
 * is ready to be used immediately, for example in canvas operations. There
 * is no need to wait for the HTMLImageElement.onload event.
 *
 * If the read operation is aborted, the promise will be rejected with an
 * instance of Aborted.
 *
 * If the read operation fails with an error, or the file is not a valid image,
 * the promise will be rejected with an instance of Error. */
export function readImage(file: Blob): Promise<HTMLImageElement> {
    return readAsDataUrl(file)
            .then((dataUrl: string) => {
                return new Promise<HTMLImageElement>((resolve, reject) => {
                    const image = document.createElement("img");
                    image.src = dataUrl;

                    image.onload = () => resolve(image);
                    image.onerror = () => reject(new Error("Invalid image."));
                });
            });
}

export function dataUrlToBlob(dataUrl: string): Blob {

    if (!dataUrl.startsWith("data:")) {
        throw new Error("Invalid data URL.");
    }

    const uri = new URL(dataUrl);

    const matches = /^([^,]*?)(;base64)?,(.*)$/.exec(uri.pathname)!;

    const mediaType = matches[1]!;
    const isBase64 = !!matches[2];
    const data = matches[3]!;

    const blobProperties: BlobPropertyBag = {type: mediaType};

    if (isBase64) {
        return new Blob([base64.decodeFromString(data)], blobProperties);
    } else {
        return new Blob([data], blobProperties);
    }
}

export class Aborted {
    public toString() {
        return "Aborted.";
    }
}
