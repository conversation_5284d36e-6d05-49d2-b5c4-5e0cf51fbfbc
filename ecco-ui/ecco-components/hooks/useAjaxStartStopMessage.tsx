import {useEffect, useState} from "react";
import {AjaxStartEvent, AjaxStopEvent} from "@eccosolutions/ecco-common";



/** @return message to show while loading */
export function useAjaxStartStopMessage(): string | null {
    const [message, setMessage] = useState<string | null>(null);
    useEffect(() => {
        function handleStart(event?: AjaxStartEvent | undefined) {
            setMessage((event && event.message) || "");
        }
        function handleStop() {
            setMessage( null);
        }
        AjaxStartEvent.bus.addHandler(handleStart);
        AjaxStopEvent.bus.addHandler(handleStop);
        return () => {
            AjaxStartEvent.bus.removeHandler(handleStart);
            AjaxStopEvent.bus.removeHandler(handleStop);
        }
    }, []);

    return message;
}
