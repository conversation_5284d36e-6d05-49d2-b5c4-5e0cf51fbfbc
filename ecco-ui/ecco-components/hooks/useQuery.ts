import {useLocation} from "react-router";

export class URLSearchParamsExtended extends URLSearchParams {
    getInt(name: string): number | null {
        const val = this.get(name)
        return  val == null ? null : parseInt(val)
    }

    addStringIfExists(name: string, value: string | null) {
        if (value) {
            this.set(name, value)
        }
    }

    addNumberIfExists(name: string, value: number | null) {
        if (value) {
            this.set(name, value.toFixed())
        }
    }
}

export function useQuery() {
    return new URLSearchParamsExtended(useLocation().search); // prob should useCallback but doesn't render often
}

