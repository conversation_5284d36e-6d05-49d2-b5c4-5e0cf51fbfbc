import {Reducer, useReducer} from "react";

/**
 * Use as:
 * const [expandedRows, toggleRow] = useToggleList();
 * <... onClick={() => toggleRow(i)} />
 *
 */
export function useToggleList() {
    function handleExpand(expandedRows: number[], index: number): number[] {
        const foundIndex = expandedRows.indexOf(index);
        if (foundIndex === -1) {
            return [...expandedRows, index];
        }
        return [
            ...expandedRows.slice(0, foundIndex),
            ...expandedRows.slice(foundIndex + 1, expandedRows.length)
        ];
    }

    return useReducer<Reducer<number[], number>>(handleExpand, []);
}