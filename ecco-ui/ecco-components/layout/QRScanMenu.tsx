import * as React from "react";
import {FC, useState} from "react";
import {Icon, IconButton} from "@eccosolutions/ecco-mui";
import {EccoTheme} from "ecco-components-core";
import {EccoV3Modal} from "ecco-components-core";
import {handleLazy} from "../Loading";
import {QrCodeScan} from "../inputs/barcode/QrCodeScan";

export const QRScanMenu: FC<{uuid?: string | undefined}> = ({uuid}) => {
    const [open, setOpen] = useState(false);

    return (
        <>
            {open && (
                <EccoTheme prefix="qrScan">
                    <EccoV3Modal
                        title={"scan"}
                        show={true}
                        onCancel={() => setOpen(false)}
                        action="close"
                    >
                        {handleLazy(<QrCodeScan />)}
                    </EccoV3Modal>
                </EccoTheme>
            )}
            <IconButton aria-label="qr scan" onClick={() => setOpen(true)}>
                <Icon className={"fa fa-qrcode"} />
            </IconButton>
        </>
    );
};
QRScanMenu.displayName = "QRScanMenu";
