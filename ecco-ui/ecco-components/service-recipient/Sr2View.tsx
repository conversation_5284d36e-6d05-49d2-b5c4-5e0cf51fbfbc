import * as React from "react";
import {FC} from "react";
import {AppBarContextProvider, WithHeader} from "../AppBarBase";
import {Sr2AppBar} from "./Sr2AppBar";
import {useServicesContext} from "../ServicesContext";
import {useAdminModeMenu} from "../menu/ToggleMenuItem";
import {CardHeader} from "@eccosolutions/ecco-mui";
import {
    LoadSRWithEntitiesContext,
    ServiceRecipientWithEntitiesContext,
    useServiceRecipientWithEntities
} from "../data/serviceRecipientHooks";
import {SvcRecPageRouter} from "./SvcRecPageRouter";
import {SessionData} from "ecco-dto";

// Sr2View uses Sr2AppBarProvider to pass in children through ...props
// where a child 'AppHeader' has useAppBarOptions to change the useAppBarContext
// Sr2View is used in staticFiles/scripts/offline/router.tsx
export const Sr2AppBarProvider: FC<{
    basepath: string;
    srId: number;
    preview?: boolean | undefined;
}> = props => (
    <AppBarContextProvider>
        <Sr2AppBar {...props} allowLinkOut={true} />
    </AppBarContextProvider>
);
function getTitle(sr: ServiceRecipientWithEntitiesContext, sessionData: SessionData) {
    if (sr?.client?.knownAs) {
        return (
            sr.serviceRecipient.displayName +
            " - " +
            sr.client.knownAs +
            (sr.client.pronounsId
                ? ` (${sessionData
                      .getListDefinitionEntryById(sr.client.pronounsId)
                      .getDisplayName()})`
                : "")
        );
    }
    /* eg incident is set in IncidentServiceRecipient */
    return sr.serviceRecipient.displayName;
}
const AppHeader: FC<{srId: number}> = props => {
    const srId = props.srId;
    const {sessionData} = useServicesContext();
    const {context} = useServiceRecipientWithEntities(srId);
    const serviceRecipient = context?.serviceRecipient;
    //const [subtitle, setSubtitle] = React.useState<string | null>(null);

    useAdminModeMenu("manage pathway config", sessionData.hasRoleAdmin());

    // subtitle
    /*useEffect(() => {
        if (serviceRecipient) {
            const service = sessionData.getServiceCategorisationName(serviceRecipient.serviceAllocationId);
            if (referral) {
                const status = sessionData.getDto().messages[referral.statusMessageKey];
                setSubtitle(status ? `${service} (${status})` : `${service}`);
            } else {
                setSubtitle(`${service}`);
            }
        }
    }, [serviceRecipient, referral]);*/

    if (!serviceRecipient) {
        return null;
    }

    const name = getTitle(context, sessionData);
    const service = sessionData.getServiceCategorisationName(serviceRecipient.serviceAllocationId);
    const status =
        (context?.referral && sessionData.getDto().messages[context.referral.statusMessageKey]) ||
        (context?.incident && sessionData.getDto().messages[context.incident.statusMessageKey]);
    const subtitle = `${service}`.concat(status ? ` [${status}]` : "");

    return (
        <>
            {/* from ReferralFrame.tsx - was simply useAppBarOptions("referral"); */}
            <WithHeader
                deps={[srId, subtitle]}
                discriminator={serviceRecipient.prefix}
                header={<CardHeader title={name} subheader={subtitle} />}
                children={props.children}
            ></WithHeader>
        </>
    );
};

export const Sr2View: FC<{
    basepath: string;
    srId: number;
    eventId?: string | undefined;
}> = props => (
    /* holds the Sr2MenuItems */

    <Sr2AppBarProvider basepath={props.basepath} srId={props.srId} preview={true}>
        {/* changing the header needs to be inside <AppBarContext.Provider> */}
        <AppHeader srId={props.srId} />

        <LoadSRWithEntitiesContext srId={props.srId}>
            <SvcRecPageRouter
                basepath={props.basepath}
                preview={true}
                srId={props.srId}
                eventId={props.eventId}
            />
        </LoadSRWithEntitiesContext>
    </Sr2AppBarProvider>
);
