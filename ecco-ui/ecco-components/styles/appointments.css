
div.rotaReport {
    width: 90%;
    margin: 0 auto;
}

.rotaReport hr { margin: 1em auto; width: 75%}

.rotaReport h2 { margin-left: 0; }
.rotaReport h2, .rotaReport h3, .rotaReport li header { font-weight: bold; }

span.rotaTitle { margin-left: .5em}

.rotaReport .rotaDay { padding: 1em 0; border-bottom: gray 1px solid; }

.rotaDay.past, .rotaDay .past {
    color: gray;
}

.rotaDay ol { font-size: 90%; list-style: none }

.rotaDay dl { font-size: 90%; width: 100%; }

.rotaDay dt {
    float: left;
    width: 4em;
    margin-left: 24px;
}

.rotaDay dd+dt { clear: left; }

.rotaDay dd {
    float: left;
    width: calc(90% - 4em);
    margin-left: 24px;
    font-weight: lighter
}

.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}


@media print {
/* selected styles from thickbox.css */

    /** { padding: 0; margin: 0; }*/

    .rotaReport header *:first-letter { text-transform: capitalize; }

    .rotaReport h2, .rotaReport h3, .rotaReport li header { font-weight: bold; }

    body { font-family: serif !important; }


/* two column layout - WebKit ignores columns for printing at the moment, though [they only work on screen] */

    .rotaReport .reportContent {
        column-count: 2; -moz-column-count: 2; -webkit-column-count: 2;
        column-gap: 2em; -moz-column-gap: 2em; -webkit-column-gap: 2em;
        column-rule: gray 1px solid; -moz-column-rule: gray 1px solid; -webkit-column-rule: gray 1px solid;
    }

    .rotaDay {
        page-break-inside: avoid; break-inside: avoid; -webkit-column-break-inside: avoid;
    }

 /* selected styles from housestyle_themestandard.css */

    #logo { float: left; }

    #container { float: left; width: 100%; margin-right: -100px; }

}

