import * as React from "react";
import {FC} from "react";
import * as FileSaver from "file-saver";
import * as XLSX from "sheetjs-style";

export const ExportExcel: FC<{fileName: string; data: any}> = props => {
    const exportToExcel = () => {
        const mime = "vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";
        const ws = XLSX.utils.json_to_sheet(props.data);
        const wb = {Sheets: {data: ws}, SheetNames: ["data"]};
        const excelBuffer = XLSX.write(wb, {bookType: "xlsx", type: "array"});
        const data = new Blob([excelBuffer], {type: mime});
        FileSaver.saveAs(data, `${props.fileName}.xlsx`);
    };
    return <span onClick={() => exportToExcel()}>export excel</span>;
};
