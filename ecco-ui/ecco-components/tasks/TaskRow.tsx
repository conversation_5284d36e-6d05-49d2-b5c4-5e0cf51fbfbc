import {TaskDto, WorkflowOperations} from "ecco-dto/workflow-dto";
import * as React from "react";
import {Attributes, Component, Fragment, FunctionComponentElement, ReactNode} from "react";
import {EccoDate} from "@eccosolutions/ecco-common";
import {taskDueClass, taskDueStatus} from "./TaskUtils";
import {ErrorBoundary} from "ecco-components-core";
import {TaskForm} from "./TaskForm";
import {WorkflowReloadEvent} from "./WorkflowLoader";
import {ServiceType, SessionData} from "ecco-dto";
import {find} from "@softwareventures/array";

export type TaskState = "unavailable" | "unassigned" | "assigned" | "assigned to me" | "completed";

export const states = {
    "unavailable": {iconClass: "fa fa-ban task-unavailable", labelClass: ""},
    "unassigned": {iconClass: "fa fa-check-circle", labelClass: "task-available"},
    "assigned to me": {iconClass: "fa fa-user task-owner", labelClass: ""},
    "assigned": {iconClass: "fa fa-user task-assigned", labelClass: ""},
    "completed": {iconClass: "fa fa-check-circle", labelClass: "task-completed"}
};

/** Omitting userId will mean assigned will always return "assigned" if assignedTo is non-null */
export function getTaskStateFromProp(task: TaskDto, userId?: string | undefined): TaskState {
    return !task
        ? "unavailable"
        : task.isCompleted
          ? "completed"
          : task.assignedTo
            ? task.assignedTo == userId
                ? "assigned to me"
                : "assigned"
            : task.isAvailable
              ? "unassigned"
              : "unavailable";
}

export function getTask(tasks: TaskDto[], taskName: string): TaskDto | null {
    return find(tasks, task => task.taskName == taskName);
}

function getTaskProps(task: TaskDto, userId: string) {
    const taskState = getTaskStateFromProp(task, userId);
    const uiState = states[taskState];
    const taskStateTitle = getTaskStateTitle(taskState, task.assignedTo);
    return {uiState, taskStateTitle};
}

function getTaskStateTitle(taskState: TaskState, assignedTo: string) {
    let taskStateTitle = taskState.toString();
    if (taskState.indexOf("assigned") == 0) {
        // for "assigned to me" and "assigned"
        taskStateTitle = "assigned to " + assignedTo; // TODO: change to me if matches my username from sessionData
    }
    return taskStateTitle;
}

/** Version of React.FunctionComponentFactory that requires props - because that definition breaks stuff */
type FunctionComponentFactory<P> = (props: Attributes & P, ...children: ReactNode[]) => FunctionComponentElement<P>;

interface TaskRowProps {
    sessionData: SessionData;
    serviceType: ServiceType;
    serviceAllocationId?: number | undefined;
    disabled: boolean;
    workflow: WorkflowOperations;
    task: TaskDto;
    username: string;
    title: string;
    subTitle?: string | JSX.Element | undefined;
    onClick: () => void;
    SummaryComponent: FunctionComponentFactory<{
        taskName: string;
        onHasAudit?: ((hasAudit: boolean) => void) | undefined;
        useLatestAudit?: boolean | undefined;
    }>;
}

/**
 * Move some TaskDto props into state, so that we can use the task state or the audit information
 */
interface TaskRowState {
    taskAssignedTo: string;
    taskComplete: boolean;
    taskStatusTitle: string;
    iconClass: string;
    labelClass: string;
    hasAuditCalled: boolean;
    editing: boolean;
}

export class TaskRow extends Component<TaskRowProps, TaskRowState> {
    constructor(props: TaskRowProps) {
        super(props);
        const {uiState, taskStateTitle} = getTaskProps(props.task, props.username);
        this.state = {
            taskAssignedTo: props.task.assignedTo,
            taskComplete: props.task.isCompleted,
            taskStatusTitle: taskStateTitle,
            iconClass: uiState.iconClass,
            labelClass: uiState.labelClass,
            hasAuditCalled: false,
            editing: false
        };
    }

    override render() {
        const props = this.props;

        // we should have an editable clock if we have a perpetual schedule, as it might be we need to set the dueDate
        const schedulePerpetual = props.serviceType
            .getTaskDefinitionEntry(props.task.taskName)
            ?.isTaskDueDateSchedulePerpetual();

        // if we have a task to edit (not completed) then we can edit
        // this means we'll have an actual task to edit (we could use hateoas or fromLinearTaskHandle(props.task.taskHandle).taskInstanceUuid != null)
        // ensure we use the state to get the latest calculation - it changes based on updateLegacyTaskStatus
        const isEditable =
            schedulePerpetual ||
            (this.state.taskStatusTitle &&
                (this.state.taskStatusTitle.indexOf("assigned") == 0 ||
                    this.state.taskStatusTitle.indexOf("unassigned") == 0));
        const showClock = schedulePerpetual || (!!props.task.dueDate && !props.task.isCompleted); // has due date and not-complete
        // task is not complete and assigned to me or another user
        /*const isUserAssigned =
            this.state.taskStatusTitle && this.state.taskStatusTitle.indexOf("assigned") == 0;
        const showUser = isUserAssigned;*/
        const showTick = !showClock;
        const dueStatus = taskDueStatus(props.task.dueDate);
        const clockColour = taskDueClass(dueStatus);

        return (
            <div className="e-row">
                <ErrorBoundary>
                    <span className={`e-label ${this.state.labelClass}`}>
                        {this.state.editing && (
                            <TaskForm
                                taskHandle={props.task.taskHandle}
                                serviceAllocationId={props.serviceAllocationId} // NB if our task was loaded from TaskStatus we'd have serviceAllocationId
                                show={this.state.editing}
                                setShow={() => {
                                    this.setState({editing: false});
                                    WorkflowReloadEvent.bus.fire();
                                }}
                            />
                        )}
                        {showClock && isEditable && (
                            <i
                                style={{width: "15px", height: "15px"}}
                                className={`fa fa-clock-o ${clockColour}${
                                    isEditable ? " clickable" : ""
                                }`}
                                title={`${
                                    EccoDate.parseIso8601FromDateTime(
                                        props.task.dueDate
                                    )?.formatIso8601() ?? ""
                                } ${this.state.taskStatusTitle}`}
                                onClick={() => isEditable && this.setState({editing: true})}
                            />
                        )}
                        {showTick && (
                            <i
                                style={{width: "15px", height: "15px"}}
                                className={`${this.state.iconClass} ${
                                    isEditable ? "clickable " : ""
                                }`}
                                title={this.state.taskStatusTitle}
                                onClick={() => isEditable && this.setState({editing: true})}
                            />
                        )}
                        <span id={`ra-${props.task.taskName}`} className="ra-task">
                            <span>
                                <button
                                    className="button btn btn-link"
                                    disabled={props.disabled}
                                    onClick={() =>
                                        props.workflow
                                            .ensureUnplannedWorkflowTask(props.task)
                                            .then(props.onClick)
                                    }
                                >
                                    {props.title}
                                </button>
                            </span>
                        </span>
                        {/* TODO currently error's rendering subTitle if in management mode */}
                        {props.subTitle && (
                            <Fragment>
                                <br />
                                <span>{props.subTitle}</span>
                            </Fragment>
                        )}
                    </span>
                    <span className="input">
                        <span style={{marginRight: "5"}}>&nbsp;</span>
                        <span className="ra-audit">
                            <span style={{fontSize: "0.9em"}}>
                                <props.SummaryComponent
                                    taskName={props.task.taskName}
                                    onHasAudit={
                                        this.state.hasAuditCalled
                                            ? undefined
                                            : (hasAudit: boolean) =>
                                                  this.updateLegacyTaskStatus(hasAudit)
                                    }
                                />
                            </span>
                        </span>
                    </span>
                </ErrorBoundary>
            </div>
        );
    }

    /**
     * Update, client-side, where the new tasks list can't tell if intermediate tasks have been achieved or not (from the server side)
     */
    private updateLegacyTaskStatus(hasAudit: boolean) {
        this.setState({
            hasAuditCalled: true
        });
        // only update possibly legacy tasks which are available - this represents the unknowable info server-side
        const legacyTask = this.state.taskAssignedTo == "possibly complete";
        if (!legacyTask || !this.props.task.isAvailable || this.state.taskComplete) {
            return;
        }
        if (hasAudit && !this.state.taskComplete) {
            const uiState = states["completed"];
            const taskStateTitle = getTaskStateTitle("completed", this.props.task.assignedTo);
            this.setState({
                taskComplete: true,
                taskStatusTitle: taskStateTitle,
                iconClass: uiState.iconClass,
                labelClass: uiState.labelClass
            });
        }
        if (!hasAudit && this.state.taskAssignedTo) {
            const uiState = states["unassigned"];
            const taskStateTitle = getTaskStateTitle("unassigned", "");
            this.setState({
                taskAssignedTo: "",
                taskStatusTitle: taskStateTitle,
                iconClass: uiState.iconClass,
                labelClass: uiState.labelClass
            });
        }
    }
}
