import {ReferralDto} from "ecco-dto";

export const mockReferralDto = (anyId: number): ReferralDto =>
    ({
        prefix: "r",
        serviceRecipientId: anyId + 2e6,
        referralId: anyId,
        referralCode: anyId.toString(),
        requestedDelete: false,
        clientId: 101,
        contactId: 22,
        calendarId: "",
        clientCode: "",
        serviceAllocationId: -1,
        clientDisplayName: "Mock Mike",
        firstName: "Mock",
        lastName: "<PERSON>",
        displayName: "Mock Mike",
        source: "self-referral",
        receivedDate: "11/12/2013",
        signpostedBack: false,
        statusMessageKey: "status.started",
        supportWorkerDisplayName: "worker1",
        interviewer1WorkerDisplayName: "worker1",
        supportWorkerId: 111999,
        interviewer1ContactId: 111999,
        calendarEvents: [],
        daysAttending: 31,
        interviewDna: 0,
        fundingAccepted: false,
        acceptOnServiceState: "UNSET",
        appropriateReferralState: "UNSET"
    } as any as ReferralDto);

export function mockReferralDtoPromise(rId: number) {
    return Promise.resolve(mockReferralDto(rId));
}
