import {ApiClient} from "../web-api";
import {ChartRepository} from "./ChartRepository";
import {ChartDefinitionDto} from "../reports/charts-dto";

export class ChartAjaxRepository implements ChartRepository {

    constructor(private apiClient: ApiClient) {
    }

    findAllChartDefinitions(): Promise<ChartDefinitionDto[]> {
        const apiPath = `reportDef/`;
        return this.apiClient.get<ChartDefinitionDto[]>(apiPath);
    }

    findChartDefByUuid(chartDefUuid: string): Promise<ChartDefinitionDto> {
        const apiPath = `reportDef/${chartDefUuid}/`;
        return this.apiClient.get<ChartDefinitionDto>(apiPath);
    }
}
