import {EccoDate, EccoDateTime, Slice} from "@eccosolutions/ecco-common";
import {EvidenceGroup, ReviewChoicesDto, SupportSmartStepsSnapshotDto, SupportWork} from "../evidence-dto";
import {ReviewChoices} from "./domain";
import {SupportSmartStepsSnapshotRepository} from "./SupportSmartStepsSnapshotRepository";
import {SupportWorkRepository} from "./SupportWorkRepository";
import {ApiClient} from "../web-api";

interface IdWrapper {
    id: number;
}

export class SupportWorkAjaxRepository
    implements SupportWorkRepository, SupportSmartStepsSnapshotRepository
{
    constructor(private apiClient: ApiClient) {}

    /** Used for radar chart and evidence history
     * This is ordered by newest first: workDate DESC, created DESC
     * Used by attachments.
     */
    public findSupportWorkByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        pageNumber?: number | undefined,
        attachmentsOnly?: boolean | undefined,
        hactOnly?: boolean | undefined,
        statusChangeOnly?: boolean | undefined,
        serviceIds?: number[] | undefined
    ): Promise<SupportWork[]> {
        let apiPath = `service-recipients/${serviceRecipientId}/evidence/${evidenceGroup.name}/`;

        if (attachmentsOnly) {
            // find all attachments
            // repeat...
            // find each work item with ref 'work'
            // render with SupportHistoryItemControl
            apiPath = apiPath.concat("attachments/");
        }

        const query = {
            page: pageNumber != null ? pageNumber.toString() : undefined,
            hactOnly: hactOnly ? hactOnly.toString() : undefined,
            statusChangeOnly: statusChangeOnly ? statusChangeOnly.toString() : undefined,
            serviceIds: serviceIds ? serviceIds.toString() : undefined
        };

        return this.apiClient
            .get<Slice<SupportWork>>(apiPath, {query})
            .then(slice => slice.content);
    }

    /** SupportEvidenceController.findSupportWorkByServiceRecipientId */
    public findOneSupportWorkByWorkUuid(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        workUuid: string
    ): Promise<SupportWork> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${evidenceGroup.name}/uuid/${workUuid}/`;

        return this.apiClient.get<SupportWork>(apiPath);
    }

    /**
     * Loads all the status changed work for all service recipients.
     * Note this is an array of arrays - use first element in each one to find the srId it relates to if needed
     */
    findAllSupportWorkByStatusChangesByServiceRecipientIds(
        serviceRecipientIds: number[] | string[],
        evidenceGroup = EvidenceGroup.needs,
        hactOnly = false
    ): Promise<SupportWork[][]> {
        // TODO performance - see SupportEvidenceController.findAllByServiceRecipientIds
        const apiPath = `service-recipients/evidence/${evidenceGroup.name}/`;
        const query = hactOnly
            ? {query: {serviceRecipientId: serviceRecipientIds.join(","), hactOnly: "true"}}
            : {query: {serviceRecipientId: serviceRecipientIds.join(","), changesOnly: "true"}};
        return this.apiClient.get<SupportWork[][]>(apiPath, query);
    }

    /** Used for initial view of support history */
    findSupportWorkByServiceRecipientIdFirstPage(
        serviceRecipientId: number,
        evidenceGroup = EvidenceGroup.needs
    ): Promise<Slice<SupportWork>> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${evidenceGroup.name}/`;
        return this.apiClient.get<Slice<SupportWork>>(apiPath, {query: {page: "0"}});
    }

    /** For when we want to find all, and then put in the offline database */
    findSupportWorkByServiceRecipientIdAndRiskManagementOutstanding(
        serviceRecipientId: number
    ): Promise<SupportWork[]> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${EvidenceGroup.needs.name}/riskManagementOutstanding/`;

        return this.apiClient.get<SupportWork[]>(apiPath);
    }

    /**
     * When working online we just want to the latest for the page we are on
     */
    findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroup(
        serviceRecipientId: number,
        evidenceGroupKey: string
    ): Promise<SupportSmartStepsSnapshotDto> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${evidenceGroupKey}/snapshots/latest/`;
        return this.apiClient.get<SupportSmartStepsSnapshotDto>(apiPath);
    }

    /**
     * When working online we just want to the latest for the page we are on
     */
    findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroupAtTime(
        serviceRecipientId: number,
        evidenceGroupKey: string,
        workDate: EccoDateTime
    ): Promise<SupportSmartStepsSnapshotDto> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${evidenceGroupKey}/snapshots/`;
        // the workDate timezone is assumed to be UTC - server-side actionInstanceSnapshotQuery assumes its UTC also
        const query = {
            query: {
                workDate: `${workDate.formatIso8601()}Z`
            }
        };
        return this.apiClient.get<SupportSmartStepsSnapshotDto>(apiPath, query);
    }

    setCustomReviewDate(serviceRecipientId: number, customReviewDate: EccoDate) {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${EvidenceGroup.needs.name}/review/schedule/`;
        return this.apiClient.post<void>(apiPath, {
            customReviewDate: customReviewDate.formatIso8601()
        });
    }

    setScheduleReviewDates(serviceRecipientId: number, schedule: string) {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${EvidenceGroup.needs.name}/review/schedule/automated/`;
        return this.apiClient.post<void>(apiPath, {reviewSchedule: schedule});
    }

    /** The review setup information screen which preceeds the review evidence pages */
    findReviewChoicesByServiceRecipientId(serviceRecipientId: number): Promise<ReviewChoices> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${EvidenceGroup.needs.name}/reviewChoices/`;
        return this.apiClient.get<ReviewChoicesDto>(apiPath).then(dto => new ReviewChoices(dto));
    }

    setReviewComplete(serviceRecipientId: number, reviewId: number): Promise<void> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${EvidenceGroup.needs.name}/review/${reviewId}/complete/`;
        return this.apiClient.post<void>(apiPath, null);
    }

    createReview(serviceRecipientId: number, date: EccoDate): Promise<number> {
        const apiPath = `service-recipients/${serviceRecipientId}/evidence/${EvidenceGroup.needs.name}/review/`;

        return this.apiClient
            .post<IdWrapper>(apiPath, {reviewDate: date.formatIso8601()})
            .then(idWrapper => idWrapper.id);
    }

    public deleteByWorkUuidWithServiceRecipientId(workUuid: string, serviceRecipientId: number) {
        const apiPath = `evidence/${EvidenceGroup.needs.name}/${workUuid}/`;
        return this.apiClient.del(apiPath, {serviceRecipientId});
    }
}

