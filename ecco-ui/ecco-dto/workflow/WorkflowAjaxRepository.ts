import {WorkflowDto} from '../workflow-dto';
import {WorkflowDtoRepository} from "./WorkflowDtoRepository";
import {ApiClient} from '../web-api';

export class WorkflowAjaxRepository implements WorkflowDtoRepository {
    constructor(private apiClient: ApiClient) {
    }

    public findOneWorkflowByServiceRecipientId(srId: number): Promise<WorkflowDto> {
        var path = `service-recipients/${srId}/workflow/`;
        return this.apiClient.get<WorkflowDto>(path);
    }
}

