import * as React from "react";
import {FC, useEffect, useMemo, useState} from "react";
import {
    Grid,
    LineChart,
    Line,
    CartesianGrid,
    XAxis,
    YA<PERSON><PERSON>,
    <PERSON>lt<PERSON>,
    Legend
} from "@eccosolutions/ecco-mui";
import {SupportAction, SupportSmartStepsSnapshotDto} from "ecco-dto";
import {useServicesContext} from "ecco-components";
import {EccoDate, EccoDateTime, NumberToObjectMap, StringUtils} from "@eccosolutions/ecco-common";

export interface ProgressSmartStepsScore {
    snapshotDate: string;
    snapshot: SupportSmartStepsSnapshotDto;
}

export const ProgressLineChartSmartSteps: FC<{
    srId: number;
    evidenceGroup: string;
    scoreListDef: string | undefined;
}> = ({srId, evidenceGroup, scoreListDef}) => {
    const [data, setData] = useState<ProgressSmartStepsScore[]>([]);
    const {sessionData, supportSmartStepsSnapshotRepository, riskWorkRepository} =
        useServicesContext();

    useEffect(() => {
        // last 3 months
        const now = EccoDateTime.nowLocalTime();
        const monthsBack = 2; // with this month makes 3
        const dates: EccoDate[] = [];
        for (let i = 0; i < monthsBack; i++) {
            const prevMonth = now.toEccoDate().subtractMonths(monthsBack - i);
            const prevMonthEnd = prevMonth.addMonths(1).withDate(1).subtractDays(1);
            dates.push(prevMonthEnd);
        }
        dates.push(now.toEccoDate());

        // 3 snapshots
        const singlePromise = (date: EccoDate) =>
            evidenceGroup == "threat"
                ? riskWorkRepository.findRiskSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroupAtTime(
                      srId,
                      date.toDateTimeMidnight()
                  )
                : supportSmartStepsSnapshotRepository.findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroupAtTime(
                      srId,
                      "needs",
                      date.toDateTimeMidnight()
                  );
        const promises = dates.map(singlePromise);
        Promise.all(promises).then(snapshots => {
            const data: ProgressSmartStepsScore[] = [];
            snapshots.forEach((s, i) => {
                if (s) {
                    data.push({
                        snapshotDate: dates[i]
                            .toLocalJsDate()
                            .toLocaleString("default", {month: "short"}),
                        snapshot: s
                    });
                }
            });
            setData(data);
        });
    }, []);

    return <ProgressLineChartSmartStepsConvert data={data} scoreListDef={scoreListDef} />;
};

export const ProgressLineChartSmartStepsConvert: FC<{
    data: ProgressSmartStepsScore[];
    scoreListDef: string | undefined;
}> = ({data, scoreListDef}) => {
    const {sessionData} = useServicesContext();

    const progressPoints = data.map(s => {
        // for this one snapshot, store actions per outcomeId
        const actionsByOutcomeId: NumberToObjectMap<SupportAction[]> = {};

        // filter to only hierarchy 0
        // NB should really select those with a score configured
        const latestActions = s.snapshot.latestActions.filter(
            a => a.hierarchy == null || a.hierarchy == 0
        );

        latestActions.forEach(a => {
            const outcomeId = sessionData.getAnyActionById(a.actionId).getOutcome().getId();
            const outcome = actionsByOutcomeId[outcomeId];
            if (outcome) {
                outcome.push(a);
            } else {
                actionsByOutcomeId[outcomeId] = [a];
            }
        });

        const lookupScoreValue = (score: number | null) => {
            if (score == null) {
                return 0;
            }
            if (scoreListDef) {
                const value = Number(sessionData.getListDefinitionEntryById(score).getValue());
                return isNaN(value) ? 0 : value;
            }
            return score;
        };

        // if we only have one outcome, then record individual smart steps
        const lineIdByOutcome = Object.values(actionsByOutcomeId).length > 1;

        // for each outcomeId, get the average score
        const lineIdToScore: NumberToObjectMap<number> = {};

        for (let outcomeIdStr in actionsByOutcomeId) {
            const outcomeId = parseInt(outcomeIdStr);
            const actions = actionsByOutcomeId[outcomeId];
            if (lineIdByOutcome) {
                const total = actions.reduce((t, a) => t + (lookupScoreValue(a.score) || 0), 0);
                lineIdToScore[outcomeId] = Math.round(total / actions.length);
            } else {
                actions.forEach(a => {
                    lineIdToScore[a.actionId] = lookupScoreValue(a.score) || 0;
                });
            }
        }

        // for each line, construct the points
        const points: {id: number; name: string; value: number}[] = [];
        for (let lineIdStr in lineIdToScore) {
            const lineId = parseInt(lineIdStr);
            const name = lineIdByOutcome
                ? sessionData.getAnyOutcomeById(lineId)?.name
                : sessionData.getAnyActionById(lineId).getName();
            points.push({
                id: lineId,
                name: name ? name : "-",
                value: lineIdToScore[lineId]
            });
        }

        return {
            name: s.snapshotDate,
            points: points
        };
    });

    return (
        <ProgressLineChartLayout
            data={progressPoints}
        />
    );
};

const lineColours = (i: number) => {
    const c = [
        "#ff1500",
        "#FF9900",
        "#387908",
        "#b700ff",
        "#00FFC4",
        "#0022FF",
        "#ff0059",
        "#640879"
    ];
    return i > c.length - 1 ? c[i - c.length] : c[i];
};
interface ProgressPoint {
    name: string;
    points: {id: number; name: string; value: number}[];
}

const ProgressLineChartLayout: FC<{data: ProgressPoint[]}> = ({data}) => {
    // return a flat data structure required for the chart
    const {
        data: flattenedData,
        lineIds,
        lineNames
    } = useMemo(() => {
        const ids: number[] = [];
        const idNames: string[] = [];
        const flattenedDataTmp: {name: string}[] = [];
        data.forEach((p, i) => {
            const obj = {
                name: p.name
            };
            p.points.forEach(pt => {
                // @ts-ignore
                obj[`point_${pt.id}`] = pt.value;
                if (!ids.includes(pt.id)) {
                    ids.push(pt.id);
                    idNames.push(pt.name);
                }
            });
            flattenedDataTmp.push(obj);
        });
        return {data: flattenedDataTmp, lineIds: ids, lineNames: idNames};
    }, [data]);

    return (
        <Grid container>
            <Grid item xs={12}>
                <LineChart
                    width={400}
                    height={300}
                    data={flattenedData}
                    margin={{top: 5, right: 20, left: 10, bottom: 5}}
                >
                    <XAxis dataKey={o => o.name} />
                    <YAxis domain={[0, 10]} />
                    <Tooltip />
                    <Legend />
                    <CartesianGrid stroke="#f5f5f5" />

                    {lineIds.map((id, i) => (
                        <Line
                            key={`line-${id}`}
                            name={StringUtils.trimText(lineNames[i], 15) || "-"}
                            type="monotone"
                            dataKey={`point_${id}`}
                            stroke={lineColours(i)}
                        />
                    ))}
                </LineChart>
            </Grid>
        </Grid>
    );
};
