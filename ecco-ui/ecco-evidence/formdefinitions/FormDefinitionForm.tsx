import {Nullable} from "@eccosolutions/ecco-common";
import {CommandQueue} from "ecco-commands";
import * as React from "react";
import {FC} from "react";
import {CommandForm, CommandSubform, EccoAPI, ModalCommandForm, useServicesContext} from "ecco-components";
import {FormDefinition as FormDefinitionDto} from "ecco-dto/form-definition-dto";
import {FormDefinition} from "./FormDefinition";

type LocalProps = { services: EccoAPI, commandForm: CommandForm };

interface ModalProps {
    serviceRecipientId: number;
    formDefinition?: FormDefinitionDto | undefined; // absent on new formDefinition
    setShow: (show: boolean) => void;
}
interface Props extends ModalProps {
    readOnly: boolean;
}

interface State {
    formDefinition: FormDefinitionDto;
}

export class FormDefinitionForm extends CommandSubform<Props & LocalProps, State> {
    constructor(props: Props & LocalProps) {
        super(props);
        this.state = {
            formDefinition: props.formDefinition || ({} as FormDefinitionDto)
        };
    }

    getErrors(): string[] {
        const errors: string[] = [];
        const formDefinition = this.state.formDefinition;
        // NOTE: Done in order they appear on the form
        // if (!formDefinition.categoryId) {
        //     errors.push("type is required");
        // }
        return errors;
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const formDefinitionIn = this.props.formDefinition || ({} as FormDefinitionDto);
        const formDefinition = this.state.formDefinition;
    }

    private updateFormDefinition = (formDefinition: Partial<Nullable<FormDefinitionDto>>) => {
        this.setState({formDefinition: formDefinition as FormDefinitionDto});
    };

    override render() {
        return (
            <FormDefinition
                formDefinition={this.state.formDefinition}
                onChange={this.updateFormDefinition}
            />
        );
    }
}

export const FormDefinitionFormModal: FC<ModalProps> = props => {
    const eccoAPI = useServicesContext();

    return (
        <ModalCommandForm
            show={true} // i.e. we're a form that's modal when shown
            setShow={props.setShow}
            title={props.formDefinition ? "edit form definition" : "add new form definition"}
            action={props.formDefinition ? "update" : "save"}
            maxWidth="sm"
        >
            {form => <FormDefinitionForm
                {...props}
                readOnly={!(eccoAPI.sessionData.hasRoleReports() && eccoAPI.sessionData.hasRoleAdmin())}
                services={eccoAPI}
                commandForm={form}
            />}
        </ModalCommandForm>
    );
};
