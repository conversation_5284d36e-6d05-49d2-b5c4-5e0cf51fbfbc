{"private": true, "name": "ecco-forms", "version": "0.0.0", "license": "UNLICENSED", "main": "./index.js", "typings": "./types/index.d.ts", "scripts": {"analyse": "webpack --json | webpack-bundle-size-analyzer", "clean": "tsc --build --clean", "emit": "webpack --config-name=dev && eslint --ext .ts,.tsx .", "build": "eslint --ext .ts,.tsx . && webpack", "lint": "eslint --ext .ts,.tsx .", "test": "yarn test-parallel-safe && yarn test-sequential", "test-parallel-safe": "echo Nothing to do", "test-sequential": "echo Nothing to do"}, "dependencies": {"@rjsf/core": "3.2.1", "@rjsf/material-ui": "3.2.1", "markdown-to-jsx": "^6.11.4", "react": "16.13.1", "react-dom": "16.13.1"}, "devDependencies": {"@types/markdown-to-jsx": "^6.11.0", "@types/react": "^16.9.56", "@types/react-dom": "^16.9.24", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "babel-loader": "^8.0.6", "esbuild-loader": "^2.21.0", "eslint": "^7.14.0", "terser-webpack-plugin": "^4.2.3", "ts-loader": "^5.3.3", "typescript": "5.8.3", "url-loader": "4.1.1", "webpack": "^5.101.0", "webpack-bundle-size-analyzer": "^3.1.0", "webpack-cli": "^6.0.1"}}