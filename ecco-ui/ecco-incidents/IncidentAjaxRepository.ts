import {IncidentRepository} from "ecco-dto";
import {ApiClient, IncidentDto} from "ecco-dto";

export class IncidentAjaxRepository implements IncidentRepository {
    constructor(private apiClient: ApiClient) {}

    public findById(incidentId: number): Promise<IncidentDto> {
        const apiPath = `incidents/${incidentId}/`;
        return this.apiClient.get<IncidentDto>(apiPath);
    }

    public findByServiceRecipientId(srId: number): Promise<IncidentDto> {
        const apiPath = `incidents/service-recipients/${srId}/`;
        return this.apiClient.get<IncidentDto>(apiPath);
    }
}
