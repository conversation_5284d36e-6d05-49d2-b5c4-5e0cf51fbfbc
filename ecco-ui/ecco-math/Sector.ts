import {angles} from "./angles";

/** Represents a non-overlapping range of angles.
 *
 * Abstractly, this is equivalent to a sector of a circle. */
export class Sector {
    /** Constructs a new Sector that represents the range of angles starting
     * from the specified start angle and continuing clockwise to the specified
     * end angle.
     *
     * The angles need not be normalized to any particular range; they will be
     * normalized automatically. */
    constructor(private readonly startAngle: number, private readonly endAngle: number) {
        this.startAngle = angles.normalize(startAngle);
        this.endAngle = this.startAngle + angles.difference(this.startAngle, angles.normalize(endAngle));
    }

    /** Gets the most anti-clockwise angle that bounds the sector.
     *
     * This angle is normalized to the range -pi (inclusive) to pi (exclusive).
     *
     * The value returned by this method is always numerically less than or
     * equal to the value returned by this.getEndAngle(). */
    public getStartAngle(): number {
        return this.startAngle;
    }

    /** Gets the most clockwise angle that bounds the sector.
     *
     * This angle is normalized to the range startAngle (inclusive) to
     * (startAngle + 2 * pi) (exclusive), where startAngle is the value
     * returned by this.getStartAngle().
     *
     * The value returned by this method is always numerically greater than or
     * equal to the value returned by this.getStartAngle(). */
    public getEndAngle(): number {
        return this.endAngle;
    }
}
