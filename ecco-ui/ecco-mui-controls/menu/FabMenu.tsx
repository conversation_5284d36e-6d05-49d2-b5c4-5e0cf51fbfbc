import * as React from "react";
import {FC} from "react";
import {Fab} from "@eccosolutions/ecco-mui";
import {IconMenu} from "./IconMenu";
import AddIcon from "@material-ui/icons/Add";

export const FabMenu: FC<{open: boolean; setOpen: (open: boolean) => void}> = props => (
    <IconMenu
        id="fab-menu"
        iconComponent={
            <Fab size="small" color="default">
                <AddIcon />
            </Fab>
        }
        open={props.open}
        onClose={() => props.setOpen(false)}
        onClick={() => props.setOpen(true)}
    >
        {props.children}
    </IconMenu>
);
