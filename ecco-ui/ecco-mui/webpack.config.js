
var path = require("path");
var TerserJsPlugin = require('terser-webpack-plugin');

var projectDir = __dirname;
var distDir = path.resolve(projectDir, "./dist/");
var debugDir = path.resolve(projectDir, "./debug/");

const esbuildDisabled = false; // because it barfs on

function configuration(mode) {
    var configuration = {
        mode: mode,
        name: mode === "production" ? "prod" : "dev",
        entry: "./index.ts",
        externals:
            [
                // We internalise @mui/icons, @mui/pickers
                "bowser",
                /ecco-[a-z-]+$/, // works for @eccosolutions/ecco- too, but don't want .css .png etc to be externalised
                // We include "@material-ui/**" here and only here,
                "moment",
                "react",
                "react-async",
                "react-bootstrap",
                "react-dom",
                "react-router",
                "react-router-dom"
            ],
        module: {
            rules: [
                {
                    test: /\.js$/,
                    loader: 'babel-loader',
                    options: {
                        presets: ["@babel/preset-env"],
                    }
                },
                {
                    // COPIED from Create-React-App react-scripts/config/webpack.config.js
                    // "url" loader works like "file" loader except that it embeds assets
                    // smaller than specified limit in bytes as data URLs to avoid requests.
                    // A missing `test` is equivalent to a match.
                    test: [/\.png$/],
                    loader: require.resolve('url-loader'),
                    options: {
                        limit: true, // Unlimited - don't use file loader
                        // name: 'static/media/[name].[hash:8].[ext]',
                    },
                },
                {
                    test: /\.tsx?$/,
                    use: mode === "production" || esbuildDisabled ? {
                        loader: "ts-loader",
                        options: {
                            configFile: "tsconfig.json",
                            compilerOptions: {
                                noEmit: false
                            }
                        }
                    }
                    : {
                        loader: "esbuild-loader",
                        options: {
                            loader: 'tsx',  // Or 'ts' if you don't need tsx
                            target: 'es2015',
                        }
                    },
                    exclude: /(^|\/)node_modules\//
                }
            ]
        },
        resolve: {
            extensions: [".tsx", ".ts", ".js", ".png"]
        },
        output: {
            path: mode === "production"
                ? distDir
                : debugDir,
            filename: "ecco-mui.js",
            libraryTarget: "umd",
            devtoolModuleFilenameTemplate: "[resource-path]?[loaders]"
        }
    };

    if (mode === "production") {
        configuration.optimization = {
            minimizer: [
                new TerserJsPlugin({
                    parallel: true,
                    terserOptions: {
                        compress: {
                            passes: 2
                        }
                    }
                })
            ]
        };
    } else {
        configuration.devtool = "inline-source-map"
    }

    return configuration;
}

module.exports = [configuration("production"), configuration("development")];
