import {utc} from "moment";

import {arrayBuffers, base64, NOT_FOUND} from "@eccosolutions/ecco-common";
import {CryptoService, Uuid} from "@eccosolutions/ecco-crypto";
import {ApiClient, RECENT_USER_CHANGE, RequestOptions, UserDeviceDto} from "ecco-dto";
import {AuthenticationException} from "./security/AuthenticationException";
import {SecurityRepository} from "./security/SecurityRepository";
import {UserDevice} from "./security/UserDevice";
import {UserSession} from "./security/UserSession";
import {UserSessionRepository} from "./UserSessionRepository";
import ArrayBufferView = arrayBuffers.ArrayBufferView;

// Changed to const to avoid IDEA/Webstorm organise imports doing things out of order
const toDataView = arrayBuffers.toDataView;


export class UserSessionManager {

    constructor(private apiClient: ApiClient,
            private cryptoService: CryptoService,
            private securityRepository: SecurityRepository,
            private userSessionRepository: UserSessionRepository) {
    }

    public loginIfNeeded(username: string, password?: string, offline?: boolean): Promise<void> {
        return this.userSessionRepository.findUserSession()
            .catch(() => {
                return this.login(username, password, offline);
            })
            .then(() => {})
    }

    /** If online, log the user in remotely and store the user device key encrypted with the password.
     *  If offline, log the user in locally.
     *  throw AuthenticationException for auth related errors
     */
    public login(username: string,
                 password = username, // This is to support Office 365 logins
                 offline?: boolean): Promise<UserSession> {
        if (username == null) {
            throw new TypeError("Username must not be null.");
        }

        username = username.toLowerCase().trim();

        sessionStorage.setItem(RECENT_USER_CHANGE, username)

        // cryptoService below uses crypto.subtle which requires https
        // but not all sites are https, so we exit early for now to prevent
        // a failure causing a quirky form-based login issue - see DEV-2195.
        const schemeIsHttps = window.location.protocol == 'https:';
        const hostnameIsLocalhost = location.hostname === "localhost" || location.hostname === "127.0.0.1";
        if (!offline && !(schemeIsHttps || hostnameIsLocalhost)) {
            return Promise.resolve(null as unknown as UserSession);
        }

        // Retrieve a User-Device record from the Offline Repository. The
        // User-Device record represents the User on this Device. If the User
        // has not yet used this Device offline, then there will be no
        // User-Device record and the Promise will resolve to null.
        let maybeUserDevice: Promise<UserDevice> = this.securityRepository.findOneUserDeviceByUsername(username);

        // If we are offline, and there is no relevant User-Device record in
        // the Offline Repository, the login attempt fails immediately.
        // The user must log in online at least once before they can log in
        // offline.
        if (offline) {
            maybeUserDevice = maybeUserDevice
                    .then(maybeUserDevice => {
                        if (maybeUserDevice == null) {
                            throw new AuthenticationException("Could not find '" + username + "' as an offline user.");
                        } else {
                            return maybeUserDevice;
                        }
                    });
        }

        // Retrieve the User's Credentials Key Salt, or generate a new one if
        // the User does not have a User-Device record on this device.
        let credentialsKeySalt = maybeUserDevice
                .then(maybeUserDevice => this.getOrGenerateCredentialsKeySalt(maybeUserDevice));

        // Generate the Credentials Key for the new Session based on whatever
        // password was entered (could be incorrect).
        let credentialsKey = credentialsKeySalt
                .then(salt => this.cryptoService.deriveKey(password, salt));

        if (!offline) {
            // Retrieve an updated User-Device record from the server, or ask
            // it to create a new one if required.

            let onlineUserDeviceDto = maybeUserDevice.then(maybeOfflineUserDevice =>
                // typically, this is the first request of the API via the form login
                this.fetchNewOrExistingUserDevice(maybeOfflineUserDevice, username, password)
            );

            let userDeviceId = onlineUserDeviceDto
                    .then(onlineUserDeviceDto => Uuid.parse(onlineUserDeviceDto.guid));

            let userDeviceKey = onlineUserDeviceDto
                    .then(onlineUserDeviceDto => base64.decodeFromString(onlineUserDeviceDto.base64Key));

            let encryptedUserDeviceKey = userDeviceKey
                    .then(userDeviceKey => credentialsKey
                            .then(credentialsKey => this.cryptoService
                                    .encrypt({secretData: userDeviceKey}, credentialsKey)));

            maybeUserDevice = credentialsKeySalt
                    .then(credentialsKeySalt => onlineUserDeviceDto
                            .then(onlineUserDeviceDto => userDeviceId
                                    .then(userDeviceId => encryptedUserDeviceKey
                                            .then(encryptedUserDeviceKey => this.securityRepository.registerUserDevice(
                                                    username,
                                                    credentialsKeySalt,
                                                    userDeviceId,
                                                    encryptedUserDeviceKey)))));
        }

        // If the User-Device record is still null, then the username does not
        // correspond to a registered User.
        let userDevice = maybeUserDevice
                .then(maybeUserDevice => {
                    if (maybeUserDevice == null) {
                        throw new AuthenticationException("Invalid user: '" + username + "'.");
                    } else {
                        return maybeUserDevice;
                    }
                });

        // Validate the credentials key by attempting to use it to decrypt the user-device key.
        let validatedCredentialsKey = credentialsKey
                .then(credentialsKey => userDevice
                        .then(userDevice => this.cryptoService
                                .decrypt(userDevice.getUserDeviceKeyCipherMessage(), credentialsKey)
                                .then(() => credentialsKey)));

        // If the credentials key does not validate, swap the byte order and try again.
        // This is for compatibility with old code which accidentally swapped the byte order of the credentials
        // key on little-endian systems.
        validatedCredentialsKey = validatedCredentialsKey
            .catch(() => credentialsKey.then(swapByteOrder))
            .then(credentialsKey => userDevice
                .then(userDevice => this.cryptoService
                    .decrypt(userDevice.getUserDeviceKeyCipherMessage(), credentialsKey)
                    .then(() => credentialsKey)));

        // When the credentials key is validated, create the user session.
        let userSessionDto = validatedCredentialsKey
                .then(credentialsKey => userDevice
                        .then(userDevice => {
                            console.log("Creating user session for %s.", userDevice.getUsername());

                            return this.userSessionRepository
                                    .login(userDevice.getUsername(),
                                            base64.encodeToString(credentialsKey),
                                            userDevice.getUserDeviceId().toString());
                        }));

        let userSession = userSessionDto
                .then(userSessionDto => userDevice
                        .then(userDevice => UserSession.fromDto(userSessionDto, userDevice)));

        return Promise.resolve(userSession
                .catch(reason => {
                    console.error("Authentication failure: %s", reason.stack || reason.toString());

                    if (reason.statusCode == 403) {
                        // We can end up here if the login details are correct
                        // but JCE is missing or misconfigured.
                        // For example this can happen if Java is updated and
                        // the installed JCE is not compatible.
                        console.info("Authentication failure possibly caused by missing Java Cryptographic " +
                                "Extensions. Check that JCE is properly installed.");
                    }

                    throw reason;
                }));
    }

    public logout(): Promise<void> {
        return this.userSessionRepository.logout();
    }

    /** Finds the current user session.
     *
     * If there is no current user session then the promise will be rejected with an AuthenticationException.
     * If there is no userDevice for the session username (poss if have cleared database but not session), then
     * also reject with AuthenticationException */
    public findCurrentUserSession(): Promise<UserSession> {
        let userSessionDto = this.userSessionRepository.findUserSession();

        let userDevice = userSessionDto
                .then(userSessionDto => this.securityRepository.findOneUserDeviceByUsername(userSessionDto.username));

        return userSessionDto
                .then(userSessionDto => userDevice
                        .then(userDevice => {
                            return userDevice
                                ? UserSession.fromDto(userSessionDto, userDevice)
                                : Promise.reject(new AuthenticationException("No userDevice found for " + userSessionDto.username));
                        }));
    }

    private getOrGenerateCredentialsKeySalt(userDevice: UserDevice): Promise<ArrayBufferView> {
        if (userDevice == null) {
            return this.cryptoService.generateSalt();
        } else {
            return Promise.resolve(userDevice.getCredentialsKeySalt());
        }
    }

    private fetchNewOrExistingUserDevice(maybeOfflineUserDevice: UserDevice,
            username: string, password: string): Promise<UserDeviceDto> {

        const userDevicePath = !maybeOfflineUserDevice
            ? "keys/userDevices/valid/"
            : `keys/userDevices/valid/${maybeOfflineUserDevice.getUserDeviceId()}/`;

        let requestOptions: RequestOptions = {
            credentials: {
                username: username,
                password: password
            }
        };

        return this.apiClient.post<UserDeviceDto>(userDevicePath, {}, false, requestOptions)
            .catch( reason => {
                // If (and only if) explicitly 404, then clean up automatically and get a fresh key
                if (reason.statusCode === NOT_FOUND) {
                    return this.securityRepository
                        .deleteUserDeviceByUsername(username)
                        .then(() =>
                            this.apiClient.post<UserDeviceDto>(
                                "keys/userDevices/valid/",
                                {},
                                false,
                                requestOptions
                            )
                        );
                }
                throw reason;
            });
    }

    /** Clear last sync data - for use only when clearing all local data */
    public clearLastSync() {
        localStorage.removeItem("ecco-last-sync-user");
        localStorage.removeItem("ecco-last-sync-time");
    }

    public updateLastSync(username: string) {
        var now = new Date().getTime();
        localStorage.setItem("ecco-last-sync-user", username);
        localStorage.setItem("ecco-last-sync-time", now.toString());
    }

    public getLastSyncUser(): string | null {
        return localStorage.getItem("ecco-last-sync-user");
    }

    public getLastSyncInstant(): string | null {
        const timeStr = localStorage.getItem("ecco-last-sync-time");
        if (!timeStr) {
            return null;
        }
        return new Date(parseInt(timeStr)).toISOString();
    }

    public getLastSyncTime(): string | null {
        const timeStr = localStorage.getItem("ecco-last-sync-time");
        if (isFinite(parseInt(timeStr!))) {
            return utc(parseInt(timeStr!)).fromNow();
        }
        else {
            return timeStr;
        }
    }
}

function swapByteOrder(key: ArrayBufferView): ArrayBufferView {
    const keyView = toDataView(key);
    const result = toDataView(new Uint8Array(key.byteLength));
    for (let i=0; i<keyView.byteLength; i+= 4) {
        result.setUint32(i, keyView.getUint32(i, true), false);
    }
    return result;
}