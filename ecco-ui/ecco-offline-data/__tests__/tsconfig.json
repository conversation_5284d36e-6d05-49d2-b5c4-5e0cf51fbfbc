{
  "extends": "../../tsconfig.ecco-module.json",
  "compilerOptions": {
    "allowSyntheticDefaultImports": true,
    "noEmit": true,
    "module": "AMD",
    "moduleResolution": "Node",
    // Doesn't seem to be read when run via gulp, but need for running via IDEA
    "esModuleInterop": true,
    "target": "ES2018",
    "baseUrl": "",
    "paths": {
      "*": ["*", "./typings/*"]
    },
    "lib": ["es2017.object", "es2015", "dom"],
    "alwaysStrict": true,
    "jsx": "react",
    "strict": false,
    "strictPropertyInitialization": false, // TODO: can switch to true when start using strictNullChecks
    "types": ["@types/jest", "application-properties"]
  },
  "references": [{"path": ".."}]
}
