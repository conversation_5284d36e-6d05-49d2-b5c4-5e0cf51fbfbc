import {BAD_REQUEST, WebApiError} from "@eccosolutions/ecco-common";
import {loginPageAuthErrorHandler} from "./loginPageAuthErrorHandler";

/** Page not found */
export var NOT_FOUND_ERROR = {};

function isWebApiError(error: any): error is WebApiError {
    return typeof error.getMessage == "function";
}

export function showErrorAsAlert(error: unknown) {
    if (!isWebApiError(error)) {
        const message = (error as any).message;
        alert(message || JSON.stringify(error));
        return;
    }
    if (error.statusCode == BAD_REQUEST) {
        alert(
            error.getMessage() || "Weird.  We didn't accept that.  Did you fill out all the fields?"
        );
        return;
    }
    if (error.statusCode >= 400 && error.statusCode < 600) {
        alert(`Something went wrong. Error code: ${error.statusCode}\n${error.getMessage()}`);
        return;
    }
    console.log("error saving: ", error);
    alert(
        "Failed to save your changes (possibly no data connection). Close this window and click 'save' to retry..."
    );
}

// Allow ES6 Promise or Promise to be used
export function withAuthErrorHandler<T, P extends Promise<T>>(promise: P): P {
    return promise.catch(error => {
        if (!loginPageAuthErrorHandler(error)) {
            console.error("withAuthErrorHandler - non-auth error: ", error);
            throw error;
        }
        return null;
    }) as P;
}

/** @deprecated - use from ecco-dto to avoid bringing in offline-data */
export function stringifyPossibleError(e: unknown) {
    return JSON.stringify(e, Object.getOwnPropertyNames(e));
}