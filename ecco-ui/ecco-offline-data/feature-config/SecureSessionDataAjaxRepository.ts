import {Encrypted} from "@eccosolutions/ecco-common";
import {
    ApiClient,
    SessionDataDto,
    SessionDataPlainFields,
    SessionDataSecretFields,
    UserSessionDataPlainFields,
    UserSessionDataSecretFields
} from "ecco-dto";

export interface EncryptedSessionDataDto extends Encrypted<SessionDataSecretFields, SessionDataPlainFields> {
}

export type EncryptedUserSessionDataDto = Encrypted<UserSessionDataSecretFields, UserSessionDataPlainFields>

export class SecureSessionDataAjaxRepository {
    constructor(private apiClient: ApiClient) {
    }

    public getUserConfig(userDeviceId: string): Promise<EncryptedUserSessionDataDto> {
        return this.apiClient.secureGet(userDeviceId, "config/user/");
    }

    /** Note: Now unencrypted from service-worker cache */
    public getSessionData(): Promise<SessionDataDto> {
        return this.apiClient.get("config/global");
    }
}
