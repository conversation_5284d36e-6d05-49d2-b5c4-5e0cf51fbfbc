import Lazy = require("lazy");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import * as dto from "ecco-dto";

import {EccoDateTime} from "@eccosolutions/ecco-common";
import types = require("./types");
import Group = types.Group;
import ReferralAggregate = types.ReferralAggregate;
import SequenceAnalysis = types.SequenceAnalysis;
import Transformer = types.Transformer;
import referralCommonAnalysis = require("./referralCommonAnalysis");
import groupByReferralProjectName = referralCommonAnalysis.groupByReferralProjectName;
import tableRepresentations = require("../tables/predefined-table-representations");
import {ClientAttendance, FormEvidence, RepairDto} from "ecco-dto";
import {AnalysisContext} from "../chart-domain";
import {
    booleanColumn,
    columnMap,
    dateTimeColumn,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    numberColumn,
    textColumn
} from "../controls/tableSupport";
import {ReferralSummaryDto} from "ecco-dto";
import {
    customFormWorkOnlyColumns,
    listDefIdLookup,
    referralSummaryColumns
} from "../tables/predefined-table-representations";

class ReferralAggregateGroupWithDaysOfWeekCounts implements Group<ReferralAggregate> {
    sunday = 0;
    monday = 0;
    tuesday = 0;
    wednesday = 0;
    thursday = 0;
    friday = 0;
    saturday = 0;
    constructor(public key: string, public elements: Sequence<ReferralAggregate>) {}
}

function countsOfDayAttending(
    key: string,
    input: Sequence<ReferralAggregate>
): ReferralAggregateGroupWithDaysOfWeekCounts {
    function applyAnalysis(
        prev: ReferralAggregateGroupWithDaysOfWeekCounts,
        item: ReferralAggregate
    ): ReferralAggregateGroupWithDaysOfWeekCounts {
        prev.monday += item.referral.daysAttending & dto.DAYS_AS_BITS.MON ? 1 : 0;
        prev.tuesday += item.referral.daysAttending & dto.DAYS_AS_BITS.TUE ? 1 : 0;
        prev.wednesday += item.referral.daysAttending & dto.DAYS_AS_BITS.WED ? 1 : 0;
        prev.thursday += item.referral.daysAttending & dto.DAYS_AS_BITS.THUR ? 1 : 0;
        prev.friday += item.referral.daysAttending & dto.DAYS_AS_BITS.FRI ? 1 : 0;
        prev.saturday += item.referral.daysAttending & dto.DAYS_AS_BITS.SAT ? 1 : 0;
        prev.sunday += item.referral.daysAttending & dto.DAYS_AS_BITS.SUN ? 1 : 0;
        return prev;
    }

    var memo = new ReferralAggregateGroupWithDaysOfWeekCounts(key, input);
    return input.reduce((prev, item) => applyAnalysis(prev, item), memo);
}

/** flatten and group by related activity interests */
function groupByActivityInterest(
    input: Sequence<ReferralAggregate>
): Sequence<ReferralAggregateGroupWithDaysOfWeekCounts> {
    var flattened = input
        .map(item => {
            return item.activityInterest!.map(interest => {
                return {key: interest.name, item: item};
            });
        })
        .flatten<{key: string; item: ReferralAggregate}>();
    var grouped = flattened
        .groupBy(inputElement => inputElement.key)
        .pairs()
        .map(group => ({key: group[0], items: group[1].map(withKey => withKey.item)}));
    return grouped.map(entry => countsOfDayAttending(entry.key, Lazy(entry.items)));
}

function activitiesByProject(
    input: Sequence<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<ReferralAggregateGroupWithDaysOfWeekCounts> {
    return groupByReferralProjectName(input, ctx).map(pair => {
        var input: Sequence<ReferralAggregate> = pair.elements;
        return countsOfDayAttending(pair.key, input);
    });
}

class ReferralGroupWithActivityAnalysis extends SequenceAnalysis<ReferralAggregateGroupWithDaysOfWeekCounts> {
    constructor(ctx: AnalysisContext, data: Sequence<ReferralAggregateGroupWithDaysOfWeekCounts>) {
        super(ctx, data, (item: ReferralAggregateGroupWithDaysOfWeekCounts) => item.key);
        this.recordRepresentation = {
            DaysOfWeekAnalysis: tableRepresentations.daysOfweekAnalysisColumns
        };
        // On click gives us a RAGWDOWC which we can either use as itself or as a sequence of referrals
        this.addOnClickAnalyser("referrals", "WrapUnGroupReferralAggregateSequenceAnalyser");
        this.addOnClickManyAnalysis("referrals", "ReferralAggregateAnalysis");
    }
}
export var activityCountsByProjectAnalyser: Transformer<
    ReferralAggregate,
    ReferralAggregateGroupWithDaysOfWeekCounts
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ReferralAggregateGroupWithDaysOfWeekCounts> {
    return new ReferralGroupWithActivityAnalysis(ctx, activitiesByProject(input, ctx));
};
export var activityCountsByActivityInterestAnalyser: Transformer<
    ReferralAggregate,
    ReferralAggregateGroupWithDaysOfWeekCounts
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ReferralAggregateGroupWithDaysOfWeekCounts> {
    return new ReferralGroupWithActivityAnalysis(ctx, groupByActivityInterest(input));
};

const activityAttendanceOnlyColumns = columnMap(
    numberColumn<ClientAttendance>("sr-id", row => row.getDto().serviceRecipientId),

    // referral assumed
    textColumn<ClientAttendance>(
        "r-id",
        row =>
            row.getDto().referralSummary.referralCode ||
            row.getDto().referralSummary.referralId!.toString()
    ),
    textColumn<ClientAttendance>(
        "c-id",
        row =>
            row.getDto().referralSummary.clientCode ||
            row.getDto().referralSummary.clientId.toString()
    ),
    textColumn<ClientAttendance>("client", row => row.getDto().referralSummary.displayName),
    // referral assumed

    textColumn<ClientAttendance>("activity", (row, ctx) =>
        listDefIdLookup(row.getDto().parentActivity!.activityTypeId, ctx.getSessionData())
    ),
    textColumn<ClientAttendance>("venue", row => row.getDto().parentActivity!.venueName),
    textColumn<ClientAttendance>("description", row => row.getDto().parentActivity!.description),
    numberColumn<ClientAttendance>("duration", row =>
        row.getDto().parentActivity ? row.getDto().parentActivity!.duration : 0
    ),
    dateTimeColumn<ClientAttendance>("date", row =>
        EccoDateTime.parseIso8601Utc(row.getDto().parentActivity!.startDateTime)
    ),
    booleanColumn<ClientAttendance>("invited", row => row.getDto().invited),
    booleanColumn<ClientAttendance>("attending", row => row.getDto().attending),
    booleanColumn<ClientAttendance>("attended", row => row.getDto().attended),
    textColumn<ClientAttendance>("comment", row => row.getDto().comment),

    // see ClientAttendanceToViewModel
    numberColumn<ClientAttendance>("g-id", row => row.getDto().parentActivity!.id),
    numberColumn<ClientAttendance>("g-id course", row => row.getDto().parentActivity!.parentId),
    textColumn<ClientAttendance>("g-service", row =>
        row.getSessionData().getServiceName(row.getDto().parentActivity!.serviceId!)
    ),
    textColumn<ClientAttendance>("g-project", row =>
        row.getSessionData().getProjectName(row.getDto().parentActivity!.projectId!)
    ),
    booleanColumn<ClientAttendance>("course", row => row.getDto().parentActivity!.course ?? null)
);

const activityAttendanceToReferralSummaryColumns = joinNestedPathColumnMaps<
    ClientAttendance,
    ReferralSummaryDto
>("r", row => row.getDto().referralSummary, referralSummaryColumns);

const activityAttendanceWithReferralSummaryColumns = joinColumnMaps(
    activityAttendanceOnlyColumns,
    activityAttendanceToReferralSummaryColumns
);

export const activityAttendanceToCustomFormColumns = joinNestedPathColumnMaps<
    ClientAttendance,
    FormEvidence<any>
>("f", row => row.getDto().customFormWorkLatest, customFormWorkOnlyColumns);
const activityAttendanceWithReferralSummaryWithFormColumns = joinColumnMaps(
    activityAttendanceWithReferralSummaryColumns,
    activityAttendanceToCustomFormColumns
);

export class ActivityAttendanceOnlyAnalysis extends SequenceAnalysis<ClientAttendance> {
    constructor(ctx: AnalysisContext, data: Sequence<ClientAttendance>) {
        super(
            ctx,
            data,
            (item: ClientAttendance) =>
                `${item.getDto().parentActivity!.uuid}:${item.getDto().referralSummary.referralId}`
        );
        this.recordRepresentation = {
            ActivityAttendanceWithReferralSummary: activityAttendanceWithReferralSummaryColumns,
            ActivityAttendanceWithReferralSummaryWithForm:
                activityAttendanceWithReferralSummaryWithFormColumns,
            ActivityAttendanceOnly: activityAttendanceOnlyColumns
        };
    }
}
