import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import {EccoDateTime} from "@eccosolutions/ecco-common";
import types = require("./types");
import Analyser = types.Analyser;
import Group = types.Group;
import SequenceAnalysis = types.SequenceAnalysis;
import {BaseServiceRecipientCommandDto} from "ecco-dto";
import {GroupFn, extractPair, filterByPropertyValue} from "../analysis/types";
import {AnalyserReportStage, AnalysisContext, AnalysisStageContext} from "../chart-domain";
// NB need to nb these in main
//import {CommandHistoryItemControl} from "../../service-recipients/CommandHistoryItemControl";
import {AppointmentActionCommand, AppointmentRecurringActionCommand} from "ecco-commands";
import {columnMap, dateTimeColumn, numberColumn, textColumn} from "../controls/tableSupport";

//*********************************
// Analysis: breakdown of service recipient commands

export const commandOnlyColumns = columnMap(
    // timestamp is created time
    dateTimeColumn<BaseServiceRecipientCommandDto>("timestamp", row =>
        EccoDateTime.parseIso8601Utc(row.timestamp)
    ),
    numberColumn<BaseServiceRecipientCommandDto>("sr-id", row => row.serviceRecipientId),
    textColumn<BaseServiceRecipientCommandDto>("username", row => row.userName),
    textColumn<BaseServiceRecipientCommandDto>("command name", row => row.commandName),
    textColumn<BaseServiceRecipientCommandDto>("command", row => JSON.stringify(row))
);

export class ServiceRecipientCommandOnlyAnalysis extends SequenceAnalysis<BaseServiceRecipientCommandDto> {
    constructor(ctx: AnalysisContext, data: Sequence<BaseServiceRecipientCommandDto>) {
        super(ctx, data, (item: BaseServiceRecipientCommandDto) => item.uuid);
        this.recordRepresentation = {
            ServiceRecipientCommandOnly: commandOnlyColumns
        };
        this.derivativeAnalysers = {
            serviceRecipientCommandFilterByRotaCommandName:
                serviceRecipientCommandFilterByRotaCommandAnalyser,
            serviceRecipientCommandFilterByCommand: serviceRecipientCommandFilterByCommandAnalyser,
            serviceRecipientCommandCountsByUsername: serviceRecipientCommandCountsByUsernameAnalyser
            // serviceRecipientCommandCountsByCommandName:
            //     serviceRecipientCommandCountsByCommandNameAnalyser
        };
    }
}

//*********************************
// Analysis: grouped

const serviceRecipientCommandFilterByCommandAnalyser: Analyser<
    Sequence<BaseServiceRecipientCommandDto>,
    Sequence<BaseServiceRecipientCommandDto>
> = function (
    ctx: AnalysisContext,
    input: Sequence<BaseServiceRecipientCommandDto>
): ServiceRecipientCommandOnlyAnalysis {
    const stageCtx = (ctx as AnalysisStageContext).getCurrentStage() as AnalyserReportStage;
    const filterNames = stageCtx.getAnalyserConfig()?.analyserFilters;
    const filtersArr = filterNames
        ? filterNames.map(
              f => (i: BaseServiceRecipientCommandDto) => JSON.stringify(i).match(f) != null
          )
        : [(i: BaseServiceRecipientCommandDto) => true];
    const filteredData = filtersArr.reduce((data, curr) => data.filter(curr), input);
    return new ServiceRecipientCommandOnlyAnalysis(ctx, filteredData);
};

const serviceRecipientCommandFilterByRotaCommandAnalyser: Analyser<
    Sequence<BaseServiceRecipientCommandDto>,
    Sequence<BaseServiceRecipientCommandDto>
> = function (
    ctx: AnalysisContext,
    input: Sequence<BaseServiceRecipientCommandDto>
): ServiceRecipientCommandOnlyAnalysis {
    return new ServiceRecipientCommandOnlyAnalysis(
        ctx,
        filterByPropertyValue(
            input,
            "commandName",
            [
                AppointmentRecurringActionCommand.discriminator,
                AppointmentActionCommand.discriminator
            ],
            ctx
        )
    );
};

/*const serviceRecipientCommandCountsByCommandNameAnalyser: Analyser<
    Sequence<BaseServiceRecipientCommandDto>,
    Sequence<Group<BaseServiceRecipientCommandDto>>
> = function (
    ctx: AnalysisContext,
    input: Sequence<BaseServiceRecipientCommandDto>
): GroupedServiceRecipientCommandAnalysis {
    return new GroupedServiceRecipientCommandAnalysis(
        ctx,
        serviceRecipientCommandCountsBy(input, groupByServiceRecipientCommandName, ctx)
    );
};*/

// author
const serviceRecipientCommandCountsByUsernameAnalyser: Analyser<
    Sequence<BaseServiceRecipientCommandDto>,
    Sequence<Group<BaseServiceRecipientCommandDto>>
> = function (
    ctx: AnalysisContext,
    input: Sequence<BaseServiceRecipientCommandDto>
): GroupedServiceRecipientCommandAnalysis {
    return new GroupedServiceRecipientCommandAnalysis(
        ctx,
        serviceRecipientCommandCountsBy(input, groupByServiceRecipientUsername, ctx)
    );
};

/*function translateCommandName(
    cmd: BaseServiceRecipientCommandDto,
    sessionData: SessionData
): string {
    const handler = CommandHistoryItemControl.createCommandViewHandler(cmd, sessionData);
    return handler.getTitleForCommand();
}*/

/*export function groupByServiceRecipientCommandName(
    input: Sequence<BaseServiceRecipientCommandDto>,
    ctx?: AnalysisContext
): Sequence<Group<BaseServiceRecipientCommandDto>> {
    return input
        .groupBy(inputElement => {
            return translateCommandName(inputElement, ctx.getSessionData());
        })
        .pairs()
        .map(extractPair);
}*/

export function groupByServiceRecipientUsername(
    input: Sequence<BaseServiceRecipientCommandDto>,
    ctx?: AnalysisContext
): Sequence<Group<BaseServiceRecipientCommandDto>> {
    return input
        .groupBy(inputElement => inputElement.userName || "")
        .pairs()
        .map(extractPair);
}

//*********************************
// Analysis: grouped supporting functions

class GroupedServiceRecipientCommandAnalysis extends SequenceAnalysis<
    Group<BaseServiceRecipientCommandDto>
> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<BaseServiceRecipientCommandDto>>) {
        super(ctx, data, (item: Group<BaseServiceRecipientCommandDto>) => item.key);
        this.addOnClickAnalyser("ungroup", WrapServiceRecipientCommandSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", ServiceRecipientCommandOnlyAnalysis);
    }
}

/** This deals with clicking on chart segment */
const WrapServiceRecipientCommandSequenceAnalyser: Analyser<
    Group<BaseServiceRecipientCommandDto>,
    Sequence<BaseServiceRecipientCommandDto>
> = function (
    ctx: AnalysisContext,
    input: Group<BaseServiceRecipientCommandDto>
): ServiceRecipientCommandOnlyAnalysis {
    return new ServiceRecipientCommandOnlyAnalysis(ctx, input.elements);
};

function serviceRecipientCommandCountsBy(
    input: Sequence<BaseServiceRecipientCommandDto>,
    groupFn: GroupFn<BaseServiceRecipientCommandDto>,
    ctx: AnalysisContext
): Sequence<Group<BaseServiceRecipientCommandDto>> {
    return groupFn(input, ctx).map(pair => {
        var input: Sequence<BaseServiceRecipientCommandDto> = pair.elements;
        return {
            key: pair.key,
            count: input.size(),
            elements: input
        };
    });
}
