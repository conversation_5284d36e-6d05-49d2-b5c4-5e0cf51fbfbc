import Lazy = require("lazy");
import tableRepresentations = require("../tables/predefined-table-representations");
import types = require("./types");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import Analyser = types.Analyser;
import Group = types.Group;
import SequenceAnalysis = types.SequenceAnalysis;
import GroupFn = types.GroupFn;
import {AppointmentDto, DemandScheduleDto} from "ecco-dto";
import {Agreement, AgreementDto, ServiceRecipientDemandDto} from "ecco-rota";
import {AnalysisContext} from "../chart-domain";
import {EccoDate} from "@eccosolutions/ecco-common";

export class RotaAgreementAnalysis extends SequenceAnalysis<Agreement> {
    constructor(ctx: AnalysisContext, data: Sequence<Agreement>) {
        super(ctx, data, (item: Agreement) => item.getServiceRecipientId().toString());
        this.derivativeAnalysers = {
            flattenToRelevantSchedules: flattenToRelevantSchedulesAnalyser
        };
        this.recordRepresentation = {};
    }
}

/** The query is based on schedule date, but we get the agreements with all schedules in, so filter them */
const flattenToRelevantSchedulesAnalyser: Analyser<
    Sequence<Agreement>,
    Sequence<DemandScheduleDto>
> = function (ctx: AnalysisContext, data: Sequence<Agreement>): RotaScheduleAnalysis {
    const start = ctx.getReportFrom()!;
    const end = ctx.getReportTo() || EccoDate.todayUtc();
    return new RotaScheduleAnalysis(
        ctx,
        data
            .map(agreement => agreement.getDemandSchedules())
            .flatten<DemandScheduleDto>()
            .filter(schedule => !schedule.parentScheduleId)
            .filter(schedule =>
                start.earlierThanOrEqual(EccoDate.parseIso8601(schedule.end)) && schedule.end
                    ? end.laterThanOrEqual(EccoDate.parseIso8601(schedule.start))
                    : true
            )
    );
};

export class RotaScheduleAnalysis extends SequenceAnalysis<DemandScheduleDto> {
    constructor(ctx: AnalysisContext, data: Sequence<DemandScheduleDto>) {
        super(ctx, data, (item: DemandScheduleDto) => item.scheduleId.toString());
        this.derivativeAnalysers = {
            filterRelevantSchedules: filterRelevantSchedules,
            flattenToScheduleAppointments: flattenToScheduleAppointmentsAnalyser
        };
        this.recordRepresentation = {
            //DemandScheduleOnly: tableRepresentations.rotaScheduleColumns
        };
        this.addOnClickAnalyser("ungroup", "ungroupToScheduleAppointmentsAnalyser");
    }
}
const filterRelevantSchedules: Analyser<
    Sequence<DemandScheduleDto>,
    Sequence<DemandScheduleDto>
> = function (ctx: AnalysisContext, data: Sequence<DemandScheduleDto>): RotaScheduleAnalysis {
    return new RotaScheduleAnalysis(
        ctx,
        data.filter(schedule => !schedule.parentScheduleId)
    );
};

const flattenToScheduleAppointmentsAnalyser: Analyser<
    Sequence<DemandScheduleDto>,
    Sequence<AppointmentDto>
> = function (ctx: AnalysisContext, data: Sequence<DemandScheduleDto>): RotaAppointmentAnalysis {
    return new RotaAppointmentAnalysis(
        ctx,
        data.map(ds => ds.appointments).flatten<AppointmentDto>()
    );
};

export var ungroupToScheduleAppointmentsAnalyser = function (
    ctx: AnalysisContext,
    data: DemandScheduleDto
): RotaAppointmentAnalysis {
    return new RotaAppointmentAnalysis(ctx, Lazy(data.appointments));
};

export class RotaAppointmentAnalysis extends SequenceAnalysis<AppointmentDto> {
    constructor(ctx: AnalysisContext, data: Sequence<AppointmentDto>) {
        super(ctx, data, (item: AppointmentDto) => item.ref.toString());
        this.derivativeAnalysers = {};
        this.recordRepresentation = {
            //AppointmentOnly: tableRepresentations.rotaAppointmentColumns
        };
    }
}

// ********************************

/** This deals with clicking on chart segment */
const WrapRecipientSequenceAnalyser: Analyser<
    Group<ServiceRecipientDemandDto>,
    Sequence<ServiceRecipientDemandDto>
> = function (ctx: AnalysisContext, input: Group<ServiceRecipientDemandDto>): RotaDemandAnalysis {
    return new RotaDemandAnalysis(ctx, input.elements);
};

//*********************************
// Analysis:

class GroupedRecipientAnalysis extends SequenceAnalysis<Group<ServiceRecipientDemandDto>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<ServiceRecipientDemandDto>>) {
        super(ctx, data, (item: Group<ServiceRecipientDemandDto>) => item.key);
        this.addOnClickAnalyser("ungroup", WrapRecipientSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", RotaDemandAnalysis);
    }
}

//*********************************
// Analysers:

type RecipientToGroupedRecipientAnalyser = Analyser<
    Sequence<ServiceRecipientDemandDto>,
    Sequence<Group<ServiceRecipientDemandDto>>
>;

function countsOfNumberOfItems(
    key: string,
    input: Sequence<ServiceRecipientDemandDto>
): Group<ServiceRecipientDemandDto> {
    return {
        key: key,
        count: input.size(),
        elements: input
    };
}

function countsByGroupFn(
    input: Sequence<ServiceRecipientDemandDto>,
    groupFn: GroupFn<ServiceRecipientDemandDto>
): Sequence<Group<ServiceRecipientDemandDto>> {
    return groupFn(input).map(pair => {
        let input: Sequence<ServiceRecipientDemandDto> = pair.elements;
        return countsOfNumberOfItems(pair.key, input);
    });
}

class GroupedServiceRecipientDemandDtoAnalysis extends GroupedRecipientAnalysis {
    constructor(ctx: AnalysisContext, data: Sequence<Group<ServiceRecipientDemandDto>>) {
        super(ctx, data);
        this.addOnClickAnalyser("ungroup", WrapRecipientSequenceAnalyser);
        this.addOnClickManyAnalysis("ungroup", RotaDemandAnalysis);
    }
}

//*********************************
// Analysis:

export class AgreementsAnalysis extends SequenceAnalysis<AgreementDto> {
    constructor(ctx: AnalysisContext, data: Sequence<AgreementDto>) {
        super(ctx, data, (item: AgreementDto) => item.serviceRecipientId.toString());
        this.derivativeAnalysers = {
            // TODO
        };
        this.recordRepresentation = {
            AgreementItem: tableRepresentations.AgreementReportItemColumns
        };
    }

    override getKey(item: AgreementDto) {
        return item.agreementId.toString();
    }
}

const extractAgreementsAnalyser: Analyser<
    Sequence<ServiceRecipientDemandDto>,
    Sequence<AgreementDto>
> = function (ctx: AnalysisContext, data: Sequence<ServiceRecipientDemandDto>): AgreementsAnalysis {
    return new AgreementsAnalysis(
        ctx,
        data.map(recipient => recipient.agreements).flatten<AgreementDto>()
    );
};

export class RotaDemandAnalysis extends SequenceAnalysis<ServiceRecipientDemandDto> {
    constructor(ctx: AnalysisContext, data: Sequence<ServiceRecipientDemandDto>) {
        super(ctx, data, (item: ServiceRecipientDemandDto) => item.serviceRecipientId.toString());
        this.derivativeAnalysers = {
            extractAgreements: extractAgreementsAnalyser
        };
        this.recordRepresentation = {
            ServiceRecipientItem: tableRepresentations.rotaDemandColumns
        };
        this.addOnClickAnalyser("ungroup", "wrapSequenceWithOriginalAnalyser");
    }

    override getKey(item: ServiceRecipientDemandDto) {
        return item.serviceRecipientId.toString();
    }
}

/** ungroup on click: by use byName as otherwise it's infinitely loopy for TSC 1.5 */
export var wrapSequenceWithOriginalAnalyser = function (
    ctx: AnalysisContext,
    input: ServiceRecipientDemandDto
): RotaDemandAnalysis {
    // FIXME: WHY HO WHY DOES THIS CAUSE LOOPY NOW ?
    return new RotaDemandAnalysis(ctx, Lazy([input]));
};

types.analysersByName["wrapSequenceWithOriginalAnalyser"] = wrapSequenceWithOriginalAnalyser;
types.analysersByName["WrapRecipientSequenceAnalyser"] = WrapRecipientSequenceAnalyser;
types.analysersByName["ungroupToScheduleAppointmentsAnalyser"] =
    ungroupToScheduleAppointmentsAnalyser;
