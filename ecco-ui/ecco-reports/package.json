{"private": true, "name": "ecco-reports", "version": "0.0.0", "license": "UNLICENSED", "main": "./build-tsc/index.js", "typings": "./build-tsc/index.d.ts", "scripts": {"clean": "tsc --build --clean", "emit": "webpack --config-name=dev && eslint --ext .ts .", "build": "eslint --ext .ts . && webpack", "lint": "eslint --ext .ts .", "test": "echo && yarn cy:test", "cy:test": "echo Nothing to do", "cy:dev": "cypress open --component"}, "dependencies": {"application-properties": "0.0.0", "@eccosolutions/ecco-common": "2.0.0", "@eccosolutions/ecco-mui": "0.0.0", "@eccosolutions/ecco-mui-controls": "0.0.0", "@softwareventures/nullable": "^3.2.0", "ecco-components": "0.0.0", "ecco-commands": "0.0.0", "ecco-dto": "0.0.0", "lazy.js": "0.4.2", "react": "16.13.1", "react-dom": "16.13.1"}, "peerDependencies": {}, "devDependencies": {"@bahmutov/cypress-esbuild-preprocessor": "^2.2.0", "@testing-library/cypress": "^8.0.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "babel-loader": "^8.0.6", "cypress": "^11.2.0", "esbuild": "^0.17.19", "esbuild-loader": "^2.21.0", "eslint": "^7.14.0", "terser-webpack-plugin": "^4.2.3", "typescript": "5.8.3", "webpack": "^5.101.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^4.15.2"}}