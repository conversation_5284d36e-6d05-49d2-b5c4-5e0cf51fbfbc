import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {AgreementDto, RotaDto, RotaProposalDto} from "./rota-dto";
import {Agreement, agreementDtosToDomain, Rota, RotaProposal} from "./rota-domain";
import {RotaRepository} from "./RotaRepository";
import {CommandAjaxRepository, CommandRepository} from "ecco-commands";
import {
    ApiClient,
    AppointmentTypeDto,
    EventSnapshotDto,
    RateCardDto,
    SessionData,
    AppointmentDto,
    DemandScheduleDto
} from "ecco-dto";

export class RotaAjaxRepository implements RotaRepository {
    private readonly cmdRepository: CommandRepository;

    constructor(
        private apiClient: ApiClient,
        private sessionDataFactory: () => Promise<SessionData>
    ) {
        this.cmdRepository = new CommandAjaxRepository(apiClient);
    }

    public findRotaResources(
        startDate: EccoDate,
        endDate: EccoDate | null,
        resourceFilter: string,
        demandFilter: string | null
    ): Promise<number[]> {
        const apiPath = `rota/${resourceFilter}/resources/`;
        const options = {
            query: {
                startDate: startDate.formatIso8601(),
                endDate: endDate?.formatIso8601(),
                demandFilter: demandFilter ?? undefined
            }
        };

        return this.apiClient.get<number[]>(apiPath, options);
    }

    public findRotaDemands(
        startDate: EccoDate,
        endDate: EccoDate | null,
        resourceFilter: string,
        demandFilter: string | null
    ): Promise<number[]> {
        // defers to original
        return this.findAgreementSrIdsByDemandAndScheduleDate(
            resourceFilter,
            demandFilter,
            startDate,
            endDate
        );
    }

    /**
     * Calls RotaController.viewRota()
     * Loads a RotaDto populated with resources or demand.
     *
     * @param startDate - if null, defaults to today
     * @param endDate - if null, defaults to start date
     *
     * @param resourceFilter - required, e.g. workers:all
     * @param demandFilter - if null, selects all
     * @param loadResource
     * @param loadDemand
     */
    public findRotaByDate(
        startDate: EccoDate | null,
        endDate: EccoDate | null | undefined,
        resourceFilter: string,
        demandFilter: string | null,
        loadResource = true,
        loadDemand = true
    ): Promise<Rota> {
        const apiPath = `rota/${resourceFilter}/view/`;
        const options = {
            query: {
                startDate: startDate?.formatIso8601(),
                endDate: endDate?.formatIso8601(),
                demandFilter: demandFilter ?? undefined,
                loadResource: loadResource.toString(),
                loadDemand: loadDemand.toString()
            }
        };

        return this.apiClient
            .get<RotaDto>(apiPath, options)
            .then(dto =>
                this.sessionDataFactory().then(
                    sessionData => new Rota(this, sessionData, this.cmdRepository, dto)
                )
            );
    }

    /**
     * Used by the scheduler.
     */
    public findEventSnapshotsTodayUntilNowUtc(): Promise<EventSnapshotDto[]> {
        const apiPath = `calendarEventSnapshot/range/`;
        const options = {
            query: {
                from: EccoDate.todayUtc().toDateTimeMidnight().formatIso8601(),
                to: EccoDateTime.nowUtc().formatIso8601()
            }
        };
        return this.apiClient.get<EventSnapshotDto[]>(apiPath, options);
    }

    public findEventSnapshotsFromToUtc(
        fromUtc: EccoDateTime | undefined,
        toUtc: EccoDateTime
    ): Promise<EventSnapshotDto[]> {
        const apiPath = `calendarEventSnapshot/range/`;
        const options = {
            query: {
                from: fromUtc?.formatIso8601(),
                to: toUtc.formatIso8601()
            }
        };
        return this.apiClient.get<EventSnapshotDto[]>(apiPath, options);
    }

    public findMyMissedEventSnapshotsBeforeUtc(
        beforeUtc: EccoDateTime,
        resourceContactId: number
    ): Promise<EventSnapshotDto[]> {
        const apiPath = `calendarEventSnapshot/missed/`;
        const options = {
            query: {
                before: beforeUtc.formatIso8601(),
                resourceContactId: resourceContactId.toString()
            }
        };
        return this.apiClient.get<EventSnapshotDto[]>(apiPath, options);
    }

    public suggestAllocationsByDate(
        startDate: EccoDate,
        endDate: EccoDate,
        resourceFilter: string,
        demandFilter: string
    ): Promise<RotaProposal> {
        const apiPath = `rota/${resourceFilter}/suggested-allocations`;
        const options = {
            query: {
                startDate: startDate.formatIso8601(),
                endDate: endDate.formatIso8601(),
                demandFilter: demandFilter
            }
        };

        return this.apiClient
            .get<RotaProposalDto>(apiPath, options)
            .then(dto => new RotaProposal(dto));
    }

    public findAllAppointmentTypes(): Promise<AppointmentTypeDto[]> {
        const apiPath = `appointmentTypes/`;

        return this.apiClient.get<AppointmentTypeDto[]>(apiPath);
    }

    public findAgreementByAgreementId(agreementId: number): Promise<Agreement> {
        const apiPath = `rota/agreements/${agreementId}/`;

        return this.apiClient.get<AgreementDto>(apiPath).then(dto => new Agreement(dto));
    }

    /** TODO Long winded way of getting the schedule, better as API */
    public findScheduleById(agreementId: number, scheduleId: number): Promise<DemandScheduleDto> {
        return this.findAgreementByAgreementId(agreementId).then(
            a => a.getDemandSchedules().filter(s => s.scheduleId === scheduleId)[0]
        );
    }

    public findAgreementsByServiceRecipientId(serviceRecipientId: number): Promise<Agreement[]> {
        const apiPath = `rota/agreements/serviceRecipient/${serviceRecipientId}/`;

        return this.apiClient.get<AgreementDto[]>(apiPath).then(agreements => {
            return agreementDtosToDomain(agreements);
        });
    }

    /**
     * Loads AgreementController findAllAgreementsByDemandAndScheduleDate
     * which loads demand including care runs - depending on the handler
     */
    public findAgreementSrIdsByDemandAndScheduleDate(
        resourceFilter: string,
        demandFilter: string | null,
        startDate: EccoDate,
        endDate: EccoDate | null
    ): Promise<number[]> {
        const apiPath = `rota/${resourceFilter}/agreements/srIds/`;
        const options = {
            query: {
                startDate: startDate.formatIso8601(),
                endDate: endDate?.formatIso8601(),
                demandFilter: demandFilter ?? undefined
            }
        };

        return this.apiClient.get<number[]>(apiPath, options);
    }

    /**
     * Loads AgreementController findAllAgreementsByDemandAndScheduleDate
     * which loads demand including care runs - depending on the handler
     */
    public findAgreementsByDemandAndScheduleDate(
        resourceFilter: string,
        demandFilter: string | null,
        startDate: EccoDate,
        endDate: EccoDate | null
    ): Promise<Agreement[]> {
        const apiPath = `rota/${resourceFilter}/agreements/`;
        const options = {
            query: {
                startDate: startDate.formatIso8601(),
                endDate: endDate?.formatIso8601(),
                demandFilter: demandFilter ?? undefined
            }
        };

        return this.apiClient.get<AgreementDto[]>(apiPath, options).then(agreements => {
            return agreementDtosToDomain(agreements);
        });
    }

    // Used for reporting - specifically fetchRelatedEntities with 'scheduleAptsConfirmedAtEnd'
    public findScheduleRecurrencesConfirmed(
        scheduleRef: string,
        start: EccoDate,
        end?: EccoDate
    ): Promise<AppointmentDto[]> {
        const apiPath = `rota/-/schedule/${scheduleRef}/confirmed/`; // see RotaController
        const options = {
            query: {
                startDate: start.formatIso8601(),
                endDate: end ? end.formatIso8601() : undefined
            }
        };

        return this.apiClient.get<AppointmentDto[]>(apiPath, options);
    }

    public findAgreementsByReferralId(referralId: number): Promise<Agreement[]> {
        const apiPath = `rota/agreements/referral/${referralId}/`;

        return this.apiClient.get<AgreementDto[]>(apiPath).then(agreements => {
            return agreementDtosToDomain(agreements);
        });
    }

    /**
     * @param scheduleRef The schedule reference, not a repeating recurrence, but a recurring entry handle.
     */
    public findScheduleByHandle(scheduleRef: string): Promise<DemandScheduleDto> {
        const apiPath = `rota/agreements/schedules/ref/${scheduleRef}/`;

        return this.apiClient.get<DemandScheduleDto>(apiPath);
    }

    public findRateCardsForAgreement(appointmentId: number): Promise<RateCardDto[]> {
        const apiPath = `rota/agreements/${appointmentId}/rateCards/`;

        return this.apiClient.get<RateCardDto[]>(apiPath);
    }
}
