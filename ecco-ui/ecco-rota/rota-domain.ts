import {default as TimeSlotBasedCalendar} from "./TimeSlotBasedCalendar";
import {
    AjaxStartEvent,
    AjaxStopEvent,
    bus,
    EccoDate,
    EccoDateTime,
    EccoTime,
    HateoasResource,
    Interval,
    intervalFromDto,
    LinkDto,
    StringToObjectMap
} from "@eccosolutions/ecco-common";
import {AvailabilityDto} from "./dto";
import {
    AgreementDto,
    AgreementParameters,
    AllocationDto,
    PeriodDto,
    RotaDto,
    RotaProposalDto,
    RotaResourceDto,
} from "./rota-dto";
import {RotaRepository} from "./RotaRepository";
import {
    asAddedRemoved,
    asDateChange,
    CalendarDays,
    getRelation,
    SessionData,
    SupportAction,
    AppointmentDto,
    DemandScheduleDto,
    IntervalType
} from "ecco-dto";
import {
    AppointmentActionCommand,
    AppointmentRecurringActionCommand,
    Command,
    CommandRepository,
    ServiceRecipientAppointmentScheduleCommandDto
} from "ecco-commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {uniqueByIdentity} from "@softwareventures/array";

/** The number of Rota Time Slots in one hour.
 *
 * See Rota#getTimeSlots() for more documentation on Time Slots.
 *
 * TODO: This information should really come from the server. */
export const TIME_SLOTS_PER_HOUR = 4;

/** Return the elements in `here` that are not in `butNotHere` */
function diff<T extends number | string>(here: T[] = [], butNotHere: T[] = []): T[] {
    return here.filter(h => butNotHere.indexOf(h) < 0);
}

function mostToLeastAvailability(a: DemandResource, b: DemandResource) {
    return b.getAvailability().unallocatedSlots() - a.getAvailability().unallocatedSlots()
}

/** The rota for a given period. */
export class Rota {
    private readonly date: EccoDate;
    private readonly timeSlots: TimeSlots;
    private readonly resources: readonly DemandResource[];
    private readonly demandAppointments: Activity[];

    /** Constructs a Rota Domain Object by converting it from the supplied
     * Data Transfer Object.
     *
     * This constructor is intended for use by implementations of
     * RotaRepository. It should not be called from other code as there will
     * be no way to persist the constructed Rota. */
    constructor(
        public readonly repository: RotaRepository,
        private readonly sessionData: SessionData,
        private readonly cmdRepository: CommandRepository,
        private readonly dto: RotaDto
    ) {
        this.date = EccoDate.parseIso8601(dto.startDate)!;
        this.timeSlots = new TimeSlots(TIME_SLOTS_PER_HOUR);
        this.demandAppointments = activityDtosToDomain(
            repository,
            sessionData,
            cmdRepository,
            this,
            this.timeSlots,
            dto.demandAppointments
        );
        this.resources = workerDtosToDomain(
            repository,
            sessionData,
            cmdRepository,
            this,
            dto.resources
        ).sort(mostToLeastAvailability); // NOTE: appends to this.activities
    }

    public getDto(): RotaDto {
        return this.dto;
    }

    public replaceWith(data: RotaDto): Rota {
        return new Rota(this.repository, this.sessionData, this.cmdRepository, data);
    }

    /**
     * When we load resources by srId, we end up with many Rota, so we merge to one to avoid further work.
     * Merge by concatenating the resources
     * @param data
     */
    public merge(resourceFilter: string, demandFilter: string, data: RotaDto): Rota {
        const newResources = this.dto.resources.concat(data.resources);
        const newDemands = this.dto.demandAppointments.concat(data.demandAppointments);
        const newData = {
            resourceFilter: resourceFilter,
            demandFilter: demandFilter,
            startDate: this.dto.startDate,
            endDate: this.dto.endDate,
            resources: newResources,
            demandAppointments: newDemands
        };
        return new Rota(this.repository, this.sessionData, this.cmdRepository, newData);
    }

    public withAddedDemandFrom(rota: Rota) {
        if (rota.getUnallocatedOrDroppedActivities().length > 0) {
            this.dto.demandAppointments = this.dto.demandAppointments.concat(
                rota.getDto().demandAppointments
            );
            return this.replaceWith(this.dto);
        }
        return this; // Ref doesn't change if nothing changed
    }

    /** Gets the date of this rota. */
    public getDate() {
        return this.date;
    }

    /** Gets the Time Slots that the rota is divided into.
     *
     * Every day the rota is divided into a number of equally long Time Slots.
     *
     * The first Time Slot of each day starts at the beginning of the day,
     * and each consecutive Time Slot begins immediately after the last, until
     * the end of the day.
     *
     * Rota Activities are allocated in integer multiples of Time Slots. So,
     * for instance, an Activity might be three Time Slots long.
     *
     * The start time of a Rota Activity must align to the start time of a
     * Time Slot. */
    public getTimeSlots() {
        return this.timeSlots;
    }

    /** Gets the list of workers who may be assigned activities on the rota.
     *
     * Do not modify this array! */
    public getResources() {
        return this.resources;
    }

    /** Gets the list of activities that have not yet been allocated to any
     * worker on this rota. Returns an empty array if no activities. Should never be null.
     *
     * Do not modify this array! */
    public getAllActivities() {
        return this.demandAppointments;
    }

    /** return activities that are unallocated or dropped */
    public getUnallocatedOrDroppedActivities() {
        return this.demandAppointments.filter(
            (activity: Activity) => activity.getAllocatedWorkerJobs().length == 0
        );
    }

    public getAllocatedActivities() {
        return this.demandAppointments.filter(
            (activity: Activity) => activity.getAllocatedWorkerJobs().length > 0
        );
    }

    public getAvailableWorkers(activity: Activity) {
        return this.resources.filter(worker => worker.availableForPeriod(activity));
    }

    public getResourceFilter() {
        return this.dto.resourceFilter;
    }

    public getDemandType() {
        // TODO: Double check back end intent - perhaps it can be simplified there
        return this.dto.resourceFilter.split("s:")[0]; // i.e. careruns:all -> carerun (singular)
    }

    public getDemandFilter() {
        return this.dto.demandFilter;
    }

    public static calculateHours(appts: Activity[] | null | undefined): number {
        // the total of all hours could be done by calculating the last total column - eg WorkerTableRow.calculateWorkerTotal + unassigned
        // but this should be exactly the same as all the rota appointments on the page
        let totalHrs = 0;
        if (appts) {
            appts
                .filter(a => !!a)
                .forEach((appt: Activity) => {
                    // allDay is probably a way to avoid calendar events adding up
                    totalHrs += appt.isAllDay()
                        ? 0
                        : appt.getEnd()
                        ? appt.getEnd()!!.subtractDateTime(appt.getStart()).inHours()
                        : 0;
                });
        }
        return totalHrs;
    }
}

const HOURS_PER_DAY = 24;
const MILLISECONDS_PER_DAY = HOURS_PER_DAY * 60 * 60 * 1000;

export class TimeSlots {
    private timeSlots: TimeSlot[];

    constructor(timeSlotsPerHour: number) {
        this.timeSlots = [];

        const timeSlotsPerDay = HOURS_PER_DAY * timeSlotsPerHour;
        const millisecondsPerTimeSlot = MILLISECONDS_PER_DAY / timeSlotsPerDay;

        const makeTimeSlot = (i: number) => {
            const startTime = EccoTime.fromMillisecondsSinceDayStart(millisecondsPerTimeSlot * i);
            const endTime = EccoTime.fromMillisecondsSinceDayStart(millisecondsPerTimeSlot * (i + 1));

            const timeSlot = {
                getIndex: () => i,
                getStartTime: () => startTime,
                getEndTime: () => endTime,
                getPrevious: () => this.timeSlots[i - 1] || null,
                getNext: () => this.timeSlots[i + 1] || null,
                getAtIndex: (slot: number) => this.timeSlots[slot] || null,
                forEachUntil: (endTimeSlot: TimeSlot, callback: (timeSlot: TimeSlot) => void) => {
                    const endIndex = endTimeSlot.getIndex();
                    let currentTimeSlot = timeSlot;
                    while (currentTimeSlot && currentTimeSlot.getIndex() <= endIndex) {
                        callback(currentTimeSlot);
                        currentTimeSlot = currentTimeSlot.getNext();
                    }
                }
            };

            return timeSlot;
        };

        for (var i = 0; i < timeSlotsPerDay; ++i) {
            this.timeSlots[i] = makeTimeSlot(i);
        }
    }

    /** Gets the first Time Slot of the day. */
    public getFirst() {
        return this.timeSlots[0];
    }

    /** Gets the last Time Slot of the day. */
    public getLast() {
        return this.timeSlots[this.timeSlots.length - 1];
    }

    public count() {
        return this.timeSlots.length;
    }

    /** Gets a Time Slot by its chronological zero-based index.
     *
     * For example, timeSlots.atIndex(2) gets the third Time Slot of the
     * day. */
    public atIndex(index: number) {
        return this.timeSlots[index] || null;
    }

    /** Gets the Time Slot that contains the specified time. */
    public atTime(time: EccoTime) {
        const index = Math.floor(time.millisecondsSinceDayStart() * this.timeSlots.length / MILLISECONDS_PER_DAY);
        return this.atIndex(index);
    }

    /** Iterates over the Time Slots in chronological order, and calls
     * callback(timeSlot) for each Time Slot. */
    public forEach(callback: (timeSlot: TimeSlot) => void) {
        for (let i = 0; i < this.timeSlots.length; ++i) {
            callback(this.timeSlots[i]);
        }
    }
}

export interface TimeSlot {
    /** Gets the chronological zero-based index of this Time Slot.
     *
     * For example, if this is the first Time Slot of the day, returns 0. */
    getIndex(): number;

    /** Gets the time at which this Time Slot starts. */
    getStartTime(): EccoTime;

    /** Gets the time at which this Time Slot ends. */
    getEndTime(): EccoTime;

    /** Gets the Time Slot before this one, or null if this is the first
     * Time Slot of the day. */
    getPrevious(): TimeSlot;

    /** Gets the Time Slot after this one, or null if this is the last
     * Time Slot of the day. */
    getNext(): TimeSlot;

    /** Get another timeslot at the specified index */
    getAtIndex(slot: number): TimeSlot;

    /** Calls the specified callback for this Time Slot, and for every
     * subsequent Time Slot up to and including the specified end Time Slot. */
    forEachUntil(endTimeSlot: TimeSlot, callback: (timeSlot: TimeSlot) => void): void;
}

/** Converts an array of Worker data-transfer objects to an array of
 * Worker rich-domain objects. */
function workerDtosToDomain(repository: RotaRepository, sessionData: SessionData,
                            cmdRepository: CommandRepository,
                            rota: Rota, dtos: RotaResourceDto[]) {
    const workers: DemandResource[] = [];
    for (let i = 0; i < dtos.length; ++i) {
        const worker = new DemandResource(repository, sessionData, cmdRepository, rota, dtos[i]);
        workers.push(worker);
        rota.getAllActivities().push.apply(rota.getAllActivities(), worker.getAppointments());
    }
    return workers;
}

export interface TimeRange<T> {
    intersects(other: T): boolean;
    intersectsInterval(start: EccoDateTime, end: EccoDateTime | null): boolean;
}

export class CalendarEntry implements TimeRange<CalendarEntry> {
    constructor(public appointment: Activity, public $cell: any) {
        // $cell is $.JQuery
    }
    intersects(other: CalendarEntry): boolean {
        // if they are the same object, then there is no overlap
        if (this.appointment == other.appointment) {
            return false;
        }
        // if either have no end, then assume all day and therefore they overlap
        const myEnd = this.appointment.getEnd();
        const otherEnd = other.appointment.getEnd();
        if (myEnd == null || otherEnd == null) return true;
        // if either starts after the other ends then no overlap
        return !(
            this.appointment.getStart().laterThanOrEqual(otherEnd) ||
            other.appointment.getStart().laterThanOrEqual(myEnd)
        );
    }

    intersectsInterval(otherStart: EccoDateTime, otherEnd: EccoDateTime | null): boolean {
        // if either have no end, then assume all day and therefore they overlap
        const myEnd = this.appointment.getEnd();
        if (myEnd == null || otherEnd == null) return true;
        // if either starts after the other ends then no overlap
        return !(
            this.appointment.getStart().laterThanOrEqual(otherEnd) ||
            otherStart.laterThanOrEqual(myEnd)
        );
    }
}

export function getSlotsOnDate(period: Period, date: EccoDate) {
    const useSlot2 = period.getStartTimeSlot2() && period.getDate2()!.equals(date);
    // if we have a split and period starts on rota day, then we need to use startTimeSlot2 to get the part that's up to midnight today

    const startTimeSlot = useSlot2 ? period.getStartTimeSlot2()! : period.getStartTimeSlot();
    const endTimeSlot = useSlot2 ? period.getEndTimeSlot2() : period.getEndTimeSlot();
    const durationInTimeSlots = useSlot2 ? period.getDurationInTimeSlots2() : period.getDurationInTimeSlots();
    return {startTimeSlot, endTimeSlot, durationInTimeSlots};
}

export type AllocationOptions = {untilDate: EccoDate | null; days: CalendarDays[]};

/** A workerJob who may be assigned appointments on the rota. */
export class DemandResource {
    private readonly availability: ResourceAvailability;
    private readonly appointments: Activity[];
    private readonly calendar = new TimeSlotBasedCalendar<CalendarEntry>();

    public readonly changeEventBus = bus<WorkerChangeEvent>();
    public readonly activityAllocatedEventBus = bus<ActivityAllocatedEvent, WorkerChangeEvent>(
        this.changeEventBus
    );
    public readonly activityDeallocatedEventBus = bus<ActivityDeallocatedEvent, WorkerChangeEvent>(
        this.changeEventBus
    );

    public readonly addChangeEventHandler = this.changeEventBus.addHandler;
    public readonly removeChangeEventHandler = this.changeEventBus.removeHandler;

    public readonly addActivityAllocatedEventHandler = this.activityAllocatedEventBus.addHandler;
    public readonly removeActivityAllocatedEventHandler =
        this.activityAllocatedEventBus.removeHandler;

    public readonly addActivityDeallocatedEventHandler =
        this.activityDeallocatedEventBus.addHandler;
    public readonly removeActivityDeallocatedEventHandler =
        this.activityDeallocatedEventBus.removeHandler;

    /** Constructs a Worker Domain Object by converting it from the supplied
     * Data Transfer Object.
     *
     * This constructor is intended for internal use by the Rota Domain module.
     * It should not be called from other code as there will be no way to
     * persist the constructed DemandResource. */
    constructor(
        private readonly repository: RotaRepository,
        private readonly sessionData: SessionData,
        private readonly cmdRepository: CommandRepository,
        private readonly rota: Rota,
        private readonly dto: RotaResourceDto
    ) {
        this.availability = new ResourceAvailability(rota.getTimeSlots(), dto.availability);
        this.appointments = activityDtosToDomain(
            repository,
            sessionData,
            cmdRepository,
            rota,
            rota.getTimeSlots(),
            dto.appointments
        );
        this.appointments.forEach(activity => {
            activity.getAllocatedWorkerJobs().push(this);
        });
    }

    /** This is for use with useEffect() deps */
    public uniqueProps() {
        return `demand/${this.dto.resourceId}/${this.rota.getDto().startDate}-${
            this.rota.getDto().endDate
        }`;
    }

    public readonly toString = this.uniqueProps;

    /** Get a calendar which contains this worker's Activities (and possibly other appts later */
    public getCalendar() {
        return this.calendar;
    }

    /** Gets the rota this worker is associated with. */
    public getRota() {
        return this.rota;
    }

    /** Gets the unique workerJob ID. */
    public getResourceId() {
        return this.dto.resourceId;
    }

    /** Gets the worker's full name. */
    public getName() {
        return this.dto.name;
    }

    /** Gets the resources srId. */
    public getServiceRecipientId() {
        return this.dto.serviceRecipientId;
    }

    /** Gets the resources discriminator - as per database orm discriminator */
    public getServiceRecipientDiscriminator() {
        return this.dto.serviceRecipientDiscriminator;
    }

    public getCalendarId() {
        return this.dto.calendarId;
    }

    /** Gets the Time Slots during which the worker is available to be assigned
     * work. */
    public getAvailability() {
        return this.availability;
    }

    /** Gets the list of activity appointments that have been allocated to
     * this worker.
     *
     * Do not modify this array! */
    public getAppointments() {
        return this.appointments;
    }

    public getSkills() {
        return this.dto.skills;
    }

    public getContractedWeeklyHours() {
        return this.dto.contractedWeeklyHours;
    }

    public getProvidesAttributes() {
        return this.dto.providesAttributes.map(id =>
            this.sessionData.getListDefinitionEntryById(id).getName()
        );
    }

    /**
     *
     * @param persistent - FIXME HACK to allow shift breakdown to be shown (TODO: clean up by taking a better approach)
     * @param rescheduleTo - optionally reschedule to this datetime. NOTE: This causes activity to get mutated
     * @param allocationOptions - whether to allocate a single or recurring appointment
     */
    public allocateActivity(
        activity: Activity,
        persistent: boolean = true,
        rescheduleTo?: EccoDateTime,
        allocationOptions?: AllocationOptions,
        skipCheck = false
    ): Promise<void> {
        // For some reason we check if we've already got this one
        if (!skipCheck) {
            for (let i = 0; i < this.appointments.length; ++i) {
                if (this.appointments[i] === activity) {
                    return Promise.resolve();
                }
            }
        }

        let actionCommand: Command;
        const srId = activity.getServiceRecipientId();
        if (!srId) throw new Error("Trying to allocate an unsupported event");
        if (allocationOptions) {
            let partSchedule = {} as ServiceRecipientAppointmentScheduleCommandDto;
            const actionRecurringCommand = new AppointmentRecurringActionCommand(
                "allocate",
                Uuid.randomV4(),
                activity.getRef()!!,
                srId,

                this.rota.getResourceFilter(),
                this.rota.getDemandFilter(),
                partSchedule
            );
            partSchedule.applicableFromDate = activity.getStart().formatIso8601();
            partSchedule.days = asAddedRemoved([], allocationOptions.days);
            partSchedule.endDate = asDateChange(null, allocationOptions.untilDate);

            actionRecurringCommand.allocateResourceId = this.dto.resourceId;
            if (rescheduleTo) {
                const diffMins = rescheduleTo
                    .subtractDateTime(activity.getStart(), true)
                    .inMinutes();
                actionRecurringCommand.allocateRescheduledMins = diffMins;
            }
            actionCommand = actionRecurringCommand;
        } else {
            const actionSingleCommand = new AppointmentActionCommand(
                "allocate",
                Uuid.randomV4(),
                activity.getRef()!!,
                srId,
                this.rota.getResourceFilter(),
                this.rota.getDemandFilter()
            );
            actionSingleCommand.allocateResourceId = this.dto.resourceId;
            if (rescheduleTo) {
                actionSingleCommand.allocateRescheduledTo = rescheduleTo.formatIso8601();
            }
            actionCommand = actionSingleCommand;
        }

        const allocatePersist: Promise<HateoasResource | void> = persistent
            ? this.cmdRepository.sendCommand(actionCommand)
            : Promise.resolve();

        return allocatePersist.then(() => {
            // Modify the activity start point if needed
            if (rescheduleTo) {
                activity.mutateWithPeriodStarting(rescheduleTo); // So that it drops to right place and represents what changed on back end
            }
            this.appointments.push(activity);
            this.activityAllocatedEventBus.fire(new ActivityAllocatedEvent(this, activity));
        });
    }

    public deallocateActivity(activity: Activity, persist = true, allocateRecurring = false) {
        const activityIndex = this.appointments.indexOf(activity);

        if (activityIndex < 0) {
            return Promise.resolve();
        }

        let actionCommand: Command;
        const srId = activity.getServiceRecipientId();
        if (!srId) throw new Error("Trying to de-allocate an unsupported event");
        if (allocateRecurring) {
            let partSchedule = {} as ServiceRecipientAppointmentScheduleCommandDto;
            const actionRecurringCommand = new AppointmentRecurringActionCommand(
                "deallocate",
                Uuid.randomV4(),
                activity.getRef()!!,
                srId,
                this.rota.getResourceFilter(),
                this.rota.getDemandFilter(),
                partSchedule
            );
            partSchedule.applicableFromDate = activity.getStart().formatIso8601();
            // extract the day from the date
            const dayOfActivity = activity.getStart().getDayOfWeek(); // sun = 0
            partSchedule.days = {added: [dayOfActivity + 1]}; // sun = 1
            actionRecurringCommand.deallocateResourceId = this.dto.resourceId;
            actionCommand = actionRecurringCommand;
        } else {
            const actionSingleCommand = new AppointmentActionCommand(
                "deallocate",
                Uuid.randomV4(),
                activity.getRef()!!,
                srId,
                this.rota.getResourceFilter(),
                this.rota.getDemandFilter()
            );
            actionSingleCommand.deallocateResourceId = this.dto.resourceId;
            actionCommand = actionSingleCommand;
        }

        const deallocatePersist: Promise<HateoasResource | void> = persist
            ? this.cmdRepository.sendCommand(actionCommand)
            : Promise.resolve();

        return deallocatePersist.then(() => {
            const activityIndex = this.appointments.indexOf(activity);
            this.appointments.splice(activityIndex, 1);
            this.activityDeallocatedEventBus.fire(
                new ActivityDeallocatedEvent(this, activity, !persist)
            );
        });
    }

    public availableForPeriod(activity: Activity): boolean {
        const period = activity.getPeriod();
        const startTimeSlot = period.getStartTimeSlot();

        let workerHasAvailableTimeSlots = true;
        this.rota.getDate().equals(period.getDate()) &&
            startTimeSlot.forEachUntil(period.getEndTimeSlot(), (timeSlot: TimeSlot) => {
                if (
                    this.getCalendar().hasOverlappingActivityAtTimeSlot(
                        period.getDate(),
                        activity,
                        timeSlot.getIndex()
                    )
                ) {
                    workerHasAvailableTimeSlots = false;
                } else if (
                    !this.getAvailability().isAvailableDuringTimeSlot(period.getDate(), timeSlot)
                ) {
                    workerHasAvailableTimeSlots = false;
                }
            });
        const startTimeSlot2 = period.getStartTimeSlot2();
        startTimeSlot2 &&
            this.rota.getDate().equals(period.getDate2()!) &&
            startTimeSlot2.forEachUntil(period.getEndTimeSlot2()!, timeSlot => {
                if (
                    this.getCalendar().hasOverlappingActivityAtTimeSlot(
                        period.getDate2()!,
                        activity,
                        timeSlot.getIndex()
                    )
                ) {
                    workerHasAvailableTimeSlots = false;
                } else if (
                    !this.getAvailability().isAvailableDuringTimeSlot(period.getDate2()!, timeSlot)
                ) {
                    workerHasAvailableTimeSlots = false;
                }
            });

        return workerHasAvailableTimeSlots;
    }

    /** Find the earliest availability (within 8 hours prior) for this resource that fits this period */
    public earliestAvailableForPeriod(period: Period): {start: TimeSlot; end: TimeSlot} | null {
        console.debug(
            `period: start ${period.getStartTimeSlot().getIndex()}, end ${period
                .getEndTimeSlot()
                .getIndex()}`
        );
        // starting 8 hours before, find the first available slot
        let startIndex = period.getStartTimeSlot().getIndex() - TIME_SLOTS_PER_HOUR * 8;
        let index = startIndex > 0 ? startIndex : 0;
        const length =
            period.getEndTimeSlot().getIndex() + 1 - period.getStartTimeSlot().getIndex();
        let count = 0;
        let startSlot = this.rota.getTimeSlots().atIndex(index);
        let endIndex = period.getEndTimeSlot().getIndex() + TIME_SLOTS_PER_HOUR * 8;
        endIndex = endIndex <= TIME_SLOTS_PER_HOUR * 24 ? endIndex : TIME_SLOTS_PER_HOUR * 24;
        console.debug(`search: start ${index}, end ${endIndex}`);
        while (index < endIndex) {
            let timeSlot = this.rota.getTimeSlots().atIndex(index);

            if (
                this.getAvailability().isAvailableDuringTimeSlot(period.getDate(), timeSlot) &&
                !this.getCalendar().hasEntryAtTimeSlot(period.getDate(), timeSlot.getIndex())
            ) {
                if (count == 0) {
                    startSlot = timeSlot;
                }
                count++;
                if (count == length) {
                    return {start: startSlot, end: timeSlot};
                }
            } else {
                count = 0;
            }
            index++;
        }
        return null;
    }

    getSchedulesForAppts() {
        return Promise.all(
            uniqueByIdentity(this.getAppointments(), appt => appt.getScheduleId())
                .filter(appt => appt.getAgreementId() != null)
                .map(appt =>
                    this.getRota().repository.findScheduleById(
                        appt.getAgreementId(),
                        appt.getScheduleId()
                    )
                )
        );
    }
}

export interface ActivityChangeEvent {
    getActivity(): Activity;
}

export interface WorkerChangeEvent {
    getWorker(): DemandResource;
}

export class ActivityAllocatedEvent implements ActivityChangeEvent, WorkerChangeEvent {
    constructor(private worker: DemandResource, private activity: Activity) { }

    public getWorker() {
        return this.worker;
    }

    public getActivity() {
        return this.activity;
    }
}

export class ActivityDeallocatedEvent implements WorkerChangeEvent {
    constructor(private worker: DemandResource, private activity: Activity, private hideOnly = false) { }

    public getWorker() {
        return this.worker;
    }

    public getActivity() {
        return this.activity;
    }

    isHideOnly() {
        return this.hideOnly;
    }
}

export class ResourceAvailability {
    private availabilityByTimeSlotIndex: StringToObjectMap<boolean[]> = {};
    /** Sorted by start date, earliest first */
    private availableIntervals: Array<Interval>;

    /** Constructs a DemandResource Availability Domain Object by converting it from
     * the supplied Period Data Transfer Objects.
     *
     * This constructor is intended for internal use by the Rota Domain module.
     * It should not be called from other code as there will be no way to
     * persist the constructed WorkerAvailability. */
    constructor(timeSlots: TimeSlots, periodDtos: PeriodDto[]) {
        this.availableIntervals = periodDtos
            .sort( (a,b) => a.start.localeCompare(b.start))
            .map( dto => {
                const interval = new Interval( EccoDateTime.parseIso8601Utc(dto.start)!, EccoDateTime.parseIso8601Utc(dto.end)! );
                const period = new Period(timeSlots, interval.start, interval.end, false);
                { // scope here allows const to ensure we're not using stuff later that we shouldn't
                    const dateStr = period.getDate().formatIso8601();
                    this.ensureAvailByTimeSlotIndex(timeSlots, dateStr);
                    period.getStartTimeSlot().forEachUntil(period.getEndTimeSlot(), (timeSlot: TimeSlot) => {
                        this.availabilityByTimeSlotIndex[dateStr][timeSlot.getIndex()] = true;
                    });
                }
                // Process the split part if one exists and apply to the correct date
                let startTimeSlot2 = period.getStartTimeSlot2();
                if (startTimeSlot2 != null) {
                    const dateStr2 = period.getDate2()!.formatIso8601();
                    this.ensureAvailByTimeSlotIndex(timeSlots, dateStr2);
                    startTimeSlot2.forEachUntil(period.getEndTimeSlot2()!, (timeSlot: TimeSlot) => {
                        this.availabilityByTimeSlotIndex[dateStr2][timeSlot.getIndex()] = true;
                    });
                }
                return interval;
        });
    }

    private ensureAvailByTimeSlotIndex(timeSlots: TimeSlots, dateStr: string) {
        if (!this.availabilityByTimeSlotIndex[dateStr]) {
            const timeSlotsForDay: boolean[] = [];
            timeSlots.forEach(() => timeSlotsForDay.push(false));
            this.availabilityByTimeSlotIndex[dateStr] = timeSlotsForDay;
        }
    }

    public isAvailableDuringTimeSlot(date: EccoDate, timeSlot: TimeSlot) {
        const dateStr = date.formatIso8601();
        const arr = this.availabilityByTimeSlotIndex[dateStr];
        if (!arr) {
            this.availabilityByTimeSlotIndex[dateStr] = [];
        }
        return this.availabilityByTimeSlotIndex[dateStr][timeSlot.getIndex()];
    }

    public unallocatedSlots() {
        let sum = 0;
        for (let date in this.availabilityByTimeSlotIndex) {
            // noinspection JSUnfilteredForInLoop
            sum = this.availabilityByTimeSlotIndex[date].reduce((s,n) => s + (n ? 1 : 0), sum)
        }
        return sum;
    }

    public describe() {
        return this.availableIntervals.map( (interval) => interval.formatHoursMinsWithDay() ).join(", ");
    }

    public describeAvailabilityForDate(date: EccoDate): string {
        return this.availableIntervals
            .filter(interval => interval.start.toEccoDate().equals(date) )
             .map(interval => interval.formatHoursMins() ).join(", ");
    }

    /** Return first available interval that starts at or after instant */
    startingAtOrAfter(instant: EccoDateTime): Interval | undefined {
        return this.availableIntervals
            .find(interval => interval.start.laterThanOrEqual(instant));
    }

    /** Return last available interval that starts at or after instant */
    endingAtOrAfter(instant: EccoDateTime): Interval | undefined {
        return this.availableIntervals
            .find(interval => interval.end.laterThanOrEqual(instant));
    }
}

/** Converts an array of Activity data-transfer objects to an array of
 * Activity rich-domain objects. */
function activityDtosToDomain(repository: RotaRepository, sessionData: SessionData, cmdRepository: CommandRepository,
                              rota: Rota, timeSlots: TimeSlots, dtos: AppointmentDto[]) {
    const activities: Activity[] = [];
    dtos.forEach( (dto) => {
        activities.push(new Activity(repository, sessionData, cmdRepository, rota, timeSlots, dto));
    });
    return activities;
}

export function emitStartStopAroundPromise<T>(promise: Promise<T>, message?: string) {
    AjaxStartEvent.bus.fire(new AjaxStartEvent(message));
    return promise.then(r => {AjaxStopEvent.bus.fire(); return r}, err => {AjaxStopEvent.bus.fire(); throw err;});
}

export const REL_RUN_BREAKDOWN = "carerun-breakdown";
const REL_EDIT = "edit"
export const REL_DEMAND_SCHEDULE = "demand-schedule"
export const REL_DEMAND_SCHEDULE_TASK_HANDLES = "demand-schedule-task-handles";
export const REL_DEMAND_SCHEDULE_ADDITIONAL = "demand-schedule-additional"
export const REL_DEMAND_SCHEDULE_ADDITIONAL_EVENTS = "demand-schedule-additional-events";
export const REL_CALENDAR_EVENTS = "calendar-events";
export const calendarHrefToCalendarId = (href: string) => href.substr(href.indexOf("?") + "?calendarId=".length, 36)

/** An activity that may be assigned to a workerJob. Its dto maps to a RotaAppointmentViewModel.kt */
export class Activity implements HateoasResource {
    public static readonly sortStartAsc = (a: Activity, b: Activity) =>
        a.getStart().compare(b.getStart());

    private startDateTime: EccoDateTime;
    /** Should be null if dto.allDay is true */
    private endDateTime: EccoDateTime | null;
    private period: Period;
    public readonly links: LinkDto<string>[];
    private readonly previousPeriod: Period;
    private readonly allocatedWorkerJobs: DemandResource[] = [];

    public readonly changeEventBus = bus<ActivityChangeEvent>();
    public readonly activityAllocatedEventBus = bus<ActivityAllocatedEvent, ActivityChangeEvent>(
        this.changeEventBus
    );
    public readonly activityDeallocatedEventBus = bus<
        ActivityDeallocatedEvent,
        ActivityChangeEvent
    >(this.changeEventBus);

    public readonly addChangeEventHandler = this.changeEventBus.addHandler;
    public readonly removeChangeEventHandler = this.changeEventBus.removeHandler;

    public readonly addActivityAllocatedEventHandler = this.activityAllocatedEventBus.addHandler;
    public readonly removeActivityAllocatedEventHandler =
        this.activityAllocatedEventBus.removeHandler;

    public readonly addActivityDeallocatedEventHandler =
        this.activityDeallocatedEventBus.addHandler;
    public readonly removeActivityDeallocatedEventHandler =
        this.activityDeallocatedEventBus.removeHandler;

    /** Constructs an Activity Domain Object by converting it from the supplied
     * Data Transfer Object.
     *
     * This constructor is intended for internal use by the Rota Domain module.
     * It should not be called from other code as there will be no way to
     * persist the constructed Activity. */
    constructor(
        private readonly repository: RotaRepository,
        private readonly sessionData: SessionData,
        private readonly cmdRepository: CommandRepository,
        private readonly rota: Rota,
        private readonly timeSlots: TimeSlots,
        private readonly dto: AppointmentDto // | CalendarableEventDto // TODO: We should have CalendarableEventDto wrapped in a different domain class
    ) {
        this.links = dto.links;
        this.startDateTime = EccoDateTime.parseIso8601IgnoringTimezone(dto.start)!;
        this.endDateTime = EccoDateTime.parseIso8601IgnoringTimezone(dto.end);
        this.period = new Period(timeSlots, this.startDateTime, this.endDateTime, dto.allDay);
        this.previousPeriod = this.period;
    }

    mutateWithPeriodStarting(newStart: EccoDateTime) {
        const newEnd = this.dto.allDay
            ? null
            : newStart.addMilliseconds(
                  this.endDateTime!.subtractDateTime(this.startDateTime).inMilliseconds()
              );
        this.startDateTime = newStart;
        this.endDateTime = newEnd;
        this.period = new Period(this.timeSlots, newStart, newEnd, this.dto.allDay);
    }

    // TODO: Clean up this HACK for show breakdown
    private ctl: any = null;
    public setDodgyReference(ctl: any) {
        this.ctl = ctl;
    }
    public getDodgyCallback() {
        return this.ctl;
    }

    public getDto() {
        return this.dto;
    }

    public getPreviousPeriod() {
        return this.previousPeriod;
    }

    /** Gets the unique activity ID. */
    public getRef() {
        return this.dto.ref;
    }

    public isCareRun() {
        return Activity.isCareRun(this.dto);
    }
    public static isCareRun(resource: HateoasResource) {
        return !!getRelation(resource, REL_RUN_BREAKDOWN);
    }

    public getScheduleId() {
        return this.dto.scheduleId;
    }
    public getAgreementId() {
        return this.dto.agreementId;
    }

    /** Gets the recurrence title
     * For example: "Reading & Writing". */
    public getEvent() {
        return this.dto.title;
    }

    /** Gets a string describing the activity that will take place.
     * For example: "Reading & Writing". */
    public getEventTypeName() {
        return this.dto.eventTypeName;
    }

    /** Gets the unique ID of the client who will take part in this activity. */
    public getClientRef() {
        return this.dto.clientRef;
    }

    /** Gets the full name of the client who will take part in this activity. */
    public getServiceRecipientName() {
        return this.dto.serviceRecipientName;
    }

    /** Gets the serviceRecipientId who takes part in this activity. */
    public getServiceRecipientId() {
        return this.dto.serviceRecipientId;
    }

    public getLocation() {
        return this.dto.location;
    }

    /** Gets the serviceRecipientId who takes part in this activity. */
    public getServiceRecipientDiscriminator() {
        return this.dto.serviceRecipientDiscriminator;
    }

    public getRequiredAttributes() {
        return this.dto.requiredAttributes
            ? this.dto.requiredAttributes.map(id =>
                  this.sessionData.getListDefinitionEntryById(id).getName()
              )
            : [];
    }

    /** Get information as to how well a resource matches the requirements */
    public getMissingRequiredAttrs(resource: DemandResource) {
        const requiredAttrs = this.getRequiredAttributes();
        const providesAttributes = resource.getProvidesAttributes() || [];

        return diff(requiredAttrs, providesAttributes);
    }

    /** When this activity starts */
    public getStart() {
        return this.startDateTime;
    }

    /** When this activity ends - null if an all day event */
    public getEnd() {
        return this.endDateTime;
    }

    /** Gets the period of time during which this activity will take place. */
    public getPeriod() {
        return this.period;
    }

    public getTitle() {
        return this.dto.title;
    }

    public getRota() {
        return this.rota;
    }

    public getAllocatedWorkerJobs(): DemandResource[] {
        return this.allocatedWorkerJobs;
    }

    /** Identify this as a non-rota event */
    public isNonRota() {
        return this.dto.nonRotaEvent;
    }

    public isAllDay(): boolean {
        return this.dto.allDay;
    }

    /** Last command to touch this appointment - see Recurrence.java */
    public getUpdatedBy() {
        return this.dto.updatedByUri;
    }

    public appearsOnDate(date: EccoDate) {
        const start = this.getStart().toEccoDate();
        let end = this.getEnd()?.toEccoDate();

        // FUDGE the end date - see commit contents, and ticket
        // this prevents the same event appearing over multiple days on day view

        if (this.dto.allDay && end) {
            end = end.subtractDays(1);
        }
        return start.earlierThanOrEqual(date) && (end == null || date.earlierThanOrEqual(end));
    }

    /** If this activity has been dropped from the schedule. */
    public isDropped(): boolean {
        return this.dto.status == "DROPPED";
    }

    /** If this activity has been allocated . */
    public isAllocated(): boolean {
        return this.dto.status == "CONFIRMED";
    }

    /** Allocate a worker to this activity. This can be the first worker, or an additional worker where
     *  more than one worker is required */
    public allocateWorkerJob(
        worker: DemandResource,
        startTime?: EccoDateTime,
        allocationOptions?: AllocationOptions
    ): Promise<void> {
        return emitStartStopAroundPromise(
            worker
                .allocateActivity(this, true, startTime, allocationOptions)
                .then(() => this.addWorker(worker)),
            "Allocating..."
        );
    }

    /** Remove a worker from this activity. This may be one of several workers where an activity requires
     *  or allows more than one worker */
    public deallocateWorkerJob(
        workerJob: DemandResource,
        persist = true,
        allocateRecurring = false
    ) {
        return emitStartStopAroundPromise(
            workerJob
                .deallocateActivity(this, persist, allocateRecurring)
                .then(() => this.removeWorkerJob(workerJob, persist)),
            "Deallocating..."
        );
    }

    /** Drop (cancel) an activity. */
    public drop(serviceRecipientId: number, reasonId?: number): Promise<HateoasResource | void> {
        const actionCommand = new AppointmentActionCommand(
            "drop",
            Uuid.randomV4(),
            this.getRef()!!,
            serviceRecipientId,
            this.rota.getResourceFilter(),
            this.rota.getDemandFilter()
        );
        actionCommand.dropReasonId = reasonId;
        return this.cmdRepository.sendCommand(actionCommand);
    }

    /** Reinstate a dropped activity. */
    public reinstate(): Promise<HateoasResource | void> {
        const srId = this.getServiceRecipientId();
        if (!srId) throw new Error("Trying to allocate an unsupported event");
        const actionCommand = new AppointmentActionCommand(
            "reinstate",
            Uuid.randomV4(),
            this.getRef()!!,
            srId,
            this.rota.getResourceFilter(),
            this.rota.getDemandFilter()
        );
        return this.cmdRepository.sendCommand(actionCommand);
    }

    private addWorker(worker: DemandResource): void {
        if (this.allocatedWorkerJobs.indexOf(worker) == -1) {
            this.allocatedWorkerJobs.push(worker);
            this.activityAllocatedEventBus.fire(new ActivityAllocatedEvent(worker, this));
        } else {
            throw new Error("Resource already allocated");
        }
    }

    private removeWorkerJob(workerJob: DemandResource, persist: boolean = true): void {
        const existing = this.allocatedWorkerJobs.indexOf(workerJob);
        if (this.allocatedWorkerJobs.indexOf(workerJob) >= 0) {
            this.allocatedWorkerJobs.splice(existing, 1);
            this.activityDeallocatedEventBus.fire(
                new ActivityDeallocatedEvent(workerJob, this, !persist)
            );
        } else {
            console.error("we're calling removeWorker twice it would seem");
            // throw new Error("Worker not found in list");
        }
    }
}

export class Links {

    constructor(private links: LinkDto[]) {
    }

    public appointments(): LinkDto {
        for (let link in this.links) {
            if (this.links[link].rel == 'appointments') {
                return this.links[link];
            }
        }
        throw new Error("appointments link doesn't exist");
    }
}

/** Converts an array of agreement data-transfer objects to an array of
 * Agreement rich-domain objects. */
export function agreementDtosToDomain(dtos: AgreementDto[]) {
    const agreements: Agreement[] = [];
    for (let i = 0; i < dtos.length; ++i) {
        agreements.push(new Agreement(dtos[i]));
    }
    return agreements;
}

/** See rota-dto/AgreementDto */
export class Agreement {
    /** HATEOAS links */
    private links: Links;

    /** Agreed hours */
    agreedHours: number;

    /** The start date for this agreement */
    start: EccoDate;

    /** The end date for this agreement */
    end: EccoDate | null;

    constructor(private dto: AgreementDto) {
        this.links = new Links(dto.links);
        this.agreedHours = dto.agreedHours;
        this.start = EccoDate.parseIso8601(dto.start);
        this.end = EccoDate.parseIso8601(dto.end);
    }

    public getAgreementId() {
        return this.dto.agreementId;
    }

    public getContractId(): number | null {
        return this.dto.contractId as number | null;
    }
    public getContractName() {
        return this.dto.contractName ?? "";
    }

    public getAppointmentsHref() {
        return this.links.appointments().href;
    }

    public getDescription() {
        return `${this.dto.serviceRecipientName} : ${this.getDateDescription()}`;
    }

    public getDateDescription() {
        return `${this.start.formatShort()}${
            this.end ? " to " + this.end.formatShort() : " onwards"
        }`;
    }

    public getServiceRecipientId() {
        return this.dto.serviceRecipientId;
    }

    public setServiceRecipientName(name: string) {
        this.dto.serviceRecipientName = name;
    }

    /** The full name of the client/building/staff member who will take part in this activity. */
    public getServiceRecipientName() {
        return this.dto.serviceRecipientName;
    }

    public getParameters() {
        return this.dto.parameters;
    }

    public getParameterAsString(key: keyof AgreementParameters): string | undefined {
        return this.dto.parameters && this.dto.parameters[key]
            ? this.dto.parameters[key].toString()
            : undefined;
    }

    public getDemandSchedules(): DemandScheduleDto[] {
        return this.dto.demandSchedules || [];
    }
}

/** A period of time during the day. */
export class Period {
    private date: EccoDate;
    private startTimeSlot: TimeSlot;
    private readonly startPartialSlot: number;
    private endTimeSlot: TimeSlot;
    /** Start of part that runs up to midnight */
    private date2: EccoDate | null = null;
    private startTimeSlot2: TimeSlot | null = null;
    private endTimeSlot2: TimeSlot | null = null;

    /** Constructs a Period Domain Object by converting it from the supplied
     * Data Transfer Object.
     *
     * This constructor is intended for internal use by the Rota Domain module.
     * It should not be called from other code as there will be no way to
     * persist the constructed Period. */
    constructor(
        private timeSlots: TimeSlots,
        private start: EccoDateTime,
        private end: EccoDateTime | null,
        private allDay: boolean
    ) {
        this.startTimeSlot = timeSlots.atTime(start.toEccoTime());
        this.endTimeSlot = allDay
            ? timeSlots.getLast()
            : timeSlots.atTime(end!!.subtractMilliseconds(1).toEccoTime());
        this.date = start.toEccoDate();

        if (this.startTimeSlot.getIndex() > this.endTimeSlot.getIndex()) {
            this.startTimeSlot2 = this.startTimeSlot;
            this.startTimeSlot = timeSlots.atIndex(0);
            this.endTimeSlot2 = timeSlots.getLast();
            this.date2 = this.date;
            this.date = end?.toEccoDate()!;
        }

        this.startPartialSlot =
            (start.toEccoTime().subtract(this.startTimeSlot.getStartTime()).getMinutes() *
                TIME_SLOTS_PER_HOUR) /
            60;
    }

    /**
     * Get the date that these timeslots refer to
     */
    public getDate() {
        return this.date;
    }

    public getDate2() {
        return this.date2;
    }

    /** Gets the Time Slot at which the Period starts.
     *
     * The Period starts at the beginning of the specified Time Slot. */
    public getStartTimeSlot() {
        return this.startTimeSlot;
    }

    public getStartPartialSlot() {
        return this.startPartialSlot;
    }

    /** Gets the Time Slot at which the period ends.
     *
     * The Period ends at the end of the specified Time Slot. */
    public getEndTimeSlot() {
        return this.endTimeSlot;
    }

    /*
     * If this was a midnight spanning period, then this is the later segment up to midnight */
    public getStartTimeSlot2() {
        return this.startTimeSlot2;
    }

    public getEndTimeSlot2() {
        console.assert(this.startTimeSlot2 != null);
        return this.endTimeSlot2;
    }

    /** Gets the duration of the period as a count of Time Slots. */
    public getDurationInTimeSlots() {
        return this.endTimeSlot.getIndex() - this.startTimeSlot.getIndex() + 1;
    }
    public getDurationInTimeSlots2() {
        return this.endTimeSlot2!.getIndex() - this.startTimeSlot2!.getIndex() + 1;
    }
}

export class Allocation {
    public selected: boolean;

    constructor(private dto: AllocationDto) {
        this.selected = this.isFullMatch()
    }

    resourceName() { return this.dto.allocatedSupply ? this.dto.allocatedSupply.target : "-" }

    getDto() { return this.dto; }

    demandName() {
        return this.dto.demand.target.serviceRecipientName;
    }

    isFullMatch() {
        // It is possible that the allocated time doesn't match given how Optaplanner works, so we have no real
        // way to avoid this at the moment (poss can with Drools engine).
        // TODO: reject if not within worker availability
        // TODO: somehow tag if worker is already assigned (fixedAllocations)
        // What we can do is show if the skills match or not
        return this.hardRequirementsMatch(); // && softRequirementsMatch();
    }

    getMatchingResourceAvailability() {
        console.log(this.dto.allocatedSupply.availability);
        const {start, end} = this.dto.demand.time;
        const matches = this.dto.allocatedSupply.availability.filter(avail => avail.start.localeCompare(start) <= 0 && avail.end.localeCompare(end) >= 0);
        return matches.length > 0 ? intervalFromDto(matches[0]) : null;
    }

    private hardRequirementsMatch() {
        const hard = this.dto.demand.requirements;
        const nonMatch = hard.some(it =>
            !this.dto.allocatedSupply.provides.some(    p =>
                p.id == it.id
            )
        );
        return !nonMatch;
    }
}

export class RotaProposal {
    private allocations: Allocation[];

    constructor(private dto: RotaProposalDto) {
        this.allocations = dto.variableAllocations
            .sort( (a, b) => a.demand.time.start.localeCompare(b.demand.time.start))
            .map(a => new Allocation(a));
    }

    getAllocations() {
        return this.allocations;
    }

    public getDto() { return this.dto; }
}


export class Availability {
    /** This will act as an adapter for the DTO */
    constructor(private dto: AvailabilityDto) {
    }

    public displayName() {
        return this.dto.displayName;
    }

    public startTime() {
        return EccoDateTime.parseIso8601IgnoringTimezone(this.dto.dtStart);
    }

    public endTime() {
        return EccoDateTime.parseIso8601IgnoringTimezone(this.dto.dtEnd);
    }

    public clearAvailability() {
        this.dto.available = [];
    }

    public availables() {
        return this.dto.available;
    }

    public toDto() {
        return this.dto;
    }
}


// *************
// CRON SCHEDULE
// *************
// BaseHistoryItemControl shows target date in history or the schedule as-is (could show next due)
// ForwardPlan (newer timeline version) which (ideally) shows a repeated entry at the times of day
// calendarEventCard shows care tasks next due - which is the latest dateTime from the server
// smartStepAnalysis shows the dashboard 'checks due' which (ideally) shows a repeated entry at the times of day

export function expandByScheduleTime<T extends SupportAction>(actions: T[]): T[] {
    let dueActions: T[] = [];
    actions.forEach(a => {
        if (a.targetSchedule) {
            const nextDue = EccoDateTime.parseIso8601(a.targetDateTime);
            const schedule = ScheduleData.fromTargetSchedule(nextDue, a.targetSchedule);
            const timesForDay =
                nextDue && ScheduleData.getDateTimesOn(nextDue.toEccoDate(), schedule.getTimes());
            timesForDay?.forEach((t, i) => {
                if (i == 0) {
                    a.targetDateTime = t.formatIso8601();
                    dueActions.push(a)
                } else {
                    const clone: T = {
                        ...a,
                        targetDateTime: t
                    }
                    dueActions.push(clone)
                }
            })
        }
    })
    return dueActions
        .filter(a => !!EccoDateTime.parseIso8601(a.targetDateTime))
        .sort((a, b) =>
            EccoDateTime.parseIso8601(a.targetDateTime)!.compare(
                EccoDateTime.parseIso8601Utc(b.targetDateTime)!
            )
        );
}

const toTitleCase = (str:string) => str.charAt(0).toUpperCase() + str.slice(1);

/**
 * Data Transformation from ui recurring patterns to cron-like representation used for commands.
 * Used by appointment schedules and target date schedules.
 */
export class ScheduleData {
    // NB schedule format - see BaseGoalUpdateCommandViewModel.java and Schedule.java
    // NB This class only deals with a single date for the moment
    private static schedulePattern =
        /(start:([\d-]{10}))? ?(days:([\w*,]+))? ?(times:((\d\d:\d\d)(,\d\d:\d\d)*)?)? ?(end:([\d-]{10}))? ?(week:([\d]{1}))?/;

    /**
     * Use ScheduleData directly which is safer than re-constructing a 'schedule' string
     */
    public static fromDemandSchedule(dto: DemandScheduleDto): ScheduleData {
        let days: string | null = null;
        let end: string | null;
        const start = dto.start;
        const time = dto.time != null ? EccoTime.parseIso8601(dto.time).formatHoursMinutes() : null;
        end = dto.end;
        if (dto.calendarDays) {
            const daysOrderedDemandSchedule = ["sun", "mon", "tues", "wed", "thurs", "fri", "sat"];
            const daysConv = daysOrderedDemandSchedule.filter(
                (day, i) => dto.calendarDays.indexOf(i + 1) > -1
            );
            days = daysConv.join(",");
        }
        return new ScheduleData(
            null,
            start,
            days,
            [time!],
            end,
            undefined,
            undefined,
            dto.intervalType
        );
    }

    /**
     * @param nextDueDateTime the next due datetime for this schedule
     * @param start the start date of the schedule
     * @param days the days of the week to repeat on
     * @param times the times of the day
     * @param end the end date of the schedule
     * @param nonStandardCron whether to include non-standard cron aspects, like 'week' / 'intervalType'
     * @param week the week interval to repeat on - such as 2 for every 2 weeks
     */
    constructor(
        private readonly nextDueDateTime: EccoDateTime | null = null,
        private readonly start: string | null = null,
        private readonly days: string | null = null,
        private readonly times: string[] = [],
        private readonly end: string | null = null,

        // non-cron elements of week and intervalType (eg "WK" | "MTH" | "QTR" | "BI" | "YR";)
        private readonly nonStandardCron: boolean = false,
        private readonly week: string | null = null,
        private readonly intervalType: IntervalType | null = null
    ) {}

    /**
     * Create an instance from the schedule, with an optional targetDate to give it some context (ie next due)
     * @param targetDateTime a datetime from which to work out the schedule
     * @param schedule the schedule details
     */
    public static fromTargetSchedule(
        targetDateTime: EccoDateTime | null | undefined,
        schedule?: string | null
    ): ScheduleData {
        if (schedule) {
            const matches = ScheduleData.schedulePattern.exec(schedule);
            return new ScheduleData(
                targetDateTime,
                matches!![2],
                matches!![4],
                matches!![6]?.split(","),
                matches!![10],
                true,
                matches!![12]
            );
        }
        return new ScheduleData(targetDateTime);
    }

    public getStart() {
        return EccoDate.parseIso8601(this.start);
    }

    public getNextDue(): EccoDateTime | null {
        return this.nextDueDateTime;
    }

    public getDay(day: string): boolean {
        return this.days ? this.days.indexOf(day) > -1 : false;
    }

    public getDays(): string | null {
        return this.days;
    }

    getDaysMap() {
        return {
            mon: this.getDay("mon"),
            tues: this.getDay("tues"),
            wed: this.getDay("wed"),
            thurs: this.getDay("thurs"),
            fri: this.getDay("fri"),
            sat: this.getDay("sat"),
            sun: this.getDay("sun")
        };
    }

    public isNonStandard() {
        return this.nonStandardCron;
    }

    public getWeek(): number | null {
        return this.week ? Number.parseInt(this.week) : null;
    }

    public getIntervalType(): IntervalType | null {
        return this.intervalType;
    }

    public getTimes(): EccoTime[] {
        return this.times.map(str => EccoTime.parseIso8601IgnoringTimezone(str + ":00.0000"));
    }

    public static getDateTimesOn(date: EccoDate, times: EccoTime[]): EccoDateTime[] {
        return times.map(t => {
            return EccoDateTime.fromDateAndTime(date, t);
        });
    }

    public getEnd(): EccoDate | null {
        return EccoDate.parseIso8601(this.end);
    }

    // See also extractScheduleFromState for the reverse
    // NB schedule format - see BaseGoalUpdateCommandViewModel.java
    public getScheduleForCmd(): string | null {
        const start = this.getStart();
        const times = this.getTimes();
        const end = this.getEnd();
        const week = this.getWeek();
        let days = this.getDays() && this.getDays()!.split(",").length == 7 ? "*" : this.getDays();

        if (start || days || times.length || end || week) {
            days = days || "*"; // allow selecting no days
            return (
                (start ? "start:" + this.start + " " : "") +
                "days:" +
                days +
                (times.length ? " times:" + times.map(t => t.formatHoursMinutes()).join(",") : "") +
                (end ? " end:" + this.end : "") +
                (week ? " week:" + this.week : "")
            );
        }
        return null;
    }

    /**
     * Mimics the defunct AgreementOfAppointmentsController#demandScheduleToViewModel's setScheduleDescription(getDescription(entity))
     function scheduleDescription(entity: DemandScheduleDto) {
            let desc = "";
            // NB Need some boolean to indicate ad-hoc - see DemandResourceAssembler:21
            if (entity.start == entity.end) {
                desc = desc + "ad-hoc - "
                    .append(getFormattedDate(entity.getStart(), getAuthenticatedUser().getLocale()));
            }
            else {
                s.append(getFormattedDate(entity.getStart(), getAuthenticatedUser().getLocale()))
                    .append(" - ").append(getFormattedDate(entity.getEnd(), getAuthenticatedUser().getLocale()))
                    .append(": ");
                int marker = s.length();
                for (int i = 1; i <= 7; i++) {
                    if (entity.getDays().isCalendarDay(i)) {
                        if (s.length() > marker) {
                            s.append(',');
                        }
                        s.append(symbols.getShortWeekdays()[i]);
                    }
                }
            }
            s.append(" @ ").append(DateTimeFormat.forStyle("-S").print(entity.getTime()));\
            s.append(" every ").append(entity.getIntervalFrequency()).append(" week(s)");
            return s.toString();
          }
     *
     */
    asText(withStartEnd = false): string {
        const anyChanges = this.getScheduleForCmd();
        if (!anyChanges) {
            return "";
        }

        const dueText =
            (this.getNextDue() ? ` due ${this.getNextDue()!!.formatShort()}` : "") +
            (this.getNextDue() && this.times ? " :" : "") +
            (this.times ? " " + this.times : "");
        const weekNum = this.getWeek() || 1;
        let intervalTypeStr: string = "";
        switch (this.getIntervalType()) {
            case "MTH":
                intervalTypeStr = "month";
                break;
            case "QTR":
                intervalTypeStr = "quarter";
                break;
            case "BI":
                intervalTypeStr = "bi-annual";
                break;
            case "YR":
                intervalTypeStr = "year";
                break;
            default:
                intervalTypeStr = "day";
                break;
        }
        const futureText =
            (!this.days ||
            this.days == "*" ||
            this.days.length == "sun,mon,tues,wed,thurs,fri,sat".length
                ? `every ${intervalTypeStr}`
                : this.days == "mon,tues,wed,thurs,fri"
                ? "weekdays"
                : "on " + this.days.split(",").map(toTitleCase).join(", ")) +
            (weekNum == 1 ? "" : " every " + weekNum.toString() + " weeks") +
            (withStartEnd && this.getStart() ? " from " + this.getStart()!!.formatShort() : "") +
            (withStartEnd && this.getEnd() ? " until " + this.getEnd()!!.formatShort() : "");

        return dueText + (futureText ? " (" + futureText + ")" : "");
    }
}
