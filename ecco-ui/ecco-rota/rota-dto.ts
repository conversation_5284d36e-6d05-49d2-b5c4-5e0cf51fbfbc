import {IntervalDto, <PERSON>Dto} from "@eccosolutions/ecco-common";
import {AppointmentDto, DemandScheduleDto} from "ecco-dto";

/** Data-transfer object representing the rota for a given day.
 *
 * This interface must match the Java class com.ecco.rota.webApi.dto.Rota. */
export interface RotaDto {
    /** The resourceFilter used to fetch this rota */
    resourceFilter: string;

    /** The demandFilter used to fetch this rota */
    demandFilter: string;

    /** The date of this rota.
     *
     * This is an ISO 8601 format string ("YYYY-MM-DD"). */
    startDate: string;

    /** This is an ISO 8601 format string ("YYYY-MM-DD").  endDate is inclusive (i.e. endDate==startDate for single day) */
    endDate: string;

    /** The workers who may be assigned activities on the rota. */
    resources: RotaResourceDto[];

    /** Activities that have not yet been allocated to any worker. */
    demandAppointments: AppointmentDto[];
}

/** As persisted in database servicerecipients table */
export type ServiceRecipientDiscriminator = "bldg" | "rfrl" | "wrkr" | "inc" | "repair" | "mvoid";

/** Data-transfer object representing a worker who may be assigned appointments
 * on the rota.
 *
 * This interface must match the Java class com.ecco.rota.webApi.dto.RotaResourceViewModel. */
export interface RotaResourceDto {
    /** The unique ID of the associated WorkerJob or BookableResource. */
    resourceId: number;

    /** The worker/resource's calendarId */
    calendarId: string;

    /** The worker/resource's full name. */
    name: string;

    /** The id of the resource's service recipient */
    serviceRecipientId: number;

    serviceRecipientDiscriminator: ServiceRecipientDiscriminator;

    contractedWeeklyHours: number;

    /** The worker/resource's skills as text */
    skills: string;

    /** The list def ids for the attributes provided by this resource */
    providesAttributes: number[];

    /** The times of day when the worker/resource is available to be assigned activities.
     *
     * For a run / shift, this will include the id of the allocated resource.
     *
     * This is an array of zero or more Periods when the worker is available.
     * For the format of each Period, see the documentation for PeriodDto. */
    availability: AvailabilityPeriodDto[];

    /** Activities that have been assigned to this worker/resource. */
    appointments: AppointmentDto[];
}


export interface AgreementParameters {
    hoursPerWeek: number;
}

/** Data-transfer object representing an agreement of appointments .
 *
 * This interface must match the Java class com.ecco.rota.webApi.dto.AgreementResource.
 * Typically this is transferred on loading into the Agreement domain class - see RotaAjaxRepository.
 * Also see rota-domain.activityDtosToDomain
 */
export interface AgreementDto {
    /** HATEOAS links */
    links: LinkDto[];

    /** The id of this entity */
    agreementId: number;

    /** Id of contract which may be a block one spanning across service or be a personal */
    contractId?: number;

    /** Descriptive name of contract */
    contractName?: string;

    /** Agreed hours */
    agreedHours: number;

    /** The id of the recipient of these services */
    serviceRecipientId: number;

    /** The full name of the recipient of these services */
    // client-side ONlY
    serviceRecipientName: string;

    /** The start date for this agreement */
    start: string;

    /** The end date for this agreement */
    end: string;

    /** Things like hours per week, but poss other things too */
    parameters?: AgreementParameters;

    /** (client-side only used in reporting) Optionally populated array of appointments that relate to this agreement */
    demandSchedules?: DemandScheduleDto[];
}

/**
 * A handle for the directly assigned task to the schedule
 * See also CareTask.
 */
export interface DemandScheduleTaskHandleDto {
    taskInstanceId: string;
}

/**
 * A task directly assigned to the schedule (regardless of targetDate).
 * See also CareTask.
 */
export interface DemandScheduleTaskDto {
    taskDefId: number;
    taskDefName: string;
    taskInstanceId: string;
    taskText: string | null;
    taskDescription: string | null;
}

/** Data-transfer object representing a period of time during a day.
 *
 * The period is represented as a start time in hours and minutes, and an
 * end time in hours and minutes.
 *
 * The start time is inclusive and the end time is inclusive.
 *
 * Note that this object is serialized as an array, hence the dubious property
 * names. */
export interface PeriodDto {
    /** The start date time. ISO format */
    start: string;

    /** The end date time. ISO format */
    end: string;
}

export interface AvailabilityPeriodDto extends PeriodDto {
    allocatedResourceName?: string
}

export interface OptaPlannerScore {
    /** This is probably always zero for our use case */
    initScore: number
    /** zero or less.  Zero means that we solved all the hard constraints */
    hardScore: number
    /** zero or less.  Zero means that we solved all the soft constraints */
    softScore: number
    /** Is this solved */
    feasible: boolean
}


export interface DemandDto {
    target: AppointmentDto
    time: IntervalDto
    requirements: {id: number}[] // listdef ids
}

export interface SupplyDto {
    id: number // workerId
    target: string // worker name for now
    availability: IntervalDto[],
    provides: {id: number}[] // listdef ids
}

export interface AllocationDto {
    demand: DemandDto
    allocatedSupply: SupplyDto
}

export interface RotaProposalDto {
    score: OptaPlannerScore
    fixedAllocations: AllocationDto[]
    variableAllocations: AllocationDto[]
}