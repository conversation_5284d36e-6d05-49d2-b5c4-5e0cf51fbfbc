import {AsyncSessionD<PERSON>, handleL<PERSON>y, LoadingSpinner, useServicesContext} from "ecco-components";
import {EccoTheme} from "ecco-components-core";
import {App} from "ecco-offline";
import * as React from "react";
import {useEffect} from "react";
import * as ReactDOM from "react-dom";
import {BrowserRouter} from "react-router-dom";
import {initApiClient, sessionDataFn} from "./services"; // Should be pointing at build/offline/router
import {ServicesContextProvider} from "./ServicesContextProvider";
import * as serviceWorker from "./serviceWorkerRegistration";
import CareAppBar from "ecco-components/care/CareAppBar";
import {PrintableAppointments} from "ecco-components/calendar/PrintableAppointments";
import {getUserSessionManager, OfflineSyncStatusEvent} from "ecco-offline-data";
import {ApiClient, isOffline} from "ecco-dto";
import {Typography} from "@eccosolutions/ecco-mui";

interface PageProps {
    readonly appPath: string;
}

const Page = ({appPath}: PageProps) => {
    const {sessionData} = useServicesContext();

    const username = sessionData.getDto().username;
    useEffect(() => {
        loginIfNeeded(username);
    }, [username]);

    const CareApp = () => (
        /* NB called by the app, which doesn't include CarePage (which now sets the menu) so we keep the menus CareAppBar also */
        <CareAppBar>
            {sessionData.isEnabled("app.care.staffAppIsPrintableAppts") ? (
                <PrintableAppointments
                    calendarId={sessionData.getDto().calendarId}
                    subjectDisplayName={sessionData.getDto().individualUserSummary.displayName}
                />
            ) : (
                /* <CarePage/> is currently in ecco-offline */
                <Typography>TODO CareApp to resolve at this address</Typography>
            )}
        </CareAppBar>
    );

    console.debug(`BrowserRouter basename=${appPath}`);
    return handleLazy(
        <BrowserRouter basename={appPath}>
            {!sessionData ? (
                <LoadingSpinner />
            ) : !sessionStorage["forceApp"] && // if found, load <App>
              // TODO: Allow sysadmin to override feature toggles locally for testing app
              //   i.e. store in localStorage as JSON and merge after we've read global from web API
              sessionData.hasRoleCarer() ? (
                <CareApp />
            ) : (
                /* for development, it can be handy to just change this to <CareApp/> */
                <App />
            )}
        </BrowserRouter>
    );
};

interface AppProps {
    readonly appPath: string;
    readonly apiClient: ApiClient;
}

const StaffApp = ({appPath, apiClient}: AppProps) => (
    <AsyncSessionData promiseFn={sessionDataFn}>
        <ServicesContextProvider client={apiClient}>
            <EccoTheme prefix="app">
                <Page appPath={appPath} />
            </EccoTheme>
        </ServicesContextProvider>
    </AsyncSessionData>
);

function addToCache(cacheName: string, urls: string[]) {
    return window.caches.open(cacheName).then(cache => cache.addAll(urls));
}

export interface AppOptions {
    readonly appPath: string;
    readonly remoteRoot: string;
}

export function start({appPath, remoteRoot}: AppOptions): void {
    serviceWorker.register(); // Note only active if NODE_ENV is production

    // Ensure we cache what we loaded - could iterate through document <script> tags for this
    window.addEventListener("load", () => {
        // ...determine the list of related URLs for the current page...
        addToCache("public", [
            `${appPath}jquery-3.6.0.min.js`,
            `${appPath}jquery-ui-1.10.3.custom.min.js`,
            `${appPath}jquery-datepicker.js`,
            `${appPath}lazy.min.js`,
            `${appPath}bootstrap.min.js`
        ]);
        addToCache("images", [
            `${appPath}favicon.ico`,
            `${appPath}images/datepicker.png`,
            `${appPath}images/flag_red24.png`,
            `${appPath}images/link.png`,
            `${appPath}images/plus24.png`,
            `${appPath}images/star24.png`,
            `${appPath}images/tick.png`
        ]);
        addToCache("api", [
            // `${remoteRoot}/api/config/user/`, // DONT as app is not logged in at this point
            // `${remoteRoot}/api/config/global` // ditto
        ]);

        OfflineSyncStatusEvent.initNavigator(navigator);
    });

    const apiClient = initApiClient(remoteRoot);

    ReactDOM.render(
        <StaffApp appPath={appPath} apiClient={apiClient} />,
        document.getElementById("appbar") as HTMLElement
    );
}

function loginIfNeeded(username: string) {
    const userSessionManager = getUserSessionManager();
    if (userSessionManager) {
        // TODO: We need to avoid this path if using ecco login
        userSessionManager.loginIfNeeded(username, undefined, isOffline());
    }
}
