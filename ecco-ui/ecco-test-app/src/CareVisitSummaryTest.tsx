import {CareVisitRoot} from "ecco-components/care/CareVisitRoot";
import {CareVisitSummaryCard} from "ecco-components/care/CareVisitSummaryCard";
import * as React from "react";
import {CareTask} from "ecco-components/care/careVisitState";
import {GoalUpdateCommand} from "ecco-commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {EvidenceGroup, SmartStepStatus} from "ecco-dto";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {ROTA_VISIT} from "ecco-components/care/careVisitReducer";

const taskUpdateCmd = (
    task: CareTask,
    outcomeId: number | null,
    plannedDateTime: EccoDateTime | null
) => {
    return new GoalUpdateCommand(
        "update",
        Uuid.randomV4(),
        Uuid.randomV4(),
        200001,
        ROTA_VISIT,
        task.actionDefId,
        EvidenceGroup.needs,
        Uuid.parse(task.taskInstanceId)
    )
        .changeStatus(null, SmartStepStatus.AchievedAndStillRelevant)
        .setForceStatusChange()
        .changeStatusChangeReason(task.listDefId, outcomeId)
        .setPlannedDateTime(plannedDateTime);
};

export function CareVisitSummaryTest() {
    return (
        <CareVisitRoot
            careVisitInitState={{
                serviceRecipientId: 200013, // testdomcare srId
                stopOptionsOn: true,
                displayName: "Grace Fields",
                visitDateTime: "12th Dec, 8:00pm",
                resources: {links: []},
                commentForm: {},
                address: "Blah, CB3 3BB",
                tasks: [],
                tasksLoad: () =>
                    Promise.resolve([
                        {
                            taskText: "Clean bedroom",
                            taskTime: null,
                            taskDescription: null,
                            taskInstanceId: Uuid.randomV4().toString(),
                            actionDefId: 1,
                            listDefId: null
                        },
                        {
                            taskText: "Administer medication (overdue)",
                            taskDescription: null,
                            taskTime: EccoDateTime.nowLocalTime().subtractMinutes(20),
                            taskInstanceId: Uuid.randomV4().toString(),
                            actionDefId: 1,
                            listDefId: null
                        },
                        {
                            taskText: "Administer medication",
                            taskDescription: null,
                            taskTime: EccoDateTime.nowLocalTime().addMinutes(10),
                            taskInstanceId: Uuid.randomV4().toString(),
                            actionDefId: 1,
                            listDefId: null
                        },
                        {
                            taskText: "Administer medication (later)",
                            taskDescription: null,
                            taskTime: EccoDateTime.nowLocalTime().addMinutes(610),
                            taskInstanceId: Uuid.randomV4().toString(),
                            actionDefId: 1,
                            listDefId: null
                        }
                    ]),
                loneWorkerUpdate: () => Promise.reject(),
                taskUpdateCmd
            }}
        >
            <CareVisitSummaryCard />
        </CareVisitRoot>
    );
}
