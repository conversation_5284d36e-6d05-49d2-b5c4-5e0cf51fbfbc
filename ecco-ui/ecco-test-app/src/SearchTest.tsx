import {PersonSearchBar} from "ecco-components/inputs/PersonSearchBar";
import {PersonSearchCriteriaDto} from "ecco-dto";
import * as React from "react";
import {useState} from "react";

export default function SearchTest() {
    const [criteria, setCriteria] = useState<PersonSearchCriteriaDto | null>({lastName: "bloggs", firstName: null});
    return <>
        <PersonSearchBar
            resultName="client"
            criteria={criteria!!}
            onTrigger={setCriteria}
            searchActive={false}
        />
        <div>{JSON.stringify(criteria)}</div>
    </>;
}