import {useState} from "react";
import * as React from "react";
import {But<PERSON>} from "@eccosolutions/ecco-mui";
import {TaskForm} from "ecco-components";

export const TaskTest = () => {
    const [show, setShow] = useState(false);

    return <>
        <div>login to testdomcare...</div>
        <div>then we look for task: 200052-25 (instant bd1af36e-7037-490e-8ad3-65fd3fa6ecaa)</div>
        <Button onClick={() => setShow(true)}>modal</Button>
        {show && <TaskForm taskHandle={"200052-25-bd1af36e-7037-490e-8ad3-65fd3fa6ecaa"} setShow={setShow} show={show}/>}
    </>;
};