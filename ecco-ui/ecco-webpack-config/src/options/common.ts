import {resolve as resolvePath} from "node:path";
import {mapObjectValues} from "@softwareventures/object";
import {readDotEnv} from "./client-env";
import {readPackageJson} from "./package-json";
import {hasProperty} from "unknown";
import {WebpackEnv} from "../webpack/env";
import {WebpackArguments} from "../webpack/arguments";

export interface CommonOptions {
    readonly packageName?: string | undefined;
    readonly paths?: CommonPaths | undefined;
    readonly sourceMap?: boolean | undefined;
    readonly profile?: boolean | undefined;
    readonly externals?: Externals | undefined;
    readonly clientEnv?: Readonly<Record<string, string>> | undefined;
    readonly tests?:
        | {
              readonly setup?:
                  | undefined
                  | {
                        readonly beforeTestEnvironment?: ReadonlyArray<string> | undefined;
                        readonly afterTestEnvironment?: ReadonlyArray<string> | undefined;
                    };
              readonly moduleMap?: Readonly<Record<string, string>> | undefined;
          }
        | undefined;
    readonly visualize?: boolean | undefined;
}

export interface CommonPaths {
    readonly root: string;
    readonly packageJson?: string | undefined;
    readonly dotEnv?: string | undefined;
    readonly src?: string | undefined;
    readonly tests?: string | undefined;
    readonly build?: string | undefined;
}

export type Externals = Record<string, External>;

export type External = string | string[];

export interface ResolvedCommonOptions<TMode extends string> {
    readonly mode: TMode;
    readonly packageName: string | undefined;
    readonly paths: ResolvedCommonPaths;
    readonly sourceMap: boolean;
    readonly profile: boolean;
    readonly externals: Externals;
    readonly clientEnv: Readonly<Record<string, string>>;
    readonly tests: {
        readonly setup: {
            readonly beforeTestEnvironment: ReadonlyArray<string>;
            readonly afterTestEnvironment: ReadonlyArray<string>;
        };
        readonly moduleMap: Readonly<Record<string, string>>;
    };
    readonly visualize: boolean;
}

export interface ResolvedCommonPaths {
    readonly root: string;
    readonly packageJson: string;
    readonly src: string;
    readonly tests: string;
    readonly build: string;
}

export interface CommonOptionOverrides<TMode extends string | undefined> {
    readonly mode: TMode;
    readonly paths?: CommonPathOverrides | undefined;
    readonly sourceMap?: boolean | undefined;
    readonly profile?: boolean | undefined;
    readonly clientEnv?: Readonly<Record<string, string>> | undefined;
    readonly visualize?: boolean | undefined;
}

export interface CommonPathOverrides {
    readonly root?: string | undefined;
    readonly packageJson?: string | undefined;
    readonly dotEnv?: string | undefined;
    readonly src?: string | undefined;
    readonly tests?: string | undefined;
    readonly build?: string | undefined;
}

export function resolveCommonOptionOverrides(
    webpackEnv: WebpackEnv,
    args: WebpackArguments
): CommonOptionOverrides<"production" | "development" | undefined> {
    return {
        mode: args.mode === "none" ? "development" : args.mode,
        paths: {
            root: typeof webpackEnv["rootPath"] === "string" ? webpackEnv["rootPath"] : undefined,
            packageJson:
                typeof webpackEnv["packageJsonPath"] === "string"
                    ? webpackEnv["packageJsonPath"]
                    : undefined,
            dotEnv:
                typeof webpackEnv["dotEnvPath"] == "string" ? webpackEnv["dotEnvPath"] : undefined,
            src: typeof webpackEnv["srcPath"] === "string" ? webpackEnv["srcPath"] : undefined,
            build: typeof webpackEnv["buildPath"] === "string" ? webpackEnv["buildPath"] : undefined
        },
        sourceMap: webpackEnv["sourceMap"] != null ? Boolean(webpackEnv["sourceMap"]) : undefined,
        profile: webpackEnv["profile"] != null ? Boolean(webpackEnv["profile"]) : undefined,
        clientEnv:
            typeof webpackEnv["clientEnv"] == "object"
                ? Object.fromEntries(
                      Object.entries(webpackEnv["clientEnv"])
                          .filter(([_, value]) => typeof value !== "object")
                          .map(([key, value]) => [key, String(value)] as const)
                  )
                : {},
        visualize: webpackEnv["visualize"] != null ? Boolean(webpackEnv["visualize"]) : undefined
    };
}

export async function resolveCommonOptions<TMode extends "production" | "development" | "test">(
    options: CommonOptions,
    overrides: CommonOptionOverrides<TMode>
): Promise<ResolvedCommonOptions<TMode>> {
    const mode = overrides.mode;
    const rootPath = resolvePath(overrides.paths?.root ?? options.paths?.root ?? ".");
    const packageJsonPath = resolvePath(
        rootPath,
        overrides.paths?.packageJson ?? options.paths?.packageJson ?? "package.json"
    );
    const dotEnvPath = resolvePath(
        rootPath,
        overrides.paths?.dotEnv ?? options.paths?.dotEnv ?? ".env"
    );
    const srcPath = resolvePath(rootPath, overrides.paths?.src ?? options.paths?.src ?? "src");
    const testsPath = resolvePath(
        rootPath,
        overrides.paths?.tests ?? options.paths?.tests ?? "tests"
    );
    const buildPath = resolvePath(
        rootPath,
        overrides.paths?.build ?? options.paths?.build ?? "build"
    );
    const packageJson = await readPackageJson(packageJsonPath);
    const packageName =
        hasProperty(packageJson, "name") && typeof packageJson.name === "string"
            ? packageJson.name
            : undefined;
    const sourceMap = overrides.sourceMap ?? options.sourceMap ?? false;
    const profile = overrides.profile ?? options.profile ?? false;
    const externals = options.externals ?? {};
    const clientEnv = {
        NODE_ENV: mode,
        ...options.clientEnv,
        ...(await readDotEnv({
            mode,
            paths: {
                dotEnv: dotEnvPath
            }
        })),
        ...overrides.clientEnv
    };
    const tests = {
        setup: {
            beforeTestEnvironment:
                options.tests?.setup?.beforeTestEnvironment?.map(path =>
                    resolvePathIfExplicitlyRelative(rootPath, path)
                ) ?? [],
            afterTestEnvironment:
                options.tests?.setup?.afterTestEnvironment?.map(path =>
                    resolvePathIfExplicitlyRelative(rootPath, path)
                ) ?? []
        },
        moduleMap: mapObjectValues(options.tests?.moduleMap ?? {}, path =>
            resolvePathIfExplicitlyRelative(rootPath, path)
        )
    };
    const visualize = overrides.visualize ?? options.visualize ?? false;

    return {
        mode,
        packageName,
        paths: {
            root: rootPath,
            packageJson: packageJsonPath,
            src: srcPath,
            tests: testsPath,
            build: buildPath
        },
        sourceMap,
        profile,
        externals,
        clientEnv,
        tests,
        visualize
    };
}

function resolvePathIfExplicitlyRelative(relativeTo: string, p: string): string {
    if (p.match(/^\.\.?[/\\]/u)) {
        return resolvePath(relativeTo, p);
    } else {
        return p;
    }
}
