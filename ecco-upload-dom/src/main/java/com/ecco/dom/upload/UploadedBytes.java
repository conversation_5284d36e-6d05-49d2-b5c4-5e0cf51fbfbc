package com.ecco.dom.upload;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Lob;
import javax.persistence.Table;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;

// followed http://stackoverflow.com/questions/2112615/persisting-large-files-with-hibernate-annotations
@Entity
@Table(name="uploadbytes")
public class UploadedBytes extends AbstractLongKeyedEntity {

    private static final long serialVersionUID = 1L;

    // @Basic allows a property to be lazy - Column is used in schema generation, http://stackoverflow.com/questions/1383229/java-persistence-jpa-column-vs-basic
    // however, its safer to specify a lazy association than have a lazy property
    @Basic(fetch = FetchType.LAZY)
    @Column
    @Lob
    private byte[] bytes;


    public UploadedBytes() {
        // for frameworks
    }

    public UploadedBytes(byte[] bytes) {
        this.bytes = bytes;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public void setBytes(byte[] bytes) {
        this.bytes = bytes;
    }
}
