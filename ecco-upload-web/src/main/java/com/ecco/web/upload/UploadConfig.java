package com.ecco.web.upload;

import com.ecco.dom.upload.UploadedFile;
import com.ecco.upload.dao.UploadedFileRepository;

import java.io.IOException;
import java.util.List;

/**
 * @param <T> the attachment entity type
 */
public interface UploadConfig<T extends UploadedFile> {

    T constructAttachment() throws IOException;

    UploadedFileRepository<T> getRepository();

    List<? extends UploadedFile> findFiles();
}
