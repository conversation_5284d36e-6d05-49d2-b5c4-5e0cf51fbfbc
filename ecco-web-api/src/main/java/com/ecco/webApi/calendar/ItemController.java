package com.ecco.webApi.calendar;

import com.ecco.calendar.CombinedEntry;
import com.ecco.infrastructure.web.WebSlice;
import com.ecco.service.EventService;
import com.ecco.calendar.core.CalendarService;

import com.ecco.calendar.core.webapi.EventResource;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;

@PreAuthorize("hasRole('ROLE_SYSADMIN')")
@WebSlice("api")
@RestController
public class ItemController {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    private final CalendarService calendarService;
    private final EventService eventService;

    @Autowired
    public ItemController(CalendarService calendarService, EventService eventService) {
        this.calendarService = calendarService;
        this.eventService = eventService;
    }

    /**
     * @param calendarUid - UID for the calendar
     * @param startDate - optional (but required if endDate is going to be specified)
     * @param endDate - optional. If startDate is provided but no endDate, will default to 1 year from startDate
     * @return
     */
    // FIXME: Note that /api/calendar/ is mapped to calendar api servlet... which we want to move across I think
    @RequestMapping(value = "/cal/{calendarUid}/items/", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Iterable<EventResource> findCalendarItems(
            @PathVariable String calendarUid,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE) LocalDate startDate,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE) LocalDate endDate) {

        Collection<CombinedEntry> entries = eventService.getCalendars(Collections.singletonList(calendarUid), startDate.toDateTimeAtStartOfDay(), endDate.toDateTimeAtStartOfDay());

        // don't wire in the ServiceRecipientEventDecorator since we probably don't use this method anyway - its sysadmin
        return new EventResourceAssembler(null, EventController.class).toResourceFromEntries(Arrays.asList(calendarUid), entries);
    }

    // TODO: Change from GET to PUT - decide on whether existing calendarId being a userUid is the way to go!
    @RequestMapping(value = "/items/{itemId}/move/from/{srcCalendarId}/to/{dstCalendarId}",
            method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void moveItem(
            @PathVariable String itemId,
            @PathVariable String srcCalendarId,
            @PathVariable String dstCalendarId) {

        calendarService.moveEntryToAnotherCalendar(srcCalendarId, dstCalendarId, itemId);
    }

    @RequestMapping(value = "/items/move/from/{srcCalendarId}/to/{dstCalendarId}",
            method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void moveItems(
            @PathVariable String srcCalendarId,
            @PathVariable String dstCalendarId,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE) LocalDate startDate,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE) LocalDate endDate) {
        Iterable<EventResource> entries = findCalendarItems(srcCalendarId, startDate, endDate);
        for (EventResource entry : entries) {
            calendarService.moveEntryToAnotherCalendar(srcCalendarId, dstCalendarId, entry.getEntryId());
        }
    }
}
