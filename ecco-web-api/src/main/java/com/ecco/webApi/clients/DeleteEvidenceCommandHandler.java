package com.ecco.webApi.clients;

import com.ecco.dao.EvidenceSupportWorkRepository;
import com.ecco.dao.ThreatWorkRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.EvidenceFormWork;
import com.ecco.dom.EvidenceSupportWork;
import com.ecco.dom.EvidenceThreatWork;
import com.ecco.dom.commands.DeleteEvidenceCommand;
import com.ecco.dom.commands.DeleteRequestEvidenceCommand;
import com.ecco.groupsupport.repositories.GroupSupportActivityInvolvementRepository;
import com.ecco.evidence.EvidenceTask;
import com.ecco.service.TaskDefinitionService;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.ecco.webApi.viewModels.DeleteEvidenceCommandViewModel;
import com.ecco.webApi.viewModels.DeleteRequestEvidenceCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;


@Component
public class DeleteEvidenceCommandHandler extends ServiceRecipientCommandHandler<
        DeleteEvidenceCommandViewModel, DeleteEvidenceCommand, @NonNull DeleteEvidenceParams> {

    @NonNull
    private final ServiceRecipientCommandRepository serviceRecipientCommandRepository;

    @PersistenceContext
    private EntityManager entityManager;

    private final EvidenceSupportWorkRepository supportWorkRepository;
    private final ThreatWorkRepository riskWorkRepository;
    private final GroupSupportActivityInvolvementRepository groupSupportRepository;
    private final TaskDefinitionService taskDefinitionService;

    @Autowired
    public DeleteEvidenceCommandHandler(ObjectMapper objectMapper,
                                        @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                        @NonNull EvidenceSupportWorkRepository supportWorkRepository,
                                        @NonNull ThreatWorkRepository riskWorkRepository,
                                        @NonNull GroupSupportActivityInvolvementRepository groupSupportRepository,
                                        @NonNull TaskDefinitionService taskDefinitionService) {
        super(objectMapper, serviceRecipientCommandRepository, DeleteEvidenceCommandViewModel.class);
        this.serviceRecipientCommandRepository = serviceRecipientCommandRepository;
        this.supportWorkRepository = supportWorkRepository;
        this.riskWorkRepository = riskWorkRepository;
        this.groupSupportRepository = groupSupportRepository;
        this.taskDefinitionService = taskDefinitionService;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull DeleteEvidenceParams params,
                                           @NonNull DeleteEvidenceCommandViewModel viewModel) {

        DeleteRequestEvidenceCommand deleteRequestCmd = (DeleteRequestEvidenceCommand) this.serviceRecipientCommandRepository.findOneByUuid(viewModel.requestDeletionUuid);
        Assert.notNull(deleteRequestCmd, "delete request not found");

        DeleteRequestEvidenceCommandViewModel deleteRequestVm = getDeleteRequestViewModel(deleteRequestCmd);
        Assert.state(deleteRequestVm.workUuid.equals(params.workUuid), "delete request is not for the same work entity: " + params.workUuid);
        if (deleteRequestVm.revoke) {
            throw new IllegalArgumentException("Cannot delete using a delete-request marked as revoke: " + deleteRequestCmd.getUuid());
        }

        // ensure the latest command entered is not a revoke
        List<DeleteRequestEvidenceCommand> cmdVms = serviceRecipientCommandRepository.findDeleteEvidenceRequestsByServiceRecipientId(viewModel.serviceRecipientId);
        DeleteRequestEvidenceCommandViewModel latestDeleteRequestCmd = cmdVms.stream()
                .map(this::getDeleteRequestViewModel)
                .filter(vm -> vm.workUuid.equals(params.workUuid))
                .reduce((prev, curr) -> prev.timestamp.isAfter(curr.timestamp) ? prev : curr).get();
        if (latestDeleteRequestCmd.revoke) {
            throw new IllegalArgumentException("latest delete request is marked as revoke: " + latestDeleteRequestCmd.uuid);
        }

        EvidenceTask task = EvidenceTask.fromTaskName(viewModel.evidenceTask);
        var type = taskDefinitionService.getTaskType(task);
        if (taskDefinitionService.isThreatBased(type)) {
            this.deleteRisk(params.workUuid);
        } else if (taskDefinitionService.isQuestionnaireBased(type)) {
            this.deleteSupport(params.workUuid);
        } else if (taskDefinitionService.isSupportSmartStepBased(type)) {
            this.deleteSupport(params.workUuid);
        } else if (taskDefinitionService.isCustomFormBased(type)) {
            this.deleteForm(params.workUuid);
        } else {
            throw new IllegalArgumentException("not implemented delete handler for discriminator: " + viewModel.evidenceTask);
        }

        return null;
    }

    private void deleteSupport(UUID workUuid) {
        // EntityNotFound a possible exception if the entity has already been deleted
        EvidenceSupportWork item = entityManager.getReference(EvidenceSupportWork.class, workUuid);
        groupSupportRepository.clearSupportWorkLink(item.getId());
        entityManager.remove(item);
    }

    private void deleteForm(UUID workUuid) {
        // EntityNotFound a possible exception if the entity has already been deleted
        EvidenceFormWork item = entityManager.getReference(EvidenceFormWork.class, workUuid);
        entityManager.remove(item);
    }

    private void deleteRisk(UUID workUuid) {
        // EntityNotFound a possible exception if the entity has already been deleted
        EvidenceThreatWork item = entityManager.getReference(EvidenceThreatWork.class, workUuid);
        supportWorkRepository.clearThreatWorkLink(workUuid);
        entityManager.remove(item);
    }

    private DeleteRequestEvidenceCommandViewModel getDeleteRequestViewModel(DeleteRequestEvidenceCommand deleteRequest) {
        DeleteRequestEvidenceCommandViewModel deleteRequestVm;
        try {
            deleteRequestVm = objectMapper.readValue(deleteRequest.getBody(), DeleteRequestEvidenceCommandViewModel.class);
            Assert.state(deleteRequestVm.valid(), "Nonnull requirements not met");
        } catch (IOException e) {
            throw new RuntimeException("Cannot convert to view model on delete-request: " + deleteRequest.getUuid(), e);
        }
        return deleteRequestVm;
    }

    @NonNull
    @Override
    protected DeleteEvidenceCommand createCommand(Serializable targetId, @NonNull DeleteEvidenceParams params,
                                                  @NonNull String requestBody,
                                                  @NonNull DeleteEvidenceCommandViewModel viewModel,
                                                  long userId) {
        Assert.state(params.serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");

        // TODO safer to overwrite the viewModel's jsonViewModel with the object as-at now?
        // If that is the case, perhaps we don't need the viewModel to even have evidenceGroup or evidenceTas

        return new DeleteEvidenceCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody,
                params.serviceRecipientId, viewModel.evidenceGroup, viewModel.evidenceTask);
    }

}
