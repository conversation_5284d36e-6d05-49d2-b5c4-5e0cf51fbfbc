package com.ecco.webApi.contacts;

import com.ecco.dom.Company;
import com.ecco.webApi.contacts.address.AddressToViewModel;
import java.util.function.Function;
import com.ecco.dom.contacts.Address;

import org.jspecify.annotations.Nullable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class CompanyToViewModel implements Function<Company, CompanyViewModel> {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    private final AddressToViewModel addressToViewModel = new AddressToViewModel();

    @Nullable
    @Override
    public CompanyViewModel apply(@Nullable Company input) {
        if (input == null) {
            throw new NullPointerException("input Company must not be null");
        }

        CompanyViewModel viewModel = new CompanyViewModel();
        viewModel.companyId = input.getId();
        viewModel.companyName = input.getCompanyName();
        viewModel.phoneNumber = input.getPhoneNumber();
        viewModel.calendarId = input.getCalendarId();

        Address clientAddress = input.getAddress();
        if (clientAddress != null) {
            viewModel.address = addressToViewModel.apply(clientAddress);
        }

        return viewModel;
    }
}
