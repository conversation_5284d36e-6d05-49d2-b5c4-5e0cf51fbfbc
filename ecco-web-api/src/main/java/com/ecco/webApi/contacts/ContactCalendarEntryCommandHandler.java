package com.ecco.webApi.contacts;

import com.ecco.webApi.calendar.EventController;
import com.ecco.dao.commands.ContactsCommandRepository;
import com.ecco.dom.commands.ContactCalendarEntryCommand;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.calendar.CalendarEntryCommandViewModel;
import com.ecco.webApi.calendar.ContactCalendarEntryCommandViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;
import java.net.URI;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static java.util.Collections.singletonList;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * @see CalendarEntryCommandHandlerSupport
*/
@Component
public class ContactCalendarEntryCommandHandler extends ContactBaseCommandHandler<
        ContactCalendarEntryCommandViewModel, ContactCalendarEntryCommand, @NonNull ContactCommandParams> {

    @NonNull
    private final CalendarEntryCommandHandlerSupport calendarEntryCommandSupportHandler;
    @NonNull
    private final EntityUriMapper entityUriMapper;

    @PersistenceContext
    EntityManager entityManager;

    @Autowired
    public ContactCalendarEntryCommandHandler(@NonNull CalendarEntryCommandHandlerSupport calendarEntryCommandSupportHandler,
                                              @NonNull ObjectMapper objectMapper,
                                              @NonNull ContactsCommandRepository contactsCommandRepository,
                                              @NonNull EntityUriMapper entityUriMapper) {
        super(objectMapper, contactsCommandRepository, ContactCalendarEntryCommandViewModel.class);
        this.calendarEntryCommandSupportHandler = calendarEntryCommandSupportHandler;
        this.entityUriMapper = entityUriMapper;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth,
                                           @NonNull ContactCommandParams params,
                                           @NonNull ContactCalendarEntryCommandViewModel viewModelWrapper) {

        CalendarEntryCommandViewModel viewModel = viewModelWrapper.calendarEntryViewModel;
        switch (viewModel.operation) {
            case BaseCommandViewModel.OPERATION_ADD:
                // params.contactId is the current user when not on a client file - see calendar.ts
                return this.link(this.calendarEntryCommandSupportHandler.addCalendarEntry(null, params.contactId, viewModel));

            case BaseCommandViewModel.OPERATION_UPDATE:
                URI uri = this.entityUriMapper.uriForEntity(ContactCalendarEntryCommandViewModel.class.getSimpleName(), viewModelWrapper.uuid);
                return this.link(this.calendarEntryCommandSupportHandler.updateCalendarEntry(uri, viewModel));

            case BaseCommandViewModel.OPERATION_REMOVE:
                this.calendarEntryCommandSupportHandler.removeCalendarEntry(viewModel);
                break;

            default:
                throw new IllegalArgumentException("invalid operation: " + viewModel.operation);
        }
        return null;
    }

    private CommandResult link(String entryUuid) {
        return CommandResult.ofLink(linkToApi(methodOn(EventController.class).findEventsByIds(singletonList(entryUuid))).withSelfRel());
    }

    @NonNull
    @Override
    protected ContactCalendarEntryCommand persistCommand(@NonNull ContactCalendarEntryCommand command) {
        var cmdSaved = super.persistCommand(command);
        entityManager.flush();
        return cmdSaved;
    }

    @NonNull
    @Override
    protected ContactCalendarEntryCommand createCommand(Serializable targetId, @NonNull ContactCommandParams params,
                                                        @NonNull String requestBody,
                                                        @NonNull ContactCalendarEntryCommandViewModel viewModel,
                                                        long userId) {
        Assert.state(params.contactId == viewModel.contactId, "contactId in body must match URI");

        return new ContactCalendarEntryCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, params.contactId);
    }

}
