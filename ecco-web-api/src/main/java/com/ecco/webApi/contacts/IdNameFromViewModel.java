package com.ecco.webApi.contacts;

import com.ecco.service.IdNameService;
import com.ecco.dom.IdName;

import org.jspecify.annotations.NonNull;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import org.jspecify.annotations.Nullable;
import java.util.List;
import java.util.function.Function;

public final class IdNameFromViewModel<T extends IdName> implements Function<String, T> {
    @NonNull
    private final Class<T> type;

    @NonNull
    private final IdNameService idNameService;

    IdNameFromViewModel(@NonNull Class<T> type, @NonNull IdNameService idNameService) {
        this.type = type;
        this.idNameService = idNameService;
    }

    @Nullable
    @Override
    public T apply(@Nullable String input) {
        if (!StringUtils.hasText(input)) {
            return null;
        }

        List<IdName> idNames = idNameService.getIdNames(type);

        for (IdName idName : idNames) {
            Assert.notNull(idName.getName()); // config error - field should be non null
            if (type.isAssignableFrom(idName.getClass())
                    && idName.getName().equalsIgnoreCase(input)) {
                @SuppressWarnings("unchecked")
                T result = (T) idName;

                return result;
            }
        }

        throw new IllegalArgumentException("Can't find " + type.getCanonicalName() + " matching key: " + input);
    }
}
