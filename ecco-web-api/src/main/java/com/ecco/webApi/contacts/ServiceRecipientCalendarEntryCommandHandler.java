package com.ecco.webApi.contacts;

import com.ecco.webApi.calendar.EventController;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.commands.ServiceRecipientCalendarEntryCommand;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.calendar.CalendarEntryCommandViewModel;
import com.ecco.webApi.calendar.ServiceRecipientCalendarEntryCommandViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;
import java.net.URI;
import java.util.Collections;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * Creates/Update a calendar entry.
 * This mimics the current approach which updates events using a POST in EntityController through EventTypeDefinition.
 * Updates are done on CustomEventImpl with an AOP in place to then update the underlying calendar.
 * Therefore we maintain consistency in both calendar systems using this approach for now.
*/
@Component
public class ServiceRecipientCalendarEntryCommandHandler extends ServiceRecipientCommandHandler<
        ServiceRecipientCalendarEntryCommandViewModel, ServiceRecipientCalendarEntryCommand, @NonNull ServiceRecipientCalendarEntryParams> {

    // serviceRecipientRepository repo only exists for the purpose of contactId - which is a good thing to store in svcrec_commands
    // although we could store the longer uuid of calendarId, or even in a separate calendar command table.
    @NonNull
    private final ServiceRecipientRepository serviceRecipientRepository;

    @PersistenceContext
    EntityManager entityManager;

    @NonNull
    private final CalendarEntryCommandHandlerSupport calendarEntryCommandSupportHandler;

    @NonNull
    private final EntityUriMapper entityUriMapper;

    @Autowired
    public ServiceRecipientCalendarEntryCommandHandler(@NonNull CalendarEntryCommandHandlerSupport calendarEntryCommandSupportHandler,
                                                       @NonNull ObjectMapper objectMapper,
                                                       @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                       @NonNull ServiceRecipientRepository serviceRecipientRepository,
                                                       @NonNull EntityUriMapper entityUriMapper) {
        super(objectMapper, serviceRecipientCommandRepository, ServiceRecipientCalendarEntryCommandViewModel.class);
        this.calendarEntryCommandSupportHandler = calendarEntryCommandSupportHandler;
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.entityUriMapper = entityUriMapper;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth,
                                           @NonNull ServiceRecipientCalendarEntryParams params,
                                           @NonNull ServiceRecipientCalendarEntryCommandViewModel viewModelWrapper) {

        var contactId = getContactId(viewModelWrapper.serviceRecipientId);
        CalendarEntryCommandViewModel viewModel = viewModelWrapper.calendarEntryViewModel;
        switch (viewModel.operation) {
            case BaseCommandViewModel.OPERATION_ADD:
                return this.link(this.calendarEntryCommandSupportHandler.addCalendarEntry(viewModelWrapper.serviceRecipientId, contactId, viewModel));

            case BaseCommandViewModel.OPERATION_UPDATE:
                URI uri = this.entityUriMapper.uriForEntity(ServiceRecipientCalendarEntryCommandViewModel.class.getSimpleName(), viewModelWrapper.uuid);
                return this.link(this.calendarEntryCommandSupportHandler.updateCalendarEntry(uri, viewModel));

            case BaseCommandViewModel.OPERATION_REMOVE:
                this.calendarEntryCommandSupportHandler.removeCalendarEntry(viewModel);
                break;

            default:
                throw new IllegalArgumentException("invalid operation: " + viewModel.operation);
        }
        return null;
    }

    @NonNull
    @Override
    protected ServiceRecipientCalendarEntryCommand persistCommand(@NonNull ServiceRecipientCalendarEntryCommand command) {
        var cmdSaved = super.persistCommand(command);
        entityManager.flush();
        return cmdSaved;
    }

    private CommandResult link(String entryUuid) {
        return CommandResult.ofLink(linkToApi(methodOn(EventController.class).findEventsByIds(Collections.singletonList(entryUuid))).withSelfRel());
    }

    @NonNull
    @Override
    protected ServiceRecipientCalendarEntryCommand createCommand(Serializable targetId, @NonNull ServiceRecipientCalendarEntryParams params,
                                                                 @NonNull String requestBody,
                                                                 @NonNull ServiceRecipientCalendarEntryCommandViewModel viewModel,
                                                                 long userId) {
        Assert.state(params.serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");

        var contactId = getContactId(params.serviceRecipientId);
        return new ServiceRecipientCalendarEntryCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody,
                params.serviceRecipientId, contactId);
    }

    private long getContactId(int serviceRecipientId) {
        return serviceRecipientRepository.findOne(serviceRecipientId).getContact().getId();
    }

}
