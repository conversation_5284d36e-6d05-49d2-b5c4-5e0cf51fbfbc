package com.ecco.webApi.contacts;

import com.ecco.servicerecipient.ServiceRecipientSummary;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

/**
 * Data-transfer object representing a workerJob.
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(NON_NULL)
public class WorkerJobViewModel {
    Integer id;
    Long workerId;
    String code;
    Integer workerJobId;
    Integer contractedWeeklyHours;
    LocalDate startDate;
    LocalDate endDate;
    ServiceRecipientSummary serviceRecipient;
}
