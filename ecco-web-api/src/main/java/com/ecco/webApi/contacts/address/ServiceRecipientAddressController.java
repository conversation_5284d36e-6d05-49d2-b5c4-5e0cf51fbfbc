package com.ecco.webApi.contacts.address;

import com.ecco.contacts.dao.AddressHistoryRepository;
import com.ecco.dom.contacts.AddressHistory;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

import static java.util.stream.Collectors.toList;

@RestController
@RequiredArgsConstructor
public class ServiceRecipientAddressController extends BaseWebApiController {

    private final ServiceRecipientAddressLocationChangeCommandHandler serviceRecipientAddressLocationChangeCommandHandler;
    private final AddressHistoryRepository addressHistoryRepository;

    private final AddressHistoryToViewModel addressHistoryToViewModel = new AddressHistoryToViewModel();

    // retain the '-recipient' for historical
    @PostJson(value={"/service-recipients/{serviceRecipientId}/address-location/command/",
            "/service-recipient/{serviceRecipientId}/address-location/command/"})
    @ResponseStatus(HttpStatus.OK)
    public Result addressLocationCommand(
            @NonNull Authentication authentication,
            @PathVariable Integer serviceRecipientId,
            @NonNull @RequestBody String requestBody) throws IOException {
        Assert.notNull(serviceRecipientId, "Must specify srId");

        return serviceRecipientAddressLocationChangeCommandHandler.handleCommand(authentication, serviceRecipientId, requestBody);
    }

    @GetJson("/service-recipients/{serviceRecipientId}/address-location/history/")
    public List<AddressHistoryViewModel> findAll(@PathVariable int serviceRecipientId) {
        List<AddressHistory> values = addressHistoryRepository.findByServiceRecipientIdOrderByValidFromDesc(serviceRecipientId);
        return values.stream().map(addressHistoryToViewModel).collect(toList());
    }

    @GetJson("/contacts/{contactId}/address-location/history/")
    public List<AddressHistoryViewModel> findAll(@PathVariable long contactId) {
        List<AddressHistory> values = addressHistoryRepository.findByContactIdOrderByValidFromDesc(contactId);
        return values.stream().map(addressHistoryToViewModel).collect(toList());
    }

}
