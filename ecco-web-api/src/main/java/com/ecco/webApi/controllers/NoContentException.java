package com.ecco.webApi.controllers;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception thrown when the client makes a Web API request for an entity that
 * does not exist but the result could.
 */
@ResponseStatus(HttpStatus.NO_CONTENT)
public final class NoContentException extends RuntimeException {
    private Object id;

    public NoContentException(Object id) {
        super("Entity not found: id = " + id);
        this.id = id;
    }

    public Object getId() {
        return id;
    }
}
