package com.ecco.webApi.controllers;

import lombok.RequiredArgsConstructor;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.ecco.config.dom.Template;
import com.ecco.config.dom.Template.RootEntity;
import com.ecco.config.dom.Template.TemplateSourceType;
import com.ecco.config.repositories.TemplateRepository;
import com.ecco.webApi.viewModels.Result;

/**
 * For ecco use only
 */
@PreAuthorize("hasRole('ROLE_SYSADMIN')")
@RestController
@RequiredArgsConstructor
public class TemplateController extends BaseWebApiController {

    private final TemplateRepository templateRepository;


    @GetJson("/templates/")
    public Iterable<Template> listTemplates() {

        return templateRepository.findAll();
    }


    @SuppressWarnings("MVCPathVariableInspection")
    @GetMapping(value = "/templates/{rootEntity}/{sourceFormat}/{templateName}/",
            produces = MediaType.APPLICATION_XML_VALUE)
    public String findTemplateByName(@PathVariable String templateName)  {

        Template template = templateRepository.findById(templateName).orElseThrow(NullPointerException::new);
        return template.getBody();
    }


    @PostMapping("/templates/{rootEntity}/{sourceFormat}/{templateName}/")
    public Result updateTemplateByName(
            @PathVariable RootEntity rootEntity,
            @PathVariable String sourceFormat,
            @PathVariable String templateName,
            @RequestBody String body) {

        Template template = templateRepository.findById(templateName).orElse(null);

        if (template == null) {
            template = new Template(templateName, TemplateSourceType.valueOf(sourceFormat.toUpperCase()), rootEntity);
        }
        template.setBody(body);
        Template saved = templateRepository.save(template);
        return new Result(saved.getName());
    }
}
