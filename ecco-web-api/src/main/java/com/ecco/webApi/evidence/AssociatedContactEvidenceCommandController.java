package com.ecco.webApi.evidence;

import com.ecco.dom.commands.EvidenceAssociatedContactCommand;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;


@RestController
public class AssociatedContactEvidenceCommandController extends BaseWebApiController {

    @NonNull
    private final EvidenceAssociatedContactCommandHandler associatedContactCommandHandler;
    @NonNull
    private final GoalCommandExtractCommandViewModelJson extractJsonBody;

    @Autowired
    public AssociatedContactEvidenceCommandController(EvidenceAssociatedContactCommandHandler associatedContactHandler,
                                                      @Qualifier("SvcRecExtractJson") @NonNull GoalCommandExtractCommandViewModelJson extractJsonBody) {
        this.associatedContactCommandHandler = associatedContactHandler;
        this.extractJsonBody = extractJsonBody;
    }

    /** Used by lone-working to 'start' and 'stop' on a work item
     */
    @PostJson("/service-recipients/{serviceRecipientId}/evidence/{evidenceGroupKey}/{taskName}/contact/{contactId}/")
    public Result associateContact(
            AssociatedContactParams params,
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return this.associatedContactCommandHandler.handleCommand(authentication, params, requestBody);
    }


    /**
     * @return the JSON as a string array (using the same JSON we stored going in)
     */
    @GetJson("/service-recipients/{serviceRecipientId}/commands/evidence/{evidenceGroupKey}/contact/")
    public String findCommandsByServiceRecipientAndEvidenceGroup(
            @NonNull Authentication authentication,
            @PathVariable int serviceRecipientId,
            @NonNull @PathVariable String evidenceGroupKey) {

        List<EvidenceAssociatedContactCommand> commands =
                commandRepository.findAssociatedContactCommandByServiceRecipientIdAndEvidenceGroup(serviceRecipientId, evidenceGroupKey);

        return extractJsonBody.asJsonArray(commands);

    }

}
