package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceGroup;

import java.util.List;

/**
 * Evidence of a piece of work done to support a client, and any corresponding
 * changes to the client's support plan.
 */
public class BaseEvidenceSnapshotViewModel {

    /** Note this has to be a 'Key' to ensure it's kept as clear key for retrieval when encrypting. */
    public EvidenceGroup evidenceGroupKey;

    public int serviceRecipientId;

    /**
     * The parent of this evidence (e.g. a referralId, workerId)
     */
    public Long parentId;

    /**
     * Snapshot of latest flags.
     */
    public List<FlagViewModel> latestFlags;
}
