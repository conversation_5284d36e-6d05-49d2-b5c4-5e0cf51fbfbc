package com.ecco.webApi.evidence;

import com.ecco.upload.webapi.UploadedFileResource;
import org.joda.time.LocalDateTime;

import java.util.List;
import java.util.UUID;

/** Base class for (currently referral-related, not HR, etc) work evidence */
public abstract class BaseWorkViewModel {

    /**
     * The work ID.
     */
    public UUID id;

    /**
     * The service recipient ID of the referral corresponding to this support work.
     */
    public Integer serviceRecipientId;

    /**
     * The service this work was recorded on.
     */
    public Integer serviceAllocationId;

    /**
     * The instant when the work was updated from a request to be deleted
     */
    public boolean requestedDelete;

    /** The code (usually referral id, worker id, but can be overridden) of the related object */
    public String parentCode;

    /** A prefix representing what type of service recipient this is: (r)eferral, (b)uilding or (w)orker */
    public String parentPrefix;

    /**
     * The display name of the person who recorded this evidence.
     */
    public String authorDisplayName;
    /**
     * A comment that annotates this support work. The comment was written by
     * the person named in {@link #authorDisplayName}.
     */
    public String comment;
    /**
     * The type of work (e.g. meeting, phone call)
     */
    public Integer commentTypeId;

    /**
     * Event id for a related calendar item
     */
    public String eventId;

    /**
     * Time spent on the work in minutes
     */
    public Integer minsSpent;

    /**
     * The id of the signature if this work has been signed, if not signed, this is null.
     */
    public UUID signatureId;
    /**
     * The datetime the support work was carried out.  Where this is combined with minsSpent to give a specific
     * duration, then this is the start of the period.
     *
     * This is the time as reported by the API client (so 7am BST will have been stored as 7am)
     */
    public LocalDateTime workDate;
    /**
     * The datetime this entry was created.  Where possible this should be set from the client (so that order of
     * items is correct offline)
     */
    public LocalDateTime createdDate;

    /**
     * The (workflow) task that was being performed when this evidence was recorded.
     * Expect things like "needsAssessment", "needsReduction", "needsAssessmentReductionReview", "rotaVisit"
     */
    public String taskName;

    /**
     * Attachments related to this work
     */
    public List<UploadedFileResource> attachments;

    public List<FlagViewModel> flags;
}