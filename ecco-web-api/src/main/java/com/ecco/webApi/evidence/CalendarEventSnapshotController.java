package com.ecco.webApi.evidence;

import com.ecco.dao.DemandScheduleRepository;
import com.ecco.evidence.repositories.CalendarEventSnapshotRepository;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static java.util.stream.Collectors.toList;
import static org.springframework.format.annotation.DateTimeFormat.ISO.DATE_TIME;

@RestController
@RequestMapping("/calendarEventSnapshot")
@PreAuthorize("hasRole('ROLE_STAFF')")
public class CalendarEventSnapshotController extends BaseWebApiController {

    private final CalendarEventSnapshotRepository calendarEventSnapshotRepository;
    private final CalendarEventSnapshotToDto calendarEventSnapshotToDto;

    @Autowired
    public CalendarEventSnapshotController(CalendarEventSnapshotRepository repository,
                                           ServiceRecipientRepository serviceRecipientRepository,
                                           DemandScheduleRepository demandScheduleRepository) {
        this.calendarEventSnapshotRepository = repository;
        this.calendarEventSnapshotToDto = new CalendarEventSnapshotToDto(serviceRecipientRepository, demandScheduleRepository);
    }

    /**
     * Our ecco-common/time.ts exports without a timezone, so the utc incoming is treated as local datetime
     */
    @GetJson(value = "/range/")
    public List<CalendarEventSnapshotDtoResource> findAllByDate(@RequestParam(required = false) @DateTimeFormat(iso = DATE_TIME) LocalDateTime from,
                                                                @RequestParam @DateTimeFormat(iso = DATE_TIME) LocalDateTime to) {
        var result = from != null
                ? this.calendarEventSnapshotRepository.findAllByPlannedStartInstantGreaterThanEqualAndPlannedStartInstantBefore(from.toInstant(ZoneOffset.UTC),
                    to.toInstant(ZoneOffset.UTC))
                : this.calendarEventSnapshotRepository.findAllByPlannedStartInstantBefore(to.toInstant(ZoneOffset.UTC));
        return result.map(calendarEventSnapshotToDto)
                .collect(toList());
    }

    /**
     * Get the missed events for a calendar (those without an endInstant)
     */
    @GetJson(value = "/missed/")
    public List<CalendarEventSnapshotDtoResource> findMissedByCalendarId(
            @RequestParam @DateTimeFormat(iso = DATE_TIME) LocalDateTime before,
            @RequestParam("resourceContactId") Long resourceContactId) {
        var result = this.calendarEventSnapshotRepository.findAllByPlannedStartInstantBeforeAndResourceContactIdAndEndInstantIsNull(before.toInstant(ZoneOffset.UTC), resourceContactId);
        return result.map(calendarEventSnapshotToDto)
                .collect(toList());
    }

    // for testing
    @GetJson(value = "/all/")
    public List<CalendarEventSnapshotDtoResource> findAll() {
        return this.calendarEventSnapshotRepository.findAll().stream()
                .map(calendarEventSnapshotToDto)
                .collect(toList());
    }

}
