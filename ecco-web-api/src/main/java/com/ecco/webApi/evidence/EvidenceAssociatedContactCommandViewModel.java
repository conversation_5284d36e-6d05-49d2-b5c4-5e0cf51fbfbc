package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceGroup;
import com.ecco.evidence.EvidenceTask;
import org.jspecify.annotations.NonNull;
import org.springframework.web.util.UriComponentsBuilder;

import org.jspecify.annotations.Nullable;
import java.util.UUID;

/**
 * A contact associated with a piece of evidence.
 * This can be used to record lone working information on the worker.
 */
public class EvidenceAssociatedContactCommandViewModel extends BaseServiceRecipientCommandViewModel {

    /** For Jackson etc */
    EvidenceAssociatedContactCommandViewModel() {
        super();
    }

    public enum AttendanceStatus {
        START,
        END
    }

    @NonNull
    public UUID workUuid;

    public long contactId;

    // Non-null because we don't have a snapshot to update yet
    @NonNull
    public AttendanceStatus attendanceStatus;

    /**
     * The eventId uid that the work is for (eg 'rota visit' calendar uid)
     */
    @NonNull
    public String eventId;

    /**
     * The location of the contact at this (created) moment in time.
     * {@link CommentCommandViewModel}
     */
    @Nullable
    public LocationViewModel location;

    public EvidenceAssociatedContactCommandViewModel(@NonNull UUID workUuid,
                                                     @NonNull String eventId,
                                                     int serviceRecipientId,
                                                     @NonNull EvidenceGroup evidenceGroup,
                                                     @NonNull EvidenceTask evidenceTask,
                                                     long contactId) {
        super(UriComponentsBuilder
            .fromUriString("service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/{sourceTaskName}/contact/{contactId}/")
            .buildAndExpand(serviceRecipientId, evidenceGroup.nameAsLowercase(), evidenceTask.getTaskName(), contactId)
            .toString(), serviceRecipientId);
        this.contactId = contactId;
        this.eventId = eventId;
        this.workUuid = workUuid;
    }

    public boolean hasChanges() {
        return true;
    }

}
