package com.ecco.webApi.evidence;

import com.ecco.dao.EvidenceAttachmentRepository;
import com.ecco.dao.EvidenceAttachmentSummary;
import com.ecco.dao.EvidenceAttachmentViewModel;
import com.ecco.dao.ServiceRecipientAttachmentRepository;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dom.TaskDefinitionNameIdMappings;
import com.ecco.dom.ServiceRecipientAttachment;
import com.ecco.evidence.EvidenceTask;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.web.WebSlice;
import com.ecco.service.TaskDefinitionService;
import com.ecco.serviceConfig.dom.TaskDefinition;
import com.ecco.webApi.upload.UploadedFileResourceAssembler;
import com.ecco.upload.webapi.UploadedFileResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import org.jspecify.annotations.NonNull;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * @since 12/08/2014
 */
@RestController
@WebSlice("api")
public class EvidenceAttachmentController {
    private final ServiceRecipientAttachmentRepository serviceRecipientAttachmentRepository;
    private final EvidenceAttachmentRepository evidenceAttachmentRepository;

    @NonNull
    private final UploadedFileResourceAssembler fileResourceAssembler;
    @NonNull
    private final TaskDefinitionService taskDefinitionService;

    @Autowired
    public EvidenceAttachmentController(ApplicationProperties appProps,
                                        ServiceRecipientAttachmentRepository serviceRecipientAttachmentRepository,
                                        TaskDefinitionService taskDefinitionService,
                                        EvidenceAttachmentRepository evidenceAttachmentRepository) {
        this.serviceRecipientAttachmentRepository = serviceRecipientAttachmentRepository;
        this.evidenceAttachmentRepository = evidenceAttachmentRepository;
        this.fileResourceAssembler = new UploadedFileResourceAssembler(appProps.getApplicationRootPath());
        this.taskDefinitionService = taskDefinitionService;
    }

    @GetJson("/service-recipients/{serviceRecipientId}/evidence/{evidenceGroupName}/attachments/unused/")
    public Stream<UploadedFileResource> findReferralAttachmentsNotUsedInSupportWorkEvidence(
            @PathVariable int serviceRecipientId, @PathVariable String evidenceGroupName) {

        var evidenceGroup = taskDefinitionService.findGroupFromGroupName(evidenceGroupName);

        final List<ServiceRecipientAttachment> files =
                serviceRecipientAttachmentRepository.findAllUnusedByServiceRecipientIdAndEvidencePageGroup(
                        serviceRecipientId, String.valueOf(evidenceGroup.getId()));
        return files.stream().map(fileResourceAssembler);
    }

    @GetJson("/service-recipients/{serviceRecipientId}/evidence/{workId}/attachments/")
    public Stream<UploadedFileResource> findReferralAttachmentsForSupportWork(
            @PathVariable int serviceRecipientId,
            @PathVariable String workId) {
        final List<ServiceRecipientAttachment> files =
                serviceRecipientAttachmentRepository.findAllByServiceRecipientIdAndWorkId(serviceRecipientId, workId);
        return files.stream().map(fileResourceAssembler);
    }

    @GetJson("/service-recipients/{serviceRecipientId}/evidence/attachments/")
    public List<EvidenceAttachmentViewModel> findAttachmentsByServiceRecipientId(@PathVariable int serviceRecipientId) {
        var support = evidenceAttachmentRepository.findSupportAttachmentsByServiceRecipientId(serviceRecipientId, Long.valueOf(EvidenceGroup.NEEDS.getId()).intValue());
        var threat = evidenceAttachmentRepository.findThreatAttachmentsByServiceRecipientId(serviceRecipientId);
        // TODO custom form, but it currently doesn't have the table for it
        return Stream.concat(support.stream(), threat.stream()).sorted(Comparator.comparing(EvidenceAttachmentSummary::getWorkDate)
                            .reversed()
                        .thenComparing(Comparator.comparing(EvidenceAttachmentSummary::getCreated)
                            .reversed())
                )
                .map(i -> {
                    var taskName = TaskDefinitionNameIdMappings.fromTaskDefIdToTaskName(i.getTaskDefId());
                    var type = taskDefinitionService.getTaskType(EvidenceTask.fromTaskName(taskName));

                    // see TaskEvidenceType (ts) or TaskDefinition.Type (java) - but this doesn't differentiate evidence tasks
                    // NB doesn't cover custom forms / checklist / rota etc
                    if (TaskDefinition.Type.EVIDENCE_RISK == type) {
                        var vm = new EvidenceAttachmentViewModel(type.name(), i.getWorkId());
                        vm.add(linkToApi(methodOn(RiskEvidenceController.class).findOneThreatWorkByWorkUuid(i.getServiceRecipientId(), i.getWorkId()))
                                .withRel("work"));
                        return vm;
                    } else if (TaskDefinition.Type.EVIDENCE_QUESTIONNAIRE == type) {
                        var vm = new EvidenceAttachmentViewModel(type.name(), i.getWorkId());
                        vm.add(linkToApi(methodOn(QuestionnaireEvidenceController.class).findOneByWorkUuid(i.getWorkId()))
                                .withRel("work"));
                        return vm;
                    } else if (TaskDefinition.Type.EVIDENCE_SUPPORT == type) {
                        var vm = new EvidenceAttachmentViewModel(type.name(), i.getWorkId());
                        vm.add(linkToApi(methodOn(SupportEvidenceController.class).findOneSupportWorkByWorkUuid(i.getServiceRecipientId(), EvidenceGroup.NEEDS.getName(), i.getWorkId()))
                                .withRel("work"));
                        return vm;
                    }
                    throw new IllegalArgumentException("unknown evidence task name type: " + type);
                })
                .distinct() // or by workUuid only, see https://javadevcentral.com/java-stream-distinct-by-property
                .collect(Collectors.toList());
    }

}
