package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceThreatOutcome;

import java.util.function.Function;

public class EvidenceRiskAreaToViewModel implements Function<EvidenceThreatOutcome, EvidenceRiskAreaViewModel> {

    @Override
    public EvidenceRiskAreaViewModel apply(EvidenceThreatOutcome input) {
        EvidenceRiskAreaViewModel result = new EvidenceRiskAreaViewModel();

        result.id = input.getWorkId();
        result.serviceRecipientId = input.getServiceRecipientId();
        result.serviceAllocationId = input.getServiceRecipient().getServiceAllocationId();
        result.riskAreaId = input.getOutcome().getId();
        result.riskAreaName = input.getOutcome().getName();
        result.levelMeasure = input.getLevelMeasure();
        result.level = input.getLevel();
        result.trigger = input.getTrigger();
        result.control = input.getControl();
        result.workDate = input.getWork().getWorkDate().toLocalDateTime();
        result.workUuid = input.getWorkId();

        return result;
    }

}
