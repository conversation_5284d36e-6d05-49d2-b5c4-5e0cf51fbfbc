package com.ecco.webApi.evidence;

import org.joda.time.LocalDateTime;

import java.util.UUID;

public class EvidenceRiskAreaViewModel {

    public UUID id;
    public Integer serviceRecipientId;
    public Integer serviceAllocationId;
    public Long riskAreaId;
    public String riskAreaName;
    public String levelMeasure;
    public Integer level;
    public String trigger;
    public String control;
    public LocalDateTime workDate;
    public UUID workUuid;

}
