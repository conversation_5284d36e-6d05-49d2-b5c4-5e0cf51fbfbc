package com.ecco.webApi.evidence;

import lombok.Data;
import org.joda.time.LocalDateTime;

/**
 * Evidence of an answer on a client questionnaire
 */
@Data
public class QuestionAnswerSnapshotViewModel {

    /**
     * The id of the database entry for this change (GenericTypeAnswer)
     */
    public Long id;

    /**
     * The id of the question
     */
    public Long questionId;

    /**
     * The datetime the work was carried out - ISO8601 extended format
     */
    public LocalDateTime workDate;

    /**
     * The answer (which can be an id)
     */
    public String answer;

}
