package com.ecco.webApi.evidence;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.ClientRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dao.ReferralSummary;
import com.ecco.dom.Referral;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.ecco.webApi.contacts.ClientToViewModel;
import com.ecco.webApi.contacts.ClientViewModel;
import com.ecco.webApi.controllers.BaseWebApiController;

public class ReferralBaseController extends BaseWebApiController {

    private final ReferralRepository referralRepository;
    private final ClientRepository clientRepository;
    private final ListDefinitionRepository listDefinitionRepository;
    private final EntityRestrictionService entityRestrictionService;
    private final RepositoryBasedServiceCategorisationService serviceCategorisationService;
    private final ReferralSummaryToViewModel referralSummaryToViewModel;
    private final ClientToViewModel clientToViewModel;

    public ReferralBaseController(ReferralRepository referralRepository,
                                  ClientRepository clientRepository,
                                  EntityRestrictionService entityRestrictionService,
                                  RepositoryBasedServiceCategorisationService serviceCategorisationService,
                                  ClientToViewModel clientToViewModel,
                                  ListDefinitionRepository listDefinitionRepository) {
        this.referralRepository = referralRepository;
        this.clientRepository = clientRepository;
        this.entityRestrictionService = entityRestrictionService;
        this.serviceCategorisationService = serviceCategorisationService;
        this.referralSummaryToViewModel = new ReferralSummaryToViewModel();
        this.clientToViewModel = clientToViewModel;
        this.listDefinitionRepository = listDefinitionRepository;
    }

    /**
     * Return the referral matching this serviceRecipientId
     */
    public ReferralViewModel findOneByServiceRecipient(int serviceRecipientId) {
        Referral referral = referralRepository.findByServiceRecipient_Id(serviceRecipientId);
        // return the behaviour as if one result
        ReferralToViewModel referralToViewModel = new ReferralToViewModel(this.listDefinitionRepository)
                .withRestrictions(entityRestrictionService.getRestrictedServicesProjectsDto(serviceCategorisationService));
        return referralToViewModel.apply(referral);
    }

    public ReferralSummaryViewModel findOneReferralSummaryByServiceRecipientAsReferral(int serviceRecipientId, Boolean withoutAclSecurity) {
        ReferralSummary referral = referralRepository.findOneReferralSummaryByServiceRecipientId(serviceRecipientId);
        assert referral != null;
        if (!Boolean.TRUE.equals(withoutAclSecurity)) {
            entityRestrictionService.verifyAccess(referral.serviceRecipientId, referral.serviceIdAcl, referral.projectIdAcl, serviceCategorisationService);
        }
        return referralSummaryToViewModel.apply(referral);
    }

    // NB clone of ClientController findOne
    public ClientViewModel findOneClient(long id) {
        return clientToViewModel.apply(clientRepository.findById(id).orElse(null));
    }

}
