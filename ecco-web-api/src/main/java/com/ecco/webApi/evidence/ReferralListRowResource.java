package com.ecco.webApi.evidence;

import static lombok.AccessLevel.PROTECTED;

import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaProperty;
import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaMetadata;
import com.fasterxml.jackson.databind.jsonFormatVisitors.JsonValueFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

import org.joda.time.LocalDate;
import org.springframework.hateoas.RepresentationModel;

@NoArgsConstructor(access = PROTECTED)
@AllArgsConstructor
@Getter
public class ReferralListRowResource extends RepresentationModel<ReferralListRowResource> {
    // Not a visible column
    private Integer serviceRecipientId;
    private Long referralId;

    @JsonSchemaMetadata(title = "r-id", order = 10)
    private String referralCode;
    @JsonSchemaMetadata(title = "name", order = 20)
    private String clientDisplayName;
    @JsonSchemaMetadata(title = "contactId", order = 25)
    private Long contactId; // this is an Individual id
    @JsonSchemaMetadata(title = "supportWorkerId", order = 25)
    private Long supportWorkerId; // this is an Individual id
    @JsonSchemaMetadata(title = "supportWorkerDisplayName", order = 25)
    private String supportWorkerDisplayName;
    @JsonSchemaMetadata(title = "interviewer1ContactId", order = 25)
    private Long interviewer1ContactId;
    @JsonSchemaMetadata(title = "from", order = 30)
    private String agency;
    @JsonSchemaMetadata(order = 29)
    private boolean selfReferral;
    @JsonSchemaMetadata(title = "to", order = 40)
    private Integer serviceAllocationId;
    @JsonSchemaMetadata(title = "received", order = 50)
    @JsonSchemaProperty(format = JsonValueFormat.DATE)
    private LocalDate receivedDate;
    @JsonSchemaMetadata(title = "start", order = 70)
    @JsonSchemaProperty(format = JsonValueFormat.DATE)
    private LocalDate receivingServiceDate;
    // TODO: When jackson-jsonSchema gets custom format support, lose the 'title:format' hacks
    @JsonSchemaMetadata(title = "status:message-key", order = 80)
    private String statusMessageKey;
    @JsonSchemaMetadata(title = "status summary", order = 80)
    private String statusSummary;
    @JsonSchemaMetadata(title = "next task due by", order = 90)
    private LocalDate nextDueSlaDate;
    @JsonSchemaMetadata(title = "next due task", order = 100)
    private Long nextDueTaskId;


    @Getter
    @RequiredArgsConstructor(access = PROTECTED)
    public static class ServiceDescription {
        private final Long serviceId;
        private final String service;
        private final Long projectId;
        private final String deliveredBy;

    }
}
