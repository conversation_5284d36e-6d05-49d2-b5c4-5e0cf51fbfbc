package com.ecco.webApi.evidence;

import javax.annotation.meta.TypeQualifier;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * The annotated element must not be null when the sibling field "operation" matches any in value.
 * If always required then simply use @Nonnull
 */
@Documented
@TypeQualifier
@Retention(RetentionPolicy.RUNTIME)
public @interface RequiredForOperations {

    /** Typically "add" (OPERATION_ADD) and/or "update" and/or "remove" */
    String[] value();
}
