package com.ecco.webApi.evidence;

import static com.ecco.dom.EvidenceGroup.*;

import org.jspecify.annotations.NonNull;

import com.ecco.dao.*;
import com.ecco.dom.*;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.evidence.EvidenceTask;
import com.ecco.evidence.ParentChildResolver;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.service.EventService;
import com.ecco.service.TaskDefinitionService;
import com.ecco.webApi.CommandResult;
import com.ecco.calendar.core.CalendarService;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.commands.CommentCommand;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.net.URI;


public class SupportCommentCommandHandler extends EvidenceCommandHandler<CommentCommandViewModel, CommentCommand,
        @NonNull EvidenceParams> {

    @NonNull
    private final ClientRepository clientRepository;

    @NonNull
    private final EvidenceSupportWorkRepository workRepository;

    @NonNull
    protected final EvidenceSupportCommentRepository commentRepository;

    @NonNull
    private final ReviewRepository reviewRepository;

    @NonNull
    private final EventService eventService;

    @NonNull
    private final CustomEventRepository nonRecurringRepository;

    public SupportCommentCommandHandler(ObjectMapper objectMapper,
                                        ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                        @NonNull EvidenceSupportWorkRepository workRepository,
                                        @NonNull EvidenceSupportCommentRepository commentRepository,
                                        @NonNull ClientRepository clientRepository,
                                        @NonNull ReviewRepository reviewRepository,
                                        ServiceRepository serviceRepository,
                                        ServiceRecipientRepository serviceRecipientRepository,
                                        TaskDefinitionService taskDefinitionService,
                                        ParentChildResolver parentChildResolver,
                                        @NonNull CalendarService calendarService,
                                        @NonNull EventService eventService,
                                        @NonNull EntityUriMapper entityUriMapper,
                                        @NonNull CustomEventRepository eventRepository) {
        super(objectMapper, serviceRecipientRepository, serviceRecipientCommandRepository, serviceRepository,
                parentChildResolver, calendarService, taskDefinitionService, entityUriMapper,
                CommentCommandViewModel.class);
        this.workRepository = workRepository;
        this.commentRepository = commentRepository;
        this.clientRepository = clientRepository;
        this.reviewRepository = reviewRepository;
        this.eventService = eventService;
        this.nonRecurringRepository = eventRepository;
    }

    @Override
    public CommandResult handleInternal(int parentServiceRecipientId, Integer childServiceRecipientId,
                                        Authentication auth, @NonNull EvidenceParams params, CommentCommandViewModel viewModel) {

        EvidenceGroup grp = taskDefinitionService.findGroupFromGroupName(params.evidenceGroupKey);
        Assert.state(grp.equals(NEEDS) || grp.equals(CHECKLIST) || grp.equals(SUPPORTSTAFFNOTES) || grp.equals(ENGAGEMENTCOMMENTS) || grp.equals(MANAGERNOTES));

        EvidenceSupportComment newSnapshot = createevidenceSupportComment(parentServiceRecipientId, childServiceRecipientId, auth, params, viewModel);

        // save the comment only
        commentRepository.save(newSnapshot);

        // create calendar entries at the end
        // otherwise it flushes uncompleted items, and the work.eventId isn't saved
        // its in the same tx, so the ref integrity isn't an issue
        if (newSnapshot.getWork().getEventId() != null) {
            URI uri = this.entityUriMapper.uriForEntity(CommentCommandViewModel.class.getSimpleName(), viewModel.uuid);
            String calEventId = newSnapshot.getWork().getEventId();
            ensureConcreteRecurrence(uri, calEventId);

            var saveEventInfo = (viewModel.eventStatusId != null) || (viewModel.eventStatusRateId != null);
            if (saveEventInfo) {
                // create an entry to record the status rate (eg chargeable dropped reason)
                CustomEventImpl event = nonRecurringRepository.findOneByUid(calEventId)
                        .orElse(EventEntryAssembler.setUpNewCalendarEntry(newSnapshot.getServiceRecipientId(), calEventId));
                if (viewModel.eventStatusId != null) {
                    event.setEventStatusId(viewModel.eventStatusId.to);
                }
                if (viewModel.eventStatusRateId != null) {
                    event.setEventStatusRateId(viewModel.eventStatusRateId.to);
                }
                nonRecurringRepository.save(event); // Avoids sync to Cosmo as we don't update the calendar when doing planned work
                // flush may not be needed here
                entityManager.flush();
            }
        }

        return null;
    }

    protected EvidenceSupportComment createevidenceSupportComment(int parentServiceRecipientId, Integer childServiceRecipientId, Authentication auth, @NonNull EvidenceParams params, CommentCommandViewModel viewModel) {
        // due to the independent nature of the web-api calls, it could be that an action/goal has saved the work item
        // first, and so we need to load the work item to check it exists and populate it accordingly
        // NB this persists the work item if it is new
        EvidenceSupportWork existingWork = findOrCreateWork(parentServiceRecipientId, childServiceRecipientId, auth, params, viewModel);

        // see GoalController which gets the previous snapshot and constructs a new one
        // however, we want to use something like the processor we had where possible, since there will be common scenarios
        // in updating data - such as editing history - which will need to be done carefully once
        //protected void createOrUpdateSupportHistorySnapshot(Authentication authentication, CommentCommandViewModel viewModel, long clientContactId) {
            /*
            // load all the latest snapshot at once - the only reason we don't use GoalController.addEquivalentSupportWork approach
            List<? extends GenericTypeAction> latestActions = supportEvidenceService.getGenericTypeLatestActions(vm.parentId);
            List<? extends GenericTypeOutcome> latestOutcomes = supportEvidenceService.getGenericTypeLatestOutcomes(vm.parentId);
            EvidenceSnapshotFromCommandProcessor processor = new EvidenceSnapshotFromCommandProcessor();
            SupportEvidenceBuilder eb = new SupportEvidenceBuilder();
            processor.process(eb, outcomeRepository, reviewService, null, snapshotActions, viewModel);
            evidenceSupportWork work = eb.getWork();
            // save the work
            supportEvidenceService.setWork(work, toTargets(vm), expires, getAllActions(this.outcomeRepository));
            */
        //}

        // see GoalController which gets the previous snapshot and constructs a new one
        // but we use an existing builder here which works updates things to the latest snapshot
        SupportEvidenceBuilder builder = new SupportEvidenceBuilder(existingWork);
        applyViewModel(viewModel, builder);
        EvidenceSupportWork work = builder.build();

        EvidenceSupportComment newSnapshot = work.getComment();

        if (viewModel.attachmentIdsToAdd != null) {
            for (Long fileId : viewModel.attachmentIdsToAdd) {
                ServiceRecipientAttachment file = entityManager.getReference(ServiceRecipientAttachment.class, fileId);
                EvidenceSupportAttachment workAttachment = new EvidenceSupportAttachment(file);
                workAttachment.setCreated(viewModel.timestamp.toDateTime());
                work.addAttachment(workAttachment);
            }
        }

        // Newer list-def based flag ids
        BaseServiceRecipient sr = getParentAsRef(parentServiceRecipientId);
        if (viewModel.flagIds != null) {
            if (viewModel.flagIds.added != null && viewModel.flagIds.added.size() > 0) {
                for (Integer flagDefId : viewModel.flagIds.added) {
                    //ListDefinitionEntry flagDef = entityManager.getReference(ListDefinitionEntry.class, flagDefId);
                    EvidenceSupportFlag flag = new EvidenceSupportFlag(flagDefId);
                    flag.setServiceRecipientId(sr.getId());
                    flag.setCreated(viewModel.timestamp.toDateTime());
                    flag.setWorkDate(work.getDate());
                    flag.setValue(true);
                    work.addFlag(flag);
                }
            }
            if (viewModel.flagIds.removed != null && viewModel.flagIds.removed.size() > 0) {
                for (Integer flagDefId : viewModel.flagIds.removed) {
                    //ListDefinitionEntry flagDef = entityManager.getReference(ListDefinitionEntry.class, flagDefId);
                    EvidenceSupportFlag flag = new EvidenceSupportFlag(flagDefId);
                    flag.setServiceRecipientId(sr.getId());
                    flag.setCreated(viewModel.timestamp.toDateTime());
                    flag.setWorkDate(work.getDate());
                    flag.setValue(false);
                    work.addFlag(flag);
                }
            }
        }

        // see ThreatEvidenceService.setWork
        // we DON'T currently set the target dates in the calendar, etc
        // entityService allows for 'persist', which threatWorkRepository doesn't
        //uuidEntityService.setEntity(work);

        return newSnapshot;
    }

    /** Other commands may create work first, so we may be creating a new work item or updating it.
     */
    EvidenceSupportWork findOrCreateWork(int parentServiceRecipientId, Integer childServiceRecipientId,
                                         Authentication auth, @NonNull EvidenceParams params, CommentCommandViewModel viewModel) {

        EvidenceSupportWork work = workRepository.findById(viewModel.workUuid).orElse(null);

        if (work == null) {
            ClientDetail client = clientRepository.findOneByServiceRecipientId(parentServiceRecipientId);
            EvidenceTask task = EvidenceTask.fromTaskName(params.getTaskName());
            EvidenceGroup grp = taskDefinitionService.findGroupFromGroupName(params.evidenceGroupKey);
            SupportEvidenceBuilder builder = createNewSupportWork(parentServiceRecipientId, childServiceRecipientId, auth,
                    task, grp, viewModel.workUuid, viewModel.timestamp, client);
            withReview(viewModel, builder);
            work = builder.build();
            // see ThreatEvidenceService.setWork
            // we DON'T currently set the target dates in the calendar, etc
            // entityService allows for 'persist', which threatWorkRepository doesn't
            //uuidEntityService.setEntity(work);
            entityManager.persist(work);
            return work;

        } else {
            Assert.state(work.getServiceRecipient().getId().equals(params.serviceRecipientId));
        }
        return work;
    }

    protected void withReview(CommentCommandViewModel viewModel, SupportEvidenceBuilder builder) {
        if (viewModel.reviewId != null) {
            Review review = reviewRepository.getOne(viewModel.reviewId);
            builder.review(review);
        }
    }

    @NonNull
    @Override
    protected CommentCommand persistCommand(@NonNull CommentCommand command) {
        var cmdSaved = super.persistCommand(command);
        entityManager.flush();
        return cmdSaved;
    }

}
