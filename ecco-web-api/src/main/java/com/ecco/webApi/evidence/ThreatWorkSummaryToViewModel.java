package com.ecco.webApi.evidence;

import com.ecco.dao.ThreatWorkSummary;
import com.ecco.webApi.upload.UploadedFileResourceAssembler;

import java.util.Collections;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ThreatWorkSummaryToViewModel implements Function<ThreatWorkSummary, EvidenceThreatWorkViewModel> {

    private final RiskAreaEvidenceToViewModel riskAreaEvidenceToViewModel = new RiskAreaEvidenceToViewModel();
    private final ThreatActionSummaryToViewModel riskActionsToViewModel = new ThreatActionSummaryToViewModel();
    private final EvidenceFlagSummaryToViewModel flagsToViewModel = new EvidenceFlagSummaryToViewModel();
    private final UploadedFileResourceAssembler fileAssembler;


    ThreatWorkSummaryToViewModel(UploadedFileResourceAssembler assembler) {
        this.fileAssembler = assembler;
    }

    @Override
    public EvidenceThreatWorkViewModel apply(ThreatWorkSummary input) {
        if (input == null) {
            throw new NullPointerException("input evidenceSupportWork must not be null");
        }

        EvidenceThreatWorkViewModel viewModel = new EvidenceThreatWorkViewModel();
        viewModel.id = input.getId();
        viewModel.requestedDelete = input.getRequestedDelete() != null;
        viewModel.taskName = input.getTaskName();
        viewModel.serviceRecipientId = input.getServiceRecipientId();
        viewModel.serviceAllocationId = input.getServiceAllocationId();
        viewModel.authorDisplayName = input.getAuthor().getDisplayName();
        viewModel.signatureId = input.getSignatureId();

        viewModel.riskActions = input.getActions().stream().map(riskActionsToViewModel).collect(Collectors.toList());
        viewModel.riskAreas = input.getRiskAreas().stream().map(riskAreaEvidenceToViewModel).collect(Collectors.toList());
        viewModel.flags = input.getFlags().stream().map(flagsToViewModel).collect(Collectors.toList());
        viewModel.handledSupportWorkIds = input.getHandledSupportWorkIds();
        viewModel.comment = input.getComment();
        viewModel.commentTypeId = input.getCommentTypeId();
        viewModel.minsSpent = input.getCommentMinutesSpent();
        viewModel.workDate = input.getWorkDate().toLocalDateTime(); // TODO: review did involve Locale.ROOT
        viewModel.createdDate = input.getCreatedDate().toLocalDateTime();
        viewModel.associatedActions = input.getAssociatedActions().stream()
                .map(action -> action.actionId)
                .collect(Collectors.toList());

        viewModel.attachments = input.getAttachments() == null ? Collections.emptyList()
                : input.getAttachments().stream()
                    .map(f -> fileAssembler.apply(f.getFile()))
                    .sorted()
                    .toList();

        return viewModel;
    }

}
