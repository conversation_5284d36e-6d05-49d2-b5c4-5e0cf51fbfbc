package com.ecco.webApi.featureConfig;

import org.jspecify.annotations.NullMarked;

import com.ecco.serviceConfig.viewModel.*;
import com.ecco.webApi.forms.FormDefinitionViewModel;
import com.ecco.webApi.listsConfig.*;
import com.ecco.webApi.listsConfig.IdNameViewModel;
import com.ecco.webApi.serviceConfig.TaskDefinitionViewModel;
import com.ecco.webApi.serviceConfig.ServiceViewModel;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SuppressWarnings("NotNullFieldNotInitialized")
@NullMarked
public class GlobalConfigViewModel {
    public Map<String, SoftwareModuleViewModel> softwareModulesEnabled = new HashMap<>(1);
    public Map<String, FeatureSetViewModel> featureSets = new HashMap<>(1);

    public Map<Integer, ServiceTypeViewModel> serviceTypesById;
    public Map<String, FormDefinitionViewModel> formDefinitionsById;
    public Iterable<ServiceViewModel> services;
    public Iterable<ProjectViewModel> projects;
    public Iterable<ServiceCategorisationViewModel> serviceCategorisations;
    public Map<String, Collection<ListDefinitionEntryViewModel>> listDefinitions;
    public List<TaskDefinitionViewModel> taskDefinitions;
    public Iterable<AppointmentTypeViewModel> appointmentTypes;
    public Iterable<OutcomeViewModel> supportOutcomes;
    public Iterable<RiskAreaViewModel> riskAreas;
    public Iterable<QuestionGroupViewModel> questionGroups;
    /**
     * Settings key = namespace + ":" + key
     */
    public Map<String, String> settings;
    public List<IdNameViewModel> flags;
    public Iterable<FundingSourceViewModel> fundingSources;
    public Map<String, String> messages;
    public List<UnitOfMeasurementDto> unitOfMeasurements;
}
