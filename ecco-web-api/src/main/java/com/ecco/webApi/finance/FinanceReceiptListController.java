package com.ecco.webApi.finance;

import com.ecco.dom.finance.FinanceReceipt;
import com.ecco.infrastructure.rest.hateoas.schema.*;
import com.ecco.repositories.finance.FinanceReceiptRepository;
import com.ecco.webApi.users.GroupsController;
import com.ecco.webApi.viewModels.ResourceList;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.types.LinkDescriptionObject;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.querydsl.QSort;
import org.springframework.hateoas.IanaLinkRelations;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

import java.util.Optional;

import static com.ecco.dom.finance.QFinanceReceipt.financeReceipt;
import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

@RestController
@RequestMapping("/finance/receipts")
@Secured("ROLE_STAFF")
public class FinanceReceiptListController extends SchemaProvidingController<FinanceReceiptListController> {
    private static final String REL_INSTANCES = "instances";

    private final FinanceReceiptRepository financeReceiptRepository;
    private final FinanceReceiptListRowResourceAssembler rowResourceAssembler;


    public FinanceReceiptListController(FinanceReceiptRepository financeReceiptRepository,
                                        FinanceReceiptListRowResourceAssembler rowResourceAssembler) {
        super();
        this.financeReceiptRepository = financeReceiptRepository;
        this.rowResourceAssembler = rowResourceAssembler;
    }

    @Override
    public String getEntityTypeName() {
        return "receipts";
    }

    @GetJson("/")
    public ResourceList<FinanceReceiptListRowResource> list(
            @RequestParam(name = "search", required = false)
            @JsonSchemaMetadata(order = 3)
            String search,
            @RequestParam(name = "srId", required = false)
            @JsonSchemaMetadata(order = 10, title = "serviceRecipientId")
            Integer srId,
            @RequestParam(name = "page", defaultValue="0")
            Integer page,
            @RequestParam(name = "pageSize", defaultValue="15") // could do getPageSize("pageSize.users"));
            @JsonSchemaProperty(readOnly = true)
            Integer pageSize) {

        PageRequest pr = pageAndSorting(page, pageSize);
        Predicate p = receiptQuery(search, srId);
        final Page<FinanceReceipt> receipts = financeReceiptRepository.findAll(p, pr);

        Page<FinanceReceiptListRowResource> resourcePage = receipts.map(rowResourceAssembler::toModel);
        ResourceList<FinanceReceiptListRowResource> resourceList = new ResourceList<>(resourcePage.getContent());

        if (resourcePage.hasPrevious()) {
            resourceList.add(
                    linkToApi(methodOn(FinanceReceiptListController.class).list(search, srId,  page - 1, pageSize))
                        .withRel(IanaLinkRelations.PREV));
        }
        if (resourcePage.hasNext()) {
            resourceList.add(
                    linkToApi(methodOn(FinanceReceiptListController.class).list(search, srId,page + 1, pageSize))
                        .withRel(IanaLinkRelations.NEXT));
        }
        resourceList.setNumPages(resourcePage.getTotalPages());
        resourceList.setPageSize(pageSize);
        resourceList.setNumItems(resourcePage.getTotalElements());
        addDescribedByLink(resourceList);
        return resourceList;
    }

    @Override
    public ResponseEntity<JsonSchema> describe(WebRequest request) {
        Object invocation = self().list("", 0,0, 0);

        JsonSchema listRequestParamSchema = getSchemaCreator().createForRequestParams(invocation);

        // Make sure the parameters are valid services and projects by extending the enumeration of them.
        // NB extends[] = allOf, type[] = anyOf (this is more explicit in JSON Schema v4)
        var linkBuilder = linkToApi(methodOn(GroupsController.class).enumerateAllGroups());
        listRequestParamSchema.setExtends(new JsonSchema[] {
                new TypedReferenceSchema(linkBuilder.toUri().toString(),
                        listRequestParamSchema.getType())
        });

        LinkDescriptionObject instancesLink = new SchemaProvidingLinkDescriptionObject()
                .setRel(REL_INSTANCES)
                .setMethod(RequestMethod.GET.toString())
                .setHref(linkTo(invocation).toUriComponentsBuilder().replaceQuery(null).build(false).toString())
                .setSchema(listRequestParamSchema);

        JsonSchema schema = getSchemaCreator().create(FinanceReceiptListRowResource.class,
                self().describe(request),
                Optional.of(instancesLink),
                Optional.empty());

        cacheForXSecs(request, 180);
        return ResponseEntity.ok(schema);
    }

    private Predicate receiptQuery(String search, Integer srId) {
        BooleanBuilder p = new BooleanBuilder();
        if (search != null) {
            p.and(financeReceipt.description.containsIgnoreCase(search));
        }
        if (srId != null) {
            p.and(financeReceipt.serviceRecipientId.eq(srId));
        }
        return p;
    }

    private PageRequest pageAndSorting(int page, int pageSize) {
        QSort sort = new QSort(financeReceipt.receivedDate.desc());
        return PageRequest.of(page, pageSize, sort);
    }

}
