package com.ecco.webApi.groupSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import com.ecco.dao.EvidenceSupportCommentRepository;
import lombok.RequiredArgsConstructor;
import org.joda.time.LocalDate;

import com.ecco.dom.groupsupport.GroupActivity_Referral;
import com.ecco.dom.groupsupport.GroupSupportAttendance;
import com.google.common.collect.Maps;

@RequiredArgsConstructor
public class ClientAttendanceToViewModel implements Function<GroupActivity_Referral, ClientAttendanceViewModel> {

    private final EvidenceSupportCommentRepository supportCommentRepository;

    private final GroupSupportActivityToViewModel activityToViewModel = new GroupSupportActivityToViewModel();

    private boolean withParentActivity = false;

    private LocalDate attendanceStartDate;

    private LocalDate attendanceEndDate;


    /** Make transformer also populate the attendances that fall in the supplied inclusive date range */
    public ClientAttendanceToViewModel withAttendances(LocalDate startDate, LocalDate endDate) {
        this.attendanceStartDate = startDate;
        this.attendanceEndDate = endDate;
        return this;
    }

    /** Makes the transformer also populate the parentActivity */
    public ClientAttendanceToViewModel withParentActivity() {
        this.withParentActivity = true;
        return this;
    }

    @Override
    public ClientAttendanceViewModel apply(GroupActivity_Referral input) {
        ClientAttendanceViewModel result = new ClientAttendanceViewModel();

        // minimal properties transferred - others are populated through a separate reporting api call
        result.serviceRecipientId = input.getReferral().getServiceRecipient().getId();
        result.attending = input.isAttending();
        result.attended = input.isAttended();
        result.supportWorkUuid = input.getSupportWorkUuid();
        if (input.getSupportWorkUuid() != null) {
            supportCommentRepository.findOneByWork_Id(input.getSupportWorkUuid()).ifPresent(note -> result.comment = note.getComment());
        }
        if (withParentActivity) {
            result.parentActivity = activityToViewModel.apply(input.getMultiId().getActivity());
        }
        if (attendanceStartDate != null) {
            result.dailyAttendances = populateAttendances(input);
        }
        return result;
    }

    private List<DailyAttendanceViewModel> populateAttendances(GroupActivity_Referral input) {

        Map<LocalDate, GroupSupportAttendance> attendancesByDate =
                Maps.uniqueIndex(input.getAttendance(), GroupSupportAttendance.EXTRACT_ATTENDED_AT);

        ArrayList<DailyAttendanceViewModel> dailyAttendances = new ArrayList<>();
        for (LocalDate day = attendanceStartDate; !day.isAfter(attendanceEndDate); day = day.plusDays(1)) {
            GroupSupportAttendance attendance = attendancesByDate.get(day);
            if (attendance != null) {
                DailyAttendanceViewModel dailyAttendance =
                        ReferralClientAttendanceWithEndDateViewModel.createAttendance(input.getReferral(), day, attendance);
                dailyAttendances.add(dailyAttendance);
            }
        }
        return dailyAttendances;
    }
}
