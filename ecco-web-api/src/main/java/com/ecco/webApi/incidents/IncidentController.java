package com.ecco.webApi.incidents;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.incidents.IncidentServiceRecipient;
import com.ecco.repositories.incidents.IncidentRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.CreateServiceRecipientCommandController;
import com.ecco.webApi.controllers.CreateServiceRecipientParams;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Nonnull;
import java.io.IOException;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.springframework.hateoas.IanaLinkRelations.SELF;


@RestController
@RequestMapping("/incidents")
public class IncidentController extends BaseWebApiController {

    private final IncidentRepository incidentRepository;
    private final IncidentToViewModel incidentToViewModel;
    private final ObjectMapper objectMapper;
    private final CreateServiceRecipientCommandController createServiceRecipientCommandController;

    public IncidentController(IncidentRepository incidentRepository,
                              ObjectMapper objectMapper,
                              CreateServiceRecipientCommandController createServiceRecipientCommandController,
                              ListDefinitionRepository listDefinitionRepository) {
        this.incidentRepository = incidentRepository;
        this.objectMapper = objectMapper;
        this.createServiceRecipientCommandController = createServiceRecipientCommandController;
        this.incidentToViewModel = new IncidentToViewModel(listDefinitionRepository);
    }

    // see ContractController.EXTRACT_ID_FN
    // see BuildingController.EXTRACT_ID_FN
    public static Pattern extractIdPattern = Pattern.compile("\\w+/(\\d+)/");
    public static final Function<String, Integer> EXTRACT_ID_FN = (href) -> {
        Matcher matches = extractIdPattern.matcher(href);
        matches.find();
        String id = matches.group(1);
        return Integer.parseInt(id);
    };

    @GetJson("/{incidentId}/")
    public IncidentViewModel getIncident(@PathVariable int incidentId) {
        return incidentToViewModel.apply(incidentRepository.getById(incidentId));
    }

    @GetJson("/service-recipients/{srId}/")
    public IncidentViewModel getIncidentBySrId(@PathVariable int srId) {
        return incidentToViewModel.apply(incidentRepository.findIncidentByServiceRecipientId(srId).get());
    }

    @PreAuthorize("isFullyAuthenticated()")
    @Nonnull
    Result createImport(IncidentViewModel incidentViewModel) throws IOException {
        Assert.isNull(incidentViewModel.incidentId, "No id should be set on POST");

        CreateIncidentCommandViewModel createVm = new CreateIncidentCommandViewModel(incidentViewModel);
        CreateServiceRecipientParams params = new CreateServiceRecipientParams(IncidentServiceRecipient.PREFIX);
        String createVmStr = objectMapper.writeValueAsString(createVm);
        Result commandResult = createServiceRecipientCommandController.createServiceRecipientCommand(SecurityContextHolder.getContext().getAuthentication(), params, createVmStr);

        // return what was expected previously - before a command was used
        if (commandResult.isCommandSuccessful()) {
            // TODO we could avoid this if BaseCommandHandler.createCommandAndHandleInternal was to return the id of the entity, not command
            var iId = IncidentController.EXTRACT_ID_FN.apply(commandResult.getLink(SELF.value()).getHref());
            var srId = incidentRepository.getServiceRecipientId(iId);
            // fudge the srId in the message for now
            return new Result(""+srId, iId);
        }
        // if an error - return it
        return commandResult;
    }

}
