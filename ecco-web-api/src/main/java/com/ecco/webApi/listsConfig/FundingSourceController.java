package com.ecco.webApi.listsConfig;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.ecco.webApi.controllers.NotFoundException;
import java.util.stream.Stream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.ecco.dao.FundingSourceRepository;
import com.ecco.dom.FundingSource;
import com.ecco.service.IdNameService;
import com.ecco.webApi.controllers.BaseWebApiController;

@PreAuthorize("hasRole('ROLE_STAFF')")
@Controller
public class FundingSourceController extends BaseWebApiController {

    private final IdNameService idNameService;
    private final FundingSourceFromViewModel fsFromViewModel;
    private final FundingSourceToViewModel fsToViewModel;
    private final FundingSourceRepository repository;


    @Autowired
    public FundingSourceController(IdNameService idNameService, FundingSourceRepository repository) {
        this.idNameService = idNameService;
        this.repository = repository;
        this.fsFromViewModel = new FundingSourceFromViewModel();
        this.fsToViewModel = new FundingSourceToViewModel();
    }

    @RequestMapping(value = "/fundingSource/{id}", method = GET, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    public FundingSourceViewModel findOne(@PathVariable long id) {
        return fsToViewModel.apply(repository.findById(id).orElse(null));
    }

    @RequestMapping(value = "/fundingSource/byName/{name}/", method = GET, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    public FundingSourceViewModel findFirstByName(@PathVariable String name) {
        List<FundingSource> fsList = repository.findAllByName(name);

        if (fsList.isEmpty()) {
            throw new NotFoundException(name);
        }
        else {
            return fsList.stream().findFirst().map(fsToViewModel).orElse(null);
        }
    }

    @RequestMapping(value = "/fundingSource/", method = GET, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    public Stream<FundingSourceViewModel> findAll() {
        return repository.findAll().stream().map(fsToViewModel);
    }

    @RequestMapping(value = "/fundingSource/", method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @ResponseBody
    public Map<String, Long> create(@RequestBody FundingSourceViewModel vm) {
        Assert.isNull(vm.id, "No id should be set on POST");
        Assert.hasText(vm.name, "FundingSource needs a name");

        final FundingSource fs = fsFromViewModel.apply(vm);

        // no deduplication but we shouldn't create the same origin twice by name
        List<FundingSource> matching = repository.findAllByName(vm.name);
        throwForExistingOrTooManyMatches(matching);

        repository.save(fs);
        idNameService.clearFromCache(FundingSource.class);

        return Collections.singletonMap("id", fs.getId());
    }

}
