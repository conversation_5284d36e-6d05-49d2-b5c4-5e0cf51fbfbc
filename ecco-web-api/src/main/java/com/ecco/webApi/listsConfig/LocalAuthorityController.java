package com.ecco.webApi.listsConfig;

import com.ecco.dao.LocalAuthorityRepository;
import com.ecco.dom.LocalAuthority;
import com.ecco.service.IdNameService;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.NotFoundException;

import java.util.stream.Stream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

@PreAuthorize("hasRole('ROLE_STAFF')")
@Controller
public class LocalAuthorityController extends BaseWebApiController {

    private final IdNameService idNameService;
    private final LocalAuthorityFromViewModel laFromViewModel;
    private final LocalAuthorityToViewModel laToViewModel;
    private final LocalAuthorityRepository repository;


    @Autowired
    public LocalAuthorityController(IdNameService idNameService, LocalAuthorityRepository repository) {
        this.idNameService = idNameService;
        this.repository = repository;
        this.laFromViewModel = new LocalAuthorityFromViewModel();
        this.laToViewModel = new LocalAuthorityToViewModel();
    }

    @RequestMapping(value = "/localAuthority/{id}", method = GET, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    public LocalAuthorityViewModel findOne(@PathVariable long id) {
        return laToViewModel.apply(repository.findById(id).orElse(null));
    }

    @RequestMapping(value = "/localAuthority/byName/{name}/", method = GET, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    public LocalAuthorityViewModel findFirstByName(@PathVariable String name) {
        List<LocalAuthority> laList = repository.findAllByName(name);

        if (laList.isEmpty()) {
            throw new NotFoundException(name);
        }
        else {
            return laList.stream().findFirst().map(laToViewModel).orElse(null);
        }
    }

    @RequestMapping(value = "/localAuthority/", method = GET, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    public Stream<LocalAuthorityViewModel> findAll() {
        return repository.findAll().stream().map(laToViewModel);
    }

    @RequestMapping(value = "/localAuthority/", method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @ResponseBody
    public Map<String, Long> create(@RequestBody LocalAuthorityViewModel vm) {
        Assert.isNull(vm.id, "No id should be set on POST");
        Assert.hasText(vm.name, "LocalAuthority needs a name");

        final LocalAuthority la = laFromViewModel.apply(vm);

        // no deduplication but we shouldn't create the same origin twice by name
        List<LocalAuthority> matching = repository.findAllByName(vm.name);
        throwForExistingOrTooManyMatches(matching);

        repository.save(la);
        idNameService.clearFromCache(LocalAuthority.class);

        return Collections.singletonMap("id", la.getId());
    }

}
