package com.ecco.webApi.listsConfig;

import com.ecco.dom.LocalAuthority;
import java.util.function.Function;

import org.jspecify.annotations.Nullable;

final class LocalAuthorityFromViewModel implements Function<LocalAuthorityViewModel, LocalAuthority> {

    @Nullable
    @Override
    public LocalAuthority apply(@Nullable LocalAuthorityViewModel input) {
        if (input == null) {
            throw new NullPointerException("input LocalAuthority must not be null");
        }

        LocalAuthority e = new LocalAuthority();
        // ignore id
        e.setName(input.name);

        return e;
    }

}
