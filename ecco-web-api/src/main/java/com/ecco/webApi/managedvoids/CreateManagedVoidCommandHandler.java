package com.ecco.webApi.managedvoids;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.commands.CreateServiceRecipientCommand;
import com.ecco.dom.managedvoids.ManagedVoid;
import com.ecco.repositories.managedvoids.ManagedVoidRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.CreateServiceRecipientParams;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.io.Serializable;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

public class CreateManagedVoidCommandHandler extends ServiceRecipientCommandHandler<CreateManagedVoidCommandViewModel,
        CreateServiceRecipientCommand, CreateServiceRecipientParams> {

    @Nonnull
    private final ManagedVoidRepository managedVoidRepository;
    private ManagedVoidFromViewModel managedVoidFromViewModel;

    public CreateManagedVoidCommandHandler(ObjectMapper objectMapper,
                                           @Nonnull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                           ManagedVoidRepository managedVoidRepository,
                                           ManagedVoidFromViewModel managedVoidFromViewModel) {
        super(objectMapper, serviceRecipientCommandRepository, CreateManagedVoidCommandViewModel.class);
        this.managedVoidRepository = managedVoidRepository;
        this.managedVoidFromViewModel = managedVoidFromViewModel;
    }

    @Override
    protected CreateServiceRecipientCommand createCommand(Serializable targetId, CreateServiceRecipientParams params, String requestBody,
                                                          CreateManagedVoidCommandViewModel viewModel, long userId) {
        Assert.state(params.getPrefix().equals(viewModel.getPrefix()), "prefix in body must match URI");

        return new CreateServiceRecipientCommand(
                viewModel.uuid,
                viewModel.timestamp,
                userId,
                requestBody,
                (Integer) targetId);
    }

    @Override
    protected CommandResult handleInternal(@NotNull Authentication auth, CreateServiceRecipientParams params, @NotNull CreateManagedVoidCommandViewModel viewModel) {
        int id = CreateManagedVoidCommandHandler.createManagedVoid(viewModel.getManagedVoidViewModel(), this.managedVoidFromViewModel,
                this.managedVoidRepository);
        var managedVoid = this.managedVoidRepository.getById(id);

        // return the srId for the createCommand
        return CommandResult.ofLink(linkToApi(methodOn(ManagedVoidController.class).getManagedVoid(id)).withSelfRel())
                .withTargetId(managedVoid.getServiceRecipient().getId());
    }

    public static int createManagedVoid(ManagedVoidViewModel viewModel, ManagedVoidFromViewModel fromViewModel,
                                   ManagedVoidRepository managedVoidRepository) {
        final ManagedVoid managedVoid = fromViewModel.apply(viewModel);
        managedVoidRepository.save(managedVoid);
        return managedVoid.getId();
    }

}
