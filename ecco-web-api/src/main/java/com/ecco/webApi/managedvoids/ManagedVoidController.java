package com.ecco.webApi.managedvoids;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.managedvoids.ManagedVoidServiceRecipient;
import com.ecco.repositories.managedvoids.ManagedVoidRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.CreateServiceRecipientCommandController;
import com.ecco.webApi.controllers.CreateServiceRecipientParams;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Nonnull;
import java.io.IOException;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.springframework.hateoas.IanaLinkRelations.SELF;


@RestController
@RequestMapping("/managedvoids")
public class ManagedVoidController extends BaseWebApiController {

    private final ManagedVoidRepository managedVoidRepository;
    private final ManagedVoidToViewModel managedVoidToViewModel;
    private final ObjectMapper objectMapper;
    private final CreateServiceRecipientCommandController createServiceRecipientCommandController;

    public ManagedVoidController(ManagedVoidRepository managedVoidRepository,
                                 ObjectMapper objectMapper,
                                 CreateServiceRecipientCommandController createServiceRecipientCommandController,
                                 ListDefinitionRepository listDefinitionRepository) {
        this.managedVoidRepository = managedVoidRepository;
        this.objectMapper = objectMapper;
        this.createServiceRecipientCommandController = createServiceRecipientCommandController;
        this.managedVoidToViewModel = new ManagedVoidToViewModel(listDefinitionRepository);
    }

    // see ContractController.EXTRACT_ID_FN
    // see BuildingController.EXTRACT_ID_FN
    public static Pattern extractIdPattern = Pattern.compile("\\w+/(\\d+)/");
    public static final Function<String, Integer> EXTRACT_ID_FN = (href) -> {
        Matcher matches = extractIdPattern.matcher(href);
        matches.find();
        String id = matches.group(1);
        return Integer.parseInt(id);
    };

    @GetJson("/{managedVoidId}/")
    public ManagedVoidViewModel getManagedVoid(@PathVariable int managedVoidId) {
        return managedVoidToViewModel.apply(managedVoidRepository.getById(managedVoidId));
    }

    @GetJson("/service-recipients/{srId}/")
    public ManagedVoidViewModel getManagedVoidBySrId(@PathVariable int srId) {
        return managedVoidToViewModel.apply(managedVoidRepository.findManagedVoidByServiceRecipientId(srId).get());
    }

    @PreAuthorize("isFullyAuthenticated()")
    @Nonnull
    Result createImport(ManagedVoidViewModel managedVoidViewModel) throws IOException {
        Assert.isNull(managedVoidViewModel.managedVoidId, "No id should be set on POST");

        CreateManagedVoidCommandViewModel createVm = new CreateManagedVoidCommandViewModel(managedVoidViewModel);
        CreateServiceRecipientParams params = new CreateServiceRecipientParams(ManagedVoidServiceRecipient.PREFIX);
        String createVmStr = objectMapper.writeValueAsString(createVm);
        Result commandResult = createServiceRecipientCommandController.createServiceRecipientCommand(SecurityContextHolder.getContext().getAuthentication(), params, createVmStr);

        // return what was expected previously - before a command was used
        if (commandResult.isCommandSuccessful()) {
            // TODO we could avoid this if BaseCommandHandler.createCommandAndHandleInternal was to return the id of the entity, not command
            long rId = ManagedVoidController.EXTRACT_ID_FN.apply(commandResult.getLink(SELF.value()).getHref());
            return new Result(rId);
        }
        // if an error - return it
        return commandResult;
    }

}
