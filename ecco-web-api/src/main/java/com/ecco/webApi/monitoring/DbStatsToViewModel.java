package com.ecco.webApi.monitoring;

import java.sql.Connection;
import java.sql.SQLException;



import java.util.function.Function;

import javax.sql.DataSource;

public class DbStatsToViewModel implements Function<Void,DbStatsViewModel> {

    private DataSource dataSource;

    public DbStatsToViewModel(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public DbStatsViewModel apply(Void input) {
        DbStatsViewModel result = new DbStatsViewModel();

        try (Connection connection = dataSource.getConnection()){
            result.canConnect = connection != null;
            if (connection != null) {
                connection.close();
            }
        } catch (SQLException e) {
            result.canConnect = false;
        }

        if (dataSource instanceof org.apache.tomcat.jdbc.pool.DataSource basicDataSource ) {
//            org.apache.tomcat.jdbc.pool.DataSource basicDataSource = (org.apache.tomcat.jdbc.pool.DataSource) dataSource;
            result.numActive = basicDataSource.getNumActive();
            result.numIdle = basicDataSource.getNumIdle();

            result.minIdle = basicDataSource.getMinIdle();

            result.maxActive = basicDataSource.getMaxActive();
            result.maxIdle = basicDataSource.getMaxIdle();

            result.minIdle = basicDataSource.getMinIdle();
        }

        return result;
    }
}
