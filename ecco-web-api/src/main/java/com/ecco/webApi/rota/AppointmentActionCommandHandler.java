package com.ecco.webApi.rota;

import com.ecco.calendar.event.RotaDemandPostChangeEvent;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.agreements.DemandSchedule;
import com.ecco.dom.commands.AppointmentActionCommand;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.rota.service.RotaDelegator;
import com.ecco.rota.service.RotaHandler;
import com.ecco.rota.service.RotaService;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.calendar.core.CalendarService;
import com.ecco.calendar.core.Recurrence.RecurrenceHandle;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;
import java.net.URI;
import java.time.LocalDateTime;

/**
 * Perform an action on an appointment - ALLOCATE / DEALLOCATE / DROP / REINSTATE
 */
@Component
public class AppointmentActionCommandHandler
        extends ServiceRecipientCommandHandler<AppointmentActionCommandDto, AppointmentActionCommand, @NonNull AppointmentActionParams> {

    @PersistenceContext
    EntityManager entityManager;

    @NonNull
    final MessageBus<ApplicationEvent> messageBus;

    @NonNull
    private final RotaService rotaService;

    @NonNull
    private final CalendarService calendarService;

    @NonNull
    private final RotaDelegator rotaDelegator;

    @NonNull
    private final EntityUriMapper entityUriMapper;

    @Autowired
    public AppointmentActionCommandHandler(
            @NonNull ObjectMapper objectMapper,
            @NonNull MessageBus<ApplicationEvent> messageBus,
            @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            @NonNull EntityUriMapper entityUriMapper,
            @NonNull RotaService rotaService,
            @NonNull CalendarService calendarService,
            @NonNull RotaDelegator rotaDelegator

    ) {
        super(objectMapper, serviceRecipientCommandRepository, AppointmentActionCommandDto.class);

        this.messageBus = messageBus;
        this.rotaService = rotaService;
        this.calendarService = calendarService;
        this.entityUriMapper = entityUriMapper;
        this.rotaDelegator = rotaDelegator;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @NonNull AppointmentActionParams params,
                                           @NonNull AppointmentActionCommandDto dto) {

        Assert.state(params.getResourceFilter().equals(dto.resourceFilter), "resourceFilter not matching");
        var handler = rotaDelegator.selectHandler(dto.resourceFilter, dto.demandFilter);

        switch (dto.operation.toLowerCase()) {
            case AppointmentActionCommandDto.OPERATION_ALLOCATE -> {
                allocateAppointment(handler, dto);
                return new CommandResult().withMessage("allocated");
            }
            case AppointmentActionCommandDto.OPERATION_DEALLOCATE -> {
                deallocateAppointment(dto);
                return new CommandResult().withMessage("deallocated");
            }
            case AppointmentActionCommandDto.OPERATION_DROP -> {
                dropAppointment(dto);
                return new CommandResult().withMessage("requirement dropped");
            }
            case AppointmentActionCommandDto.OPERATION_REINSTATE -> {
                reinstateAppointment(dto);
                return new CommandResult().withMessage("requirement reinstated");
            }
            default -> throw new IllegalArgumentException("cannot handle operation: " + dto.operation);
        }

        // there isn't a link for viewing/editing a single appointment really, so just return null
            // return CommandResult.ofLink(linkToApi(methodOn(RotaController.class).getAppointment(dto.activityRef)).withRel(REL_EDIT));
    }

    protected @NonNull AppointmentActionCommand persistCommand(@NonNull AppointmentActionCommand command) {
        AppointmentActionCommand cmdSaved = super.persistCommand(command);
        entityManager.flush();
        return cmdSaved;
    }

    private void reinstateAppointment(AppointmentActionCommandDto dto) {
        rotaService.reinstateActivity(RecurrenceHandle.fromString(dto.activityRef));
    }

    private void dropAppointment(AppointmentActionCommandDto dto) {
        rotaService.dropActivity(dto.serviceRecipientId, RecurrenceHandle.fromString(dto.activityRef), dto.dropReasonId);
    }

    private void deallocateAppointment(AppointmentActionCommandDto dto) {
        assert dto.deallocateResourceId != null;
        URI uri = this.entityUriMapper.uriForEntity(AppointmentActionCommandDto.class.getSimpleName(), dto.uuid);
        deallocateAppointment(dto.activityRef, uri, dto.resourceFilter, dto.demandFilter, dto.deallocateResourceId);
    }

    public void deallocateAppointment(@NonNull String activityRef, URI updatedBy, @NonNull String demandedResourceFilter,
                                      @NonNull String serviceRecipientFilter, int deallocateResourceId) {
        var handler = rotaDelegator.selectHandler(demandedResourceFilter, serviceRecipientFilter);
        calendarService.unconfirmRecurrence(RecurrenceHandle.fromString(activityRef), updatedBy,false, handler.getResourceCalendarId(deallocateResourceId));
    }

    private void allocateAppointment(RotaHandler handler, AppointmentActionCommandDto dto) {
        URI uri = this.entityUriMapper.uriForEntity(AppointmentActionCommandDto.class.getSimpleName(), dto.uuid);
        assert dto.allocateResourceId != null;
        this.allocateAppointment(handler, dto.activityRef, dto.allocateResourceId, dto.allocateRescheduledTo, uri);
    }

    public void allocateAppointment(RotaHandler handler, @NonNull String activityRef, int allocateResourceId,
                                    @Nullable LocalDateTime allocateRescheduledTo, URI updatedBy) {
        rotaService.allocateAndMaybeRescheduleActivity(handler, updatedBy, allocateResourceId, RecurrenceHandle.fromString(activityRef), allocateRescheduledTo);
    }

    @NonNull
    @Override
    protected AppointmentActionCommand createCommand(Serializable targetId, @NonNull AppointmentActionParams params,
                                                     @NonNull String requestBody,
                                                     @NonNull AppointmentActionCommandDto viewModel,
                                                     long userId) {
        assert params != null;
        Assert.state(params.serviceRecipientId.equals(viewModel.serviceRecipientId), "serviceRecipientId in body must match URI");

        // we just want the scheduleId, but getAppointmentSchedule guarantees it for legacy data
        DemandSchedule ds = rotaService.getAppointmentSchedule(viewModel.activityRef);


        messageBus.publishBeforeTxEnd(new RotaDemandPostChangeEvent(viewModel, ds.getAgreement().getServiceRecipientId(), viewModel.getCacheResetDate(), ds.getRecurringEntryHandle(), false));

        return new AppointmentActionCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody,
                params.serviceRecipientId, ds.getId());
    }

}
