package com.ecco.webApi.rota;

import com.ecco.dom.agreements.ClientSalesInvoice;
import com.ecco.dom.agreements.ClientSalesRotaInvoiceLine;
import com.ecco.finance.webApi.dto.ClientSalesInvoiceResource;
import com.ecco.rota.webApi.dto.ClientSalesRotaInvoiceDetailResource;
import com.ecco.webApi.GuavaResourceAssemblerSupport;
import org.springframework.hateoas.server.LinkBuilder;

import org.jspecify.annotations.NonNull;
import java.time.LocalDate;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static com.ecco.rota.webApi.dto.ClientSalesRotaInvoiceDetailResource.REL_LINES;
import static com.ecco.rota.webApi.dto.ClientSalesRotaInvoiceDetailResource.REL_PROFORMA_LINES;
import static com.ecco.finance.webApi.dto.SalesInvoice.Status.DRAFT;
import static com.ecco.finance.webApi.dto.SalesInvoice.Status.POSTED;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * @since 13/10/2016
 */
public class ClientSalesRotaInvoiceDetailResourceAssembler extends GuavaResourceAssemblerSupport<ClientSalesInvoice, ClientSalesInvoiceResource> {
    private final ClientSalesRotaInvoiceDetailResourceLineAssembler clientSalesInvoiceDetailResourceLineAssembler;

    public ClientSalesRotaInvoiceDetailResourceAssembler(ClientSalesRotaInvoiceDetailResourceLineAssembler clientSalesInvoiceDetailResourceLineAssembler) {
        super(RotaActivityInvoiceController.class, ClientSalesInvoiceResource.class);
        this.clientSalesInvoiceDetailResourceLineAssembler = clientSalesInvoiceDetailResourceLineAssembler;
    }

    @Override
    @NonNull
    public ClientSalesRotaInvoiceDetailResource toModel(ClientSalesInvoice clientSalesInvoice) {
        var lines = clientSalesInvoice.getLines().stream()
                .map(l -> (ClientSalesRotaInvoiceLine) l)
                .map(clientSalesInvoiceDetailResourceLineAssembler.withInvoice(clientSalesInvoice))
                .toList();
        final ClientSalesRotaInvoiceDetailResource resource = new ClientSalesRotaInvoiceDetailResource(clientSalesInvoice.getServiceRecipientId(),
                clientSalesInvoice.getId(), clientSalesInvoice.getInvoiceDate(),
                clientSalesInvoice.isPosted() ? POSTED : DRAFT,
                lines
        );
        if (!clientSalesInvoice.isPosted()) {
            resource.add(linkToProformaLines(clientSalesInvoice.getServiceRecipientId(), clientSalesInvoice.getInvoiceDate()).withRel(REL_PROFORMA_LINES));
        }
        resource.add(linkToInvoiceLines(clientSalesInvoice.getId()).withRel(REL_LINES));
        resource.add(linkToInvoiceDetail(clientSalesInvoice.getId()).withSelfRel());
        return resource;
    }

    private static LinkBuilder linkToInvoiceDetail(Integer invoiceId) {
        return linkToApi(methodOn(RotaActivityInvoiceController.class).fetchSingleInvoice(invoiceId));
    }

    private static LinkBuilder linkToProformaLines(Integer serviceRecipientId, LocalDate invoiceDate) {
        return linkToApi(methodOn(RotaActivityInvoiceController.class).findUninvoicedWork(serviceRecipientId, invoiceDate));
    }

    private static LinkBuilder linkToInvoiceLines(Integer invoiceId) {
        return linkToApi(methodOn(RotaActivityInvoiceController.class).fetchLinesForSingleInvoice(invoiceId));
    }
}
