package com.ecco.webApi.rota;

import com.ecco.dom.contracts.Contract;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.function.Function;

public class ContractToViewModel implements Function<Contract, ContractViewModel> {

    @Override
    public ContractViewModel apply(Contract input) {

        ContractViewModel result = new ContractViewModel();
        result.contractId = input.getId();
        result.serviceRecipientId = input.getServiceRecipientId();
        result.name = input.getName();
        result.agreedCharge = input.getAgreedCharge();
        result.contractTypeId = input.getContractType() != null ? input.getContractType().getId() : null;
        result.poNumbers = input.getPONumbers();
        result.startDateTime = LocalDateTime.ofInstant(input.getStartInstant(), ZoneOffset.UTC);
        result.endDateTime = input.getEndInstant() != null ? LocalDateTime.ofInstant(input.getEndInstant(), ZoneOffset.UTC) : null;
        return result;
    }

}
