package com.ecco.webApi.rota;

import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
public class ContractViewModel {

    public Integer contractId;

    public Integer serviceRecipientId;

    public String name;

    LocalDateTime startDateTime; // FIXME: Should be an inclusive LocalDate range

    LocalDateTime endDateTime;

    String poNumbers;

    BigDecimal agreedCharge;

    Integer contractTypeId;

}
