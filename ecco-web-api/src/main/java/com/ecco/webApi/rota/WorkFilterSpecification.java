package com.ecco.webApi.rota;

import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.joda.time.DateTime;

import java.time.LocalDate;

import static com.ecco.dom.QEvidenceSupportWork.evidenceSupportWork;
import static com.ecco.infrastructure.time.JodaToJDKAdapters.localDateToJoda;

/**
 * @since 14/10/2016
 */
@RequiredArgsConstructor
@AllArgsConstructor
@Getter
class WorkFilterSpecification {
    private LocalDate startDate;
    private final LocalDate endDate;

    // TODO: filter by service agreement, etc.

    BooleanExpression toPredicate() {
        DateTime endTimeExcl = localDateToJoda(endDate.plusDays(1)).toDateTimeAtStartOfDay();
        if (startDate == null) {
            return evidenceSupportWork.workDate.lt(endTimeExcl);
        }
        DateTime startTime = localDateToJoda(startDate).toDateTimeAtStartOfDay();
        return evidenceSupportWork.workDate.goe(startTime)
                .and(evidenceSupportWork.workDate.lt(endTimeExcl));
    }
}
