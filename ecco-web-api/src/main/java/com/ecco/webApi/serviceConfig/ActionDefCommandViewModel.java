package com.ecco.webApi.serviceConfig;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.UUID;

/**
 * Base command for configuration operations on outcomes
 */
@Slf4j
public class ActionDefCommandViewModel extends BaseCommandViewModel {

    @NonNull
    public String operation;
    @NonNull
    public UUID actionDefUuid;
    @Nullable
    public UUID actionGroupDefUuid; // only required on addition
    @Nullable
    public ChangeViewModel<String> nameChange;
    @Nullable
    public ChangeViewModel<Integer> orderByChange;
    @Nullable
    public ChangeViewModel<Boolean> disabledChange;

    // Distinguisher so its clear looking at the stored commands
    // what outcome it is - it could be this, the OutcomeThreatCommandViewModel,
    // or some OutcomeHr.
    // The url alone might not be as helpful to remember, or as reliable
    // when parsing audits to the screen.
    //@Nullable
    //public OutcomeType outcomeType;

    public boolean hasChanges() {
        return nameChange != null || orderByChange != null || disabledChange != null;
    }

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected ActionDefCommandViewModel() {
        super();
    }

    public ActionDefCommandViewModel(@NonNull String operation, @NonNull UUID actionDefUuid) {
        super(UriComponentsBuilder
                .fromUriString("config/actionDef/")
                .toUriString());
        this.operation = operation;
        this.actionDefUuid = actionDefUuid;
    }

    public boolean valid() {

        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_ADD)) {
            if (this.actionGroupDefUuid == null) {
                log.error("Required field: actionGroupDefUuid");
                return false;
            }
        }

        return valid;
    }

}
