package com.ecco.webApi.serviceConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import com.ecco.serviceConfig.dom.Action;
import com.ecco.serviceConfig.dom.ActionGroup;
import com.ecco.serviceConfig.dom.Outcome;
import org.jspecify.annotations.Nullable;

public class ActionsFromOutcomes implements Function<Iterable<Outcome>, List<Action>> {

    @Override
    @Nullable
    public List<Action> apply(@Nullable Iterable<Outcome> input) {
        if (input == null) {
            throw new NullPointerException("input List<Outcome> must not be null");
        }

        List<Action> as = new ArrayList<>();
        for (Outcome o : input) {
            for (ActionGroup ag : o.getActionGroups()) {
                as.addAll(ag.getActions());
            }
        }

        return as;
    }

}
