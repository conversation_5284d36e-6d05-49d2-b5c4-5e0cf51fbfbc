package com.ecco.webApi.serviceConfig;

import com.ecco.dom.Service;
import com.ecco.service.ServiceTypeService;
import com.ecco.serviceConfig.dom.Outcome;
import com.ecco.serviceConfig.dom.OutcomeSupport;
import com.ecco.serviceConfig.dom.OutcomeThreat;
import com.ecco.serviceConfig.dom.ServiceType;
import com.ecco.serviceConfig.repositories.ActionGroupDefRepository;
import com.ecco.serviceConfig.repositories.OutcomeRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.serviceConfig.viewModel.*;
import com.ecco.webApi.annotations.DeleteJson;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.http.HttpStatus;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;
import java.util.UUID;

import static java.util.stream.Collectors.toList;

/**
 * Allows JSON export/import of collections.  Any id's in the JSON are used to to indicate entries that are the
 * same in a many-to-many relationship (no checking of values is done).  We set these ids to collectionId and allow
 * Hibernate to assign PKs as normal.
 */
@RestController
public class OutcomeController extends BaseWebApiController {

    private final OutcomeRepository outcomeRepository;
    private final ActionGroupDefRepository actionGroupDefRepository;

    private final ServiceRepository serviceRepository;
    private final ServiceTypeService serviceTypeService;

    private final OutcomeToViewModel outcomeToViewModel = new OutcomeToViewModel();
    private final ActionGroupToViewModel actionGroupToViewModel = new ActionGroupToViewModel();
    private final OutcomeFromViewModel outcomeFromViewModel = new OutcomeFromViewModel();
    private final RiskAreaFromViewModel riskAreaFromViewModel = new RiskAreaFromViewModel();
    private final RiskAreaToViewModel riskAreaToViewModel = new RiskAreaToViewModel();

    @Autowired
    public OutcomeController(OutcomeRepository outcomeRepository,
                             ActionGroupDefRepository actionGroupDefRepository,
                             ServiceRepository serviceRepository,
                             ServiceTypeService serviceTypeService) {
        this.outcomeRepository = outcomeRepository;
        this.actionGroupDefRepository = actionGroupDefRepository;
        this.serviceRepository = serviceRepository;
        this.serviceTypeService = serviceTypeService;
    }

    @GetJson("/outcomes/byId/{id}")
    public OutcomeViewModel findOneById(@PathVariable long id) {
        return outcomeToViewModel.apply(outcomeRepository.findById(id).orElse(null));
    }

    @GetJson("/outcomes/{uuid}")
    public OutcomeViewModel findOneNeed(@PathVariable UUID uuid) {
        return outcomeToViewModel.apply(outcomeRepository.findOneNeedByUuid(uuid));
    }

    @GetJson("/actionGroupDefs/{uuid}")
    public ActionGroupViewModel findOneGroup(@PathVariable UUID uuid) {
        return actionGroupToViewModel.apply(actionGroupDefRepository.findOneByUuid(uuid));
    }

    @GetJson("/riskAreas/{uuid}")
    public RiskAreaViewModel findOneThreat(@PathVariable UUID uuid) {
        return riskAreaToViewModel.apply(outcomeRepository.findOneThreatByUuid(uuid));
    }

    /**
     * Get all outcomes (support and threat)
     */
    @GetJson("/outcomes/")
    public List<OutcomeViewModel> findAll() {
        return outcomeRepository.findAll().stream().map(outcomeToViewModel).collect(toList());
    }

    /**
     * Get all support outcomes
     */
    @GetJson("/outcomes/support/")
    public List<OutcomeViewModel> findAllSupport() {
        return outcomeRepository.findAllSupport().stream().map(outcomeToViewModel).collect(toList());
    }

    /**
     * Get all threat outcomes
     */
    @GetJson("/outcomes/threat/")
    public List<RiskAreaViewModel> findAllThreat() {
        return outcomeRepository.findAllThreat().stream().map(riskAreaToViewModel).collect(toList());
    }

    /**
     * Get all support outcomes associated with this serviceId
     */
    @GetJson("/outcomes/byServiceId/{serviceId}")
    public List<OutcomeViewModel> findAllByService(@PathVariable long serviceId) {
        Service s = serviceRepository.findById(serviceId).orElseThrow(NullPointerException::new);
        ServiceType st = serviceTypeService.getServiceType(s.getServiceTypeId());
        Set<OutcomeSupport> outcomesByService = st.getOutcomeSupports();
        return outcomesByService.stream().map(outcomeToViewModel).collect(toList());
    }

    /**
     * Get all support outcomes associated with this serviceName
     */
    @GetJson("/outcomes/byServiceName/{serviceName}")
    public List<OutcomeViewModel> findAllByService(@PathVariable String serviceName) {
        Service s = serviceRepository.findOneByName(serviceName);
        ServiceType st = serviceTypeService.getServiceType(s.getServiceTypeId());
        Set<OutcomeSupport> outcomesByService = st.getOutcomeSupports();
        return outcomesByService.stream().map(outcomeToViewModel).collect(toList());
    }

    /**
     * Get all threat outcomes associated with this serviceName
     */
    @GetJson("/outcomeThreats/byServiceName/{serviceName}")
    public List<OutcomeViewModel> findAllOutcomeThreatsByService(@PathVariable String serviceName) {
        Service s = serviceRepository.findOneByName(serviceName);
        ServiceType st = serviceTypeService.getServiceType(s.getServiceTypeId());
        Set<OutcomeThreat> outcomesByService = st.getOutcomeThreats();
        return outcomesByService.stream().map(outcomeToViewModel).collect(toList());
    }

    /**
     * @deprecated in favour of command approach - see ConfigController
     */
    @Deprecated
    @PostJson("/outcomes/support")
    public Result create(@RequestBody OutcomeViewModel outcomeViewModel) {

        if (outcomeViewModel.id != null) {
            Assert.isTrue(!outcomeRepository.existsById(outcomeViewModel.id), "An outcome already exists with that id");
        }

        final Outcome outcome = outcomeFromViewModel.apply(outcomeViewModel);

        // NB no deduplication as we haven't got anything in the ui on evidence screens to handle duplicate actions
        outcomeRepository.save(outcome);

        return new Result("created", outcome.getId());
    }

    /**
     * @deprecated in favour of command approach - see ConfigController
     */
    @Deprecated
    @PostJson("/outcomes/risk")
    public Result create(@RequestBody RiskAreaViewModel outcomeViewModel) {

        if (outcomeViewModel.id != null) {
            Assert.isTrue(!outcomeRepository.findById(outcomeViewModel.id).isPresent(), "An outcome already exists with that id");
        }

        final Outcome outcome = riskAreaFromViewModel.apply(outcomeViewModel);

        // NB no deduplication as we haven't got anything in the ui on evidence screens to handle duplicate actions
        outcomeRepository.save(outcome);

        return new Result("created", outcome.getId());
    }

    @DeleteJson("/outcomes/{outcomeId}")
    @ResponseStatus(HttpStatus.OK)
    public Result delete(@PathVariable long outcomeId) {
        outcomeRepository.deleteById(outcomeId);
        return new Result("deleted", outcomeId);
    }
}
