package com.ecco.webApi.serviceConfig;

import com.ecco.serviceConfig.service.RepositoryBasedServiceTypeService;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.NotFoundException;
import com.google.common.collect.ImmutableList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class ServiceTypeController extends BaseWebApiController {

    public static class Dto {
        public final ImmutableList<ServiceTypeViewModel> serviceTypes;

        public Dto(ImmutableList<ServiceTypeViewModel> serviceTypes) {
            this.serviceTypes = serviceTypes;
        }

        public ImmutableList<ServiceTypeViewModel> getServiceTypes() {
            return serviceTypes;
        }
    }

    private final RepositoryBasedServiceTypeService serviceTypeService;

    @Autowired
    public ServiceTypeController(RepositoryBasedServiceTypeService serviceTypeService) {
        this.serviceTypeService = serviceTypeService;
    }

    @GetJson("/servicetype/{id}")
    public ServiceTypeViewModel findOne(@PathVariable int id, HttpServletResponse response) {
        ServiceTypeViewModel serviceType = serviceTypeService.findOneDto(id);
        cacheModerately(response);
        return serviceType;
    }

    @GetJson("/servicetype/byName/{name}")
    public ServiceTypeViewModel findOneByName(@PathVariable String name, HttpServletResponse response) {
        ServiceTypeViewModel serviceType = getServiceTypeViewModels().stream()
                .filter(dto -> dto.name.equals(name))
                .findFirst()
                .orElseThrow(() -> new NotFoundException(name));
        cacheModerately(response);
        return serviceType;
    }

    @GetJson("/servicetypes/")
    public Iterable<ServiceTypeViewModel> findAll(HttpServletResponse response) {
        ImmutableList<ServiceTypeViewModel> allDtos = getServiceTypeViewModels();
        cacheModerately(response);
        return allDtos;
    }

    private ImmutableList<ServiceTypeViewModel> getServiceTypeViewModels() {
        List<Long> allIds = serviceTypeService.findAllIds();
        return allIds.stream()
                .map(id -> serviceTypeService.findOneDto(id.intValue()))
                .collect(Collectors.collectingAndThen(Collectors.toList(), ImmutableList::copyOf));
    }

}
