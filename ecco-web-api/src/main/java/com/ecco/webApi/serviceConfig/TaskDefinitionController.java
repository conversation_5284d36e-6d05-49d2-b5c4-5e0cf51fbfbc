package com.ecco.webApi.serviceConfig;

import com.ecco.serviceConfig.repositories.TaskDefinitionRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import com.google.common.collect.ImmutableList;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.stream.Collectors;

@RestController
public class TaskDefinitionController extends BaseWebApiController {

    private final TaskDefinitionRepository taskDefinitionRepository;
    private final TaskDefinitionToViewModel toViewModel = new TaskDefinitionToViewModel();
    private final TaskDefinitionCommandHandler taskDefinitionCommandHandler;

    @Autowired
    public TaskDefinitionController(TaskDefinitionRepository taskDefinitionRepository,
                                    TaskDefinitionCommandHandler taskDefinitionCommandHandler) {
        this.taskDefinitionRepository = taskDefinitionRepository;
        this.taskDefinitionCommandHandler = taskDefinitionCommandHandler;
    }

    @PostJson(value = "/service-config/taskDef/")
    public Result processTaskDefinitionCommand(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return taskDefinitionCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @GetJson("/taskDefinitions/")
    public Iterable<TaskDefinitionViewModel> findAll() {
        return getTaskDefinitionViewModels();
    }

    private ImmutableList<TaskDefinitionViewModel> getTaskDefinitionViewModels() {
        return taskDefinitionRepository.findAll().stream()
                .map(toViewModel)
                .collect(Collectors.collectingAndThen(Collectors.toList(), ImmutableList::copyOf));
    }

}
