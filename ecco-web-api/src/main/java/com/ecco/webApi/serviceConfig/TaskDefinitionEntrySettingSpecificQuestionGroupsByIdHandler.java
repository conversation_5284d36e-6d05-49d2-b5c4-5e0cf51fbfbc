package com.ecco.webApi.serviceConfig;

import com.ecco.infrastructure.util.FlagMap;
import com.ecco.serviceConfig.dom.*;
import com.ecco.serviceConfig.repositories.QuestionGroupRepository;
import com.ecco.serviceConfig.repositories.ServiceType_QuestionGroupRepository;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class TaskDefinitionEntrySettingSpecificQuestionGroupsByIdHandler implements TaskDefinitionEntrySettingSpecificHandler {

    @NonNull
    private final QuestionGroupRepository questionGroupRepository;
    @NonNull
    private final ServiceType_QuestionGroupRepository serviceTypeQuestionGroupRepository;

    @PersistenceContext
    private EntityManager entityManager;

    private static final String QUESTIONGROUPSBYID = "questionGroupsById";

    @Autowired
    public TaskDefinitionEntrySettingSpecificQuestionGroupsByIdHandler(
            @NonNull QuestionGroupRepository questionGroupRepository,
            @NonNull ServiceType_QuestionGroupRepository serviceTypeQuestionGroupRepository) {
        this.questionGroupRepository = questionGroupRepository;
        this.serviceTypeQuestionGroupRepository = serviceTypeQuestionGroupRepository;
    }


    @Override
    public boolean canHandle(ServiceType_TaskDefinitionSetting taskDefinitionEntrySetting) {
        return QUESTIONGROUPSBYID.equals(taskDefinitionEntrySetting.getName());
    }

    @Override
    public void applyChanges(ServiceType_TaskDefinitionSetting taskDefinitionEntrySetting, TaskDefinitionEntrySettingCommandViewModel viewModel) {
        applyQuestionGroups(taskDefinitionEntrySetting, viewModel);
    }

    private void applyQuestionGroups(ServiceType_TaskDefinitionSetting taskDefinitionEntrySetting, TaskDefinitionEntrySettingCommandViewModel viewModel) {

        final ImmutableSet<Long> questionGroupsToAdd = findIdsDifferences(
                                                            new FlagMap(taskDefinitionEntrySetting.getValue()),
                                                            new FlagMap(viewModel.valueChange != null ? viewModel.valueChange.to : ""));
        final ImmutableSet<Long> questionGroupsToRemove = findIdsDifferences(
                                                            new FlagMap(viewModel.valueChange != null ? viewModel.valueChange.to : ""),
                                                            new FlagMap(taskDefinitionEntrySetting.getValue()));
        if (questionGroupsToAdd.isEmpty() && questionGroupsToRemove.isEmpty()) {
            return;
        }

        long serviceTypeId = taskDefinitionEntrySetting.getServiceType_TaskDefinition().getMultiId().getServiceType().getId();
        ServiceType serviceType = this.entityManager.getReference(ServiceType.class, serviceTypeId);

        final Iterable<QuestionGroup> qgToAdd = findQuestionGroupsById(questionGroupsToAdd);
        qgToAdd.forEach(qg -> {
            ServiceType_QuestionGroup_MultiId stqgId = new ServiceType_QuestionGroup_MultiId(serviceType, qg);
            ServiceType_QuestionGroup stqg = new ServiceType_QuestionGroup(stqgId);
            // NB this doesn't update the servicetype version, but we really don't need to worry about that
            serviceTypeQuestionGroupRepository.save(stqg);
        });
        final Iterable<QuestionGroup> qgToRemove = findQuestionGroupsById(questionGroupsToRemove);
        qgToRemove.forEach(qg -> {
            ServiceType_QuestionGroup_MultiId stqgId = new ServiceType_QuestionGroup_MultiId(serviceType, qg);
            ServiceType_QuestionGroup stqg = new ServiceType_QuestionGroup(stqgId);
            // NB this doesn't update the servicetype version, but we really don't need to worry about that
            serviceTypeQuestionGroupRepository.delete(stqg);
        });
    }

    private ImmutableSet<Long> findIdsDifferences(Map<String, Boolean> from, Map<String, Boolean> to) {
        Set<String> differentStr = Sets.difference(to.keySet(), from.keySet()).immutableCopy();
        Set<Long> differentIds = new HashSet<>(differentStr.size());
        differentStr.forEach(i -> differentIds.add(Long.parseLong(i)));
        return ImmutableSet.copyOf(differentIds);
    }

    private Iterable<QuestionGroup> findQuestionGroupsById(final ImmutableSet<Long> idsToAdd) {
        List<QuestionGroupSupport> all = questionGroupRepository.findAll();
        return all.stream().filter(qg -> idsToAdd.contains(qg.getId())).collect(Collectors.toList());
    }

}
