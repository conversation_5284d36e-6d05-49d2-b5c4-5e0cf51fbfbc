package com.ecco.webApi.singleValue;

import com.ecco.dom.contacts.HistoryItem;
import com.ecco.dto.ChangeViewModel;

import java.time.LocalDateTime;

public interface HistoryItemCommandSupport<T extends HistoryItem, C extends HistoryItemCommandViewModel> {

    /**
     * Create the entity to be manipulated in HistoryItemCommandSupportHandler.
     * @param cmdVM The original view model
     * @return The constructed entity
     */
    T createItem(C cmdVM);

    /**
     * Opportunity to apply any other changes to the entity, beyond those of HistoryItemCommandSupportHandler.
     * @param item The history item
     * @param cmdVM The original view model
     */
    void applyChanges(T item, C cmdVM);

    /**
     * Callback to allow HistoryItemCommand<PERSON>upportHandler to join in the warning of valiable changes.
     * NB HistoryItemCommandSupportHandler doesn't itself extend base classes to get this functionality.
     * @param cmdVM
     * @param change
     * @param from
     * @param fieldName
     */
    void warnIfPrevValueDoesntMatchDelegate(C cmdVM, ChangeViewModel<LocalDateTime> change, LocalDateTime from, String fieldName);

}
