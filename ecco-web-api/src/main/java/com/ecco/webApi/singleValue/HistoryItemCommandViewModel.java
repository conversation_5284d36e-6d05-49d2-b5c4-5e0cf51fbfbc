package com.ecco.webApi.singleValue;

import com.ecco.webApi.evidence.BaseServiceRecipientCommandViewModel;
import com.ecco.dto.ChangeViewModel;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import java.time.LocalDateTime;

public abstract class HistoryItemCommandViewModel extends BaseServiceRecipientCommandViewModel {

    @NonNull
    public static final String OPERATION_ADD = "add";
    @NonNull
    public static final String OPERATION_UPDATE = "update";
    @NonNull
    public static final String OPERATION_REMOVE = "remove";

    public String operation;

    public Integer id; // not for new entries, just online so not using uuid currently

    @Nullable
    public ChangeViewModel<LocalDateTime> validFrom;

    public HistoryItemCommandViewModel() {
        super();
    }

    public HistoryItemCommandViewModel(String commandUri, Integer serviceRecipientId) {
        super(commandUri, serviceRecipientId);
    }

    public boolean hasChanges() {
        return validFrom != null;
    }

    @Override
    public String toString() {
        return "HistoryItemCommandViewModel [id=" + id + ", operation=" + operation + ", serviceRecipientId=" + serviceRecipientId
                + ", validFromChange=" + validFrom
                + ", uuid=" + uuid + ", commandUri=" + commandUri + ", timestamp=" + timestamp + "]";
    }

}
