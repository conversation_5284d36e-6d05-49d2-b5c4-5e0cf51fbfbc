package com.ecco.webApi.singleValue;

import java.io.IOException;

import org.jspecify.annotations.NonNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;

@RestController
public class SingleValueHistoryCommandController extends BaseWebApiController {

    @NonNull
    private final SingleValueHistoryCommandHandler singleValueHistoryCommandHandler;

    @Autowired
    public SingleValueHistoryCommandController(@NonNull SingleValueHistoryCommandHandler singleValueHistoryCommandHandler) {
        this.singleValueHistoryCommandHandler = singleValueHistoryCommandHandler;
    }

    @PostJson(path={"/service-recipients/{serviceRecipientId}/singlevaluehistory/{key}/command/",
                    "/service-recipients/{serviceRecipientId}/singlevaluehistory/command/"})
    public Result changeSingleValue(@NonNull Authentication authentication, @NonNull SingleValueHistoryParams params,
                                    @NonNull @RequestBody String requestBody) throws IOException {
        return singleValueHistoryCommandHandler.handleCommand(authentication, params, requestBody);
    }

}
