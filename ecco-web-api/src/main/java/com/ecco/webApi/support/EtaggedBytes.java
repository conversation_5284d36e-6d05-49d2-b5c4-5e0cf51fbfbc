package com.ecco.webApi.support;

import lombok.Getter;
import org.jspecify.annotations.Nullable;
import org.springframework.util.DigestUtils;


@Getter
public class EtaggedBytes {

    private final byte[] payload;
    private final String encoding;

    /** Shallow Etag of payload */
    private final String etag;

    public EtaggedBytes(byte[] payload, @Nullable String encoding) {
        this.payload = payload;
        this.encoding = encoding;
        etag = calculateShallowEtag(payload);
    }

    private String calculateShallowEtag(byte[] payload) {
        return generateETagHeaderValue(payload);
    }

    /** Derived from {@link org.springframework.web.filter.ShallowEtagHeaderFilter#generateETagHeaderValue} */
    protected String generateETagHeaderValue(byte[] bytes) {
        // length of W/ + 0 + " + 32bits md5 hash + "
        StringBuilder builder = new StringBuilder(37);
            builder.append("W/\"0");
        DigestUtils.appendMd5DigestAsHex(bytes, builder);
        builder.append('"');
        return builder.toString();
    }
}
