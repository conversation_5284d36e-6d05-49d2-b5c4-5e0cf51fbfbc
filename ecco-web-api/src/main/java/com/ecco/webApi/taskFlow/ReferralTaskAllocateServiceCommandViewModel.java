package com.ecco.webApi.taskFlow;

import com.ecco.dom.Referral;
import com.ecco.webApi.viewModels.ServicesViewModel;
import org.jspecify.annotations.NonNull;
import java.util.List;

/**
 * Creates a new referral on the services defined in 'allocations'.
 * Used in 'tasks' for 'allocate to' to indicate this referral is moving to other services.
 * This is the concept of central processing - delegating an incoming referral to appropriate service(s).
 * {@link Referral#parentReferral}
 */
public class ReferralTaskAllocateServiceCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    static String TASK_ALLOCATE = "allocate";
    static String TASK_ALLOCATETOSERVICE = "allocateToServices"; // the one defined in the db

    public ServicesViewModel allocations;

    // NB these are ServiceCategory ids
    // these need to show in the audits ideally
    @NonNull
    public List<Integer> allocationIds;

    /** For Jackson etc */
    @Deprecated
    ReferralTaskAllocateServiceCommandViewModel() {
        super();
    }

    public ReferralTaskAllocateServiceCommandViewModel(int serviceRecipientId, @NonNull List<Integer> allocationIds) {
        super(serviceRecipientId, TASK_ALLOCATE, null); // TODO: Can we get a taskHandle here?
        this.allocationIds = allocationIds;
    }

    @Override
    public String toString() {
        return "ReferralTaskAllocateCommandViewModel [allocations=" + allocations + ", taskName=" + taskName
                + ", serviceRecipientId=" + serviceRecipientId + ", uuid=" + uuid + ", commandUri=" + commandUri + ", timestamp="
                + timestamp + "]";
    }

}
