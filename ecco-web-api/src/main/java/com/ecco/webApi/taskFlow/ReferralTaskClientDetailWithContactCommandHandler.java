package com.ecco.webApi.taskFlow;

import com.ecco.dao.ClientRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.ClientDetail;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

@Component
public class ReferralTaskClientDetailWithContactCommandHandler
        extends ServiceRecipientTaskCommandHandler<ReferralTaskClientDetailCommandViewModel> {

    @NonNull
    private final ClientRepository clientRepository;

    @NonNull
    private final TaskClientDetailAbstractCommandHandler detailsHandler;

    @Autowired
    public ReferralTaskClientDetailWithContactCommandHandler(ObjectMapper objectMapper,
                                                             @NonNull WorkflowTaskController workflowTaskController,
                                                             @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                             @NonNull ClientRepository clientRepository,
                                                             @NonNull TaskClientDetailAbstractCommandHandler detailsHandler) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskClientDetailCommandViewModel.class);

        this.clientRepository = clientRepository;
        this.detailsHandler = detailsHandler;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               ReferralTaskClientDetailCommandViewModel vm) {

        ClientDetail c = clientRepository.findOneByServiceRecipientId(params.serviceRecipientId);

        this.detailsHandler.handleAbstractDetails(c, vm);

        if (vm.housingBenefitChange != null) {
            c.setHousingBenefit(vm.housingBenefitChange.to);
        }

        return null;
    }
}
