package com.ecco.webApi.taskFlow;

import org.jspecify.annotations.Nullable;

import org.joda.time.DateTime;

import com.ecco.dto.ChangeViewModel;

public class ReferralTaskEditDeliveredByCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    static String TASK_NAME = "deliveredBy";

    @Nullable
    public ChangeViewModel<Long> deliveredBy;

    @Nullable
    public ChangeViewModel<DateTime> deliveredByStartDate;

    public ReferralTaskEditDeliveredByCommandViewModel() {
        super();
    }

    public ReferralTaskEditDeliveredByCommandViewModel(int serviceRecipientId, String taskHandle) {
        super(serviceRecipientId, TASK_NAME, taskHandle);
    }
}
