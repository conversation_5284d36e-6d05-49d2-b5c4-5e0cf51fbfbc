package com.ecco.webApi.taskFlow;

import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.Map.Entry;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.ecco.dao.ReferralRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.Referral;
import com.ecco.dto.ChangeViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
public class ReferralTaskSPDataCommandHandler
        extends ServiceRecipientTaskCommandHandler<ReferralTaskSPDataCommandViewModel> {

    @PersistenceContext
    private EntityManager entityManager;

    @NonNull
    private final ReferralRepository referralRepository;

    @Autowired
    public ReferralTaskSPDataCommandHandler(ObjectMapper objectMapper,
                                            @NonNull WorkflowTaskController workflowTaskController,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            ReferralRepository referralRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskSPDataCommandViewModel.class);

        this.referralRepository = referralRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               ReferralTaskSPDataCommandViewModel vm) {
        Referral r = referralRepository.findByServiceRecipient_Id(params.serviceRecipientId);

        if (vm.choicesMapChanges != null) {
            for (Entry<String, ChangeViewModel<Integer>> entry : vm.choicesMapChanges.entrySet()) {
                r.getChoicesMap().put(entry.getKey(), entry.getValue().to);
            }
        }

        if (vm.textMapChanges != null) {
            for (Entry<String, ChangeViewModel<String>> entry : vm.textMapChanges.entrySet()) {
                r.getTextMap().put(entry.getKey(), entry.getValue().to);
            }
        }
        return null;
    }
}
