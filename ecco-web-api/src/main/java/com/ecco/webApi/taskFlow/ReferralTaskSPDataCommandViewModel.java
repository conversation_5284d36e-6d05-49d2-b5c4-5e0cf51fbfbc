package com.ecco.webApi.taskFlow;

import com.ecco.dto.ChangeViewModel;
import java.util.Map;

import org.jspecify.annotations.Nullable;

public class ReferralTaskSPDataCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    @Nullable
    public Map<String, ChangeViewModel<Integer>> choicesMapChanges;

    @Nullable
    public Map<String, ChangeViewModel<String>> textMapChanges;

    /** For Jackson etc */
    @Deprecated
    ReferralTaskSPDataCommandViewModel() {
        super();
    }

    public ReferralTaskSPDataCommandViewModel(int serviceRecipientId, String taskName, String taskHandle) {
        super(serviceRecipientId, taskName, taskHandle);
    }

    @Override
    public String toString() {
        return "ReferralTaskSPDataCommandViewModel [choicesMapChanges=" + choicesMapChanges
                + ", textMapChanges=" + textMapChanges
                + ", taskName=" + taskName + ", serviceRecipientId=" + serviceRecipientId
                + ", uuid=" + uuid + ", commandUri=" + commandUri + ", timestamp=" + timestamp + "]";
    }

}
