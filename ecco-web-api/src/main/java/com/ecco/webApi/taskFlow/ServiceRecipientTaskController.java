package com.ecco.webApi.taskFlow;

import com.ecco.dao.ReferralRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dao.commands.ServiceRecipientTaskCommandService;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.dom.commands.ReferralTaskUpdateCommand;
import com.ecco.dom.commands.ServiceRecipientTaskCommand;
import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import com.ecco.evidence.ParentChildResolver;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.evidence.ExtractTaskCommandViewModelJson;
import com.ecco.webApi.evidence.GoalCommandExtractCommandViewModelJson;
import com.ecco.webApi.incidents.IncidentDetailCommandHandler;
import com.ecco.webApi.repairs.RepairDetailCommandHandler;
import com.ecco.webApi.rota.ContractTaskContractDetailCommandHandler;
import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.workers.StaffJobDetailCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

import static com.ecco.security.SecurityUtil.getUser;
import static java.util.stream.Collectors.joining;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class ServiceRecipientTaskController extends BaseWebApiController {

    @NonNull
    private final ReferralRepository referralRepository;

    @NonNull
    private final ServiceRecipientRepository serviceRecipientRepository;

    @NonNull
    private final ParentChildResolver parentChildResolver;

    @NonNull
    private final ServiceRecipientCommandRepository serviceRecipientCommandRepository;

    /**
     * The "startOnService" command handler. The "start command" is referred to in teh API command list as "startOnService".
     *
     * @see ReferralTaskEditStartOnServiceCommandViewModel
     */
    @NonNull
    private final ReferralTaskStartOnServiceCommandHandler startOnServiceCommandHandler;

    @NonNull
    private final ReferralTaskAllocateWorkerCommandHandler allocateWorkerCommandHandler;

    @NonNull
    private final ServiceRecipientTaskCommandService taskCommandRepositoryService;

    @NonNull
    private final ReferralTaskReferralDetailsCommandHandler referralTaskReferralDetailsCommandHandler;

    @NonNull
    private final ReferralTaskWaitingListScoreCommandHandler referralTaskWaitingListScoreCommandHandler;

    @NonNull
    private final ReferralTaskAppropriateReferralCommandHandler appropriateReferralCommandHandler;

    @NonNull
    private final ReferralTaskAcceptOnServiceCommandHandler acceptOnServiceCommandHandler;

    @NonNull
    private final ReferralTaskAllocateServiceCommandHandler allocateCommandHandler;

    @NonNull
    private final ReferralTaskJoinCommandHandler joinCommandHandler;

    @NonNull
    private final ReferralTaskEditDetailsCommandHandler editDetailsCommandHandler;

    @NonNull
    private final ReferralTaskEditDataProtectionCommandHandler dataProtectionCommandHandler;

    @NonNull
    private final ReferralTaskEditAgreement1CommandHandler agreement1CommandHandler;

    @NonNull
    private final ReferralTaskEditAgreement2CommandHandler agreement2CommandHandler;

    @NonNull
    private final ReferralTaskEditAgreement3CommandHandler agreement3CommandHandler;

    @NonNull
    private final ReferralTaskEditAgreement4CommandHandler agreement4CommandHandler;

    @NonNull
    private final ReferralTaskEditAgreement5CommandHandler agreement5CommandHandler;

    @NonNull
    private final ReferralTaskEditAgreement6CommandHandler agreement6CommandHandler;

    @NonNull
    private final ReferralTaskEditAgreement7CommandHandler agreement7CommandHandler;

    @NonNull
    private final ReferralTaskEditAgreement8CommandHandler agreement8CommandHandler;

    @NonNull
    private final ReferralTaskEditAgreement9CommandHandler agreement9CommandHandler;

    @NonNull
    private final ReferralTaskEditAgreement10CommandHandler agreement10CommandHandler;

    @NonNull
    private final ReferralTaskEditConsentCommandHandler consentCommandHandler;

    @NonNull
    private final ReferralTaskEditEmergencyDetailsCommandHandler editEmergencyDetailsCommandHandler;

    @NonNull
    private final ReferralTaskDeliveredByCommandHandler deliveredByCommandHandler;

    @NonNull
    private final ReferralTaskEditSourceCommandHandler editSourceCommandHandler;

    @NonNull
    private final ReferralTaskEditDestinationCommandHandler editDestinationCommandHandler;

    @NonNull
    private final ReferralTaskSPDataCommandHandler spDataCommandHandler;

    @NonNull
    private final ReferralTaskExitCommandHandler exitCommandHandler;

    @NonNull
    private final ReferralTaskScheduleReviewsCommandHandler scheduleReviewsCommandHandler;

    @NonNull
    private final ReferralTaskFundingCommandHandler fundingCommandHandler;

    @NonNull
    private final ReferralTaskAssessmentDateCommandHandler referralTaskAssessmentDateCommandHandler;

    @NonNull
    private final ReferralTaskPendingStatusCommandHandler referralTaskPendingStatusCommandHandler;

    @NonNull
    private final ReferralTaskStatusCommandHandler referralTaskStatusCommandHandler;

    @NonNull
    private final ReferralTaskClientDetailWithContactCommandHandler clientDetailCommandHandler;

    @NonNull
    private final ContractTaskContractDetailCommandHandler contractDetailCommandHandler;

    @NonNull
    private final StaffTaskStaffDetailCommandHandler staffDetailCommandHandler;

    @NonNull
    private final StaffJobDetailCommandHandler staffJobDetailCommandHandler;

    @NonNull
    private final IncidentDetailCommandHandler incidentDetailCommandHandler;

    @NonNull
    private final RepairDetailCommandHandler repairDetailCommandHandler;

    @NonNull
    private final StaffTaskPrimaryLocationCommandHandler staffTaskPrimaryLocationCommandHandler;

    @NonNull
    private final ReferralTaskClientResidenceCommandHandler clientResidenceCommandHandler;

    @NonNull
    private final ObjectMapper objectMapper;

    @NonNull
    private final ExtractTaskCommandViewModelJson extractTaskCommandAsJsonBody;

    @NonNull
    private final GoalCommandExtractCommandViewModelJson<ServiceRecipientCommand> extractJsonBody;

    @SuppressWarnings("NullableProblems")
    @Autowired
    public ServiceRecipientTaskController(
            ParentChildResolver parentChildResolver,
            ReferralRepository referralRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            ServiceRecipientTaskCommandService taskCommandRepositoryCustom,
            @NonNull ContractTaskContractDetailCommandHandler contractDetailCommandHandler,
            @NonNull StaffTaskStaffDetailCommandHandler staffDetailCommandHandler,
            @NonNull StaffTaskPrimaryLocationCommandHandler staffTaskPrimaryLocationCommandHandler,
            ReferralTaskReferralDetailsCommandHandler referralTaskReferralDetailsCommandHandler,
            ReferralTaskWaitingListScoreCommandHandler referralTaskWaitingListScoreCommandHandler,
            ReferralTaskAppropriateReferralCommandHandler appropriateReferralCommandHandler,
            ReferralTaskAcceptOnServiceCommandHandler acceptOnServiceCommandHandler,
            ReferralTaskAllocateServiceCommandHandler allocateCommandHandler,
            ReferralTaskJoinCommandHandler joinCommandHandler,
            ReferralTaskEditDetailsCommandHandler editDetailsCommandHandler,
            ReferralTaskEditEmergencyDetailsCommandHandler editEmergencyDetailsCommandHandler,
            ReferralTaskEditSourceCommandHandler editSourceCommandHandler,
            ReferralTaskEditDestinationCommandHandler editDestinationCommandHandler,
            ReferralTaskSPDataCommandHandler spDataCommandHandler,
            ReferralTaskExitCommandHandler exitCommandHandler,
            ReferralTaskEditDataProtectionCommandHandler dataProtectionCommandHandler,
            ReferralTaskEditConsentCommandHandler consentCommandHandler,
            ReferralTaskEditAgreement1CommandHandler agreement1CommandHandler,
            ReferralTaskEditAgreement2CommandHandler agreement2CommandHandler,
            ReferralTaskEditAgreement3CommandHandler agreement3CommandHandler,
            ReferralTaskEditAgreement4CommandHandler agreement4CommandHandler,
            ReferralTaskEditAgreement5CommandHandler agreement5CommandHandler,
            ReferralTaskEditAgreement6CommandHandler agreement6CommandHandler,
            ReferralTaskEditAgreement7CommandHandler agreement7CommandHandler,
            ReferralTaskEditAgreement8CommandHandler agreement8CommandHandler,
            ReferralTaskEditAgreement9CommandHandler agreement9CommandHandler,
            ReferralTaskEditAgreement10CommandHandler agreement10CommandHandler,
            ReferralTaskScheduleReviewsCommandHandler scheduleReviewsCommandHandler,
            ReferralTaskStartOnServiceCommandHandler startOnServiceCommandHandler,
            ReferralTaskAllocateWorkerCommandHandler allocateWorkerCommandHandler,
            ReferralTaskDeliveredByCommandHandler deliveredByCommandHandler,
            ReferralTaskFundingCommandHandler fundingCommandHandler,
            ReferralTaskAssessmentDateCommandHandler referralTaskAssessmentDateCommandHandler,
            ReferralTaskPendingStatusCommandHandler referralTaskPendingStatusCommandHandler,
            ReferralTaskStatusCommandHandler referralTaskStatusCommandHandler,
            StaffJobDetailCommandHandler staffJobDetailCommandHandler,
            IncidentDetailCommandHandler incidentDetailCommandHandler,
            RepairDetailCommandHandler repairDetailCommandHandler,
            @NonNull ReferralTaskClientDetailWithContactCommandHandler clientDetailCommandHandler,
            @NonNull ReferralTaskClientResidenceCommandHandler clientResidenceCommandHandler,
            ObjectMapper objectMapper,
            @Qualifier("SvcRecExtractJson") GoalCommandExtractCommandViewModelJson<ServiceRecipientCommand> extractJsonBody,
            ExtractTaskCommandViewModelJson extractTaskCommandAsJsonBody) {
        this.parentChildResolver = parentChildResolver;
        this.referralRepository = referralRepository;
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.serviceRecipientCommandRepository = serviceRecipientCommandRepository;
        this.taskCommandRepositoryService = taskCommandRepositoryCustom;
        this.referralTaskReferralDetailsCommandHandler = referralTaskReferralDetailsCommandHandler;
        this.referralTaskWaitingListScoreCommandHandler = referralTaskWaitingListScoreCommandHandler;
        this.appropriateReferralCommandHandler = appropriateReferralCommandHandler;
        this.acceptOnServiceCommandHandler = acceptOnServiceCommandHandler;
        this.allocateCommandHandler = allocateCommandHandler;
        this.joinCommandHandler = joinCommandHandler;
        this.editDetailsCommandHandler = editDetailsCommandHandler;
        this.editEmergencyDetailsCommandHandler = editEmergencyDetailsCommandHandler;
        this.editSourceCommandHandler = editSourceCommandHandler;
        this.editDestinationCommandHandler = editDestinationCommandHandler;
        this.spDataCommandHandler = spDataCommandHandler;
        this.exitCommandHandler = exitCommandHandler;
        this.dataProtectionCommandHandler = dataProtectionCommandHandler;
        this.consentCommandHandler = consentCommandHandler;
        this.agreement1CommandHandler = agreement1CommandHandler;
        this.agreement2CommandHandler = agreement2CommandHandler;
        this.agreement3CommandHandler = agreement3CommandHandler;
        this.agreement4CommandHandler = agreement4CommandHandler;
        this.agreement5CommandHandler = agreement5CommandHandler;
        this.agreement6CommandHandler = agreement6CommandHandler;
        this.agreement7CommandHandler = agreement7CommandHandler;
        this.agreement8CommandHandler = agreement8CommandHandler;
        this.agreement9CommandHandler = agreement9CommandHandler;
        this.agreement10CommandHandler = agreement10CommandHandler;
        this.scheduleReviewsCommandHandler = scheduleReviewsCommandHandler;
        this.startOnServiceCommandHandler = startOnServiceCommandHandler;
        this.allocateWorkerCommandHandler = allocateWorkerCommandHandler;
        this.deliveredByCommandHandler = deliveredByCommandHandler;
        this.fundingCommandHandler = fundingCommandHandler;
        this.referralTaskAssessmentDateCommandHandler = referralTaskAssessmentDateCommandHandler;
        this.referralTaskPendingStatusCommandHandler = referralTaskPendingStatusCommandHandler;
        this.referralTaskStatusCommandHandler = referralTaskStatusCommandHandler;
        this.clientDetailCommandHandler = clientDetailCommandHandler;
        this.contractDetailCommandHandler = contractDetailCommandHandler;
        this.staffDetailCommandHandler = staffDetailCommandHandler;
        this.staffTaskPrimaryLocationCommandHandler = staffTaskPrimaryLocationCommandHandler;
        this.staffJobDetailCommandHandler = staffJobDetailCommandHandler;
        this.incidentDetailCommandHandler = incidentDetailCommandHandler;
        this.repairDetailCommandHandler = repairDetailCommandHandler;
        this.clientResidenceCommandHandler = clientResidenceCommandHandler;
        this.objectMapper = objectMapper;
        this.extractJsonBody = extractJsonBody;
        this.extractTaskCommandAsJsonBody = extractTaskCommandAsJsonBody;
    }

    /**
     * @return the JSON as a string array (using the same JSON we stored going in)
     */
    @GetJson("/referrals/{referralId}/tasks/{taskName}/")
    public String findCommandsByReferralAndTaskName(
            @PathVariable long referralId,
            @NonNull @PathVariable String taskName) {

        int serviceRecipientId = referralRepository.getServiceRecipientId(referralId);
        List<ServiceRecipientCommand> commands =
                serviceRecipientCommandRepository.findAllByServiceRecipientIdAndTaskName(serviceRecipientId, taskName);

        String joinedJson = commands.stream()
                .map(extractJsonBody)
                .collect(joining(","));
        return "[" + joinedJson + "]";
    }

    /**
     * @return the JSON as a string array (using the same JSON we stored going in)
     */
    @GetJson("/service-recipients/{serviceRecipientId}/commands/tasks/latest/")
    public String findLatestCommandsByServiceRecipientAndTaskName(
            @PathVariable int serviceRecipientId) {

        List<ServiceRecipientTaskCommand> allCommands = taskCommandRepositoryService.findAllLatestCommandPerTaskName(serviceRecipientId);
        String joinedJson = allCommands.stream().map(extractTaskCommandAsJsonBody).collect(joining(","));
        return "[" + joinedJson + "]";
    }


    @PostJson(value = {"/service-recipients/{serviceRecipientId}/tasks/{taskName}/", "/service-recipients/tasks/{taskName}/"})
    @ResponseStatus(HttpStatus.OK)
    public Result processTaskDefinitionEntryCommand(
            @NonNull Authentication authentication,
            ServiceRecipientTaskParams params,
            @NonNull @RequestBody String requestBody) throws IOException {

        switch(params.taskName) {
        case "clientWithContact":
            return clientDetailCommandHandler.handleCommand(authentication, params, requestBody);
        case "contractDetail":
            return contractDetailCommandHandler.handleCommand(authentication, params, requestBody);
        case "staffDetail":
            return staffDetailCommandHandler.handleCommand(authentication, params, requestBody);
        case "staffLocation":
            return staffTaskPrimaryLocationCommandHandler.handleCommand(authentication, params, requestBody);
        case "accommodation":
            return clientResidenceCommandHandler.handleCommand(authentication, params, requestBody);
        case "referralDetails":
            return referralTaskReferralDetailsCommandHandler.handleCommand(authentication, params, requestBody);
        case "waitingListCriteria":
            return referralTaskWaitingListScoreCommandHandler.handleCommand(authentication, params, requestBody);
        case "referralAccepted":
            return appropriateReferralCommandHandler.handleCommand(authentication, params, requestBody);
        case "decideFinal":
            return acceptOnServiceCommandHandler.handleCommand(authentication, params, requestBody);
        case "allocateWorker":
            return allocateWorkerCommandHandler.handleCommand(authentication, params, requestBody);
        // should be 'start'
        case "start":
        case "startOnService":
            return startOnServiceCommandHandler.handleCommand(authentication, params, requestBody);
        case "deliveredBy":
            return deliveredByCommandHandler.handleCommand(authentication, params, requestBody);
        case "allocate":
            return allocateCommandHandler.handleCommand(authentication, params, requestBody);
        case "join":
            return joinCommandHandler.handleCommand(authentication, params, requestBody);
        case "days attending":
            return handleDaysAttending(authentication, params, requestBody);
        case "editDetails":
            return editDetailsCommandHandler.handleCommand(authentication, params, requestBody);
        case "dataProtection":
            return dataProtectionCommandHandler.handleCommand(authentication, params, requestBody);
        case "consent":
            return consentCommandHandler.handleCommand(authentication,  params, requestBody);
        case "agreement":
            return agreement1CommandHandler.handleCommand(authentication,  params, requestBody);
        case "agreement2":
            return agreement2CommandHandler.handleCommand(authentication,  params, requestBody);
        case "agreement3":
            return agreement3CommandHandler.handleCommand(authentication,  params, requestBody);
        case "agreement4":
            return agreement4CommandHandler.handleCommand(authentication,  params, requestBody);
        case "agreement5":
            return agreement5CommandHandler.handleCommand(authentication,  params, requestBody);
        case "agreement6":
            return agreement6CommandHandler.handleCommand(authentication,  params, requestBody);
        case "agreement7":
            return agreement7CommandHandler.handleCommand(authentication,  params, requestBody);
        case "agreement8":
            return agreement8CommandHandler.handleCommand(authentication,  params, requestBody);
        case "agreement9":
            return agreement9CommandHandler.handleCommand(authentication,  params, requestBody);
        case "agreement10":
            return agreement10CommandHandler.handleCommand(authentication,  params, requestBody);
        case "emergencyDetails":
            return editEmergencyDetailsCommandHandler.handleCommand(authentication, params, requestBody);
        case "scheduleReviews":
            return scheduleReviewsCommandHandler.handleCommand(authentication, params, requestBody);
        case "editSource":
            return editSourceCommandHandler.handleCommand(authentication, params, requestBody);
        case "editDestination":
            return editDestinationCommandHandler.handleCommand(authentication, params, requestBody);
        case "initial-sp_data":
            return spDataCommandHandler.handleCommand(authentication, params, requestBody);
        case "exit-sp_data":
            return spDataCommandHandler.handleCommand(authentication, params, requestBody);
        case "exit": // reserve for any rename of close to exit
        case "close": // NB close is the task definition name but it actually exits - a closed file means either exited or rejected
            return exitCommandHandler.handleCommand(authentication, params, requestBody);
        case "funding":
            return fundingCommandHandler.handleCommand(authentication, params, requestBody);
        case "assessmentDate":
            return referralTaskAssessmentDateCommandHandler.handleCommand(authentication, params, requestBody);
        case "pendingStatus":
            return referralTaskPendingStatusCommandHandler.handleCommand(authentication, params, requestBody);
        case "jobDetails":
            return staffJobDetailCommandHandler.handleCommand(authentication, params, requestBody);
        case "incidentDetails":
            return incidentDetailCommandHandler.handleCommand(authentication, params, requestBody);
        case "repairDetails":
            return repairDetailCommandHandler.handleCommand(authentication, params, requestBody);
        case ReferralTaskStatusCommandViewModel.TASK_NAME:
            return referralTaskStatusCommandHandler.handleCommand(authentication, params, requestBody);

        default:
            throw new IllegalArgumentException("Unrecognised command name: " + params.taskName);
        }
    }


    private Result handleDaysAttending(
            @NonNull Authentication authentication, ServiceRecipientTaskParams params,
            @NonNull String requestBody) throws IOException {

       DaysAttendingUpdateCommandViewModel viewModel = objectMapper.readValue(requestBody, DaysAttendingUpdateCommandViewModel.class);

       ServiceRecipientCommand duplicate = serviceRecipientCommandRepository.findOneByUuid(viewModel.uuid);

       if (duplicate != null) {
           return new Result("command ignored", duplicate.getId());
       }

       long userId = getUser(authentication).getId();
       ReferralTaskUpdateCommand saved = serviceRecipientCommandRepository.save(
               new ReferralTaskUpdateCommand(
                       viewModel.uuid,
                       viewModel.timestamp,
                       userId,
                       requestBody,
                       viewModel.serviceRecipientId,
                       viewModel.taskName));

       DaysOfWeek days = DaysOfWeek.fromBits(viewModel.daysAttendingChange.to);

       referralRepository.updateMeetingDays(params.serviceRecipientId, days.isSunday(), days.isMonday(), days.isTuesday(),
               days.isWednesday(), days.isThursday(), days.isFriday(), days.isSaturday());

       return new Result(Result.COMMAND_APPLIED, saved.getId());
   }

}
