package com.ecco.webApi.taskFlow;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

/** Params for the path variables */
public final class ServiceRecipientTaskParams {

    @Nullable
    public final Integer serviceRecipientId;

    @NonNull
    public final String taskName;

    public ServiceRecipientTaskParams(@Nullable Integer serviceRecipientId, @NonNull String taskName) {
        this.serviceRecipientId = serviceRecipientId;
        this.taskName = taskName;
    }

    @Nullable
    public Integer getServiceRecipientId() {
        return this.serviceRecipientId;
    }

    @NonNull
    public String getTaskName() {
        return this.taskName;
    }
}
