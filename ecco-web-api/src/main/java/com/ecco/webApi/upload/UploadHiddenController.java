package com.ecco.webApi.upload;

import com.ecco.dom.upload.UploadedFile;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.web.WebSlice;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.service.upload.UploadService;
import com.ecco.web.upload.UploadConfig;
import com.ecco.web.upload.UploadConfigGenerator;
import com.ecco.web.upload.UploadErrorResult;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Controller
@WebSlice("api")
@RequiredArgsConstructor
public class UploadHiddenController {

    private static final Logger log = LoggerFactory.getLogger(UploadHiddenController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final ApplicationProperties appProps;
    private final UploadService uploadService;
    private final UploadConfigGenerator uploadConfigGenerator;
    private final MessageSourceAccessor messageSource;

    @RequestMapping(produces="text/plain", value = "/secure/uploadHiddenJs.html", method = RequestMethod.POST)
    public @ResponseBody String uploadWithJs(MultipartHttpServletRequest multipartRequest) throws JsonProcessingException {

        try {
            // process the files held in the multipart - they could be in memory, or in temporary storage
            // "By default, the plugin uploads each selected file with an individual request" - see the multifileRequest option - https://github.com/blueimp/jQuery-File-Upload/wiki/Options
            for (MultipartFile multipartFile : multipartRequest.getFileMap().values()) {

                if (multipartFile == null || multipartFile.getOriginalFilename().length() == 0) {
                    continue;
                } else if (multipartFile.getSize() == 0) {

                    // we can't reject a non-existent field so do a global reject
                    // we want to aggregate the messages but since its global, and this is the only error that
                    // can occur now - and its minor - we simple let it go and put an appropriate plural message
                    return UploadErrorResult.error(multipartFile.getOriginalFilename(), multipartFile.getContentType(), multipartFile.getSize(), messageSource.getMessage("upload.sizeZero")).toJson();
                }

                // if it is a file, we process it
                UploadConfig<?> config = uploadConfigGenerator.constructUploadConfig(multipartRequest, multipartFile);
                final UploadedFile uploadedFile = uploadService.processFile(config);
                return objectMapper.writeValueAsString(new UploadedFileResourceAssembler(appProps.getApplicationRootPath()).toModel(uploadedFile));
            }

            // these errors don't have the ability to capture file info - just a stream of 'length' - see commons FileUploadBase and its RequestContext
            // size is specified in misc.properties ${misc.upload.maxSize}, which sets the applicationContext-web-common customMultipartResolver
            // it is also set in mysql, /etc/my.cnf under max_allowed_packet
        } catch (MaxUploadSizeExceededException e) {
            return maxSizeExceeded();
        } catch (MultipartException e) {
            return multiPartException(e);
        }

        return UploadErrorResult.error("upload.endOfController").toJson();
    }

    @ExceptionHandler(MultipartException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    private String multiPartException(MultipartException e) {
        // we get here on io interruption (eg cancel, or a page reload etc)
        log.warn("problem uploading - a cancel?: " + e);
        return UploadErrorResult.error(messageSource.getMessage("upload.problem")).toJson();
        // or return the error in the model?
        //model.put("message", "CANCELLED");
    }

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public String maxSizeExceeded() {
        return UploadErrorResult.error(messageSource.getMessage("upload.sizeExceeded")).toJson();
    }

    @RequestMapping(value = "/secure/uploadRemoveHiddenJs.html", method = RequestMethod.GET)
    public void deleteJs(@RequestParam(value="id") Integer fileId, HttpServletRequest request, HttpServletResponse response) {
        uploadService.deleteFile(fileId, uploadConfigGenerator.constructUploadConfig(request, null));
        response.setStatus(HttpStatus.OK.value());
    }

}
