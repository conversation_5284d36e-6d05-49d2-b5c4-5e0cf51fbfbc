package com.ecco.webApi.upload;

import com.ecco.infrastructure.web.UriUtils;
import com.ecco.dom.upload.SimpleUploadedFile;
import com.ecco.dom.upload.UploadedFile;
import com.ecco.dom.upload.UploadedFile.Source;
import com.ecco.upload.webapi.UploadedFileResource;
import org.jspecify.annotations.NonNull;
import org.springframework.hateoas.server.LinkBuilder;
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport;

import java.util.function.Function;

/**
 * @since 07/07/2014
 */
public class UploadedFileResourceAssembler extends RepresentationModelAssemblerSupport<UploadedFile, UploadedFileResource> implements Function<UploadedFile, UploadedFileResource> {
    private static final String REL_ENCLOSURE = "enclosure";
    private final String contextPath;

    public UploadedFileResourceAssembler(String contextPath) {
        super(UploadedFileController.class, UploadedFileResource.class);
        this.contextPath = contextPath;
    }

    @Override
    @NonNull
    public UploadedFileResource toModel(UploadedFile entity) {
        UploadedFileResource resource = new UploadedFileResource();
        resource.fileId = entity.getId();
        resource.filename = entity.getFilename();
        resource.size = entity.getSize();
        resource.bytesId = entity.getUploadedBytes().getId();
        if (entity instanceof SimpleUploadedFile) {
            resource.type = ((SimpleUploadedFile) entity).getMediaType();
        }
        addLinks(entity, resource);
        return resource;
    }


    private void addLinks(UploadedFile entity, UploadedFileResource resource) {
        addEnclosureLink(entity.getSource(), resource);
    }

    private void addEnclosureLink(Source source, UploadedFileResource resource) {
        LinkBuilder link = null;

        if (source == Source.SERVICE_RECIPIENT || source == Source.NONE) {
            // DownloadHiddenController only handles certain attachment types.
            link = linkToDownload(source.toString(), resource.fileId);
        }

        if (link != null) {
            resource.add(link.withRel(REL_ENCLOSURE));
        }
    }

    // downloadHidden hateoas link for evidence
    private LinkBuilder linkToDownload(String source, long fileId) {
        return UriUtils.hostRelativeLinkBuilder()
                .slash("api").slash("secure").slash("downloadHidden.html?id=" + fileId + "&source="+source);
    }

    @Override
    public UploadedFileResource apply(UploadedFile input) {
        return toModel(input);
    }
}
