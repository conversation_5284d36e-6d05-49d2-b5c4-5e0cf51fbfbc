package com.ecco.webApi.users;

import com.ecco.infrastructure.web.WebSlice;
import com.ecco.security.dom.UserDevice;
import com.ecco.service.security.EncryptionManagerService;
import com.ecco.webApi.viewModels.UserDeviceViewModel;
import com.google.common.net.HttpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.IanaLinkRelations;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.URI;
import java.net.UnknownHostException;
import java.util.UUID;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@RestController
@WebSlice("api")
@RequestMapping("/keys")
public class KeyController {
    private final EncryptionManagerService encryptionManagerService;
    private final UserDeviceToViewModel userDeviceToViewModel = new UserDeviceToViewModel();


    @Autowired
    public KeyController(EncryptionManagerService encryptionManagerService) {
        this.encryptionManagerService = encryptionManagerService;
    }


    @GetMapping("/userDevices/valid/{guid}/")
    @PreAuthorize("hasRole('ROLE_USER')")
    public HttpEntity<UserDeviceViewModel> getValidUserDeviceKey(@PathVariable("guid") String guid) {
        final UserDevice validatedDevice = encryptionManagerService.findValidatedDevice(UUID.fromString(guid));
        if (validatedDevice != null && validatedDevice.getUser().getUsername().equals(getCurrentUsername())) {
            final UserDeviceViewModel viewModel = userDeviceToViewModel.apply(validatedDevice);
            return new ResponseEntity<>(viewModel, HttpStatus.OK);
        } else {
            return notFoundResponse(guid);
        }
    }

    @PostMapping(value = "/userDevices/valid/{guid}/", produces = APPLICATION_JSON_VALUE)
    @PreAuthorize("hasRole('ROLE_USER')")
    public HttpEntity<UserDeviceViewModel> revalidateUserDeviceKey(@PathVariable("guid") String guid) {
        try {
            final UserDevice userDevice = encryptionManagerService.revalidateDevice(UUID.fromString(guid));
            return new ResponseEntity<>(userDeviceToViewModel.apply(userDevice), HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            return notFoundResponse(guid);
        }
    }

    @DeleteMapping(value = "/userDevices/valid/{guid}/", produces = APPLICATION_JSON_VALUE)
    @PreAuthorize("hasRole('ROLE_SYSADMIN')")
    public UserDeviceViewModel invalidateUserDeviceKey(@PathVariable("guid") String guid) {
        final UserDevice validatedDevice = encryptionManagerService.findValidatedDevice(UUID.fromString(guid));
        if (validatedDevice != null) {
            final UserDevice invalidatedDevice = encryptionManagerService.invalidateDevice(validatedDevice, null);
            return userDeviceToViewModel.apply(invalidatedDevice);
        } else {
            return invalidViewModel(guid);
        }
    }

    // typically, this is the first request of the API via the form login
    @PostMapping(value = "/userDevices/valid/", produces = APPLICATION_JSON_VALUE)
    @PreAuthorize("hasRole('ROLE_USER')")
    public HttpEntity<UserDeviceViewModel> createNewUserDeviceKey(HttpServletRequest request) throws UnknownHostException {
        final UserDevice userDevice = encryptionManagerService.createDeviceForCurrentUser(null, request.getHeader(HttpHeaders.USER_AGENT),
                InetAddress.getByName(getIpAddr(request)));
        final UserDeviceViewModel viewModel = userDeviceToViewModel.apply(userDevice);
        final org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
        headers.setLocation(URI.create(viewModel.getRequiredLink(IanaLinkRelations.SELF).getHref()));
        return new ResponseEntity<>(viewModel, headers, HttpStatus.CREATED);
    }

    private HttpEntity<UserDeviceViewModel> notFoundResponse(String guid) {
         return new ResponseEntity<>(invalidViewModel(guid), HttpStatus.NOT_FOUND);
    }

     private UserDeviceViewModel invalidViewModel(String guid) {
         final UserDeviceViewModel viewModel = new UserDeviceViewModel();
         viewModel.guid = guid;
         viewModel.valid = false;
         return viewModel;
     }

    private String getCurrentUsername() {
        return SecurityContextHolder.getContext().getAuthentication().getName();
    }

    private String getIpAddr(HttpServletRequest request) {
       String ip = request.getHeader("x-forwarded-for");
       if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
           ip = request.getHeader("Proxy-Client-IP");
       }
       if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
           ip = request.getHeader("WL-Proxy-Client-IP");
       }
       if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
           ip = request.getRemoteAddr();
       }
       return ip;
    }
}
