package com.ecco.webApi.users;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.commands.UserAccessAuditCommand;
import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.joda.time.DateTime;
import org.springframework.security.core.Authentication;

import org.jspecify.annotations.Nullable;
import java.io.Serializable;
import java.util.function.Predicate;

public class UserAccessAuditCommandHandler extends ServiceRecipientCommandHandler<UserAccessAuditCommandDto, ServiceRecipientCommand, @NonNull UserAccessAuditParams> {
    public UserAccessAuditCommandHandler(ObjectMapper objectMapper, ServiceRecipientCommandRepository serviceRecipientCommandRepository) {
        super(objectMapper, serviceRecipientCommandRepository, UserAccessAuditCommandDto.class);
    }

    @NonNull
    @Override
    protected UserAccessAuditCommand createCommand(
            Serializable targetId,
            @NonNull UserAccessAuditParams params,
            @NonNull String requestBody,
            @NonNull UserAccessAuditCommandDto viewModel,
            long userId) {
        //noinspection ConstantConditions
        return new UserAccessAuditCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody,
                viewModel.serviceRecipientId, viewModel.taskName);
    }

    @NonNull
    @Override
    protected ServiceRecipientCommand persistCommand(@NonNull ServiceRecipientCommand command) {
        var auditCommand = (UserAccessAuditCommand)command;

        // Only save if they've not accessed this svcRec for this task, at this level today already
        var midnightThisMorning = DateTime.now().withMillisOfDay(0);
//        var predicate = userAccessAuditCommand.serviceRecipientId.eq(command.getServiceRecipientId())
//                .and(userAccessAuditCommand.userId.eq(command.getUserId()))
//                .and(userAccessAuditCommand.taskName.eq(command.getTaskName()))
//                .and(userAccessAuditCommand.created.after(midnightThisMorning));
        if (((ServiceRecipientCommandRepository) commandRepository).findAllByServiceRecipientIdAndCreatedGreaterThanEqualOrderByCreatedDesc(
                command.getServiceRecipientId(), midnightThisMorning)
                .stream()
                .anyMatch(this.isMatchFor(auditCommand))) {
            return auditCommand;
        }
        return super.persistCommand(auditCommand);
    }

    private Predicate<? super ServiceRecipientCommand> isMatchFor(UserAccessAuditCommand command) {
        return it -> {
            try {
                var itDto = objectMapper.readValue(it.getBody(), UserAccessAuditCommandDto.class);
                var commandDto = objectMapper.readValue(command.getBody(), UserAccessAuditCommandDto.class);
                return it.getUserId() == command.getUserId()
                        && "userAccessAudit".equals(it.getCommandName())
                        && itDto.level.equals(commandDto.level)
                        && itDto.taskName.equals(commandDto.taskName);
            } catch (JsonProcessingException e) {
                throw new IllegalStateException(e);
            }
        };
    }


    @Nullable
    @Override
    protected CommandResult handleInternal(
            @NonNull Authentication auth, @NonNull UserAccessAuditParams userAccessAuditParams,
            @NonNull UserAccessAuditCommandDto viewModel) {
        // Nothing to do other than save the audit
        return null;
    }
}
