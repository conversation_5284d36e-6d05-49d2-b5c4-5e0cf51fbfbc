package com.ecco.webApi.users;

import com.ecco.buildings.repositories.AddressRepository;
import com.ecco.hr.dao.WorkerRepository;
import com.ecco.security.config.AclConfig;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.security.service.UserManagementService;
import com.ecco.security.service.UserManagementServiceImpl;
import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.viewModels.UserViewModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJsonReturningJson;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static java.util.stream.Collectors.toList;

@Secured("ROLE_ADMINLOGIN")
@RestController
public class UserController extends UserBaseController {

    @Autowired
    public UserController(UserManagementService userManagementService,
                            WorkerRepository workerRepository,
                            IndividualRepository individualRepository,
                            AddressRepository addressRepository, AclConfig aclConfig) {
        super(userManagementService, workerRepository, individualRepository, addressRepository, aclConfig);
    }

    @GetJson("/users/{username}/")
    public UserViewModel findOneByUsername(@PathVariable String username) {
        return userResourceAssembler.apply(userManagementService.loadUserByUsername(username));
    }

    @GetJson("/contacts/{contactId}/user/")
    public UserViewModel findOneByContactId(@PathVariable long contactId) {
        return userResourceAssembler.apply(individualRepository.findOne(contactId).getUser());
    }

    @GetJson("/usersWithoutWorker/")
    public List<UserViewModel> usersWithoutWorker() {
        return workerRepository.findEnabledStaffOrCarerUsersWithoutLinkedWorker()
                .map(userResourceAssembler::apply)
                .collect(toList());
    }

    @PostJsonReturningJson(value = "/users/")
    @ResponseStatus(HttpStatus.CREATED)
    public Result create(@RequestBody UserViewModel userViewModel) {
        Assert.isNull(userViewModel.userId, "No id should be set on POST");
        Assert.notNull(userViewModel.username, "username should be set on POST");

        // for creating, we check the username doesn't exist
        boolean exists = userManagementService.userExists(userViewModel.username);
        Assert.isTrue(!exists, "username already exists");

        final User user = userFromViewModel.apply(userViewModel);
        userManagementService.createUser(user);

        return new Result(user.getId());
    }

    @PostJsonReturningJson( "/individuals/{contactId}/user/{username}/")
    @ResponseStatus(HttpStatus.CREATED)
    public Result createUserFromContact(@PathVariable Long contactId, @PathVariable String username) {
        User user = UserManagementServiceImpl.createDefaultUser();
        user.setUsername(username);
        user.setEnabled(true); // Otherwise won't show up as linked user when refresh
        user.setContact(individualRepository.findById(contactId).orElseThrow(NullPointerException::new));
        userManagementService.createUser(user);
        return new Result(user.getId());
    }

    /** For update by ROLE_ADMINLOGIN - so no prev password needed */
    @PostJsonReturningJson("/users/{username}/updatePassword/")
    public Result updateUserPassword(
            @PathVariable String username,
            @RequestBody ChangePasswordDto dto) {
        return updatePassword(username, dto, false);
    }

}
