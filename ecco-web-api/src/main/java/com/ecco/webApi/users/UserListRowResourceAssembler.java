package com.ecco.webApi.users;

import com.ecco.security.dom.User;
import org.jspecify.annotations.NonNull;
import org.springframework.hateoas.server.LinkBuilder;
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;


public class UserListRowResourceAssembler extends RepresentationModelAssemblerSupport<User, UserListRowResource> {

    public static final String REL_EDIT = "edit";

    public UserListRowResourceAssembler() {
        super(UserListController.class, UserListRowResource.class);
    }

    @Override
    @NonNull
    public UserListRowResource toModel(User u) {
        var contact = u.getContact();
        UserListRowResource resource = new UserListRowResource(u.getId(), u.getUsername(),
                contact == null ? null : contact.getId(),
                contact == null ? null : contact.getFirstName(),
                contact == null ? null : contact.getLastName(),
                u.isEnabled(), u.getLastLoggedIn(),
                u.isMfaRequired()
            );

        addEditLink(resource, u);
        return resource;
    }

    private void addEditLink(UserListRowResource resource, User user) {
        resource.add(linkToUserFile(user.getUsername()).withRel(REL_EDIT));
    }

    public LinkBuilder linkToUserFile(String username) {
        // eg. http://localhost:8080/ecco-war/api/users/sysadmin
        var method = methodOn(UserController.class).findOneByUsername(username);
        return linkToApi(method);
    }
}
