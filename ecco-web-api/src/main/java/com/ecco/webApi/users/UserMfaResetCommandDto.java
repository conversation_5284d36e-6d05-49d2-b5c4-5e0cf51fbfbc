package com.ecco.webApi.users;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * Reset a users mfa secret.
 */
@Slf4j
@NoArgsConstructor
public class UserMfaResetCommandDto extends BaseUserCommandDto {

    /**
     * For tests only (since <PERSON> uses default constructor).
     */
    public UserMfaResetCommandDto(long userIdSubject) {
        super("reset", UriComponentsBuilder
                .fromUriString("users/{userIdSubject}/commands/resetMfa/")
                .buildAndExpand(userIdSubject)
                .toUriString(), userIdSubject);
    }
}
