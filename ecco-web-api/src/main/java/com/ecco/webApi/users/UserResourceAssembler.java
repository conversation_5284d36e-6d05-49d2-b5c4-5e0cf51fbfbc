package com.ecco.webApi.users;

import com.ecco.security.dom.User;
import com.ecco.webApi.acls.AclController;
import com.ecco.webApi.contacts.IndividualToViewModel;
import com.ecco.webApi.contacts.IndividualViewModel;
import com.ecco.webApi.viewModels.UserViewModel;
import com.ecco.security.config.AclConfig;
import org.jspecify.annotations.NonNull;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.server.LinkBuilder;
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport;
import org.springframework.util.Assert;

import org.jspecify.annotations.Nullable;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

public class UserResourceAssembler extends RepresentationModelAssemblerSupport<User, UserViewModel> {

    private final IndividualToViewModel individualToViewModel = new IndividualToViewModel();
    private final AclConfig aclConfig;

    public UserResourceAssembler(AclConfig aclConfig) {
        // FIXME: UserController probably won't resolve correctly as it doesn't specify @RequestMapping("users")
        //  We could probably refactor RepresentationModelAssemblerSupport to take a Method rather than class for linkTo(getMethod)
        super(UserController.class, UserViewModel.class);
        this.aclConfig = aclConfig;
    }

    @Nullable
    public UserViewModel apply(@Nullable User input) {
        if (input == null) {
            throw new NullPointerException("input User must not be null");
        }

        // sort the individual details first
        IndividualViewModel i = individualToViewModel.apply(input.getContact());

        UserViewModel u = new UserViewModel();
        u.individual = i;
        u.username = input.getUsername();
        u.userId = input.getId();
        u.enabled = input.isEnabled();
        u.registered = input.getRegistered();
        u.lastLoggedIn = input.getLastLoggedIn();
        u.mfaRequired = input.isMfaRequired();
        u.mfaValidated = input.getMfaSecret() != null;

        // transfer the groups the user has
        u.setGroups(input.getGroups());

        Assert.isNull(u.newPassword, "newPassword should not be given to the client");
        if (aclConfig.enableAcls()) {
            u.add(linkToAclConfig(input.getUsername()).withRel("acl"));
        }
        if (aclConfig.enableAclsAdmin()) {
            // Just use a dummy link as a flag
            u.add(Link.of("ignored", "acl-config"));
        }
        // For noinspection: The methodOn magic that uses GCLib or whatever to read the value blows up if given a Long
        //noinspection UnnecessaryUnboxing
        u.add(linkToApi(methodOn(UserCommandController.class).getUserCommands(input.getId().longValue())).withRel("audit-history"));
        return u;
    }

    @Override
    @NonNull
    public UserViewModel toModel(@NonNull User entity) {
        return apply(entity);
    }


    public static LinkBuilder linkToAclConfig(String username) {
        // eg. http://localhost:8080/ecco-war/api/acls/entriesByUser/sysadmin
        return linkToApi(methodOn(AclController.class)
                .findByUsername(username));
    }

}
