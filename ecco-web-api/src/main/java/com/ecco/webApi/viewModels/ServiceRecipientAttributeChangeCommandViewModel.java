package com.ecco.webApi.viewModels;

import com.ecco.webApi.evidence.BaseServiceRecipientCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import org.jspecify.annotations.NonNull;
import org.springframework.web.util.UriComponentsBuilder;

public class ServiceRecipientAttributeChangeCommandViewModel extends BaseServiceRecipientCommandViewModel {

    public int serviceRecipientId;

    /** Type of change, e.g. "text", "choice", "date" */
    @NonNull
    public String attributeType;

    /** JavaBean style path, e.g. "residence" */
    @NonNull
    public String attributePath;

    /**
     * e.g. from: "building:23", to: "building:123", or some simple string change
     */
    @NonNull
    public ChangeViewModel<String> valueChange;

    /** For Jackson etc */
    ServiceRecipientAttributeChangeCommandViewModel() {
        super();
    }

    /** For testing */
    public ServiceRecipientAttributeChangeCommandViewModel(int serviceRecipientId) {
        super(buildUri(serviceRecipientId),
                serviceRecipientId);
    }

    public static String buildUri(int serviceRecipientId) {
        return UriComponentsBuilder
                .fromUriString("service-recipients/{serviceRecipientId}/attributeChange/")
                .buildAndExpand(serviceRecipientId)
                .toString();
    }

    @Override
    public String toString() {
        return "ServiceRecipientAttributeChangeCommandViewModel [serviceRecipientId=" + serviceRecipientId + ", uuid=" + uuid
                + ", commandUri=" + commandUri + ", timestamp=" + timestamp + ", attributeType=" + attributeType
                + ", attributePath=" + attributePath + ", valueChange=" + valueChange + "]";
    }

}
