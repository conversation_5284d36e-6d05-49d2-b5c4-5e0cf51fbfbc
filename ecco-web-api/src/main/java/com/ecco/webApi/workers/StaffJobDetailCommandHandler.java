package com.ecco.webApi.workers;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.hr.WorkerJob;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.ecco.webApi.taskFlow.ServiceRecipientTaskCommandHandler;
import com.ecco.webApi.taskFlow.ServiceRecipientTaskParams;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.time.ZoneId;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

@Component
public class StaffJobDetailCommandHandler extends ServiceRecipientTaskCommandHandler<WorkerJobDetailCommandViewModel> {

    @NonNull
    private final WorkerJobRepository workerJobRepository;

    @Autowired
    public StaffJobDetailCommandHandler(ObjectMapper objectMapper,
                                        @NonNull WorkflowTaskController workflowTaskController,
                                        @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                        @NonNull WorkerJobRepository workerJobRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, WorkerJobDetailCommandViewModel.class);
        this.workerJobRepository = workerJobRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               WorkerJobDetailCommandViewModel vm) {

        var job = workerJobRepository.findByServiceRecipient_Id(vm.serviceRecipientId).get();
        handleUpdate(vm, job);

        return CommandResult.ofLink(linkToApi(methodOn(WorkerListController.class).self()).withRel(WorkerListRowResourceAssembler.REL_EDIT))
                .withTargetId(job.getId());
    }

    private void handleUpdate(WorkerJobDetailCommandViewModel dto, WorkerJob workerJob) {

        if (dto.getStartDate() != null) {
            workerJob.setStartDate(dto.startDate.to.atStartOfDay(ZoneId.of("UTC")));
        }
        if (dto.getEndDate() != null) {
            workerJob.setEndDate(dto.endDate.to.atStartOfDay(ZoneId.of("UTC")));
        }
        if (dto.code != null) {
            workerJob.setCode(dto.code.to);
        }
        if (dto.contractedWeeklyHours != null) {
            workerJob.setContractedWeeklyHours(dto.contractedWeeklyHours.to);
        }

        workerJobRepository.save(workerJob);
    }

}
