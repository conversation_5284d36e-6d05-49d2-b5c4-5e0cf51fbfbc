package com.ecco.web;

import org.jspecify.annotations.NonNull;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ecco.security.ReferenceDataSource;
import lombok.AllArgsConstructor;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.AbstractController;

// we bypass ParameterizableViewController in order for all our controllers to implement this
// and avoid being forced to provide a viewName. then all controllers can have the cache set too
@AllArgsConstructor
public class BasicPageController extends AbstractController {

    // any instance data on a singleton should be synch - eg see UrlFilenameViewController
    // private final Map viewNameCache = Collections.synchronizedMap(new HashMap());
    // but here, synchronise is not required, nor caching
    private String viewName;
    public void setViewName(String viewName) {
        this.viewName = viewName;
    }
    public String getViewName() {
        return this.viewName;
    }

    // to avoid setting the cache on each bean, we do so like the wizard controller
    public BasicPageController() {
        setCacheSeconds(0);
        // we don't reference the session so no need to sync it
        //setSynchronizeOnSession();
    }

    ReferenceDataSource referenceData;
    @Resource(name="applicationReferenceData")
    public void setReferenceData(ReferenceDataSource referenceData) {
        this.referenceData = referenceData;
    }
    public ReferenceDataSource getReferenceData() {
        return referenceData;
    }

    @Override
    protected ModelAndView handleRequestInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response) throws Exception {
        ModelMap model = new ModelMap();
        decorateModel(model, request);
        model.addAllAttributes(request.getParameterMap());
        setReferenceData(model, request);
        return new ModelAndView(getViewName(), model);
    }

    /** Override this method to populate the model with custom attributes. */
    protected void decorateModel(ModelMap model, HttpServletRequest request) {
    }

    protected void setReferenceData(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
    }

}
