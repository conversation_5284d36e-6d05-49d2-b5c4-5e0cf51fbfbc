package com.ecco.web;

import com.ecco.calendar.core.util.DateTimeUtils;
import com.ecco.calendar.dom.MedDate;
import com.ecco.security.SecurityUtil;
import com.ecco.security.dom.User;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

public class Formatters {
    /**
     * Converts a LocalDate for display. Assumes no timezone - so is local date/time
     */
    public static String userFriendlyDateFromLocal(LocalDate date) {
        User user = SecurityUtil.getAuthenticatedUser();
        return DateTimeUtils.getFormattedDate(date, user.getLocale());
    }

    /**
     * Converts a DateTime for display. Assumes no timezone - so is local date/time
     */
    public static String userFriendlyDateFromLocal(DateTime date) {
        User user = SecurityUtil.getAuthenticatedUser();
        return DateTimeUtils.getFormattedDate(date, user.getLocale());
    }

    public static String userFriendlyDateFromLocal(MedDate date) {
        User user = SecurityUtil.getAuthenticatedUser();
        return DateTimeUtils.getFriendlyDate(date, true, user.getLocale());
    }

    /**
     * Converts a DateTime for display.
     *
     * @param date utc date
     * @return users date
     */
    public static String userFriendlyDateFromUtc(DateTime date) {
        User user = SecurityUtil.getAuthenticatedUser();
        DateTime usersDate = DateTimeUtils.convertToTz(date, user.getTimeZone());
        return DateTimeUtils.getFormattedDate(usersDate, user.getLocale());
    }

    /**
     * Converts a DateTime for display with time. Assumes no timezone - so is local date/time
     */
    public static String userFriendlyDateTimeFromLocal(DateTime date) {
        User user = SecurityUtil.getAuthenticatedUser();
        return DateTimeUtils.getFormattedDateTime(date, user.getLocale());
    }

    /**
     * Converts a DateTime for display with time.
     *
     * @param date utc date
     * @return users date
     */
    public static String userFriendlyDateTimeFromUtc(DateTime date) {
        User user = SecurityUtil.getAuthenticatedUser();
        DateTime usersDate = DateTimeUtils.convertToTz(date, user.getTimeZone());
        return DateTimeUtils.getFormattedDateTime(usersDate, user.getLocale());
    }
}
