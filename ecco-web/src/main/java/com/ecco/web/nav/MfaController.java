package com.ecco.web.nav;

import com.ecco.infrastructure.web.WebSlice;
import com.ecco.security.ReferenceDataSource;
import com.ecco.security.SecurityUtil;
import com.ecco.security.dom.User;
import com.ecco.security.service.MfaAuthentication;
import com.ecco.security.service.UserManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;

@Controller
@WebSlice("nav")
//@Secured("ROLE_USER")
@RequestMapping(value = {"mfa", "nav/mfa"}) // NB /nav is required for the tests to pass, not ideal (see commit)
public class MfaController {

    public static String QR_PREFIX = "../../api/images/qrcode/?code=";
    public static String APP_NAME = "eccosolutions";

    private final UserManagementService userManagementService;
    private final AuthenticationSuccessHandler webSuccessHandler;
    private final ReferenceDataSource referenceData;

    @Value("${ecco.api.basePath:}")
    private String apiBasePath;

    @Autowired
    public MfaController(UserManagementService userManagementService,
                         @Qualifier("webSuccessHandler") AuthenticationSuccessHandler successHandler,
                         ReferenceDataSource referenceData) {
        this.userManagementService = userManagementService;
        this.webSuccessHandler = successHandler;
        this.referenceData = referenceData;
    }

    @GetMapping("/validate")
    public String presentTotp(MfaAuthentication authentication,
                              ModelMap model,
                              RedirectAttributes redirectAttributes,
                              HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        User user = getUser(authentication);
        if (user.getMfaSecret() == null) {
            return registerTotp(authentication, model, request);
        }
        model.put("register", false);
        model.putAll(redirectAttributes.getFlashAttributes());
        return "totp";
    }

    @PostMapping("/validate")
    public RedirectView validateTotp(@RequestParam String totp, MfaAuthentication authentication,
                                     RedirectAttributes redirectAttributes,
                                     HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        User user = SecurityUtil.getUser(authentication);
        boolean valid = false;
        var registered = user.getMfaSecret() != null;
        if (!registered) {
            userManagementService.registerMfa(authentication.getSecretKeyOffered(), user);
            // temporarily set the secretKey to then validate it (avoids reloading the user)
            user.setMfaSecret(authentication.getSecretKeyOffered());
        }
        try {
            valid = userManagementService.validateMfa(totp, user);
        } catch (InvalidKeyException ignored) {
        }
        if (valid) {
            SecurityContextHolder.getContext().setAuthentication(authentication.getFirst());
            this.webSuccessHandler.onAuthenticationSuccess(request, response, authentication.getFirst());
            return null;
        } else {
            // reset if we weren't already registered
            if (!registered) {
                user.setMfaSecret(null);
            }
            redirectAttributes.addFlashAttribute("invalid", "totp");
            return new RedirectView("/nav/mfa/validate", true);
        }
    }

    private String otpAuthUrl(String email, String mfaSecret) {
        return String.format(
                "otpauth://totp/%s:%s?secret=%s&issuer=%s",
                APP_NAME, email, mfaSecret, APP_NAME);
    }

    private String generateQRUrl(String otpAuthUrl) {
        return QR_PREFIX + URLEncoder.encode(otpAuthUrl, StandardCharsets.UTF_8);
    }

    private String registerTotp(MfaAuthentication authentication, ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        User user = SecurityUtil.getUser(authentication.getFirst());
        var secretKey = userManagementService.generateMfaSecretKey();
        authentication.setSecretKeyOffered(secretKey);
        var otpAuthUrl = otpAuthUrl(user.getEmail(), secretKey);
        model.put("QR_url", generateQRUrl(otpAuthUrl));
        model.put("otpAuthUrl", otpAuthUrl);
        model.put("register", true);
        return "totp";
    }

    private User getUser(MfaAuthentication authentication) {
        return SecurityUtil.getUser(authentication.getFirst());
    }

}
