package com.ecco.webApi.boot;

import com.azure.spring.aad.AADClientRegistrationRepository;
import com.azure.spring.aad.webapp.AADHandleConditionalAccessFilter;
import com.azure.spring.aad.webapp.AADOAuth2UserService;
import com.ecco.infrastructure.config.root.Profiles;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.context.ApplicationContext;
import org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter;
import org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter;
import org.springframework.security.web.FilterChainProxy;
import org.springframework.security.web.access.ExceptionTranslationFilter;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.AnonymousAuthenticationFilter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.security.web.authentication.switchuser.SwitchUserFilter;
import org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter;
import org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.context.SecurityContextPersistenceFilter;
import org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter;
import org.springframework.security.web.header.HeaderWriterFilter;
import org.springframework.security.web.savedrequest.RequestCacheAwareFilter;
import org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter;
import org.springframework.security.web.session.SessionManagementFilter;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.filter.CorsFilter;

import javax.servlet.Filter;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.InstanceOfAssertFactories.LIST;
import static org.assertj.core.util.introspection.FieldSupport.EXTRACTION;
import static org.springframework.boot.test.context.SpringBootTest.WebEnvironment.RANDOM_PORT;
import static org.springframework.http.HttpStatus.FOUND;

/**
 * Test the behaviour of our Spring Boot app.
 *
 * @see <a href="http://docs.spring.io/spring-boot/docs/current/reference/html/boot-features-testing.html">Spring Boot Reference</a>
 */
@SpringBootTest(webEnvironment = RANDOM_PORT, classes = Application.class) // NOTE: @Disabled doesn't stop this
@TestPropertySource(properties = {
        "debug true",
//        "liquibase DISABLED",
//        "spring.jpa.properties.javax.persistence.validation.mode none",
        "azure.activedirectory.client-id something-to-enable-autoconfig"
})
@ActiveProfiles({Profiles.EMBEDDED, "test"}) //
public class ApplicationTests {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private Filter springSecurityFilterChain;


    @Test
    public void contextContainsExpectedAutoConfiguredBeansAndFilters() {
        // Validate that we've got access to MS Graph configured and active
        assertThat(applicationContext.getBean(AADClientRegistrationRepository.class)).isNotNull();
        assertThat(applicationContext.getBean(AADOAuth2UserService.class)).isNotNull();

        // This should have been defined to register in the appropriate place due to specifying server.forward-headers-strategy: framework
        assertThat(applicationContext.getBean("forwardedHeaderFilter")).isNotNull();

        // If @EnableWebSecurity(debug = true) then we need to extract filter chain
        var chain = springSecurityFilterChain instanceof FilterChainProxy
                ? ((FilterChainProxy) springSecurityFilterChain)
                : EXTRACTION.fieldValue("filterChainProxy", FilterChainProxy.class, springSecurityFilterChain);


// FIXME:        assertThat(chain.getFilterChains()).hasSize(2);

        var filters = chain.getFilters("/login");

        // Validate that the filter that handles /oauth2/authorization/azure is present
        assertThat(filters)
                .extracting(Filter::getClass)
                .asInstanceOf(LIST)
                .containsSubsequence(
                        WebAsyncManagerIntegrationFilter.class,
                        SecurityContextPersistenceFilter.class,
                        HeaderWriterFilter.class,
                        CorsFilter.class,
                        LogoutFilter.class,
                        OAuth2AuthorizationRequestRedirectFilter.class,
                        AADHandleConditionalAccessFilter.class, // This is for client
                        OAuth2LoginAuthenticationFilter.class,
                        UsernamePasswordAuthenticationFilter.class,
                        DefaultLoginPageGeneratingFilter.class,
                        DefaultLogoutPageGeneratingFilter.class,
                        RequestCacheAwareFilter.class,
                        SecurityContextHolderAwareRequestFilter.class,
                        AnonymousAuthenticationFilter.class,
                        SessionManagementFilter.class,
                        ExceptionTranslationFilter.class,
//                        FilterSecurityInterceptor.class, // We don't have this... why
                        SwitchUserFilter.class
                );

        // matches the tomcat apiFilters in MfaWebSecurityTest filtersFromApplicationTest
        // which extends AbstractWebSecurityTest (non spring-boot)
        // except for DisableEncodeUrlFilter.class
        var apiFiltersBoot = chain.getFilters("/api/");
        assertThat(apiFiltersBoot)
                .extracting(Filter::getClass)
                .asInstanceOf(LIST)
                .containsSubsequence(
                        //DisableEncodeUrlFilter.class,
                        WebAsyncManagerIntegrationFilter.class,
                        SecurityContextPersistenceFilter.class,
                        HeaderWriterFilter.class,
                        CorsFilter.class,
                        LogoutFilter.class,
                        BasicAuthenticationFilter.class,
                        RequestCacheAwareFilter.class,
                        SecurityContextHolderAwareRequestFilter.class,
                        AnonymousAuthenticationFilter.class,
                        SessionManagementFilter.class,
                        ExceptionTranslationFilter.class,
                        FilterSecurityInterceptor.class
                );

    }

    /**
     * Ensure that OAuth2AuthorizationRequestRedirectFilter or OAuth2AuthorizationRequestRedirectWebFilter
     * is active and appropriately configured.
     * Note: For mockMvc approaches to testing OAuth2, see https://www.baeldung.com/oauth-api-testing-with-spring-mvc
     */
    @Test
    public void shouldRedirectAzureADLoginsToMicrosoft() {
        var response = restTemplate.getForEntity("/oauth2/authorization/azure", String.class);
        assertThat(response.getStatusCode()).isEqualTo(FOUND); // restTemplate using Apache HttpClient follows redirect unless configured otherwise
        assertThat(response.getHeaders().getFirst("Location"))
                .startsWith("https://login.microsoftonline.com");

//        This doesn't work as we don't get the same filters configured for some reason (but many useful ones are)
//        mockMvc.perform(get("/oauth2/authorization/azure"))
//                .andExpect(status().isFound());
    }
}
