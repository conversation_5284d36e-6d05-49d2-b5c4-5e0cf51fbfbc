<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
    xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
    typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath"
    targetNamespace="http://www.activiti.org/test">

    <process id="central-processing" isExecutable="true">
        <serviceTask id="loadReferral" name="load referral"
                activiti:expression="#{referralRepository.findByServiceRecipient_Id(execution.processBusinessKey)}"
                activiti:resultVariable="referral"/>


        <!-- TO GO in ra-id 18 settings:
        overviewForm="demoCustomOverview"
        tasksToShowClientView="emergencyDetails"
        <tasksToShowRestricted=
            "dataProtection,emergencyDetails,from,delivered by,referralDetails,pendingStatus,referralAccepted,assessmentDate,funding,assessmentAccepted,start,close"/>
        -->

        <!-- TODO: User our own service and allow "templateName" to be specified to use our templates service -->
        <serviceTask id="sendReferralReceivedMail" activiti:type="mail" activiti:async="true">
            <extensionElements>
                <activiti:field name="to"
                    expression="#{referral.client.contact.email eq '' ? '<EMAIL>' : referral.client.contact.email}" />
<!--                 <activiti:field name="to" expression="${referral.client.email}" /> -->

                    <activiti:field name="subject">
                        <activiti:string>Your referral has been received</activiti:string>
                    </activiti:field>
                    <activiti:field name="html">
                        <expression>
<![CDATA[
 <html>
 <body>
   Dear ${referral.client.displayName},<br/>
   <br/>
   Your referral has been received and we will contact you shortly.<br/>
   <br/>
   Kind regards,<br/>
   Housing Support
 </body>
</html>
]]>
                        </expression>
                    </activiti:field>
            </extensionElements>
        </serviceTask>


        <userTask id="dataProtection" name="dataProtection" activiti:candidateGroups="admin" activiti:formKey="flow:/dataProtection">
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="basic" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="emergencyDetails" name="emergencyDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/emergencyDetails"/>

        <userTask id="referralSource" name="from" activiti:candidateGroups="admin"
                activiti:formKey="flow:/sourceWithIndividual"/>

        <userTask id="referralDetails" name="referralDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralDetails">
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="basic" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="pendingStatus" name="pendingStatus" activiti:candidateGroups="admin"
                activiti:formKey="flow:/pendingStatus"/>

        <userTask id="referralAccepted" name="referralAccepted" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralAccepted">
        </userTask>

        <userTask id="assessmentDate" name="assessmentDate" activiti:candidateGroups="admin"
                activiti:formKey="flow:/assessmentDate"/>

        <userTask id="decideFinal" name="assessmentAccepted" activiti:candidateGroups="admin"
                activiti:formKey="flow:/decideFinal"/>

        <userTask id="start" name="start" activiti:candidateGroups="admin"
                activiti:formKey="flow:/start"/>

        <userTask id="allocateToServices" name="allocateToServices" activiti:candidateGroups="admin"
                activiti:formKey="flow:/allocateToServices"/>



        <startEvent id="theStart" name="Start Event"/>
        <sequenceFlow id="to_loadReferral"             sourceRef="theStart"                 targetRef="loadReferral"/>

        <sequenceFlow id="from_loadReferral"           sourceRef="loadReferral"             targetRef="fork0"/>
        <parallelGateway id="fork0"/>

<!-- Disabled as it keeps close off from completing flow .. until we have mail queue impl'd
        <sequenceFlow id="to_sendReferralReceivedMail" sourceRef="fork0"      targetRef="sendReferralReceivedMail"/>
 -->
        <sequenceFlow id="to_dataProtection"           sourceRef="fork0"      targetRef="dataProtection"/>
        <sequenceFlow id="to_emergencyDetails"   sourceRef="dataProtection"   targetRef="emergencyDetails"/>
        <sequenceFlow id="from_emergencyDetails" sourceRef="emergencyDetails" targetRef="fork1"/>

        <parallelGateway id="fork1"/>
            <sequenceFlow id="to_referralSource"   sourceRef="fork1" targetRef="referralSource"/>
            <sequenceFlow id="from_referralSource" sourceRef="referralSource" targetRef="join1"/>

            <sequenceFlow id="to_referralDetails"   sourceRef="fork1" targetRef="referralDetails"/>
            <sequenceFlow id="from_referralDetails" sourceRef="referralDetails" targetRef="join1"/>

            <sequenceFlow id="to_pendingStatus"   sourceRef="fork1" targetRef="pendingStatus"/>
            <sequenceFlow id="from_pendingStatus" sourceRef="pendingStatus" targetRef="join1"/>

            <sequenceFlow id="to_referralAccepted"   sourceRef="fork1" targetRef="referralAccepted"/>
            <sequenceFlow id="from_referralAccepted" sourceRef="referralAccepted" targetRef="join1"/>

            <sequenceFlow id="to_assessmentDate"       sourceRef="fork1" targetRef="assessmentDate"/>
            <sequenceFlow id="from_assessmentDate"     sourceRef="assessmentDate" targetRef="join1"/>

            <sequenceFlow id="to_decideFinal"       sourceRef="fork1" targetRef="decideFinal"/>
            <sequenceFlow id="from_decideFinal"     sourceRef="decideFinal" targetRef="join1"/>

            <sequenceFlow id="to_start"       sourceRef="fork1" targetRef="start"/>
            <sequenceFlow id="from_start"     sourceRef="start" targetRef="join1"/>

        <!-- end parallel tasks -->
        <parallelGateway id="join1"/>

        <sequenceFlow id="to_allocateToServices" sourceRef="join1" targetRef="allocateToServices"/>
        <sequenceFlow id="from_allocateToServices" sourceRef="allocateToServices" targetRef="theEnd"/>

        <endEvent id="theEnd" name="End Event"/>
    </process>
</definitions>
