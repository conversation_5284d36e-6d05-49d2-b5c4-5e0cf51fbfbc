<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
    xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
    typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath"
    targetNamespace="http://www.activiti.org/test">

    <process id="enable-south-rota" isExecutable="true">
        <serviceTask id="loadReferral" name="load referral"
                activiti:expression="#{referralRepository.findByServiceRecipient_Id(execution.processBusinessKey)}"
                activiti:resultVariable="referral"/>

        <!-- ** IMPORTANT: Update this expression with the ordered list when you add tasks to the workflow ** -->
        <serviceTask id="setTasksToShowClientView" name="set tasksToShowClientView"
            activiti:resultVariable="tasksToShowClientView"
            activiti:expression="emergencyDetails,agreementOfAppointments,needsReduction,rotaVisit"/>

        <serviceTask id="setTasksToShowRestricted" name="set tasksToShowRestricted"
            activiti:resultVariable="tasksToShowRestricted"
            activiti:expression=
            "from,referralDetails,funding,agreementOfAppointments,assessmentAccepted,start,rotaVisit,close"/>

        <userTask id="referralSource" name="from" activiti:candidateGroups="admin"
                activiti:formKey="flow:/sourceWithIndividual"/>

        <userTask id="referralDetails" name="referralDetails" activiti:candidateGroups="admin"
                activiti:formKey="flow:/referralDetails">
            <extensionElements>
                <activiti:formProperty id="detailPages" expression="basic" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="funding" name="funding" activiti:candidateGroups="admin" activiti:formKey="flow:/funding"/>

        <userTask id="agreementOfAppointments" name="agreementOfAppointments" activiti:candidateGroups="admin"
                activiti:formKey="entitiesByReferralId:agreementOfAppointments"/>

        <userTask id="decideFinal" name="assessmentAccepted" activiti:candidateGroups="admin"
                activiti:formKey="flow:/decideFinal"/>

        <userTask id="start" name="start" activiti:candidateGroups="admin"
                activiti:formKey="flow:/start"/>
        <!--
            <extensionElements>
                <activiti:formProperty id="show" expression="supportWorker" writable="false"/>
            </extensionElements>
         -->

        <userTask id="rotaVisit" name="rotaVisit" activiti:candidateGroups="admin"
            activiti:formKey="generic:/rotaVisit" >
            <extensionElements>
                <activiti:formProperty id="titleCode" expression="referralView.rotaVisit" writable="false"/>
                <activiti:formProperty id="showOutcomes" expression="none" writable="false"/>
                <activiti:formProperty id="showActionComponents" expression="link,target,status" writable="false"/>
                <activiti:formProperty id="actAs" expression="assessment,reduction" writable="false"/>
                <activiti:formProperty id="tookPlaceOn" expression="startEnd" writable="false"/>
                <activiti:formProperty id="showCommentComponents" expression="showRiskManagementRequired,mileage,type,minutesSpent" writable="false"/>
                <activiti:formProperty id="showSaveTypes" expression="draft,final" writable="false"/>
                <activiti:formProperty id="showMenus" expression="calendar, access, overview" writable="false"/>
                <activiti:formProperty id="sourcePage" expression="101" writable="false"/>
                <activiti:formProperty id="sourcePageGroup" expression="19" writable="false"/>
                <activiti:formProperty id="captureClientSignature" expression="y" writable="false"/>
            </extensionElements>
        </userTask>

        <userTask id="closeReferral" name="close" activiti:candidateGroups="admin" activiti:formKey="flow:/close"/>


        <startEvent id="theStart" name="Start Event"/>
        <sequenceFlow id="to_loadReferral"             sourceRef="theStart"                 targetRef="loadReferral"/>
        <sequenceFlow id="to_setTasksToShowClientView" sourceRef="loadReferral"             targetRef="setTasksToShowClientView"/>
        <sequenceFlow id="to_setTasksToShowRestricted" sourceRef="setTasksToShowClientView" targetRef="setTasksToShowRestricted"/>

        <sequenceFlow id="to_fork1" sourceRef="setTasksToShowRestricted" targetRef="fork1"/>

        <parallelGateway id="fork1"/>
            <sequenceFlow id="to_referralSource"   sourceRef="fork1" targetRef="referralSource"/>
            <sequenceFlow id="from_referralSource" sourceRef="referralSource" targetRef="join1"/>

            <sequenceFlow id="to_referralDetails"   sourceRef="fork1" targetRef="referralDetails"/>
            <sequenceFlow id="from_referralDetails" sourceRef="referralDetails" targetRef="join1"/>

            <sequenceFlow id="to_funding"       sourceRef="fork1" targetRef="funding"/>
            <sequenceFlow id="from_funding"     sourceRef="funding" targetRef="join1"/>

            <sequenceFlow id="to_agreementOfAppointments"   sourceRef="fork1" targetRef="agreementOfAppointments"/>
            <sequenceFlow id="from_agreementOfAppointments" sourceRef="agreementOfAppointments" targetRef="join1"/>

            <sequenceFlow id="to_decideFinal"       sourceRef="fork1" targetRef="decideFinal"/>
            <sequenceFlow id="from_decideFinal"     sourceRef="decideFinal" targetRef="fork2"/>

            <parallelGateway id="fork2"/>

                <sequenceFlow id="to_start"       sourceRef="fork2" targetRef="start"/>
                <sequenceFlow id="from_start"     sourceRef="start" targetRef="forkSt"/>

                <parallelGateway id="forkSt"/>

                    <sequenceFlow id="to_rotaVisit"          sourceRef="forkSt" targetRef="rotaVisit"/>
                    <sequenceFlow id="from_rotaVisit"        sourceRef="rotaVisit" targetRef="joinSt"/>

                    <sequenceFlow id="to_closeReferral" sourceRef="forkSt" targetRef="closeReferral"/>
                    <sequenceFlow id="from_closeReferral" sourceRef="closeReferral" targetRef="joinSt"/>

                <parallelGateway id="joinSt"/>

            <parallelGateway id="join2"/>

        <parallelGateway id="join1"/>

        <sequenceFlow id="to_theEnd" sourceRef="closeReferral" targetRef="theEnd"/>

        <endEvent id="theEnd" name="End Event"/>
    </process>
</definitions>
