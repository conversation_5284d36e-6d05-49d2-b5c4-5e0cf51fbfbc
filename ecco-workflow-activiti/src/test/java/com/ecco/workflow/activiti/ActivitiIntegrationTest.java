package com.ecco.workflow.activiti;
import com.ecco.config.service.SettingsService;
import com.ecco.config.service.SoftwareModuleService;
import com.ecco.config.service.StubSettingsService;
import com.ecco.dao.ReferralRepository;
import com.ecco.infrastructure.config.root.*;
import com.ecco.security.repositories.GroupRepository;
import com.ecco.security.service.UserManagementServiceImpl;
import com.ecco.test.support.SpringTestSupport;
import com.ecco.calendar.core.CalendarService;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.form.FormProperty;
import org.activiti.engine.form.TaskFormData;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.RepositoryServiceImpl;
import org.activiti.engine.impl.pvm.ReadOnlyProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.Matchers;
import org.hamcrest.TypeSafeMatcher;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.MessageDigestPasswordEncoder;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.List;
import java.util.Set;

import static java.util.stream.Collectors.toSet;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.Matchers.*;
import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(initializers = ActivitiIntegrationTest.TestAppContextInitializer.class,
    classes={ InfrastructureConfig.class, WorkflowConfig.class, ActivitiIntegrationTest.Config.class } )
@DirtiesContext
public class ActivitiIntegrationTest {
    public static class TestAppContextInitializer extends EccoApplicationContextInitializer {
        @Override
        protected void configureEnvironment(ConfigurableEccoEnvironment environment) {
            environment.setActiveProfiles(Profiles.EMBEDDED);
            SpringTestSupport.configureMockPropertySource(environment)
                    .withProperty("liquibase", LiquibaseMode.DISABLED.toString())
                    .withProperty("hibernate.hbm2ddl", "none");
        }
    }

    @Autowired
    RepositoryService repositoryService;
    @Autowired
    RuntimeService runtimeService;
    @Autowired
    TaskService taskService;
    @Autowired
    HistoryService historyService;
    @Autowired
    ExtendedFormService formService;
    @Autowired
    ReferralRepository referralRepository;

    @Test
    public void smokeTest() {
        // Pass if we can run!
    }

    @Test
    public void deploySucceeds() {
        String deploymentId = repositoryService
          .createDeployment()
          .addClasspathResource("test.bpmn20.xml")
          .deploy()
          .getId();
        System.out.println("deploymentId = " + deploymentId);
    }

    @Test
    public void canGetTasksAndFormData() {
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey("scnhs.care");
        TaskFormData formData = this.formService.getTaskFormDataForProcessDefinition(processInstance.getProcessDefinitionId(), "caseNotes");
        assertThat( formData.getFormKey(), equalTo("generic:/needsReduction"));
        assertThat( formData.getFormProperties().get(0).getId(), equalTo("titleRaw"));
        assertThat( formData.getFormProperties().get(0).getValue(), equalTo("case notes"));
    }

    @Test
    public void nhsProcessRuns() {
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey("scnhs.care");
        final String processId = processInstance.getId();

        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processId).list();
        final Matcher<Iterable<? super Task>> referralDetails = hasItem(taskWithName("referralDetails"));
        assertThat(tasks, referralDetails);
        for (Task task : tasks) {
            System.out.println(task.getProcessDefinitionId() + " : " + task.getTaskDefinitionKey());
            taskService.complete(task.getId());
        }
        tasks = taskService.createTaskQuery().processInstanceId(processId).list();
        final Matcher<Iterable<? super Task>> referralAccepted = hasItem(taskWithName("referralAccepted"));
        assertThat(tasks, referralAccepted);

        // Get a list of all the completed tasks on a workflow
        List <HistoricTaskInstance> completedTasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(processId).finished().list();
        for (HistoricTaskInstance completedTask : completedTasks) {
            System.out.println("historic completedTask = " + completedTask.getName() + ", at " + completedTask.getEndTime() + ", from process " + completedTask.getProcessInstanceId());
        }
        assertEquals("from", completedTasks.get(0).getName());

        // Get a list of all the unstarted tasks on a workflow
        List<String> unstartedTaskNames = determineUnstartedActivities(processId);
        assertThat(unstartedTaskNames, hasItem("placement (admin)"));
        assertThat(unstartedTaskNames, not(hasItem("Start Event")));
        assertThat(unstartedTaskNames, not(hasItem("End Event")));

        // Now when we complete the current task...
        for (Task task : tasks) {
            taskService.complete(task.getId());
        }
        // Then the next task is from the previously unstarted list
        tasks = taskService.createTaskQuery().processInstanceId(processId).list();
        assertThat(tasks.get(0).getName(), isIn(unstartedTaskNames));
        // And it is no longer on the unstarted list
        unstartedTaskNames = determineUnstartedActivities(processId);
        assertThat(unstartedTaskNames, not(hasItem(tasks.get(0).getName())));

        // Keep completing tasks until we are on 'initial process (admins)'
        while (!"initial process (admins)".equals(tasks.get(0).getName())) {
            taskService.complete(tasks.get(0).getId());
            tasks = taskService.createTaskQuery().processInstanceId(processId).list();
        }
        final Task activeTask = tasks.get(0);
        System.out.println("initial process (admins): taskId = " + activeTask.getId() + ", due date = " + activeTask.getDueDate());
        assertNotNull("Initial process admin should have a due date", activeTask.getDueDate());
        final String activeTaskFormKey = formService.getTaskFormKey(activeTask.getProcessDefinitionId(), activeTask.getTaskDefinitionKey());
        // Use our version of getTaskFormData() so we have test coverage of it for active tasks as well as completed tasks
        final TaskFormData activeTaskFormData = formService.getTaskFormData(activeTask.getProcessInstanceId(), activeTask.getTaskDefinitionKey());
        assertEquals("Form key on data same as directly fetched", activeTaskFormKey, activeTaskFormData.getFormKey());
        final List<FormProperty> activeTaskFormProperties = activeTaskFormData.getFormProperties();
        taskService.complete(tasks.get(0).getId());
        final HistoricTaskInstance completedTask = historyService.createHistoricTaskInstanceQuery().taskId(activeTask.getId()).singleResult();
        final String completedTaskFormKey = formService.getTaskFormKey(completedTask.getProcessDefinitionId(), completedTask.getTaskDefinitionKey());
        assertEquals("Form key after completing task", activeTaskFormKey, completedTaskFormKey);
        final TaskFormData completedTaskFormData = formService.getTaskFormData(completedTask.getProcessInstanceId(), completedTask.getTaskDefinitionKey());
        assertEquals("Form key on data same as directly fetched", completedTaskFormKey, completedTaskFormData.getFormKey());
        final List<FormProperty> completedTaskFormProperties = completedTaskFormData.getFormProperties();
        assertEquals("Form properties after completing task", activeTaskFormProperties.size(), completedTaskFormProperties.size());

        // If the workflow itself is completed, can we still fetch the task form data?
        runtimeService.deleteProcessInstance(processId, "no good reason");
        assertNull("workflow completed", runtimeService.createProcessInstanceQuery().processInstanceId(processId).singleResult());
        assertNotNull("historic workflow recorded", historyService.createHistoricProcessInstanceQuery().processInstanceId(processId).singleResult());
        final String completedProcessTaskFormKey = formService.getTaskFormKey(completedTask.getProcessDefinitionId(), completedTask.getTaskDefinitionKey());
        assertEquals("Form key after completing task", activeTaskFormKey, completedProcessTaskFormKey);
        final TaskFormData completedProcessTaskFormData = formService.getTaskFormData(completedTask.getProcessInstanceId(), completedTask.getTaskDefinitionKey());
        assertEquals("Form key on data same as directly fetched", completedTaskFormKey, completedProcessTaskFormData.getFormKey());
        final List<FormProperty> completedProcessTaskFormProperties = completedTaskFormData.getFormProperties();
        assertEquals("Form properties after completing task", activeTaskFormProperties.size(), completedProcessTaskFormProperties.size());
    }

    private static Matcher<Task> taskWithName(String taskName) {
        final Matcher<? super String> delegate = Matchers.equalTo(taskName);
        return new TypeSafeMatcher<>() {
            @Override
            protected boolean matchesSafely(Task item) {
                return delegate.matches(item.getName());
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("task with name ").appendDescriptionOf(delegate);
            }
        };
    }

    private List<String> determineUnstartedActivities(String processId) {
        List<HistoricTaskInstance> startedTasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(processId).list();
        Set<String> startedTaskNames = startedTasks.stream()
                .map(HistoricTaskInstance::getTaskDefinitionKey)
                .collect(toSet());
        final ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processId).singleResult();
        final ReadOnlyProcessDefinition deployedProcessDefinition = ((RepositoryServiceImpl) repositoryService)
                .getDeployedProcessDefinition(processInstance.getProcessDefinitionId());

        return ActivitiWorkflowServiceImpl.getUnstartedTasks(startedTaskNames, deployedProcessDefinition);
    }

    // Add stuff normally pulled in by XML config in ecco-war
    @Configuration
    static class Config {
        @PersistenceContext
        EntityManager entityManager;

        @Bean public UserManagementServiceImpl userManagementService(SettingsService settingsService) {
            return new UserManagementServiceImpl(passwordEncoder(), entityManager, null,
                    settingsService, Mockito.mock(GroupRepository.class), Mockito.mock(SoftwareModuleService.class));
        }
        @Bean public MessageDigestPasswordEncoder passwordEncoder() {
            return new MessageDigestPasswordEncoder("SHA-1");
        }
        @Bean public CalendarService cosmoCalendarService() {
            return Mockito.mock(CalendarService.class);
        }
        @Bean public ReferralRepository referralRepository() {
            return Mockito.mock(ReferralRepository.class);
        }
        @Bean public SettingsService settingsService() {
            return new StubSettingsService();
        }
    }
}
