<definitions
        xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
        xmlns:activiti="http://activiti.org/bpmn"
        targetNamespace="Examples"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL
                      http://www.omg.org/spec/BPMN/2.0/20100501/BPMN20.xsd">

    <process id="myProcess" name="My First Process">
        <startEvent id="theStart"/>

        <sequenceFlow id='flow1' sourceRef='theStart' targetRef='firstTask'/>

        <userTask id="firstTask" name="My First Task">
            <documentation>
                Do something very complicated.
            </documentation>
            <potentialOwner>
                <resourceAssignmentExpression>
                    <formalExpression>anyone</formalExpression>
                </resourceAssignmentExpression>
            </potentialOwner>
        </userTask>

        <sequenceFlow id='flow2' sourceRef='firstTask' targetRef='secondTask'/>

        <userTask id="secondTask" name="My Second Task">
            <documentation>
                Check that the complicated thing was done properly.
            </documentation>
            <potentialOwner>
                <resourceAssignmentExpression>
                    <formalExpression>supervisor</formalExpression>
                </resourceAssignmentExpression>
            </potentialOwner>
        </userTask>

        <sequenceFlow id='flow3' sourceRef='secondTask' targetRef='theEnd'/>

        <endEvent id="theEnd"/>

    </process>

</definitions>