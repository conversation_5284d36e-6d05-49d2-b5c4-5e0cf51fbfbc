package com.ecco.workflow;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.ecco.workflow.WorkflowTask.Handle;

public interface WorkflowService {

    /**
     * Return true if we are or should be using Activiti workflow for the given instance (referral id) or can migrate
     */
    boolean activitiWorkflowEnabledFor(String processKey, String instanceKey);

    /**
     * Create a new instance of a workflow process.
     * @param processKey the key identifying the workflow to instantiate (e.g. demo-all)
     * @param instanceKey the key to be associated with the instance for future lookup (e.g. serviceRecipientId)
     * @return the instantiated workflow process instance
     */
    WorkflowInstance instantiateWorkflow(String processKey, String instanceKey);

    /**
     * List all the user tasks associated with a particular workflow process instance.
     * The tasks will be returned in the following order:<ol>
     *     <li>Completed tasks, ordered by completion timestamp.</li>
     *     <li>Active tasks, ordered by start timestamp.</li>
     *     <li>Future tasks, ordered by walking the process tree depth-first.</li>
     * </ol>
     *
     * @param instance the workflow process instance
     * @return the list of tasks
     */
    List<WorkflowTask> getWorkflowTasks(WorkflowInstance instance);

    /**
     * List all the user tasks associated with a particular workflow process instance.
     * The tasks will be returned in the following order:<ol>
     *     <li>Completed tasks, ordered by completion timestamp.</li>
     *     <li>Active tasks, ordered by start timestamp.</li>
     *     <li>Future tasks, ordered by walking the process tree depth-first.</li>
     * </ol>
     *
     * @param processKey the key identifying the workflow
     * @param instanceKey the key associated with the specific instance of the workflow upon instantiation
     * @return the list of tasks
     * @deprecated prefer {@link #getWorkflowTasks(WorkflowInstance)}
     */
    @Deprecated
    List<WorkflowTask> getWorkflowTasks(String processKey, String instanceKey);

    /**
     *
     * @param filterVariableKey - key for process instance variable containing comma-separated list of task names
     *                             to return in the order to be returned. If no variable exists, then a default order
     *                             is used.
     */
    List<WorkflowTask> getFilteredWorkflowTasks(String processKey, String instanceKey, String filterVariableKey,
            String tasksToShow);

    /** DEPRECATED??? But how do we use the handle */
    Map<String,Object> getProcessInstanceVariables(String workflowKey, String instanceKey);

    /**
     * Lookup a specific task instance based on its handle.
     *
     * @param handle the handle identifying the task instance
     * @return the task or null if not found, or no longer active.
     */
    WorkflowTask findActiveWorkflowTask(WorkflowTask.Handle handle);


    /**
     * Lookup a specific instance based on its handle.
     *
     * @param taskHandle the handle identifying the task instance
     * @return the task
     * @throws IllegalArgumentException if no matching task exists
     */
    WorkflowTask getActiveOrHistoricalWorkflowTask(Handle taskHandle);

    /**
     * Creates a task which is not associated with any specific workflow instance and is assigned to a specific user.
     *
     * @param template the template definition of the task to be created
     * @return the created task
     * @throws IllegalArgumentException if the template data is invalid
     */
    WorkflowTask createAdhocWorkflowTaskForUser(WorkflowTaskCreateTemplate template, String username);

    /**
     * Creates a task which is not associated with any specific workflow instance and is assigned to a candidate group.
     *
     * @param template the template definition of the task to be created
     * @return the created task
     * @throws IllegalArgumentException if the template data is invalid
     */
    WorkflowTask createAdhocWorkflowTaskForGroup(WorkflowTaskCreateTemplate template, String candidateGroup);

    /**
     * Mark a task as completed so that process execution advances to the next step on the execution path.
     * If the task is already completed, will return silently.
     *
     * @param taskCompleted the task to be marked as completed. Null allowed, in which case returns silently
     *
     * @throws IllegalArgumentException or IllegalStateException if task is not available
     */
    void completeWorkflowTask(WorkflowTask taskCompleted);

    /**
     * Ensure the task exists
     *
     * This is not required in activiti where we are strict with the workflow provided and the tasks available.
     * However, linear workflow has legacy logic where 'accept on service' is always available (for instance),
     * even if the linear TaskStatus may not actually exist. Therefore we ensure it does exist to start the created date.
     *
     * @param handle the handle identifying the task instance
     */
    void ensureUnplannedWorkflowTask(Handle handle);

    /**
     * Claim the task for a given user
     *
     * @param handle the handle identifying the task instance
     * @param username the username of the person claiming the task
     */
    void claimTask(Handle handle, String username);

    /**
     * Delegate the given workflow task to another user
     *
     * @param handle the handle identifying the task instance
     * @param username the username of the person to delegate the task to
     */
    void delegateTask(Handle handle, String username);

    /**
     * Provide a summary of all the {@link WorkflowTarget WorkflowTargets} who have active tasks to perform.
     * This includes assigned tasks and tasks where the user or group is a candidate to perform the task.
     * The summary will be returned in no particular order.
     *
     * @return summary information for all active targets
     */
    Collection<WorkflowTaskSummary> getActiveWorkflowTaskSummary();

    /**
     * List all the user tasks which a user can perform. This includes tasks which are already assigned
     * to the user, and tasks for which the user is either a candidate or is in a group which is a candidate.
     * The tasks will be returned in the following order:<ol>
     *     <li>Tasks with a due date which are assigned to the user, with the most pressing first.</li>
     *     <li>Tasks with a due date, with the most pressing first.</li>
     *     <li>Tasks which are assigned to the user, ordered by start timestamp ascending.</li>
     *     <li>Tasks which the user is a candidate for completing, ordered by start timestamp ascending.</li>
     * </ol>
     * @param username the username for whom to return a task list
     * @return the list of tasks
     */
    List<WorkflowTask> getAvailableWorkflowTasks(String username);

    /**
     * List all the user tasks which are assigned to a user
     * The tasks will be returned in the following order:<ol>
     *     <li>Tasks with a due date, with the most pressing first.</li>
     *     <li>Other tasks ordered by start timestamp ascending.</li>
     * </ol>
     * @param username the username for whom to return a task list
     * @return the list of tasks
     */
    List<WorkflowTask> getAssignedWorkflowTasks(String username);

    /**
     * List all the user tasks assigned to one or more candidateGroups.
     *
     * The tasks will be returned in the following order:<ol>
     *     <li>Tasks with a due date, with the most pressing first.</li>
     *     <li>Remaining tasks, ordered by start timestamp ascending.</li>
     * </ol>
     * @param candidateGroups the groups for whom to return a task list
     * @return the list of tasks
     */
    List<WorkflowTask> getAvailableWorkflowTasksForGroups(List<String> candidateGroups);

    /**
     * Retrieve the definition of a workflow task, for the supplied handle.
     *
     * @param handle
     *            identifies the task as defined in the workflow process
     *            definition of a specific version (deployment) of that workflow.
     * @return the definition of the requested task
     */
    WorkflowTaskDefinition getWorkflowTaskDefinition(WorkflowTaskDefinition.TaskDefinitionHandle handle);

    /**
     * Converts a list of tasks into a map by extracting the {@link WorkflowTask#getName()} as the key
     */
    Map<String, WorkflowTask> getWorkflowTasksAsMap(List<WorkflowTask> tasks);

    /**
     * ECCO ONLY: Delete the specfied workflow.
     */
    void deleteWorkflowInstance(WorkflowInstance.Handle instance, String reason);


}
