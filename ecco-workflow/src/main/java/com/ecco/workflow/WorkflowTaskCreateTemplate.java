package com.ecco.workflow;

import com.ecco.dto.BuildableDto;
import com.ecco.dto.DtoBuilder;
import com.ecco.dto.ProxyDtoBuilderProxy;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;

/**
 * This class defines an adhoc task to be created outside of a workflow instance with its associated task definitions.
 *
 * @since 02/01/2014
 */
public interface WorkflowTaskCreateTemplate extends Serializable, BuildableDto<WorkflowTaskCreateTemplate> {
    /** The name of the task for display purposes (a messages key or a literal display name). */
    String getName();

    /** The deadline by which the task should be completed before it is considered overdue. */
    LocalDateTime getDueDate();

    /** A collection of resources which relate to the task in some way and should be recorded against it. */
    Collection<WorkflowLinkedResource> getLinkedResources();

    interface Builder extends DtoBuilder<WorkflowTaskCreateTemplate> {
        Builder name(String name);
        Builder dueDate(LocalDateTime dueDate);
        Builder linkedResources(Collection<WorkflowLinkedResource> linkedResources);
    }

    final class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, WorkflowTaskCreateTemplate.class);
        }
    }
}
