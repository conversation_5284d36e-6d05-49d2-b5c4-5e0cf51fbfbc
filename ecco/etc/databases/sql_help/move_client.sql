-- clientId-from - the client which will be hidden, moving things to the other client
-- clientId-to - the client that will pick up a new referral

-- check the clients refer to the same thing
select c.* from contacts c inner join clientdetails cd on cd.contactsId=c.id where cd.id in (<clientId-from>, <clientId-to>);

-- find the referrals from clientId-from
select group_concat(id) as referralIds from referrals where clientId in (<clientId-from>);

-- see if the client has any calendar entries to move from clientId-from to clientId-to
select group_concat(c.id) as contactIdsFrom from contacts c inner join clientdetails cd on cd.contactsId=c.id where cd.id in (<clientId-from>);
select group_concat(id) as eventIds from events where referralId in (<referralIds>);
select * from contacts_events where eventId in (<eventIds>) and contactId in (<contactIdsFrom>);
-- if EMPTY then we don't have anything assigned to the client, so don't need to move anything
-- events with referrals are fine - referrals haven't changed


-- move the referral contactId
update referrals set clientId=<clientId-to> where clientId in (<clientId-from>);

-- could remove the previous clients with remove_client
<clientId-from>


-- check we have all the columns

-- SELECT DISTINCT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('contactId') AND TABLE_SCHEMA='ecco';
--+----------------------------+
--| TABLE_NAME                 |
--+----------------------------+
--| contacts_events            | - could be client calendar, can check using referralId
--| projectcomments            | - author
--| referralcomments           | - author
--| referrals_contacts         | - won't contain client
--| supporthractions           | - author
--| supporthrcomments          | - author
--| supporthroutcomes          | - ...
--| supporthrwork              |
--| supportplanactions         |
--| supportplananswers         |
--| supportplancomments        |
--| supportplanoutcomes        |
--| supportplanrisks           |
--| supportplanwork            |
--| supportthreatactions       |
--| supportthreatcomments      |
--| supportthreatoutcomes      |
--| supportthreatwork          |
--+----------------------------+
--22 rows in set (0.03 sec)

--mysql> SELECT DISTINCT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('contactsId') AND TABLE_SCHEMA='bridge';
--+---------------+
--| TABLE_NAME    |
--+---------------+
--| clientdetails | -- we don't need to change this as we leave the client alone
--| workers       | -- n/a for a client
--+---------------+
--2 rows in set (0.03 sec)
