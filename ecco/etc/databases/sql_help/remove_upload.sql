-- EVIDENCE UPLOADS
-- collect the ids from the evidence attachment drop down link, downloadhiddenhtml?id=<raid>
select group_concat(bytesId SEPARATOR ',') as bytesId from svcrec_attachments where id in (<raids>);
-- or, possibly easier, find the raids and bytesid from:
select * from svcrec_attachments where referralId=<rid>;
delete from svcrec_attachments where id in (<raids>);
delete from uploadbytes where id in (<bytesId>);



-- REFERRAL UPLOADS

-- by r-id
-- check the files
select * from svcrec_attachments where referralId in (<rid>);
select group_concat(bytesId SEPARATOR ',') as bytesId from svcrec_attachments where referralId in (<rid>);
delete from svcrec_attachments where referralid in (<rid>);
delete from uploadbytes where id in (<bytesId>);

-- by referralattachment-id (which shows on the link on the attachments page)
select group_concat(bytesId SEPARATOR ',') as bytesId2 from svcrec_attachments where id in (<aid>);
delete from svcrec_attachments where id in (<aid>);
delete from uploadbytes where id in (<bytesId2>);



-- LOGO UPLOAD
select * from uploadfile;
delete from uploadfile where xxx;
