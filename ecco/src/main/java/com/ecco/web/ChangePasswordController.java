package com.ecco.web;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.WebUtils;

import com.ecco.config.service.SettingsService;
import com.ecco.security.dom.User;
import com.ecco.security.service.UserManagementService;
import com.ecco.web.form.ChangePasswordForm;
import com.ecco.web.validators.ChangePasswordFormValidator;

@Controller
@SessionAttributes("changePasswordForm")
@RequestMapping("/secure/changePassword.html")
public class ChangePasswordController extends BasicPageController {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private UserManagementService userManagementService;

    @Autowired
    private ChangePasswordFormValidator changePasswordFormValidator;

    @Autowired
    SettingsService settingsService;

    @ModelAttribute
    @RequestMapping(params = "username")
    public ModelAndView init(@RequestParam String username, ModelMap model, HttpServletRequest request) {

        ChangePasswordForm changePasswordForm = (ChangePasswordForm) WebUtils
                .getSessionAttribute(request, "changePasswordForm");

        if (changePasswordForm == null) {
            changePasswordForm = new ChangePasswordForm();
            changePasswordForm.setUserName(username);
        }

        model.put("changePasswordForm", changePasswordForm);
        setReferenceData(model, request);
        return new ModelAndView("changePasswordDef", model);
    }

    @RequestMapping
    public ModelAndView change(@ModelAttribute("changePasswordForm") ChangePasswordForm changePasswordForm, ModelMap model,
            HttpServletRequest request) throws Exception {

        String pageName = "loginDef";

        // big hack have had to manually add errors to the binding
        final BindException errors = new BindException(changePasswordForm, "changePasswordForm");
        changePasswordFormValidator.validate(changePasswordForm, errors);

        User user = userManagementService.loadUserByUsername(changePasswordForm.getUserName());
        if (!errors.hasErrors()
                && !userManagementService.isPasswordValid(user, changePasswordForm.getOldPassword())) {
//                && !user.hasPassword(userManagementService.encodePassword(changePasswordForm.getOldPassword(), user))) {
            errors.rejectValue("oldPassword", "invalidOldPassword");
        }

        if (settingsService.settingFor(SettingsService.SecurityAuthnPasswd.NAMESPACE, "ENABLED").isTrue()) {
            if (!errors.hasErrors() && user.rejectsStrengthOfPassword(changePasswordForm.getNewPassword())) {
                String value = settingsService.settingFor(SettingsService.SecurityAuthnPasswd.NAMESPACE, "COMPLEXITY").getValue();
                errors.rejectValue("newPassword", "invalidNewPassword_"+ value);
            }

            if (!errors.hasErrors()
                    && user.hasPreviousPassword(changePasswordForm.getNewPassword())) {
                errors.rejectValue("newPassword", "newPasswordRejected");
            }
        }

        if (errors.hasErrors()) {
            pageName = "changePasswordDef";
            BeanPropertyBindingResult beanPropertyBindingResult = (BeanPropertyBindingResult) model
                    .get("org.springframework.validation.BindingResult.changePasswordForm");
            beanPropertyBindingResult.addAllErrors(errors);
        } else {
            user.setNewPassword(changePasswordForm.getNewPassword());
            userManagementService.updateUser(user);
        }

        setReferenceData(model, request);
        return new ModelAndView(pageName, model);
    }

}
