package com.ecco.web;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.security.SecurityUtil;
import net.fortuna.ical4j.data.CalendarBuilder;
import net.fortuna.ical4j.model.Calendar;
import org.osaf.cosmo.calendar.EntityConverter;
import org.osaf.cosmo.icalendar.ICalendarClientFilterManager;
import org.osaf.cosmo.icalendar.ICalendarConstants;
import org.osaf.cosmo.icalendar.ICalendarOutputter;
import org.osaf.cosmo.model.CollectionItem;
import org.osaf.cosmo.model.Item;
import org.osaf.cosmo.model.StampUtils;
import org.osaf.cosmo.service.ContentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.WebRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.StringReader;

/**
 * Based around cosmo WebcalServlet
 */
@Controller
@RequestMapping("/secure/feed/icalendar")
@WriteableTransaction
public class IcalendarFeedController implements IcalendarFeed {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Resource(name = "contentService")
    private ContentService contentService;
    @Autowired(required = false)
    private ICalendarClientFilterManager clientFilterManager;
    private final EntityConverter entityConverter = new EntityConverter(null);

    private final ReferralContentNavigationCalendarEnhancer calendarEnhancer = new ReferralContentNavigationCalendarEnhancer();

    @Override
    @RequestMapping(method = RequestMethod.GET)
    public ResponseEntity<String> getCalendarEntries(WebRequest request) throws IOException {

        Item item = contentService.findItemByUid(SecurityUtil.getAuthenticatedUser().getContact().getCalendarId());

        if (item == null) return new ResponseEntity<>(HttpStatus.NOT_FOUND);

        if (!(item instanceof CollectionItem collection))
            return new ResponseEntity<>("Requested item not a collection", HttpStatus.METHOD_NOT_ALLOWED);

        if (StampUtils.getCalendarCollectionStamp(collection) == null)
            return new ResponseEntity<>("Requested item not a calendar collection", HttpStatus.METHOD_NOT_ALLOWED);

        // we don't bother with etags which were calc'd as part of lastModified anyway
        if (request.checkNotModified(collection.getModifiedDate().getTime() / 1000 * 1000)) {
            return null;
        }

        // convert to iCalendar
        Calendar calendar = entityConverter.convertCollection(collection);

        // Filter if necessary so we play nicely with clients
        // that don't adhere to spec
        if (clientFilterManager != null) clientFilterManager.filterCalendar(calendar);

        calendarEnhancer.enhanceContent(calendar);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ICalendarOutputter.output(calendar, outputStream);

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.setContentType(MediaType.parseMediaType(ICalendarConstants.ICALENDAR_MEDIA_TYPE));
        String filename = SecurityUtil.getAuthenticatedUsername() + "." + ICalendarConstants.ICALENDAR_FILE_EXTENSION;
        responseHeaders.set("Content-Disposition", "attachment; filename=\"" + filename + "\"");
        responseHeaders.setLastModified(collection.getModifiedDate().getTime());

        return new ResponseEntity<>(outputStream.toString(), responseHeaders, HttpStatus.OK);
    }


    /**
     * Handle POST and PUT requests by ignoring them and logging (we prob should send 501)
     *  NOTE: PROPFIND already gets 501 due to not being HTTP (so doesn't have RequestMethod entry).
     */
    @RequestMapping(method = {RequestMethod.PUT, RequestMethod.POST})
    @ResponseBody
    @ResponseStatus(HttpStatus.NOT_IMPLEMENTED)
    public String put(HttpServletRequest request, @RequestBody String body) {

        log.info("Method: {}", request.getMethod());
        log.info("Body: \n{}", body);
        try {
            Calendar calendar = new CalendarBuilder().build(new StringReader(body));
            log.warn("Ignoring attempt to modify calendar event by: {}", calendar.getProductId());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

}
