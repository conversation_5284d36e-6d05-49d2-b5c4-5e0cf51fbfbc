package com.ecco.web.admin;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.security.acls.model.AclCache;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@RequestMapping("/secure/admin/clearAclCache.html")
public class ClearAclCacheController extends BaseAdminPageController {

    @Autowired(required=false)
    AclCache aclCache;

    @RequestMapping(method=RequestMethod.GET)
    public String reloadPage() {
        return "admin/clearAclCache";
    }

    @RequestMapping(method=RequestMethod.POST)
    @CacheEvict(allEntries=true, cacheNames= {"aclVisibilityService", "aclByGroups"}) // also need to clear this
    public String clearCache(ModelMap model, @RequestParam String submit) {
        aclCache.clearCache();
        model.put("success", true);
        return "admin/clearAclCache";
    }

}
