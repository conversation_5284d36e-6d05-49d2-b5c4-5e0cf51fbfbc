<?xml version="1.0" encoding="UTF-8"?>
<Context>
    <!-- see http://tomcat.apache.org/tomcat-8.0-doc/config/jar-scanner.html
    NOTE: For this to take effect, the original copy (renamed to the application name.xml)
    needs to be removed from the conf/Catalina/{hostname}/ directory.
    For development, this is something like:
    ~/.IntelliJIdea2018.2/system/tomcat/lh_-_ecco_(1)_ecco/conf/Catalina/localhost

    For this to deploy, the following line needs to be amended to set copyXML to true in conf/server.xml
    And note: IntelliJ overwrites it's own copy from the server install version so be sure to modify that one.
          <Host name="localhost" appBase="blah" copyXML="true" ...>

          Problem is: WARNING: A docBase [/home/<USER>/dev/ecco-git/ecco/ecco/target/apache-tomcat-8.5.32/webapps/ecco/target/ecco-war-1.0.0.CI-SNAPSHOT] inside the host appBase has been specified, and will be ignored
          Error is thrown from org.apache.catalina.webresources.StandardRoot.createMainResourceSet
          Need to find a way to step through in debugger ... which means we need to connect to the tomcat instance

      WARNING: Tomcat expects war to have been deployed to webapps directory when deploying via a context.xml
    -->
    <JarScanner scanClassPath="false" scanManifest="false">
        <JarScanFilter
                defaultTldScan="false"
                tldScan="spring-webmvc*.jar,spring-security-taglibs*.jar,jstl*.jar"/>
    </JarScanner>
    <!-- since Tomcat 9.0.21 and 8.5.42
      We want SameSite=None;Secure.
      See https://stackoverflow.com/a/57622508/1998186 and link to Tomcat changes.
      And the list of incompatible clients (note: iOS 12 users must upgrade)
      https://www.chromium.org/updates/same-site/incompatible-clients
      Docs: https://tomcat.apache.org/tomcat-8.5-doc/config/cookie-processor.html
      "strict" and "lax" work for normal JSP access but if we use "none" then we the browser won't store the
      cookie and tells us that we need to specify "secure", which requires HTTPS, so will not persist the cookies
      when used on localhost. See CookieConfigServletContextListener for how we can disable Secure for localhost.
      Also see  AUTH_FLOW set-cookie.
    -->
<!--    <CookieProcessor sameSiteCookies="strict" />-->
    <CookieProcessor sameSiteCookies="${cookie.samesite:-strict}" />
</Context>