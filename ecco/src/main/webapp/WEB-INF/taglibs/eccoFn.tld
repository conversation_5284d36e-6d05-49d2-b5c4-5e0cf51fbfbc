<?xml version="1.0" encoding="UTF-8" ?>

<taglib xmlns="http://java.sun.com/xml/ns/j2ee"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee web-jsptaglibrary_2_0.xsd"
    version="2.0">

    <tlib-version>1.0</tlib-version>
    <short-name>eccoFn</short-name>

    <function>
        <name>getRootCause</name>
        <function-class>com.ecco.web.jsp.EccoFn</function-class>
        <function-signature>
            java.lang.Throwable getRootCause(java.lang.Throwable)
        </function-signature>
    </function>

    <function>
        <name>setting</name>
        <function-class>com.ecco.web.jsp.EccoFn</function-class>
        <function-signature>
            java.lang.Object setting(java.lang.String, java.lang.Object)
        </function-signature>
    </function>

    <function>
        <name>settingContains</name>
        <function-class>com.ecco.web.jsp.EccoFn</function-class>
        <function-signature>
            java.lang.Object settingContains(java.lang.String, java.lang.Object, java.lang.String)
        </function-signature>
    </function>

    <function>
        <name>settingAsArray</name>
        <function-class>com.ecco.web.jsp.EccoFn</function-class>
        <function-signature>
            java.lang.String[] settingAsArray(java.lang.String,java.util.Set)
        </function-signature>
    </function>

    <function>
        <name>settingAsSet</name>
        <function-class>com.ecco.web.jsp.EccoFn</function-class>
        <function-signature>
            java.util.Set settingAsSet(java.lang.String,java.util.Set)
        </function-signature>
    </function>

    <function>
        <name>taskNameFromUri</name>
        <function-class>com.ecco.web.jsp.EccoFn</function-class>
        <function-signature>
            java.lang.String taskNameFromUri(java.net.URI)
        </function-signature>
    </function>

    <function>
        <name>settingsFromUri</name>
        <function-class>com.ecco.web.jsp.EccoFn</function-class>
        <function-signature>
            java.util.Set settingsFromUri(java.net.URI)
        </function-signature>
    </function>

</taglib>
