<%@tag body-content="scriptless" pageEncoding="UTF-8" trimDirectiveWhitespaces="true" %>


<%@attribute name="classes" type="java.lang.String" required="false"
        description="Additional classes to add to the &lt;html&gt; element." %>
<%@attribute name="title" type="java.lang.String" required="true"
        description="The title of the page." %>
<%@attribute name="importRequireJsModules" type="java.lang.String" required="false"
        description="A space-separated list of require.js modules to import." %>
<%@attribute name="disableBootstrapPatch" type="java.lang.Boolean" required="false"
        description="If the bootstrap patch css should be linked." %>

<%@attribute name="useNoCache" type="java.lang.Boolean" required="false"
        description="True if this page should resources/noCache/ for resources." %>

<%@attribute name="addFooter" type="java.lang.Boolean" required="false"
        description="Optional. Set true to add footer" %>

<%@attribute name="isTransitionalUI" type="java.lang.Boolean" required="false"
        description="optional - true if want JQUI etc CSS/JS including" %>

<%@attribute name="importHeadFiles" type="java.lang.String" required="false"
        description="optional - comma separated list of files to include in head" %>

<%@attribute name="printable" type="java.lang.Boolean" required="false"
             description="True if this page should be presented for printing." %>

<%@attribute name="muiMultipleWorkaround" type="java.lang.Boolean" required="false"
             description="True if this page should workaround custom form importing a second mui (see commits)." %>

<%@attribute name="devMode" type="java.lang.Boolean" required="true"
             description="True if this page should use dev resources." %>

<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page" %>
<%@taglib prefix="requireJs" tagdir="/WEB-INF/tags/requireJs" %>
<%@taglib prefix="spring" uri="http://www.springframework.org/tags" %>

<c:set var="resourcePath" scope="request"
  value="${applicationProperties.resourceRootPath}"/>

<page:page-doctype/>
<html class="v2 ${classes}">
    <head>
        <meta http-equiv="content-type" content="text/html; charset=utf-8">
        <meta http-equiv="X-UA-Compatible" content="chrome=1,IE=edge">
        <%-- maximum-scale is a HACK for bldg checklist on safari iOS 9.1 see https://eccosolutions.zendesk.com/agent/tickets/826 --%>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1">

        <!--
        Java Version             <c:out value="${applicationProperties.javaVersion}"/>
        Build CommitId:          <c:out value="${applicationProperties.gitCommitId}"/>
        Build Branch:            <c:out value="${applicationProperties.gitBranch}"/>
        Build CommitTime:        <c:out value="${applicationProperties.gitCommitTime}"/>
        -->

        <link rel="icon" href="${resourcePath}themes/ecco/images/favicon.ico">
        <link rel="shortcut icon" href="${resourcePath}themes/ecco/images/favicon.ico">

        <page:page-title title="${title}"/>

        <c:if test="${useNoCache}">
            <c:url var="resourcePath" value="/r/noCache/" scope="request"/>
        </c:if>

        <link rel="stylesheet" href="${resourcePath}bootstrap/css/bootstrap.min.css">
        <c:if test="${!disableBootstrapPatch}">
            <link rel="stylesheet" href="${resourcePath}bootstrap/css/bootstrap-modal-bs3patch.css">
            <link rel="stylesheet" href="${resourcePath}bootstrap/css/bootstrap-modal.css">
        </c:if>
        <link rel="stylesheet" href="${resourcePath}bootstrap/css/bootstrap-theme.min.css">

        <link rel="stylesheet" href="${resourcePath}font-awesome/css/font-awesome.min.css">
        <link rel="stylesheet" href="${resourcePath}css/typeahead.css">
        <link rel="stylesheet" href="${resourcePath}css/jqueryui/jqueryui.css" type="text/css" media="screen, projection">
        <link rel="stylesheet" href="${resourcePath}css/common/common.css">
        <link rel="stylesheet" href="${resourcePath}css/common/evidence.css">
        <link rel="stylesheet" href="${resourcePath}css/common/house_v2.css">
        <link rel="stylesheet" href="${resourcePath}css/offline/offline.css">
        <link rel="stylesheet" href="${resourcePath}css/common/banner.css">
        <link rel="stylesheet" href="${resourcePath}css/select2/select2.min.css">

        <link rel="stylesheet" href="${resourcePath}scripts/jquery.jqplot.min.css" type="text/css">

        <c:if test="${isTransitionalUI}"> <!-- Online only stuff here. Anything above must be available offline -->
            <link rel="stylesheet" href="${resourcePath}css/jqueryui/jquery-popbox.css" type="text/css" media="screen, projection">
            <link rel="stylesheet" href="${resourcePath}themes/standard/housestyle_themestandard.css">
            <link rel="stylesheet" href="${resourcePath}/css/main-styles.css">
            <link rel="stylesheet" href="${resourcePath}css/editable/editable.css"/>
        </c:if>

        <c:url var="apiPath" value="/api/"/>
        <requireJs:init root="${resourcePath}" apiPath="${apiPath}" devMode="${devMode}"/>
        <requireJs:import modules="${importRequireJsModules}"/>
        <c:if test="${isTransitionalUI}">
            <requireJs:import modules="jquery-amd/common jquery-ui"/>
            <%@ include file="/WEB-INF/views/tiles/main_head_jsbase.jsp" %>
        </c:if>

        <page:page-head-custom files="${importHeadFiles}"/>

        <c:if test="${muiMultipleWorkaround}">
            <style>.MuiStepButton-vertical {justify-content: flex-start !important}</style>
            <%-- the extra MuiButtonBase-root causes the cancel/submit buttons out of style
                 but we only need to override the containedPrimary, but then we need to consider disabled --%>
            <style>.MuiButton-containedPrimary.Mui-disabled {
                color: rgba(0, 0, 0, 0.26) !important;
                background-color: rgba(0, 0, 0, 0.12) !important;
            }</style>
            <style>.MuiButton-containedPrimary {
                color: white !important;
                background-color: #0d83ca !important;
                border-radius: 4px !important;
                padding: 6px 16px !important;
            }</style>
        </c:if>

        <%-- visiable-print-block class wants to be visible, but is bootstrap specific on @media print, as is hidden-print and navbar etc--%>
        <%-- we want to show users the page as its printed, and to do so without bootstrap specifics --%>
        <%-- so we override the bootstrap display for printable pages --%>
        <c:if test="${printable}">
            <style>
                .visible-on-print {
                    display: block!important;
                }
            </style>
        </c:if>

    </head>
    <body data-session-len="${empty sessionLength ? 0 : sessionLength}">

        <%-- COPIED SECTION ALSO IN clean_notiles.jsp --%>
        <div class="visible-print-block visible-on-print">
            <div style="margin-top: 50px;">&nbsp;</div>

            <div style="width: 75%; margin-left: auto; margin-right: auto; text-align: center;">

                <c:if test="${empty footerImageUrl}">
                    <img style="float: left; margin-bottom:-50px;" src="${resourcePath}themes/ecco/images/logo_greyscale.png" alt="logo"/>
                </c:if>
                <c:if test="${not empty footerImageUrl}">
                    <img style="float: left; margin-bottom:-50px; max-height: 75px;" src="${footerImageUrl}" alt="${organisationName}"/>
                </c:if>

                <span style="float: left; text-align: left; margin-left: 140px; margin-top: 7px;"><c:out value="${siteTitle}"/></span>

                <div style="float: right;">
                    at <c:out value="${generatedDateTime}"/>
                    <br/>
                    by <c:out value="${loggedInUsername}"/>
                </div>
                <div style="clear: both;">&nbsp;</div>
            </div>
            <div style="width: 75%; margin-left: auto; margin-right: auto; text-align: center;">

                <span style="font-size: 1.2em; font-weight: bold;"><spring:message code="organisationName"/></span>
                <br/>
            </div>
        </div>
        <%-- COPIED SECTION ALSO IN clean_notiles.jsp --%>

        <jsp:doBody/>
        <c:if test="${addFooter}">
            <div id="footer" class="hidden-print">
                <page:footer imagesBase="${imagesBase}" footerImageUrl="${footerImageUrl}" organisationName="${organisationName}" showFooterLinks="true"/>
            </div>
        </c:if>
        <div id="snackbar"></div>
        <script>
            <c:if test="${printable}">
                <%-- we only hide the logo on .navbar to retain navigation --%>
                [].forEach.call(document.querySelectorAll('.navbar-brand'), function (el) {
                    el.style.visibility = 'hidden';
                });
            </c:if>

            var colourOverride = localStorage && localStorage['colourOverride'];
            if (colourOverride) {
                document.getElementsByTagName("html")[0].style.background = colourOverride;
                document.getElementsByTagName("body")[0].style.background = colourOverride;
                var id = document.getElementById("banner-container");
                if (id) { id.style.backgroundColor = colourOverride; }
                var navs = document.getElementsByClassName("navbar-collapse");
                if (navs && navs.length > 0) { navs[0].style.backgroundColor = colourOverride; }
            }
        </script>
    </body>
</html>