<%@tag body-content="scriptless" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"
    description="Make the JSP content conditional on the setting being enabled"%>

<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@attribute name="setting" type="java.lang.String" required="true"%>

<%-- settingService should be listed in 'exposedContextBeans' of the view resolver --%>
<c:set var="text" value="${settingsService.settingFor('com.ecco.core',setting).value}"/>
<c:if test="${not empty text}">
    <div class="container-fluid" >
        <div class="alert alert-warning" style="margin-top:20px; margin-bottom:0;">
            ${settingsService.settingFor('com.ecco.core',setting).markdownAsHtml}
        </div>
    </div>
    <br>
</c:if>
