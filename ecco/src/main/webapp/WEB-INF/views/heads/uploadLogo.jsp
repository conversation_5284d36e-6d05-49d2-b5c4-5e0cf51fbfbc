<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>

<%@ include file="/WEB-INF/views/heads/upload.jsp" %>

<script type="text/javascript">

    function handleFileUploadSuccess(json) { linkLogoToFile(json.fileId); }

    function linkLogoToFile(fileId) {
        jQuery.ajax({
                type: 'POST',
                url: '${urlServletBase}/api/settings/logo/linkHidden/' + fileId,
                success: handleLogoLinkResponse,
                dataType: 'json'
        });
    }

    function handleLogoLinkResponse(data, textStatus, jqXHR) {
        var msg;
        if (data.success) {
            jQuery("#footerLogo").attr("src", data.logoImageUrl);
            msg = "Logo saved successfully.";
        } else {
            msg = "The system encountered an error and was unable to link to the new image. " +
                    "Error message from server: " + data.error;
        }
        alertUser(msg, data.success);
    }


    function alertUser(msg, success) {
        var logoMsgElement = jQuery("#logoMsg");
        if (logoMsgElement.length == 0) {
            logoMsgElement = jQuery("div.entityForm").prepend('<p id="logoMsg" />');
            logoMsgElement = jQuery("#logoMsg")
            logoMsgElement.css("text-align", "center");
            logoMsgElement.css("margin-bottom", "10px");
        }
        logoMsgElement.css("color", success ? "green" : "red");
        logoMsgElement.html(msg);
    }


</script>
