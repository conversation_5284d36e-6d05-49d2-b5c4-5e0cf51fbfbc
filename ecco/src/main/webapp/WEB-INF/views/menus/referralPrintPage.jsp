<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@ include file="/WEB-INF/views/includes.jsp" %>

<div style="text-align: center; width: 90%; margin: 0 auto;">
    <span style="font-size: 2em; font-weight: bold;">
        <spring:message var="titleTxt" code="${referralPrintPageTitleCode}" text="${referralPrintPageTitleCode}"/>
        <span id="printable-title">${titleTxt}</span>
    </span>
    <c:set var="hideBack" value="true" scope="request"/>

    <div class="row">

        <div class="col-sm-3">
            <c:if test="${not empty referral.client.contact.avatarId}">
                <spring:url var="url" value="/api/secure/images/${referral.client.contact.avatarId}/"/>
                <img style="margin: 10px;" src="${url}" width="150" alt="photo"/>
            </c:if>
        </div>
        <div class="col-sm-6">
            <%@ include file="/WEB-INF/views/menus/referral.jsp" %>
            <%-- additional detail relating to referral --%>
            <span style="font-size: 0.9em;">
                <c:if test="${(empty referral.receivingServiceDate) and (not empty referral.acceptedOnService)}">
                    <c:set var="dte" value="${medFn:userFriendlyDateFromLocal_usingJodaDateTime(referral.decisionMadeOn)}" />
                    <spring:message code="referralState.accepted"/>: ${dte}<br/>
                </c:if>
                <c:if test="${not empty referral.receivingServiceDate}">
                    <c:set var="dte" value="${medFn:userFriendlyDateFromLocal_usingJodaDateTime(referral.receivingServiceDate)}" />
                    <spring:message code="referralState.started"/>: ${dte}<br/>
                </c:if>
            </span>
        </div>
    </div>
</div>

