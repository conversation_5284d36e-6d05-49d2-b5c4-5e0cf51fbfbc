<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%@include file="/WEB-INF/views/includes.jsp" %>

<%@taglib prefix="page" tagdir="/WEB-INF/tags/page"%>
<%@taglib prefix="requireJs" tagdir="/WEB-INF/tags/requireJs"%>

<page:page-doctype/>
<html>

<head>
<page:page-head-meta/>

<page:page-head applicationProperties="${applicationProperties}"
        titleRaw="${titleRaw}"
        titleMessageKey="${titleMessageKey}"/>

<c:url var="apiPath" value="/api/"/>
<requireJs:init root="${applicationProperties.resourceRootPath}" apiPath="${apiPath}"  devMode="${devMode}"/>
<requireJs:import modules="jquery-amd/common jquery-ui"/>

<c:if test="${not empty head}">
    <c:import url="/WEB-INF/views/heads/${head}.jsp"/>
</c:if>

<c:import url="/WEB-INF/views/heads/jquery.ui.style.jsp"/>

</head>

<body data-session-len="${empty sessionLength ? 0 : sessionLength}">

<div id="dialogContent"></div>



<%-- COPIED SECTION ALSO IN rich-client-page.tag --%>
<div style="margin-top: 50px;">&nbsp;</div>

<div style="width: 75%; margin-left: auto; margin-right: auto; text-align: center;">

    <c:if test="${empty footerImageUrl}">
        <img style="float: left; margin-bottom:-50px;" src="${imagesBase}/logo_greyscale.png" alt="logo"/>
    </c:if>
    <c:if test="${not empty footerImageUrl}">
        <img style="float: left; margin-bottom:-50px; max-height: 75px;" src="${footerImageUrl}" alt="${organisationName}"/>
    </c:if>

    <span style="float: left; text-align: left; margin-left: 140px; margin-top: 7px;"><c:out value="${siteTitle}"/></span>

    <div style="float: right;">
        at <c:out value="${generatedDateTime}"/>
        <br/>
        by <c:out value="${loggedInUsername}"/>
    </div>
    <div style="clear: both;">&nbsp;</div>
</div>
<div style="width: 75%; margin-left: auto; margin-right: auto; text-align: center;">

    <span style="font-size: 1.2em; font-weight: bold;"><spring:message code="organisationName"/></span>
    <br/>
<%-- COPIED SECTION ALSO IN rich-client-page.tag --%>



    <c:choose>
    <c:when test="${useMenu}">
        <c:import url="/WEB-INF/views/menus/${menu}.jsp"/>
    </c:when>
    <c:otherwise>
        <span style="font-size: 2em; font-weight: bold;"><c:out value="${transformedName}"/></span>
    </c:otherwise>
    </c:choose>
</div>

<c:if test="${not empty actionbar}">
    <div id="block-contentActionbar" style="display:none;" class="jsShow">
        <div id="block-contentActionbar_wrapper">
            <c:import url="/WEB-INF/views/content/${actionbar}.jsp"/>
        </div>
    </div>
</c:if>

<div>
    <c:import url="/WEB-INF/views/content/${content}.jsp"/>
    <br>
</div>

<div style="width: 75%; margin-left: auto; margin-right: auto; text-align: center;">
    <spring:message code="strapLine" text=""/>
</div>
<div style="margin-top: 50px;">&nbsp;</div>

<img id="ajax-loader-img" src="${imagesBase}/ajax-loader.gif" title="loading" style="display: none;" />

</body>

</html>
