<%@page isErrorPage="true" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@include file="/WEB-INF/views/pathvars.jsp" %>

<%@taglib prefix="ecco" tagdir="/WEB-INF/tags"%>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@taglib prefix="page" tagdir="/WEB-INF/tags/page"%>

<c:if test="${not empty title}">
    <fmt:message var="titleRaw" key="${title}" scope="request"/>
    <c:set var="title" value="${null}" scope="request" />
</c:if>

<!-- main_wide_bordered.jsp -->
<%--NOTE REFERRAL SPECIFIC BITS for image --%>
<ecco:main_dev title="" classes="layout-wide" contentFragPath="views/content/${content}" devMode="${devMode}">
    <jsp:attribute name="headFrag">
        <%@ include file="/WEB-INF/views/tiles/main_head_js.jsp" %> <%--includes /WEB-INF/views/heads/${head}.jsp --%>
    </jsp:attribute>
    <jsp:attribute name="menuFrag">
        <div <c:if test="${error}">class="errortext"</c:if>>
            <h1><c:out value="${titleRaw}"/></h1>
        </div>
        <c:if test="${not empty menu}">
            <c:import url="/WEB-INF/views/menus/${menu}.jsp"/>
        </c:if>
    </jsp:attribute>
    <jsp:attribute name="actionbarFrag">
        <c:if test="${not empty actionbar}">
            <div class="clearfix rounded actionbar">
                <c:import url="/WEB-INF/views/content/${actionbar}.jsp"/>
            </div>
        </c:if>
    </jsp:attribute>
    <jsp:body>
        <c:if test="${not empty content}">
            <div class="row">
                <div class="col-xs-12 rounded text-center" style="min-height:275px">
                    <%-- Content should deal with borders such as "col-xs-12 rounded" see charts-layout.jsp --%>
                    <c:import url="/WEB-INF/views/content/${content}.jsp"/>
                </div>
            </div>
        </c:if>
    </jsp:body>
</ecco:main_dev>

