<?xml version="1.0" encoding="UTF-8"?>

<web-app xmlns="http://java.sun.com/xml/ns/javaee"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
        version="3.0"
        metadata-complete="true">
    <!-- metadata-complete suppresses Servlet 3 annotation scanning to save startup time
         absolute-ordering prevents other classpath scanning
         see https://stackoverflow.com/a/28251777/1998186
         -->
    <absolute-ordering/>

    <display-name>ecco</display-name>
    <description>ecco web application</description>


    <!--
      - Key of the system property that should specify the root directory of this
      - web app. Applied by WebAppRootListener or Log4jConfigListener.
      -->
    <context-param>
        <param-name>webAppRootKey</param-name>
        <param-value>ecco.root</param-value>
    </context-param>


    <!-- CONTEXT AND SERVLETS-->

    <!-- start off with the log - so it loads first! -->
    <context-param>
        <param-name>log4jConfiguration</param-name>
        <!-- See https://tomcat.apache.org/tomcat-9.0-doc/config/systemprops.html regarding ${props} substitution - e.g. K8s stuff too -->
        <!-- If lucky, ${sys:env} may resolve environment variables -->
        <!-- CHANGE on premise - nothing else seems to work! -->
        <param-value>classpath:log4j-${env}.xml</param-value>
    </context-param>
    <listener id="log4j">
        <listener-class>org.apache.logging.log4j.web.Log4jServletContextListener</listener-class>
    </listener>
    <!--     <absolute-ordering> -->
    <!--         <name>log4j2</name> -->
    <!--         <others /> -->
    <!--     </absolute-ordering> -->


    <context-param>
        <param-name>contextInitializerClasses</param-name>
        <param-value>com.ecco.infrastructure.config.root.EccoApplicationContextInitializer</param-value>
    </context-param>

    <context-param>
        <param-name>contextClass</param-name>
        <param-value>org.springframework.web.context.support.AnnotationConfigWebApplicationContext</param-value>
    </context-param>

    <!-- Location of Java @Configuration classes that configure the components that makeup this application -->
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>com.ecco.webapp.config.root.RootConfig,com.ecco.infrastructure.config.root.ServletRootConfig</param-value>
    </context-param>

    <!-- latest servlet versions allows this to execute before the load-on-startup=1 -->
    <!-- this loads the contextConfigLocation -->
    <!-- and allows access to the beans from all servlets (use -servlet for context-specific info) -->
    <listener>
        <!-- this loads the spring via an ApplicationContext (don't use the BeanFactory) -->
        <listener-class>com.ecco.webapp.loader.StackTraceSanitisingContextLoaderListener</listener-class>
    </listener>

    <listener>
        <listener-class>com.ecco.infrastructure.web.CookieConfigServletContextListener</listener-class>
    </listener>

    <!-- calls main/webapp/index.jsp which does a redirect to welcome -->
    <welcome-file-list>
        <welcome-file>/index.jsp</welcome-file>
    </welcome-file-list>


    <!-- ERROR HANDLING -->

    <!--  catch other view technology errors at runtime (encountered only on page) -->
    <error-page>
        <exception-type>org.springframework.security.web.firewall.RequestRejectedException</exception-type>
        <location>/WEB-INF/views/runtimeErrorRequestRejected.jsp</location>
    </error-page>
    <!--  catch other view technology errors at runtime (encountered only on page) -->
    <error-page>
        <!--
        - throwable covers both errors and exceptions -
        - if it gets here its bypassed spring's default error handling and therefore must be a view error -
        -->
        <exception-type>java.lang.Throwable</exception-type>
        <location>/WEB-INF/views/runtimeError.jsp</location>
    </error-page>

    <error-page>
        <error-code>404</error-code>
        <location>/WEB-INF/views/runtimeErrorHttp404.jsp</location>
    </error-page>
    <!-- FIXME the following does not seem to catch the 500 status? -->
    <!-- had an error, enabled this, still throws 500 despite below -->
    <!-- although error log seems to show exception in handling this file -->
    <!-- 500 = internal error (can be caused by tiles etc - so try to catch it) - eg do a forEach step of -1 -->
    <!-- does seem to work - i've seen the right error message -->
    <!-- and the servlet exception works too (at the moment though the tiles error shows the content - not the error message!) -->
    <!-- so put these back when live - at the moment it hides our errors -->
    <!-- recreate the errors by providing a tag with an invalid attribute -->
    <error-page>
        <error-code>500</error-code>
        <location>/WEB-INF/views/runtimeErrorHttp500.jsp</location>
    </error-page>


    <!-- SESSION HANDLING -->

    <session-config>
        <session-timeout>60</session-timeout>
        <tracking-mode>COOKIE</tracking-mode>
    </session-config>


    <!-- ENCODING HANDLING -->

    <!-- For any HTTP message that does not specify a character encoding, assume that -->
    <!-- the character encoding is UTF-8. -->
    <!-- In particular this is important for HTML forms. When a browser POSTs HTML form -->
    <!-- data, it encodes the form data using the same character encoding as the page -->
    <!-- that contained the form, but it does not specify a character encoding in the -->
    <!-- HTTP request. -->
    <!-- Since all of our HTML pages are served as UTF-8, we must assume that form data -->
    <!-- is also encoded as UTF-8, unless it is explicitly marked otherwise. -->
    <!-- TODO: Ideally we should only modify incoming messages, and only then if they -->
    <!-- contain form data, but this behaviour doesn't break anything for now. -->
    <filter>
        <filter-name>encodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>encodingFilter</filter-name>
        <url-pattern>/api/*</url-pattern>
        <url-pattern>/dynamic/*</url-pattern>
        <url-pattern>/nav/*</url-pattern>
        <url-pattern>/p/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>resourceVersionKeyFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>resourceVersionKeyFilter</filter-name>
        <url-pattern>/dynamic/*</url-pattern>
        <url-pattern>/nav/*</url-pattern>
        <url-pattern>/p/*</url-pattern>
    </filter-mapping>

    <!-- as per https://stackoverflow.com/questions/30020188/how-to-configure-spring-hateoas-behind-proxy -->
    <!-- perhaps convert this to RootConfig.java (or soon to be ServletRootConfig) or HttpSecurity addFilterBefore? -->
    <!-- TODO for spring boot see https://docs.spring.io/spring-boot/docs/current/reference/html/howto.html -->
    <filter>
        <filter-name>forwardedHeaderFilter</filter-name>
        <filter-class>org.springframework.web.filter.ForwardedHeaderFilter</filter-class>
        <!-- note relativeRedirects defaults to false - see https://github.com/spring-projects/spring-framework/issues/20273 -->
        <!--
        <init-param>
            <param-name>relativeRedirects</param-name>
            <param-value>true</param-value>
        </init-param>
        -->
    </filter>
    <filter-mapping>
        <filter-name>forwardedHeaderFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!--
     Should be replaced by WebSecurityConfigurer#requestContextListener()
     but whilst the bean is registered there, it isn't called from ecco-war (but does from spring boot)
    -->
    <filter>
        <!-- So that we have RequestContextHolder usage available in Spring Security chain -->
        <filter-name>requestContextFilter</filter-name>
        <filter-class>org.springframework.web.filter.RequestContextFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>requestContextFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- SECURITY HANDLING -->
    <filter>
        <filter-name>springsecurity</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetBeanName</param-name>
            <param-value>springSecurityFilterChain</param-value>
        </init-param>
    </filter>
    <!-- monitor when a session is created and ended - so can check if user logs on twice -->
    <!--
    <listener>
      <listener-class>org.springframework.security.ui.session.HttpSessionEventPublisher</listener-class>
    </listener>
    -->
    <filter-mapping>
        <filter-name>springsecurity</filter-name>
        <!-- We want to know who is authenticated -->
        <!--<url-pattern>/nav/index.html</url-pattern>
        <url-pattern>/nav/r/*</url-pattern>
        <url-pattern>/nav/w/*</url-pattern>-->
        <url-pattern>/nav/*</url-pattern>
        <url-pattern>/dynamic/secure/*</url-pattern>
        <url-pattern>/online/*</url-pattern>
        <url-pattern>/api/*</url-pattern>
        <url-pattern>/oauth2/*</url-pattern>
        <url-pattern>/login/*</url-pattern>
        <url-pattern>/magic/*</url-pattern>
    </filter-mapping>


    <!-- CLICKSTREAM -->
    <!-- this may not trap the Spring Security pages, but at least it won't interfere with the security -->
    <filter>
        <filter-name>clickstream</filter-name>
        <filter-class>com.opensymphony.clickstream.ClickstreamFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>clickstream</filter-name>
        <url-pattern>/p/r/*</url-pattern>
        <url-pattern>/nav/secure/*</url-pattern>
        <url-pattern>/online/*</url-pattern>
        <url-pattern>/dynamic/secure/*</url-pattern>
    </filter-mapping>
    <listener>
        <listener-class>com.opensymphony.clickstream.ClickstreamListener</listener-class>
    </listener>


    <!-- LOGGING -->
    <filter>
        <filter-name>httplog</filter-name>
        <filter-class>com.ecco.web.filter.HttpLogFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>httplog</filter-name>
        <url-pattern>/nav/*</url-pattern> <!-- Same as dynamic except for app-cache behaviour (works around some appcache hell) -->
        <url-pattern>/dynamic/*</url-pattern>
        <url-pattern>/api/*</url-pattern>
        <url-pattern>/offline/*</url-pattern>
        <url-pattern>/online/*</url-pattern>
        <url-pattern>/p/*</url-pattern>
        <url-pattern>/oauth2/*</url-pattern>
        <url-pattern>/login/*</url-pattern>
    </filter-mapping>

    <!-- ETag filter being innermost (certainly inside logging) seems to make sense -->
    <filter>
       <filter-name>etagFilter</filter-name>
       <filter-class>org.springframework.web.filter.ShallowEtagHeaderFilter</filter-class>
    </filter>
    <filter-mapping>
       <filter-name>etagFilter</filter-name>
        <url-pattern>/api/*</url-pattern>
        <url-pattern>/online/*</url-pattern>
    </filter-mapping>

    <!-- springs tags don't default to escaping input! -->
    <!-- the form and error tags now do but we ensure the rest do also (eg bind) -->
    <!-- note that we consistently use c:out which escapes by default -->
    <!-- because the el format ${var} does NOT escape by default! -->
    <!-- this pretty much prevents XSS attacks -->
    <context-param>
        <param-name>defaultHtmlEscape</param-name>
        <param-value>true</param-value>
    </context-param>

    <servlet>
        <servlet-name>ecco</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextClass</param-name>
            <param-value>org.springframework.web.context.support.AnnotationConfigWebApplicationContext</param-value>
        </init-param>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>com.ecco.webapp.config.servlet.WebServletConfig</param-value>
        </init-param>
        <!-- only useful if tomcat debugging -->
        <init-param>
            <param-name>debug</param-name>
            <param-value>0</param-value>
        </init-param>
        <!-- don't show file listings -->
        <init-param>
            <param-name>listings</param-name>
            <param-value>false</param-value>
        </init-param>
        <!-- don't check to re-compile jsps -->
        <!-- or allow, and change the check interval -->
        <!--
        <init-param>
            <param-name>development</param-name>
            <param-value>false</param-value>
        </init-param>
        <init-param>
            <param-name>modificationTestInterval</param-name>
            <param-value>4</param-value>
        </init-param>
        -->
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet>
        <servlet-name>ecco-offline</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextClass</param-name>
            <param-value>org.springframework.web.context.support.AnnotationConfigWebApplicationContext</param-value>
        </init-param>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>com.ecco.offline.OfflineServletConfig</param-value>
        </init-param>
        <!-- Under tomcat 8.5 We get an NPE
            at org.apache.catalina.mapper.Mapper.internalMapWrapper(Mapper.java:861)
        	at org.apache.catalina.core.ApplicationContext.getRequestDispatcher(ApplicationContext.java:486)
            due to the webapp not being listed in the map yet.  The servlet does start okay on first request
            See https://eccosolutions.atlassian.net/browse/DEV-730
         -->
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet>
        <servlet-name>ecco-web-api</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextClass</param-name>
            <param-value>org.springframework.web.context.support.AnnotationConfigWebApplicationContext</param-value>
        </init-param>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>com.ecco.webapp.config.servlet.WebApiServletConfig</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet>
        <servlet-name>resources</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextClass</param-name>
            <param-value>org.springframework.web.context.support.AnnotationConfigWebApplicationContext</param-value>
        </init-param>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>com.ecco.webapp.config.servlet.ecco.ResourcesServletConfig</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>ecco</servlet-name>
        <!-- NB if we change this, we need to change other parts of this file -->
        <!-- the jsp redirections, sitemesh definitions, the application...-web -->
        <!-- and parts of the ui-messages.properties -->
        <url-pattern>/p/*</url-pattern>
        <url-pattern>/nav/*</url-pattern>
        <url-pattern>/dynamic/*</url-pattern>
        <url-pattern>/online/*</url-pattern>
        <url-pattern>/oauth2/*</url-pattern>
        <url-pattern>/login/*</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>ecco-offline</servlet-name>
        <url-pattern>/ui/*</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>ecco-web-api</servlet-name>
        <url-pattern>/api/*</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>resources</servlet-name>
        <url-pattern>/r/*</url-pattern>
    </servlet-mapping>

    <!-- conf/web.xml has defaults, but not latest ms ones... http://don-software-dev.blogspot.com/2009/08/tomcat-mime-type-settings.html -->
    <mime-mapping>
        <extension>docm</extension>
        <mime-type>application/vnd.ms-word.document.macroEnabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>docx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.wordprocessingml.document</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dotm</extension>
        <mime-type>application/vnd.ms-word.template.macroEnabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dotx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.wordprocessingml.template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>potm</extension>
        <mime-type>application/vnd.ms-powerpoint.template.macroEnabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>potx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.presentationml.template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ppam</extension>
        <mime-type>application/vnd.ms-powerpoint.addin.macroEnabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ppsm</extension>
        <mime-type>application/vnd.ms-powerpoint.slideshow.macroEnabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ppsx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.presentationml.slideshow</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pptm</extension>
        <mime-type>application/vnd.ms-powerpoint.presentation.macroEnabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pptx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.presentationml.presentation</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlam</extension>
        <mime-type>application/vnd.ms-excel.addin.macroEnabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlsb</extension>
        <mime-type>application/vnd.ms-excel.sheet.binary.macroEnabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlsm</extension>
        <mime-type>application/vnd.ms-excel.sheet.macroEnabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlsx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xltm</extension>
        <mime-type>application/vnd.ms-excel.template.macroEnabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xltx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.spreadsheetml.template</mime-type>
    </mime-mapping>

</web-app>
