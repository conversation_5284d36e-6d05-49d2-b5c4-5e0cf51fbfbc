
# From https://hub.docker.com/r/wnameless/oracle-xe-11g/
docker run --name oracle-11g -d -p 49160:22 -p 1521:1521 wnameless/oracle-xe-11g

# VM args:
# -Ddb=oracle -Djdbc.username=system -Djdbc.password=oracle -Denv=dev -Ddb.schema=ecco -Ddb.extraContextsx=data-reset -Dliquibase=CREATE -Duser.timezone=UTC -Xmx400m -Djava.net.preferIPv4Stack=true

# Will uses defaults
#sid: xe
#username: system
#password: oracle
#Password for SYS & SYSTEM

# Can ssh using
#ssh root@localhost -p 49160
#password: admin



