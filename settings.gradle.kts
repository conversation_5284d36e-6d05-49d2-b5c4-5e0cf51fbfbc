/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */


rootProject.name = "ecco-aggregator"
include(":commons-logging")
include(":ecco-acceptance-tests")
include(":ecco-buildings")
include(":ecco-calendar")
include(":ecco-calendar-core")
include(":ecco-calendar-cosmo")
include(":ecco-config")
include(":ecco-contracts")
include(":ecco-data-client")
include(":ecco-dao")
include(":ecco-dom")
include(":ecco-contacts")
include(":ecco-evidence")
include(":ecco-finance")
include(":ecco-group-support")
include(":ecco-hr")
include(":ecco-incidents")
include(":ecco-repairs")
include(":ecco-managedvoids")
include(":ecco-messaging")
include(":ecco-notifications")
include(":ecco-int-core")
include(":ecco-int-homemaster")
include(":ecco-int-northgate")
include(":ecco-int-oh")
include(":ecco-int-ql")
include(":ecco-infrastructure")
include(":ecco-offline")
include(":ecco-int-api-default")
include(":ecco-utils")
include(":ecco-security")
include(":ecco-security-core")
include(":ecco-reports")
include(":ecco-rota")
include(":ecco-security-ldap")
include(":ecco-service")
include(":ecco-service-config")
include(":ecco-servicerecipient")
include(":ecco-submissions-sp")
include(":ecco-upload-dom")
include(":ecco-upload-web")
include(":ecco-web")
include(":ecco-web-api")
include(":ecco-webapi-boot")
include(":ecco-workflow")
include(":ecco-workflow-activiti")
include(":parent")
include(":test-entities")
include(":test-support")
project(":commons-logging").projectDir = file("fake-commons-logging")

// Work around incorrect gradle metadata in dom4j.
// See: https://github.com/dom4j/dom4j/pull/116#issuecomment-770092526
// This should be removed after we upgrade to dom4j 2.2.0.
dependencyResolutionManagement {
    components {
        withModule("org.dom4j:dom4j") {
            allVariants {
                withDependencies {
                    clear()
                }
            }
        }
    }
}
