package com.ecco.test.entities.dom;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;

@Entity
@Table(name="test_partners")
public class TestPartner extends AbstractLongKeyedEntity {

    private static final long serialVersionUID = 1L;

    String name = null;

    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }

}
